package net.summerfarm.wnc.inbound.provider.warehouse.converter;

import net.summerfarm.wnc.api.warehouse.dto.SkuWarehouseMappingDTO;
import net.summerfarm.wnc.client.resp.SkuWarehouseMappingResp;

/**
 * Description:SKU库存仓数据数据转换器
 * date: 2023/9/4 11:19
 *
 * <AUTHOR>
 */
public class SkuWarehouseMappingDTOConverter {

    public static SkuWarehouseMappingResp dto2Resp(SkuWarehouseMappingDTO skuWarehouseMappingDTO){
        if (skuWarehouseMappingDTO == null){
            return null;
        }
        SkuWarehouseMappingResp skuWarehouseMappingResp = new SkuWarehouseMappingResp();
        skuWarehouseMappingResp.setSku(skuWarehouseMappingDTO.getSku());
        skuWarehouseMappingResp.setStoreNo(skuWarehouseMappingDTO.getStoreNo());
        skuWarehouseMappingResp.setWarehouseNo(skuWarehouseMappingDTO.getWarehouseNo());
        return skuWarehouseMappingResp;
    }
}
