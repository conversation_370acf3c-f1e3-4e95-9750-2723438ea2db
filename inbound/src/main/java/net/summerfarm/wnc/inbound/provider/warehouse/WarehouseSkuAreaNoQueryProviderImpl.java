package net.summerfarm.wnc.inbound.provider.warehouse;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.warehouse.dto.SkuWarehouseMappingDTO;
import net.summerfarm.wnc.api.warehouse.dto.WarehouseBySkuAreaNoDTO;
import net.summerfarm.wnc.api.warehouse.service.warehouse.WncSkuWarehouseMappingService;
import net.summerfarm.wnc.api.warehouse.service.warehouse.WncWarehouseCenterBySkuAreaService;
import net.summerfarm.wnc.client.provider.warehouse.WarehouseSkuAreaNoQueryProvider;
import net.summerfarm.wnc.client.req.WarehouseBySkuAreaNoQueryReq;
import net.summerfarm.wnc.client.req.WarehouseBySkuStoreNoQueryReq;
import net.summerfarm.wnc.client.resp.SkuWarehouseMappingResp;
import net.summerfarm.wnc.client.resp.WarehouseBySkuAreaNoResp;
import net.summerfarm.wnc.common.query.warehouse.SkuWarehouseMappingQuery;
import net.summerfarm.wnc.inbound.provider.warehouse.converter.SkuWarehouseMappingDTOConverter;
import net.summerfarm.wnc.inbound.provider.warehouse.converter.WarehouseSkuAreaDTOConverter;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023-06-30
 **/
@Slf4j
@DubboService
public class WarehouseSkuAreaNoQueryProviderImpl implements WarehouseSkuAreaNoQueryProvider {
	@Resource
	private WncWarehouseCenterBySkuAreaService wncWarehouseCenterBySkuAreaService;
	@Resource
	private WarehouseSkuAreaDTOConverter warehouseSkuAreaVOConverter;
	@Resource
	private WncSkuWarehouseMappingService wncSkuWarehouseMappingService;

	@Override
	public DubboResponse<List<WarehouseBySkuAreaNoResp>> queryBySkuAreNo(WarehouseBySkuAreaNoQueryReq req) {
		log.info("根据区域编号和sku查询库存仓信息请求参数{}", JSONObject.toJSONString(req));
		if (req.getAreaSkuList().size() > 50) {
			return DubboResponse.getDefaultError("批量查询数据最多50条");
		}
		List<WarehouseBySkuAreaNoDTO> warehouseBySkuAreaNoDTOS = wncWarehouseCenterBySkuAreaService.queryBySkuArea(warehouseSkuAreaVOConverter
				.dubboQuery2Query(req.getAreaSkuList()));
		return DubboResponse.getOK(warehouseSkuAreaVOConverter.response2Dubbo(warehouseBySkuAreaNoDTOS));
	}

	@Override
	public DubboResponse<List<SkuWarehouseMappingResp>> querySkuWarehouseMappings(@Valid WarehouseBySkuStoreNoQueryReq req) {
		log.info("根据城配仓编号和sku查询库存仓信息请求参数：{}", JSONObject.toJSONString(req));
		List<SkuWarehouseMappingQuery> skuWarehouseMappingQueries = req.getWarehouseBySkuStoreNoDataReqList().stream().map(e -> {
			SkuWarehouseMappingQuery query = new SkuWarehouseMappingQuery();
			query.setStoreNo(e.getStoreNo());
			query.setSku(e.getSku());
			return query;
		}).collect(Collectors.toList());
		List<SkuWarehouseMappingDTO> skuWarehouseMappingDTOList = wncSkuWarehouseMappingService.querySkuWarehouseMappings(skuWarehouseMappingQueries);
		return DubboResponse.getOK(skuWarehouseMappingDTOList.stream().map(SkuWarehouseMappingDTOConverter::dto2Resp).collect(Collectors.toList()));
	}
}
