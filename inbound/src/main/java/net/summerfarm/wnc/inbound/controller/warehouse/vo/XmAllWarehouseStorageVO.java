package net.summerfarm.wnc.inbound.controller.warehouse.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: 鲜沐全部仓库信息<br/>
 * date: 2024/4/18 15:13<br/>
 *
 * <AUTHOR> />
 */
@Data
public class XmAllWarehouseStorageVO {
    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 仓库负责人
     */
    private Integer manageAdminId;

    /**
     * 仓库类型：0、本部仓 1、外部仓 2、合伙人仓
     */
    private Integer type;

    /**
     * 仓库所属合伙人
     */
    private Integer areaManageId;

    /**
     * 开放状态：0、不开放 1、开放
     */
    private Integer status;
    /**
     * 仓库地址
     */
    private String address;

    /**
     * 高德poi
     */
    private String poiNote;

    /**
     * 邮件接收人
     */
    private String mailToAddress;

    /**
     * 修改人
     */
    private Integer updater;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 联系人
     */
    private String personContact;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 所属租户0鲜沐
     */
    private Long tenantId;

    /**
     * 仓库负责人
     */
    private String manageAdminName;

    /**
     * 收货标准
     */
    private List<WarehouseTakeStandardVO> warehouseTakeStandardVO;

    /**
     * 仓库照片
     */
    private String warehousePic;

    /**
     * 库位管理状态（0：未开启库位管理，1：已开启库位管理，2：开启中）
     */
    private String cabinetStatus;

    /**
     * 仓库外部对接状态（0：未开启，1：已开启）
     */
    private String externalStatus;

    /**
     * 产能、工作时间
     */
    private WarehouseStorageCenterBusinessVO warehouseStorageCenterBusinessVO;
}
