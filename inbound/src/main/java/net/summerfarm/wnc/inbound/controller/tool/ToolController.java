package net.summerfarm.wnc.inbound.controller.tool;

import net.summerfarm.wnc.api.tool.service.ToolService;
import net.summerfarm.wnc.common.query.fence.SkuAddressQuery;
import net.summerfarm.wnc.common.query.fence.StoreQuery;
import net.summerfarm.wnc.api.tool.dto.FenceRelateVO;
import net.summerfarm.wnc.api.tool.dto.StoreAddressVO;
import net.xianmu.common.result.CommonResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * Description:工具箱
 * date: 2023/12/7 18:17
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/tool")
public class ToolController {

    @Resource
    private ToolService toolService;

    /**
     * 查询关联地址信息
     * @param storeQuery 查询
     * @return 结果
     */
    @PostMapping("/query/address/list")
    public CommonResult<List<StoreAddressVO>> queryAddressList(@RequestBody @Validated StoreQuery storeQuery) {
        List<StoreAddressVO> storeAddressList = toolService.queryAddressList(storeQuery);
        return CommonResult.ok(storeAddressList);
    }


    /**
     * 根据地址查询围栏关联信息
     * @param skuAddressQuery 查询
     * @return 结果
     */
    @PostMapping("/query/fence-relate-info")
    public CommonResult<FenceRelateVO> queryFenceRelateInfo(@RequestBody @Validated SkuAddressQuery skuAddressQuery) {
        FenceRelateVO fenceRelateVO = toolService.queryFenceRelateInfo(skuAddressQuery);
        return CommonResult.ok(fenceRelateVO);
    }
}
