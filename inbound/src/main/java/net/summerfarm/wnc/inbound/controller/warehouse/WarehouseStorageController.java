package net.summerfarm.wnc.inbound.controller.warehouse;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.api.base.BaseController;
import net.summerfarm.wnc.api.fence.service.WarehouseInventoryMappingService;
import net.summerfarm.wnc.api.warehouse.dto.WarehouseStorageDTO;
import net.summerfarm.wnc.api.warehouse.input.*;
import net.summerfarm.wnc.api.warehouse.service.warehouse.WarehouseStorageCenterService;
import net.summerfarm.wnc.client.mq.WncMqConstants;
import net.summerfarm.wnc.client.mq.msg.WarehouseStorageCreateMsg;
import net.summerfarm.wnc.common.base.WncAssert;
import net.summerfarm.wnc.common.constants.AppConsts;
import net.summerfarm.wnc.common.query.warehouse.WarehouseStorageFenceQuery;
import net.summerfarm.wnc.common.query.warehouse.WarehouseStorageQuery;
import net.summerfarm.wnc.inbound.controller.warehouse.input.XmAllWarehouseInput;
import net.summerfarm.wnc.inbound.controller.warehouse.vo.*;
import net.summerfarm.wnc.inbound.controller.warehouse.converter.WarehouseStoragePageVOConverter;
import net.summerfarm.wnc.inbound.controller.warehouse.converter.WarehouseStorageVOConverter;
import net.summerfarm.wnc.inbound.provider.warehouse.converter.WarehouseStorageDTOConverter;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.common.user.UserBase;
import net.xianmu.common.user.UserInfoHolder;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 自营仓
 * <AUTHOR> />
 */
@RestController
@RequestMapping("/warehouse-storage")
public class WarehouseStorageController extends BaseController {

    @Resource
    private WarehouseStorageCenterService warehouseStorageCenterService;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private WarehouseInventoryMappingService warehouseInventoryMappingService;
    /**
     * 获取仓库列表
     */
    @PostMapping("/query/warehouse")
    public CommonResult<List<WarehouseStorageListVO>> queryWarehouseList(@RequestBody WarehouseStorageQuery warehouseStorageQueryInput) {
        warehouseStorageQueryInput.setTenantId(this.getTenantId());
        List<WarehouseStorageDTO> warehouseStorageDTOList = warehouseStorageCenterService.queryWarehouseList(warehouseStorageQueryInput);
        return CommonResult.ok(warehouseStorageDTOList.stream().map(WarehouseStorageVOConverter::warehouseStorageDto2WarehouseListStorageVO).collect(Collectors.toList()));
    }

    /**
     * 保存自营库存仓信息
     */
    @PostMapping("/upsert/save")
    public CommonResult<WarehouseStorageDTO> save(@RequestBody @Validated WarehouseStorageSaveInput warehouseStorageSaveCommandInput) {
        Long bizUserId = getBizUserId();
        if(bizUserId == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,"获取操作人信息异常");
        }
        warehouseStorageSaveCommandInput.setCreateAdminId(Integer.parseInt(getBizUserId().toString()));
        warehouseStorageSaveCommandInput.setTenantId(getTenantId());
        warehouseStorageSaveCommandInput.setOperatorName(getBizUserName());
        WarehouseStorageDTO warehouseStorageDTO = warehouseStorageCenterService.warehouseStorageSave(warehouseStorageSaveCommandInput);
        //发送消息
        WarehouseStorageCreateMsg warehouseStorageCreateMsg = new WarehouseStorageCreateMsg();
        warehouseStorageCreateMsg.setTenantId(warehouseStorageSaveCommandInput.getTenantId());
        warehouseStorageCreateMsg.setWarehouseNo(warehouseStorageDTO.getWarehouseNo());
        warehouseStorageCreateMsg.setWarehouseName(warehouseStorageSaveCommandInput.getWarehouseName());
        mqProducer.send(WncMqConstants.Topic.TOPIC_WNC_WAREHOUSE,WncMqConstants.Tag.TAG_WNC_WAREHOUSE_CREATE,warehouseStorageCreateMsg);
        return CommonResult.ok(warehouseStorageDTO);
    }

    /**
     * 更新自营库存仓信息
     */
    @PostMapping("/upsert/update")
    public CommonResult<Void> update(@RequestBody @Validated WarehouseStorageUpdateInput warehouseStorageUpdateInput) {
        warehouseStorageUpdateInput.setCreateAdminId(getBizUserId());
        warehouseStorageUpdateInput.setTenantId(getTenantId());
        warehouseStorageCenterService.warehouseStorageUpdate(warehouseStorageUpdateInput);

        return CommonResult.ok();
    }

    /**
     * 查询自营仓或三方仓库列表
     */
    @PostMapping("/query/list")
    public CommonResult<PageInfo<WarehouseStoragePageVO>> queryList(@RequestBody WarehouseStorageQuery warehouseStorageQueryInput) {
        WncAssert.notNull(warehouseStorageQueryInput.getWarehouseSource(),"warehouseSource不能为空");
        warehouseStorageQueryInput.setTenantId(getTenantId());
        warehouseStorageQueryInput.setDesc(1);
        PageInfo<WarehouseStorageDTO> warehouseStorageDTOPageInfo = warehouseStorageCenterService.queryPage(warehouseStorageQueryInput);

        PageInfo<WarehouseStoragePageVO> warehouseStoragePageVOPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(warehouseStorageDTOPageInfo, warehouseStoragePageVOPageInfo);
        if(!CollectionUtils.isEmpty(warehouseStorageDTOPageInfo.getList())){
            warehouseStoragePageVOPageInfo.setList(warehouseStorageDTOPageInfo.getList().stream()
                    .map(WarehouseStoragePageVOConverter::dto2Vo).collect(Collectors.toList()));
        }
        return CommonResult.ok(warehouseStoragePageVOPageInfo);
    }

    /**
     * 仓库详情信息
     */
    @PostMapping("/query/detail")
    public CommonResult<WarehouseStorageVO> detail(@RequestBody WarehouseStorageQuery warehouseStorageQuery) {
        WncAssert.notNull(warehouseStorageQuery.getId(),"id不能为空");
        WarehouseStorageDTO warehouseStorageDTO = warehouseStorageCenterService.queryDetail(warehouseStorageQuery.getId());
        if(warehouseStorageDTO != null && !Objects.equals(warehouseStorageDTO.getTenantId(),getTenantId())){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,"数据不存在");
        }
        return CommonResult.ok(WarehouseStorageVOConverter.warehouseStorageDto2WarehouseStorageVO(warehouseStorageDTO));
    }

    /**
     * 仓库详情信息通过仓库编号查询
     */
    @PostMapping("/query/detail-warehouse-no")
    public CommonResult<WarehouseStorageVO> detailWarehouseNo(@RequestBody WarehouseStorageQuery warehouseStorageQuery) {
        WncAssert.notNull(warehouseStorageQuery.getWarehouseNo(),"warehouseNo不能为空");
        return CommonResult.ok(WarehouseStorageVOConverter.warehouseStorageDto2WarehouseStorageVO(warehouseStorageCenterService.detailWarehouseNo(warehouseStorageQuery.getWarehouseNo())));
    }

    /**
     * 配送管理-列表查询
     */
    @PostMapping("/query/fence-list")
    public CommonResult<PageInfo<WarehouseStorageFencePageVO>> queryFenceList(@RequestBody WarehouseStorageFenceQuery warehouseStorageFenceQueryInput) {
        warehouseStorageFenceQueryInput.setTenantId(getTenantId());
        PageInfo<WarehouseStorageDTO> warehouseStorageDTOPageInfo = warehouseStorageCenterService.queryFenceList(warehouseStorageFenceQueryInput);

        PageInfo<WarehouseStorageFencePageVO> warehouseStorageFencePageVOPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(warehouseStorageDTOPageInfo, warehouseStorageFencePageVOPageInfo);
        if(CollectionUtils.isNotEmpty(warehouseStorageDTOPageInfo.getList())){
            warehouseStorageFencePageVOPageInfo.setList(warehouseStorageDTOPageInfo.getList().stream()
                    .map(WarehouseStorageDTOConverter::dto2WarehouseStorageFencePage).collect(Collectors.toList()));
        }

        return CommonResult.ok(warehouseStorageFencePageVOPageInfo);
    }

    /**
     * 配送管理-编辑配送区域
     */
    @PostMapping("/upsert/fence-edit")
    public CommonResult<Void> fenceEdit(@RequestBody @Validated WarehouseFenceEditInput warehouseFenceEditInput) {
        warehouseFenceEditInput.setOperatorName(getBizUserName());
        warehouseStorageCenterService.fenceEdit(warehouseFenceEditInput);
        return CommonResult.ok();
    }

    /**
     * 巡检映射sku补充数据接口
     */
    @PostMapping("/upsert/handle-area-mapping")
    public CommonResult<Void> handleAreaMappingQuestion(@RequestBody List<Integer> areaNos) {
        for (Integer areaNo : areaNos) {
            warehouseInventoryMappingService.handleAreaMappingQuestion(areaNo,false);
        }
        return CommonResult.ok();
    }

    /**
     * saas巡检映射sku补充数据接口
     */
    @PostMapping("/upsert/saas-handle-area-mapping")
    public CommonResult<Void> saasHandleAreaMappingQuestion(@RequestBody List<Integer> areaNos) {
        for (Integer areaNo : areaNos) {
            warehouseInventoryMappingService.handleAreaMappingQuestion(areaNo,true);
        }
        return CommonResult.ok();
    }

    /**
     * 围栏开启-初始化数据
     */
    @PostMapping("/upsert/mapping-handle-init")
    public CommonResult<Void> mappingDataFix(Integer areaNo,Integer storeNo,Integer packId) {
        warehouseInventoryMappingService.mappingHandleInit(areaNo,storeNo,packId);
        return CommonResult.ok();
    }

    /**
     * 围栏映射关系-初始化数据
     */
    @PostMapping("/upsert/mapping-init-all-fence")
    public CommonResult<Void> mappingDataAllFenceaFix(Integer beginId,Integer endId) {
        warehouseInventoryMappingService.mappingDataAllFenceaFix(beginId,endId);
        return CommonResult.ok();
    }

    /**
     * 映射关系数据修复-运营区域sku多仓映射
     */
    @PostMapping("/upsert/mapping-data-fix")
    public CommonResult<Void> mappingDataFix(@RequestBody List<Integer> areaNos) {
        for (Integer areaNo : areaNos) {
            warehouseInventoryMappingService.mappingDataFix(areaNo);
        }
        return CommonResult.ok();
    }

    /**
     * 查询仓库列表
     */
    @PostMapping("/query/xm-warehouse-list")
    public CommonResult<PageInfo<XmWarehouseStoragePageVO>> queryXmWarehousePageList(@RequestBody WarehouseStorageQuery warehouseStorageQuery) {
        //鲜沐租户
        warehouseStorageQuery.setTenantId(1L);
        return CommonResult.ok(WarehouseStoragePageVOConverter.dtoPage2XmVoPage(warehouseStorageCenterService.queryXmWarehousePageList(warehouseStorageQuery)));
    }

    /**
     * 更新鲜沐仓库信息
     */
    @PostMapping("/upsert/xm-warehouse")
    public CommonResult<Void> updateXmWarehouseStorage(@RequestBody @Validated XmWarehouseStorageUpdateInput xmWarehouseStorageUpdateInput) {
        warehouseStorageCenterService.updateXmWarehouseStorage(xmWarehouseStorageUpdateInput);
        return CommonResult.ok();
    }

    /**
     * 查询鲜沐仓库详情信息
     */
    @PostMapping("/query/xm-warehouse-detail")
    public CommonResult<XmWarehouseStorageVO> queryXmWarehouseDetail(@RequestBody WarehouseStorageQuery warehouseStorageQuery) {
        WncAssert.notNull(warehouseStorageQuery.getWarehouseNo(),"warehouseNo不能为空");
        return CommonResult.ok(WarehouseStorageVOConverter.warehouseStorageDto2XmVO(warehouseStorageCenterService.detailWarehouseNo(warehouseStorageQuery.getWarehouseNo())));
    }

    /**
     * 新增鲜沐仓库详情信息
     */
    @PostMapping("/upsert/xm-warehouse-save")
    public CommonResult<Void> xmWarehouseSave(@RequestBody @Validated XmWarehouseStorageSaveInput xmWarehouseStorageSaveInput) {
        //鲜沐租户
        xmWarehouseStorageSaveInput.setTenantId(1L);
        warehouseStorageCenterService.saveXmWarehouse(xmWarehouseStorageSaveInput);
        return CommonResult.ok();
    }


    /**
     * 获取鲜沐-三方仓所有仓库开放的列表
     */
    @PostMapping("/query/xm-third-all-warehouse")
    public CommonResult<List<XmThirdAllWarehouseVO>> queryXmThirdAllWarehouseList() {
        return CommonResult.ok(WarehouseStorageVOConverter.dto2XmThirdVO(warehouseStorageCenterService.queryXmThirdAllWarehouseList()));
    }


    /**
     * 获取鲜沐所有的仓库信息
     */
    @PostMapping("/query/xm-all-warehouse")
    public CommonResult<List<XmAllWarehouseStorageVO>> queryXmAllWarehouseList(@RequestBody(required = false) XmAllWarehouseInput input) {
        //获取操作人信息
        UserBase user = UserInfoHolder.getUser();
        if(user == null){
            throw new RuntimeException("登录人不能为空");
        }
        //获取当前操作人仓库权限
        List<Integer> warehouseNos = warehouseStorageCenterService.queryWarehouseNosDataPermission(user.getBizUserId(),AppConsts.Tenant.XM_TENANT_ID);
        if(CollectionUtils.isEmpty(warehouseNos)){
            return CommonResult.ok();
        }
        List<WarehouseStorageDTO> warehouseStorageDTOS = warehouseStorageCenterService.queryAllWarehouseList(WarehouseStorageQuery.builder()
                .type(input != null ? input.getType() : null)
                .status(input != null ? input.getStatus() : null)
                .tenantId(AppConsts.Tenant.XM_TENANT_ID)
                .warehouseNos(warehouseNos)
                .build());

        return CommonResult.ok(warehouseStorageDTOS.stream().map(WarehouseStorageVOConverter::warehouseStorageDto2XmAllVO).collect(Collectors.toList()));
    }
}
