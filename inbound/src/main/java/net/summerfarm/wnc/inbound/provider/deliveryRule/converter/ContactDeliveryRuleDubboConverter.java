package net.summerfarm.wnc.inbound.provider.deliveryRule.converter;

import net.summerfarm.wnc.api.deliveryRule.dto.ContactDeliveryRuleDTO;
import net.summerfarm.wnc.client.resp.deliveryRule.XmContactDeliveryRuleResp;

/**
 * Description: <br/>
 * date: 2023/11/14 11:26<br/>
 *
 * <AUTHOR> />
 */
public class ContactDeliveryRuleDubboConverter {

    public static XmContactDeliveryRuleResp dto2Resp(ContactDeliveryRuleDTO dto){
        if(dto == null){
            return null;
        }

        XmContactDeliveryRuleResp resp = new XmContactDeliveryRuleResp();

        resp.setOutBusinessNo(dto.getOutBusinessNo());
        resp.setFrequentMethod(dto.getFrequentMethod());
        resp.setWeekDeliveryFrequent(dto.getWeekDeliveryFrequent());
        resp.setDeliveryFrequentInterval(dto.getDeliveryFrequentInterval());
        resp.setBeginCalculateDate(dto.getBeginCalculateDate());

        return resp;
    }
}
