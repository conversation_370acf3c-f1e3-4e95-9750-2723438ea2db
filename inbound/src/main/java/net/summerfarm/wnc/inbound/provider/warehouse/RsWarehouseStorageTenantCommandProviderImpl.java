package net.summerfarm.wnc.inbound.provider.warehouse;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.warehouse.dto.WncWarehouseStorageTenantDTO;
import net.summerfarm.wnc.api.warehouse.service.warehouse.WncWarehouseStorageTenantService;
import net.summerfarm.wnc.client.provider.warehouse.RsWarehouseStorageTenantCommandProvider;
import net.summerfarm.wnc.client.req.RsWarehouseStorageTenantAddCommandReq;
import net.summerfarm.wnc.client.req.RsWarehouseStorageTenantDeleteCommandReq;
import net.summerfarm.wnc.common.base.WncAssert;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * Description: <br/>
 * date: 2023/4/6 11:04<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@DubboService
public class RsWarehouseStorageTenantCommandProviderImpl implements RsWarehouseStorageTenantCommandProvider {

    @Resource
    private WncWarehouseStorageTenantService rsWarehouseStorageTenantService;

    /**
     * 新增绑定关系
     * @param rsWarehouseStorageTenantAddCommandReq 新增
     * @return 结果
     */
    @Override
    public DubboResponse<Void> insertWarehouseStorageTenant(RsWarehouseStorageTenantAddCommandReq rsWarehouseStorageTenantAddCommandReq) {
        log.info("新增绑定关系:{}", JSON.toJSONString(rsWarehouseStorageTenantAddCommandReq));

        WncAssert.notNull(rsWarehouseStorageTenantAddCommandReq.getTenantId(),"tenantId不能为空");
        WncAssert.notNull(rsWarehouseStorageTenantAddCommandReq.getWarehouseNo(),"warehouseNo不能为空");

        WncWarehouseStorageTenantDTO storageTenantDTO = new WncWarehouseStorageTenantDTO();
        storageTenantDTO.setTenantId(rsWarehouseStorageTenantAddCommandReq.getTenantId());
        storageTenantDTO.setWarehouseNo(rsWarehouseStorageTenantAddCommandReq.getWarehouseNo());

        rsWarehouseStorageTenantService.bindingTenant(storageTenantDTO);
        return DubboResponse.getOK();
    }

    /**
     * 删除绑定关系
     * @param rsWarehouseStorageTenantDeleteCommandReq 删除
     * @return 结果
     */
    @Override
    public DubboResponse<Void> deleteWarehouseStorageTenant(RsWarehouseStorageTenantDeleteCommandReq rsWarehouseStorageTenantDeleteCommandReq) {
        log.info("删除绑定关系:{}", JSON.toJSONString(rsWarehouseStorageTenantDeleteCommandReq));

        WncAssert.notNull(rsWarehouseStorageTenantDeleteCommandReq.getTenantId(),"tenantId不能为空");
        WncAssert.notNull(rsWarehouseStorageTenantDeleteCommandReq.getWarehouseNo(),"warehouseNo不能为空");

        WncWarehouseStorageTenantDTO storageTenantDTO = new WncWarehouseStorageTenantDTO();
        storageTenantDTO.setTenantId(rsWarehouseStorageTenantDeleteCommandReq.getTenantId());
        storageTenantDTO.setWarehouseNo(rsWarehouseStorageTenantDeleteCommandReq.getWarehouseNo());

        rsWarehouseStorageTenantService.unbindingTenant(storageTenantDTO);
        return DubboResponse.getOK();
    }
}
