package net.summerfarm.wnc.inbound.controller.warehouse.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/3/30 16:43<br/>
 *
 * <AUTHOR> />
 */
@Data
public class WarehouseStoragePageVO {
    /**
     * id
     */
    private Integer id;
    /**
     * 仓库编号
     */
    private Integer warehouseNo;
    /**
     * 仓库名称
     */
    private String warehouseName;
    /**
     * 仓库服务商
     */
    private String warehouseServiceName;
    /**
     * 配送区域信息
     */
    private List<WarehouseStorageFenceVO> fenceList;
    /**
     * 仓库联系人
     */
    private String personContact;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 开放状态：0、不开放 1、开放
     */
    private Integer status;
}
