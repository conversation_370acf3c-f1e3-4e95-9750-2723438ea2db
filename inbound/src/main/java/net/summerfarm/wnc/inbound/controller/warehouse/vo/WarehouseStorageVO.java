package net.summerfarm.wnc.inbound.controller.warehouse.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/3/29 18:11<br/>
 *
 * <AUTHOR> />
 */
@Data
public class WarehouseStorageVO implements Serializable {
    /**
     * 仓库编号
     */
    private Integer warehouseNo;
    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 仓库地址
     */
    private String address;

    /**
     * poi
     */
    private String poiNote;

    /**
     * 仓库联系人
     */
    private String personContact;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 租户ID 鲜沐默认为0
     */
    private Long tenantId;

    /**
     * 产能
     */
    private Long capacity;

    /**
     * 预约提前期
     */
    private Integer advanceDay;
    /**
     * 开放状态：0、不开放 1、开放
     */
    private Integer status;
    /**
     * 收货标准
     */
    private List<WarehouseTakeStandardVO> warehouseTakeStandards;

    /**
     * 配送范围
     */
    private List<WarehouseStorageFenceVO> sendFenceList;

    /**
     * 鲜沐仓库负责人名称
     */
    private String manageAdminName;
}
