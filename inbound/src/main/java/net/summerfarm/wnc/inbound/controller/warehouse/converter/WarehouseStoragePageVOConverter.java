package net.summerfarm.wnc.inbound.controller.warehouse.converter;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.api.warehouse.dto.WncWarehouseStorageFenceDTO;
import net.summerfarm.wnc.api.warehouse.dto.WarehouseStorageDTO;
import net.summerfarm.wnc.inbound.controller.warehouse.vo.WarehouseStorageFenceVO;
import net.summerfarm.wnc.inbound.controller.warehouse.vo.WarehouseStoragePageVO;
import net.summerfarm.wnc.inbound.controller.warehouse.vo.XmWarehouseStoragePageVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/4/4 11:05<br/>
 *
 * <AUTHOR> />
 */
public class WarehouseStoragePageVOConverter {

    public static WarehouseStoragePageVO dto2Vo(WarehouseStorageDTO warehouseStorageDTO) {
        if (warehouseStorageDTO == null) {
            return null;
        }
        WarehouseStoragePageVO warehouseStoragePageVO = new WarehouseStoragePageVO();
        warehouseStoragePageVO.setId(warehouseStorageDTO.getId());
        warehouseStoragePageVO.setWarehouseNo(warehouseStorageDTO.getWarehouseNo());
        warehouseStoragePageVO.setWarehouseName(warehouseStorageDTO.getWarehouseName());
        if (!CollectionUtils.isEmpty(warehouseStorageDTO.getRcWarehouseStorageFenceDTOs())) {
            List<WarehouseStorageFenceVO> fenceVOS = warehouseStorageDTO.getRcWarehouseStorageFenceDTOs().stream().map(WarehouseStoragePageVOConverter::fenceDto2Vo).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(fenceVOS)){
                warehouseStoragePageVO.setFenceList(fenceVOS.stream().filter(vo -> vo != null).collect(Collectors.toList()));
            }
        }
        warehouseStoragePageVO.setPersonContact(warehouseStorageDTO.getPersonContact());
        warehouseStoragePageVO.setPhone(warehouseStorageDTO.getPhone());
        warehouseStoragePageVO.setCreateTime(warehouseStorageDTO.getCreateTime());
        warehouseStoragePageVO.setStatus(warehouseStorageDTO.getStatus());
        warehouseStoragePageVO.setWarehouseServiceName(warehouseStorageDTO.getWarehouseServiceName());

        return warehouseStoragePageVO;
    }

    public static WarehouseStorageFenceVO fenceDto2Vo(WncWarehouseStorageFenceDTO rcWarehouseStorageFenceDTO) {
        if(rcWarehouseStorageFenceDTO == null || (StringUtils.isBlank(rcWarehouseStorageFenceDTO.getProvince()) &&
                StringUtils.isBlank(rcWarehouseStorageFenceDTO.getCity()) && StringUtils.isBlank(rcWarehouseStorageFenceDTO.getArea()))){
            return null;
        }

        WarehouseStorageFenceVO warehouseStorageFenceVO = new WarehouseStorageFenceVO();
        warehouseStorageFenceVO.setProvince(rcWarehouseStorageFenceDTO.getProvince());
        warehouseStorageFenceVO.setCity(rcWarehouseStorageFenceDTO.getCity());
        warehouseStorageFenceVO.setArea(rcWarehouseStorageFenceDTO.getArea());

        return warehouseStorageFenceVO;
    }


    public static PageInfo<XmWarehouseStoragePageVO> dtoPage2XmVoPage(PageInfo<WarehouseStorageDTO> warehouseStorageDTOPageInfo) {
        PageInfo<XmWarehouseStoragePageVO> resultPage = new PageInfo<>();
        if(warehouseStorageDTOPageInfo == null){
           return resultPage;
        }
        BeanUtils.copyProperties(warehouseStorageDTOPageInfo, resultPage);
        if(!CollectionUtils.isEmpty(warehouseStorageDTOPageInfo.getList())){
            resultPage.setList(warehouseStorageDTOPageInfo.getList().stream()
                    .map(WarehouseStoragePageVOConverter::dto2XmVo).collect(Collectors.toList()));
        }

        return resultPage;
    }


    public static XmWarehouseStoragePageVO dto2XmVo(WarehouseStorageDTO warehouseStorageDTO) {
        if (warehouseStorageDTO == null) {
            return null;
        }
        XmWarehouseStoragePageVO xmWarehouseStoragePageVO = new XmWarehouseStoragePageVO();

        xmWarehouseStoragePageVO.setId(warehouseStorageDTO.getId());
        xmWarehouseStoragePageVO.setWarehouseNo(warehouseStorageDTO.getWarehouseNo());
        xmWarehouseStoragePageVO.setWarehouseName(warehouseStorageDTO.getWarehouseName());
        xmWarehouseStoragePageVO.setCreateTime(warehouseStorageDTO.getCreateTime());
        xmWarehouseStoragePageVO.setStatus(warehouseStorageDTO.getStatus());
        xmWarehouseStoragePageVO.setType(warehouseStorageDTO.getType());
        xmWarehouseStoragePageVO.setCabinetStatus(warehouseStorageDTO.getCabinetStatus());
        xmWarehouseStoragePageVO.setManageAdminName(warehouseStorageDTO.getManageAdminName());

        return xmWarehouseStoragePageVO;
    }
}