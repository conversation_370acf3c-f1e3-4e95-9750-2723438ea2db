package net.summerfarm.wnc.inbound.provider.config.converter;

import net.summerfarm.wnc.api.config.dto.ContactConfigDTO;
import net.summerfarm.wnc.api.config.dto.ContactConfigUpdateResultDTO;
import net.summerfarm.wnc.client.resp.ContactConfigResp;
import net.summerfarm.wnc.client.resp.storeConfig.ContactConfigAdjustResp;

/**
 * Description:联系人配置dto转化器
 * date: 2023/9/22 18:15
 *
 * <AUTHOR>
 */
public class ContactConfigDTOConverter {

    public static ContactConfigResp dto2Resp(ContactConfigDTO contactConfigDTO){
        if (contactConfigDTO == null){
            return null;
        }
        ContactConfigResp contactConfigResp = new ContactConfigResp();
        contactConfigResp.setContactId(contactConfigDTO.getOuterContactId());
        contactConfigResp.setStoreNo(contactConfigDTO.getStoreNo());
        return contactConfigResp;

    }

    public static ContactConfigAdjustResp resultDto2Resp(ContactConfigUpdateResultDTO contactConfigUpdateResultDTO){
        if (contactConfigUpdateResultDTO == null){
            return null;
        }
        ContactConfigAdjustResp contactConfigAdjustResp = new ContactConfigAdjustResp();
        contactConfigAdjustResp.setContactId(contactConfigUpdateResultDTO.getContactId());
        contactConfigAdjustResp.setTenantId(contactConfigUpdateResultDTO.getTenantId());
        contactConfigAdjustResp.setSuccess(contactConfigUpdateResultDTO.getSuccess());
        contactConfigAdjustResp.setFailMessage(contactConfigUpdateResultDTO.getFailMessage());
        return contactConfigAdjustResp;
    }
}
