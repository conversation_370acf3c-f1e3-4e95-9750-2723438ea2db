package net.summerfarm.wnc.inbound.provider.deliveryRule;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.deliveryRule.dto.ContactDeliveryRuleDTO;
import net.summerfarm.wnc.api.deliveryRule.service.ContactDeliveryRuleService;
import net.summerfarm.wnc.client.provider.deliveryRule.ContactDeliveryRuleQueryProvider;
import net.summerfarm.wnc.client.req.deliveryRule.XmContactDeliveryRuleQueryReq;
import net.summerfarm.wnc.client.resp.deliveryRule.XmContactDeliveryRuleResp;
import net.summerfarm.wnc.common.constants.AppConsts;
import net.summerfarm.wnc.common.enums.WncContactDeliveryRuleEnums;
import net.summerfarm.wnc.common.query.deliveryRule.ContactDeliveryRuleQuery;
import net.summerfarm.wnc.inbound.provider.deliveryRule.converter.ContactDeliveryRuleDubboConverter;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * Description: 地址配送规则查询
 * date: 2023/11/13 14:44<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@DubboService
public class ContactDeliveryRuleQueryProviderImpl implements ContactDeliveryRuleQueryProvider {

    @Resource
    private ContactDeliveryRuleService contactDeliveryRuleService;

    @Override
    public DubboResponse<XmContactDeliveryRuleResp> queryXmContactDeliveryRule(@Valid XmContactDeliveryRuleQueryReq req) {
        log.info("queryXmContactDeliveryRule req:{}", JSON.toJSONString(req));
        ContactDeliveryRuleDTO contactDeliveryRuleDTO = contactDeliveryRuleService.queryContactDeliveryRule(ContactDeliveryRuleQuery.builder()
                .outBusinessNo(req.getOutBusinessNo())
                .systemSource(WncContactDeliveryRuleEnums.SystemSource.XM.getValue())
                .tenantId(AppConsts.Tenant.XM_TENANT_ID)
                .build());
        log.info("queryXmContactDeliveryRule resq:{}", JSON.toJSONString(contactDeliveryRuleDTO));

        return DubboResponse.getOK(ContactDeliveryRuleDubboConverter.dto2Resp(contactDeliveryRuleDTO));
    }
}
