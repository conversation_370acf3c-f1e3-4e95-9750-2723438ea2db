package net.summerfarm.wnc.inbound.provider.fence.converter;

import net.summerfarm.wnc.client.req.AddressQueryReq;
import net.summerfarm.wnc.client.req.AreaQueryReq;
import net.summerfarm.wnc.client.req.fence.CityAreaQueryReq;
import net.summerfarm.wnc.client.req.fence.ContactAddressQueryReq;
import net.summerfarm.wnc.common.query.fence.AddressQuery;

/**
 * Description:联系人查询转换器
 * date: 2023/11/14 18:30
 *
 * <AUTHOR>
 */
public class ContactAddressReqConverter {

    public static AddressQuery addressReq2query(ContactAddressQueryReq contactAddressQueryReq){
        if (contactAddressQueryReq == null){
            return null;
        }
        AddressQuery addressQuery = new AddressQuery();
        AddressQueryReq addressReq = contactAddressQueryReq.getAddressReq();
        addressQuery.setProvince(addressReq.getProvince());
        addressQuery.setCity(addressReq.getCity());
        addressQuery.setArea(addressReq.getArea());
        addressQuery.setAddress(addressReq.getAddress());
        return addressQuery;
    }

    public static AddressQuery areaReq2query(AreaQueryReq addressQueryReq){
        if (addressQueryReq == null){
            return null;
        }
        AddressQuery addressQuery = new AddressQuery();
        addressQuery.setCity(addressQueryReq.getCity());
        addressQuery.setArea(addressQueryReq.getArea());
        return addressQuery;
    }


    public static AddressQuery cityAreaQueryReq2Query(CityAreaQueryReq req){
        if (req == null){
            return null;
        }
        AddressQuery addressQuery = new AddressQuery();
        addressQuery.setCity(req.getCity());
        addressQuery.setArea(req.getArea());
        return addressQuery;
    }

}
