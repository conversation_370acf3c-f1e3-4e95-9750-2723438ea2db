package net.summerfarm.wnc.inbound.controller.changeTask.converter;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.api.changeTask.dto.FenceChangeTaskDTO;
import net.summerfarm.wnc.api.changeTask.dto.FenceChangeTaskOrderDTO;
import net.summerfarm.wnc.inbound.controller.changeTask.vo.FenceChangeTaskOrderVO;
import net.summerfarm.wnc.inbound.controller.changeTask.vo.FenceChangeTaskVO;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Description:围栏切仓任务订单明细视图转换器
 * date: 2023/8/25 18:20
 *
 * <AUTHOR>
 */
public class FenceChangeTaskOrderVoConverter {

    public static FenceChangeTaskOrderVO dto2Vo(FenceChangeTaskOrderDTO fenceChangeTaskOrderDTO){
        if (fenceChangeTaskOrderDTO == null) {
            return null;
        }
        FenceChangeTaskOrderVO fenceChangeTaskOrderVO = new FenceChangeTaskOrderVO();
        fenceChangeTaskOrderVO.setId(fenceChangeTaskOrderDTO.getId());
        fenceChangeTaskOrderVO.setOuterOrderId(fenceChangeTaskOrderDTO.getOuterOrderId());
        fenceChangeTaskOrderVO.setSource(fenceChangeTaskOrderDTO.getSource());
        fenceChangeTaskOrderVO.setSourceDesc(fenceChangeTaskOrderDTO.getSourceDesc());
        fenceChangeTaskOrderVO.setOuterContactId(fenceChangeTaskOrderDTO.getOuterContactId());
        fenceChangeTaskOrderVO.setDeliveryTime(fenceChangeTaskOrderDTO.getDeliveryTime());
        fenceChangeTaskOrderVO.setOuterClientId(fenceChangeTaskOrderDTO.getOuterClientId());
        fenceChangeTaskOrderVO.setOuterClientName(fenceChangeTaskOrderDTO.getOuterClientName());
        fenceChangeTaskOrderVO.setRemark(fenceChangeTaskOrderDTO.getRemark());
        fenceChangeTaskOrderVO.setStatus(fenceChangeTaskOrderDTO.getStatus());
        fenceChangeTaskOrderVO.setStatusDesc(fenceChangeTaskOrderDTO.getStatus());
        fenceChangeTaskOrderVO.setFulfillConfirmTime(fenceChangeTaskOrderDTO.getFulfillConfirmTime());
        fenceChangeTaskOrderVO.setCreateTime(fenceChangeTaskOrderDTO.getCreateTime());
        fenceChangeTaskOrderVO.setUpdateTime(fenceChangeTaskOrderDTO.getUpdateTime());
        return fenceChangeTaskOrderVO;
    }

    public static PageInfo<FenceChangeTaskOrderVO> dtoPage2VoPage(PageInfo<FenceChangeTaskOrderDTO> dtoPageInfo) {
        List<FenceChangeTaskOrderDTO> fenceChangeTaskOrderDTOS = dtoPageInfo.getList();
        List<FenceChangeTaskOrderVO> fenceChangeTaskOrderVOS = fenceChangeTaskOrderDTOS.stream().map(FenceChangeTaskOrderVoConverter::dto2Vo).collect(Collectors.toList());

        PageInfo<FenceChangeTaskOrderVO> voPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(dtoPageInfo, voPageInfo);
        voPageInfo.setList(fenceChangeTaskOrderVOS);
        return voPageInfo;
    }
}
