package net.summerfarm.wnc.inbound.provider.delivery;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.fence.dto.DeliveryFenceDTO;
import net.summerfarm.wnc.api.fence.dto.SkuDeliveryDateDTO;
import net.summerfarm.wnc.api.fence.service.DeliveryFenceService;
import net.summerfarm.wnc.client.enums.SourceEnum;
import net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider;
import net.summerfarm.wnc.client.req.CategoryDeliveryRuleQueryReq;
import net.summerfarm.wnc.client.req.DeliveryRuleQueryReq;
import net.summerfarm.wnc.client.req.delivery.PayAfterDeliveryDateQueryReq;
import net.summerfarm.wnc.client.resp.CategoryDeliveryDateResp;
import net.summerfarm.wnc.client.resp.DeliveryRuleResp;
import net.summerfarm.wnc.client.resp.delivery.PayAfterDeliveryDateResp;
import net.summerfarm.wnc.common.base.WncAssert;
import net.summerfarm.wnc.common.config.WncConfig;
import net.summerfarm.wnc.common.constants.AppConsts;
import net.summerfarm.wnc.common.query.fence.DeliveryFenceDateQuery;
import net.summerfarm.wnc.common.query.fence.NeedOrderTimeQuery;
import net.summerfarm.wnc.common.util.RedisCacheUtil;
import net.summerfarm.wnc.inbound.provider.delivery.converter.DeliveryFenceDTOConverter;
import net.summerfarm.wnc.inbound.provider.delivery.converter.DeliveryRuleDubboConverter;
import net.summerfarm.wnc.inbound.provider.delivery.converter.PayAfterDeliveryDateQueryReqConverter;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/3/7 14:11<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@DubboService
public class DeliveryRuleQueryProviderImpl implements DeliveryRuleQueryProvider {

    @Resource
    private DeliveryFenceService deliveryFenceService;
    @Resource
    private RedisCacheUtil redisCacheUtil;
    @Resource
    private WncConfig wncConfig;

    @Override
    public DubboResponse<DeliveryRuleResp> queryDeliveryDateInfo(DeliveryRuleQueryReq deliveryRuleQueryReq) {
        log.info("查询配送日期请求报文:{}", JSON.toJSONString(deliveryRuleQueryReq));
        if (wncConfig.isCustomFencePoiWarningSwitchOpen() && StringUtils.isEmpty(deliveryRuleQueryReq.getPoi())) {
            log.error("\n poi参数为空 DeliveryRuleQueryProvider queryDeliveryDateInfo poi is null\n");
        }
        //参数校验
        checkRequestParam(deliveryRuleQueryReq);
        //查询sku配送日期相关信息
        DeliveryFenceDateQuery deliveryFenceDateQuery = DeliveryRuleDubboConverter.reqToQuery(deliveryRuleQueryReq);
        DeliveryFenceDTO deliveryFenceDTO = deliveryFenceService.querySkuDeliveryDateInfo(deliveryFenceDateQuery);
        //处理可配送日期
        if(!CollectionUtils.isEmpty(deliveryFenceDTO.getDeliveryDateList())){
            deliveryFenceService.handleWantDeliveryDate(deliveryFenceDTO.getDeliveryDateList(),deliveryFenceDTO);
        }
        log.info("查询配送日期返回结果:{}", JSON.toJSONString(deliveryFenceDTO));
        return DubboResponse.getOK(DeliveryFenceDTOConverter.dto2Resp(deliveryFenceDTO));
    }

    @Override
    public DubboResponse<DeliveryRuleResp> queryCacheDeliveryDateInfo(DeliveryRuleQueryReq deliveryRuleQueryReq) {
        log.info("查询缓存配送日期请求报文:{}", JSON.toJSONString(deliveryRuleQueryReq));
        if (wncConfig.isCustomFencePoiWarningSwitchOpen() && StringUtils.isEmpty(deliveryRuleQueryReq.getPoi())) {
            log.error("\n poi参数为空 DeliveryRuleQueryProvider queryCacheDeliveryDateInfo poi is null\n");
        }
        //参数校验
        checkRequestParam(deliveryRuleQueryReq);
        DeliveryFenceDTO deliveryFenceDTO = redisCacheUtil.getCacheObjectValue(
                "wnc:queryDeliveryTimeNew:cache" +
                        deliveryRuleQueryReq.getSource().getContent() +
                        deliveryRuleQueryReq.getContactId() +
                        deliveryRuleQueryReq.getMerchantId() +
                        deliveryRuleQueryReq.getCity() +
                        deliveryRuleQueryReq.getArea() +
                        deliveryRuleQueryReq.getOrderTime().toLocalDate() +
                        deliveryRuleQueryReq.getAddOrderFlag() +
                        deliveryRuleQueryReq.getNoNeedToStockUp(),
                60 * 5,
                () -> deliveryFenceService.queryDeliveryDateInfo(DeliveryRuleDubboConverter.reqToQuery(deliveryRuleQueryReq)),
                DeliveryFenceDTO.class);
        //获取sku配送信息
        List<SkuDeliveryDateDTO> skuDeliveryDateDTOList = deliveryFenceService.handleSkuDeliveryInfo(deliveryRuleQueryReq.getSkus(), deliveryFenceDTO);
        if(deliveryFenceDTO != null){
            deliveryFenceDTO.setSkuDeliveryDates(skuDeliveryDateDTOList);
        }
        //处理可配送日期
        if(deliveryFenceDTO != null && !CollectionUtils.isEmpty(deliveryFenceDTO.getDeliveryDateList())){
            deliveryFenceService.handleWantDeliveryDate(deliveryFenceDTO.getDeliveryDateList(),deliveryFenceDTO);
        }
        log.info("查询配送日期返回结果:{}", JSON.toJSONString(deliveryFenceDTO));
        return DubboResponse.getOK(DeliveryFenceDTOConverter.dto2Resp(deliveryFenceDTO));
    }

    @Override
    public DubboResponse<CategoryDeliveryDateResp> queryMallCategoryDeliveryDate(@Valid CategoryDeliveryRuleQueryReq categoryDeliveryRuleQueryReq) {
        log.info("查询全品类配送日期请求报文:{}", JSON.toJSONString(categoryDeliveryRuleQueryReq));
        DeliveryRuleQueryReq ruleQueryReq = new DeliveryRuleQueryReq();
        ruleQueryReq.setContactId(categoryDeliveryRuleQueryReq.getContactId());
        ruleQueryReq.setMerchantId(categoryDeliveryRuleQueryReq.getMerchantId());
        ruleQueryReq.setOrderTime(categoryDeliveryRuleQueryReq.getOrderTime());
        ruleQueryReq.setSource(SourceEnum.XM_MALL);
        //参数校验
        checkRequestParam(ruleQueryReq);

        //查询围栏配送信息
        DeliveryFenceDTO deliveryFenceDTO = redisCacheUtil.getCacheObjectValueRefreshByHalfTime(
                "wnc:queryMallCategoryDeliveryDate:cache" +
                        ruleQueryReq.getSource().getValue() +
                        ruleQueryReq.getContactId() +
                        ruleQueryReq.getMerchantId() +
                        ruleQueryReq.getOrderTime().toLocalDate(),
                60 * 5,
                () -> deliveryFenceService.queryDeliveryDateInfo(DeliveryRuleDubboConverter.reqToQuery(ruleQueryReq)),
                DeliveryFenceDTO.class);

        return DubboResponse.getOK(DeliveryFenceDTOConverter.dto2CategoryResp(deliveryFenceDTO));
    }

    @Override
    public DubboResponse<PayAfterDeliveryDateResp> queryPayAfterDeliveryDate(@Valid PayAfterDeliveryDateQueryReq req) {
        log.info("查询支付后配送日期请求报文:{}", JSON.toJSONString(req));
        if (wncConfig.isCustomFencePoiWarningSwitchOpen() && StringUtils.isEmpty(req.getPoi())) {
            log.error("\n poi参数为空 DeliveryRuleQueryProvider queryPayAfterDeliveryDate poi is null\n");
        }
        // 校验
        DeliveryRuleQueryReq ruleQueryReq = PayAfterDeliveryDateQueryReqConverter.pay2DeliveryRuleQueryReq(req);
        this.checkRequestParam(ruleQueryReq);

        List<String> allSkuList = req.getSkus();

        // 需要用下单时间查询配送日期的SKU集合
        List<String> needOrderTimeSkuList = deliveryFenceService.findNeedOrderTimeQueryDeliveryDateSkuList(NeedOrderTimeQuery.builder()
                .city(req.getCity())
                .area(req.getArea())
                .tenantId(req.getTenantId())
                .contactId(req.getContactId())
                .skus(req.getSkus())
                .build());

        // 支付时间
        LocalDateTime payTime = req.getPayTime();
        //下单时间
        LocalDateTime orderTime = req.getOrderTime();

        DeliveryFenceDateQuery deliveryFenceDateQuery = DeliveryRuleDubboConverter.reqToQuery(ruleQueryReq);
        // 没有需要用下单时间查询的SKU，则直接用支付时间 查询配送日期的
        if(CollectionUtils.isEmpty(needOrderTimeSkuList)){
            deliveryFenceDateQuery.setOrderTime(payTime);
            DeliveryFenceDTO deliveryFenceDTO = deliveryFenceService.querySkuDeliveryDateInfo(deliveryFenceDateQuery);
            return DubboResponse.getOK(DeliveryFenceDTOConverter.dto2PayAfterResp(deliveryFenceDTO));
        }

        ArrayList<SkuDeliveryDateDTO> allSkuDeliveryDateDTOList = new ArrayList<>();
        // 需要用支付时间查询配送日期的SKU集合
        List<String> needPayTimeSkuList = allSkuList.stream().filter(sku -> !needOrderTimeSkuList.contains(sku)).collect(Collectors.toList());
        deliveryFenceDateQuery.setOrderTime(payTime);
        deliveryFenceDateQuery.setSkus(needPayTimeSkuList);
        DeliveryFenceDTO payTimeResultDeliveryDateInfo = deliveryFenceService.querySkuDeliveryDateInfo(deliveryFenceDateQuery);
        List<SkuDeliveryDateDTO> payTimeSkuDeliveryDates = payTimeResultDeliveryDateInfo.getSkuDeliveryDates();

        // 需要用下单时间查询配送日期
        deliveryFenceDateQuery.setOrderTime(orderTime);
        deliveryFenceDateQuery.setSkus(needOrderTimeSkuList);
        DeliveryFenceDTO orderTimeResultDeliveryDateInfo = deliveryFenceService.querySkuDeliveryDateInfo(deliveryFenceDateQuery);
        List<SkuDeliveryDateDTO> orderTimeSkuDeliveryDates = orderTimeResultDeliveryDateInfo.getSkuDeliveryDates();

        // 组合返回结果
        allSkuDeliveryDateDTOList.addAll(payTimeSkuDeliveryDates);
        allSkuDeliveryDateDTOList.addAll(orderTimeSkuDeliveryDates);
        payTimeResultDeliveryDateInfo.setSkuDeliveryDates(allSkuDeliveryDateDTOList);

        return DubboResponse.getOK(DeliveryFenceDTOConverter.dto2PayAfterResp(payTimeResultDeliveryDateInfo));
    }
    private void checkRequestParam(DeliveryRuleQueryReq deliveryRuleQueryReq) {
        WncAssert.notNull(deliveryRuleQueryReq.getOrderTime(), "orderTime不能为空");
        WncAssert.notNull(deliveryRuleQueryReq.getSource(), "source不能为空");
        if (deliveryRuleQueryReq.getQueryBeginDate() != null) {
            WncAssert.notNull(deliveryRuleQueryReq.getQueryEndDate(), "queryEndDate不能为空");
            if (deliveryRuleQueryReq.getQueryBeginDate().isAfter(deliveryRuleQueryReq.getQueryEndDate())) {
                throw new BizException("queryEndDate、queryBeginDate入参不符合规范");
            }
        }
        if (deliveryRuleQueryReq.getQueryEndDate() != null) {
            WncAssert.notNull(deliveryRuleQueryReq.getQueryBeginDate(), "queryBeginDate不能为空");
        }
        //saas 需要城市和区域
        if (deliveryRuleQueryReq.getSource() == SourceEnum.SAAS_MALL || deliveryRuleQueryReq.getSource() == SourceEnum.SAAS_AFTER_SALE) {
            WncAssert.notNull(deliveryRuleQueryReq.getCity(), "city不能为空");
            if(deliveryRuleQueryReq.getTenantId() == null){
                log.error("查询客户配送日期方法租户ID为空，请注意，需要看下上游场景\\n");
            }
        }
        if (deliveryRuleQueryReq.getSource() == SourceEnum.XM_MALL || deliveryRuleQueryReq.getSource() == SourceEnum.XM_MALL_TIMING
                || deliveryRuleQueryReq.getSource() == SourceEnum.XM_SAMPLE_APPLY || deliveryRuleQueryReq.getSource() == SourceEnum.XM_AFTER_SALE
                || deliveryRuleQueryReq.getSource() == SourceEnum.POP_MALL || deliveryRuleQueryReq.getSource() == SourceEnum.POP_AFTER_SALE) {
            //鲜沐需要联系人id、商户id
            WncAssert.notNull(deliveryRuleQueryReq.getContactId(), "contactId不能为空");
            WncAssert.notNull(deliveryRuleQueryReq.getMerchantId(), "merchantId不能为空");
            deliveryRuleQueryReq.setTenantId(AppConsts.Tenant.XM_TENANT_ID);
        }
    }



}
