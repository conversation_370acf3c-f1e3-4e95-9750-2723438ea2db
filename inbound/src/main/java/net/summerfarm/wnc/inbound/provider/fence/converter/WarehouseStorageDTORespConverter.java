package net.summerfarm.wnc.inbound.provider.fence.converter;

import net.summerfarm.wnc.api.warehouse.dto.WarehouseStorageDTO;
import net.summerfarm.wnc.api.warehouse.dto.WncWarehouseStorageFenceDTO;
import net.summerfarm.wnc.client.enums.WarehouseSourceEnum;
import net.summerfarm.wnc.client.resp.WarehouseSkuFenceAreaResp;
import net.summerfarm.wnc.client.resp.WarehouseSkuFenceResp;
import net.summerfarm.wnc.client.resp.WarehouseSkuFenceStorageResp;
import net.summerfarm.wnc.inbound.provider.warehouse.converter.WarehouseStorageDTOConverter;
import org.springframework.util.CollectionUtils;

import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/4/18 17:50<br/>
 *
 * <AUTHOR> />
 */
public class WarehouseStorageDTORespConverter {


    public static WarehouseSkuFenceStorageResp storage2SkuFenceResp(WarehouseStorageDTO warehouseStorageDTO){
        if(warehouseStorageDTO == null){
            return null;
        }
        WarehouseSkuFenceStorageResp warehouseSkuFenceStorageResp = new WarehouseSkuFenceStorageResp();
        warehouseSkuFenceStorageResp.setWarehouseNo(warehouseStorageDTO.getWarehouseNo());
        warehouseSkuFenceStorageResp.setSourceEnum(Objects.equals(warehouseStorageDTO.getTenantId().toString(),WarehouseSourceEnum.SUMMERFARM_WAREHOUSE.getCode().toString()) ?
                WarehouseSourceEnum.SUMMERFARM_WAREHOUSE : WarehouseSourceEnum.SAAS_WAREHOUSE);

        if(!CollectionUtils.isEmpty(warehouseStorageDTO.getRcWarehouseStorageFenceDTOs())){
            warehouseSkuFenceStorageResp.setWarehouseSkuFenceAreas(warehouseStorageDTO.getRcWarehouseStorageFenceDTOs().stream()
                    .map(WarehouseStorageDTORespConverter::fence2SkuFenceAreaResp).collect(Collectors.toList()));
        }
        return warehouseSkuFenceStorageResp;
    }

    public static WarehouseSkuFenceAreaResp fence2SkuFenceAreaResp(WncWarehouseStorageFenceDTO wncWarehouseStorageFenceDTO){
        if(wncWarehouseStorageFenceDTO == null){
            return null;
        }
        WarehouseSkuFenceAreaResp warehouseSkuFenceAreaResp = new WarehouseSkuFenceAreaResp();
        warehouseSkuFenceAreaResp.setProvince(wncWarehouseStorageFenceDTO.getProvince());
        warehouseSkuFenceAreaResp.setCity(wncWarehouseStorageFenceDTO.getCity());
        warehouseSkuFenceAreaResp.setArea(wncWarehouseStorageFenceDTO.getArea());
        return warehouseSkuFenceAreaResp;
    }
}
