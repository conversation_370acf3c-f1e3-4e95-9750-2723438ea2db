package net.summerfarm.wnc.inbound.controller.warehouse.converter;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.api.warehouse.dto.WarehouseLogisticsCenterDTO;
import net.summerfarm.wnc.api.warehouse.dto.WarehouseStorageDTO;
import net.summerfarm.wnc.inbound.controller.warehouse.vo.*;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: 城配仓转化
 * date: 2023/10/8 10:41<br/>
 *
 * <AUTHOR> />
 */
public class WarehouseLogisticsCenterVOConverter {

    public static WarehouseLogisticsListVO pageDto2VoList(WarehouseLogisticsCenterDTO dto){
        if(dto == null){
            return null;
        }
        WarehouseLogisticsListVO vo = new WarehouseLogisticsListVO();

        vo.setId(dto.getId());
        vo.setStoreNo(dto.getStoreNo());
        vo.setRegion(dto.getRegion());
        vo.setStoreName(dto.getStoreName());
        vo.setStatus(dto.getStatus());
        vo.setCloseOrderType(dto.getCloseOrderType());
        if(!CollectionUtils.isEmpty(dto.getWarehouseStorageDTOList())){
            String warehouseNames = dto.getWarehouseStorageDTOList().stream().map(WarehouseStorageDTO::getWarehouseName).collect(Collectors.joining(","));
            vo.setWarehouseNames(warehouseNames);
        }
        vo.setCloseTime(dto.getCloseTime());
        vo.setStopDeliveryStatus(dto.getStopDeliveryStatus());
        vo.setFulfillmentType(dto.getFulfillmentType());
        vo.setFulfillmentTypeDesc(dto.getFulfillmentTypeDesc());
        if(dto.getStoreSiteDTO() != null){
            vo.setPunchState(dto.getStoreSiteDTO().getPunchState());
        }
        return vo;
    }


    public static PageInfo<WarehouseLogisticsListVO> dtoPage2VOPage(PageInfo<WarehouseLogisticsCenterDTO> dtoPageInfo){
        PageInfo<WarehouseLogisticsListVO> voPageInfo = new PageInfo<>();
        if(dtoPageInfo == null){
            return voPageInfo;
        }

        BeanUtils.copyProperties(dtoPageInfo,voPageInfo);
        if(!CollectionUtils.isEmpty(dtoPageInfo.getList())){
            List<WarehouseLogisticsListVO> voList = dtoPageInfo.getList().stream().map(WarehouseLogisticsCenterVOConverter::pageDto2VoList).collect(Collectors.toList());
            voPageInfo.setList(voList);
        }

        return voPageInfo;
    }

    public static WarehouseLogisticsDetailVO detailDTO2VO(WarehouseLogisticsCenterDTO dto){
        if(dto == null){
            return null;
        }
        WarehouseLogisticsDetailVO vo = new WarehouseLogisticsDetailVO();
        vo.setId(dto.getId());
        vo.setStoreNo(dto.getStoreNo());
        vo.setStoreName(dto.getStoreName());
        vo.setManageAdminId(dto.getManageAdminId());
        vo.setManageAdminName(dto.getManageAdminName());
        vo.setRegion(dto.getRegion());
        vo.setCloseTime(dto.getCloseTime());
        vo.setUpdateCloseTime(dto.getUpdateCloseTime());
        vo.setStatus(dto.getStatus());
        vo.setCloseOrderType(dto.getCloseOrderType());
        vo.setAddress(dto.getAddress());
        vo.setPoiNote(dto.getPoiNote());
        vo.setStorePic(dto.getStorePic());

        if(!CollectionUtils.isEmpty(dto.getWarehouseStorageDTOList())){
            vo.setWarehouseList(dto.getWarehouseStorageDTOList().stream().map(WarehouseStorageVOConverter::warehouseStorageDto2WarehouseListStorageVO).collect(Collectors.toList()));
        }
        if(dto.getStopDeliveryDTO() != null){
            vo.setShutdownStartTime(dto.getStopDeliveryDTO().getShutdownStartTime());
            vo.setShutdownEndTime(dto.getStopDeliveryDTO().getShutdownEndTime());
        }

        vo.setStopDeliveryStatus(dto.getStopDeliveryStatus());
        vo.setPersonContact(dto.getPersonContact());
        vo.setPhone(dto.getPhone());
        vo.setFulfillmentType(dto.getFulfillmentType());
        vo.setFulfillmentTypeDesc(dto.getFulfillmentTypeDesc());

        if(dto.getStoreSiteDTO() != null){
            vo.setPunchState(dto.getStoreSiteDTO().getPunchState());
            vo.setPunchDistance(dto.getStoreSiteDTO().getPunchDistance());
            vo.setOutTime(dto.getStoreSiteDTO().getOutTime());
            vo.setIntelligencePath(dto.getStoreSiteDTO().getIntelligencePath());
        }
        return vo;
    }

    public static WarehouseLogisticsAllVO allDto2VoList(WarehouseLogisticsCenterDTO dto){
        if(dto == null){
            return null;
        }
        WarehouseLogisticsAllVO vo = new WarehouseLogisticsAllVO();

        vo.setId(dto.getId());
        vo.setStoreNo(dto.getStoreNo());
        vo.setStoreName(dto.getStoreName());
        vo.setRegion(dto.getRegion());
        vo.setCloseTime(dto.getCloseTime());
        vo.setStatus(dto.getStatus());
        vo.setCloseOrderType(dto.getCloseOrderType());
        vo.setAddress(dto.getAddress());
        vo.setPoiNote(dto.getPoiNote());
        vo.setPersonContact(dto.getPersonContact());
        vo.setPhone(dto.getPhone());
        vo.setFulfillmentType(dto.getFulfillmentType());
        vo.setFulfillmentTypeDesc(dto.getFulfillmentTypeDesc());

        if(dto.getStoreSiteDTO() != null){
            vo.setPunchState(dto.getStoreSiteDTO().getPunchState());
            vo.setPunchDistance(dto.getStoreSiteDTO().getPunchDistance());
            vo.setOutTime(dto.getStoreSiteDTO().getOutTime());
            vo.setIntelligencePath(dto.getStoreSiteDTO().getIntelligencePath());
        }
        if(!CollectionUtils.isEmpty(dto.getWarehouseStorageDTOList())){
            vo.setWarehouseList(dto.getWarehouseStorageDTOList().stream().map(WarehouseStorageVOConverter::dto2LogisticsStorage).collect(Collectors.toList()));
        }

        return vo;
    }
}
