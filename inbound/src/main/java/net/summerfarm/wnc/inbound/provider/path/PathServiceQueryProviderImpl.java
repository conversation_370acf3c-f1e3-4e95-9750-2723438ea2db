package net.summerfarm.wnc.inbound.provider.path;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageInfo;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.path.dto.PathDTO;
import net.summerfarm.wnc.api.path.dto.SkuPathMappingDTO;
import net.summerfarm.wnc.api.path.service.PathService;
import net.summerfarm.wnc.client.provider.path.PathServiceQueryProvider;
import net.summerfarm.wnc.client.req.path.*;
import net.summerfarm.wnc.client.resp.path.ReallocatePathConfigResp;
import net.summerfarm.wnc.client.resp.path.SkuPathMappingResp;
import net.summerfarm.wnc.common.enums.PathConfigEnums;
import net.summerfarm.wnc.common.query.path.PathQuery;
import net.summerfarm.wnc.inbound.provider.path.converter.PathDubboConverter;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: 路线配置查询
 * date: 2023/11/13 14:44<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@DubboService
public class PathServiceQueryProviderImpl implements PathServiceQueryProvider {

    @Resource
    private PathService pathService;

    @Override
    public DubboResponse<PageInfo<ReallocatePathConfigResp>> queryPageReallocatePathConfig(ReallocatePathConfigPageReq req) {
        PathQuery query = PathQuery.builder()
                .beginOutNo(req.getBeginOutNo())
                .endOutNo(req.getEndOutNo())
                .frequentMethod(req.getFrequentMethod())
                .businseeType(PathConfigEnums.BusinseeType.REALLOCATE.getValue())
                .build();
        query.setPageIndex(req.getPageIndex());
        query.setPageSize(req.getPageSize());

        PageInfo<PathDTO> pathDTOList = pathService.queryPagePathConfig(query);

        PageInfo<ReallocatePathConfigResp> reallocatePathConfigRespPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(pathDTOList,reallocatePathConfigRespPageInfo);
        if(pathDTOList.getList() == null){
            return DubboResponse.getOK(reallocatePathConfigRespPageInfo);
        }
        List<ReallocatePathConfigResp> reallocatePathConfigResps = pathDTOList.getList().stream().map(PathDubboConverter::pathDTO2ReallocatePathConfigResp).collect(Collectors.toList());
        reallocatePathConfigRespPageInfo.setList(reallocatePathConfigResps);
        return DubboResponse.getOK(reallocatePathConfigRespPageInfo);
    }

    @Override
    public DubboResponse<List<ReallocatePathConfigResp>> queryReallocatePathConfig(ReallocatePathConfigReq req) {
        List<PathDTO> pathDTOList = pathService.queryPathConfigList(PathQuery.builder()
                .beginOutNo(req.getBeginOutNo())
                .endOutNo(req.getEndOutNo())
                .frequentMethod(req.getFrequentMethod())
                .businseeType(PathConfigEnums.BusinseeType.REALLOCATE.getValue())
                .build());
        List<ReallocatePathConfigResp> reallocatePathConfigResps = pathDTOList.stream().map(PathDubboConverter::pathDTO2ReallocatePathConfigResp).collect(Collectors.toList());
        return DubboResponse.getOK(reallocatePathConfigResps);
    }

    @Override
    public DubboResponse<ReallocatePathConfigResp> queryReallocatePathConfigDetailById(Long pathId) {
        if(pathId == null){
            throw new BizException("pathId不能为空");
        }
        PathDTO pathDTO = pathService.queryPathConfigDetailById(pathId);

        return DubboResponse.getOK(PathDubboConverter.pathDTO2ReallocatePathConfigResp(pathDTO));
    }

    @Override
    public DubboResponse<ReallocatePathConfigResp> queryReallocatePathConfigDetail(@Valid ReallocatePathConfigDetailReq req) {
        PathDTO pathDTO = pathService.queryPathConfigDetail(PathQuery.builder()
                .beginOutNo(req.getBeginOutNo())
                .endOutNo(req.getEndOutNo())
                .businseeType(PathConfigEnums.BusinseeType.REALLOCATE.getValue())
                .build());

        return DubboResponse.getOK(PathDubboConverter.pathDTO2ReallocatePathConfigResp(pathDTO));
    }

    @Override
    public DubboResponse<List<SkuPathMappingResp>> querySkuPathMappingList(List<SkuPathMappingQueryReq> list) {
        //参数校验
        for (SkuPathMappingQueryReq skuPath : list) {
            if(StringUtil.isBlank(skuPath.getSku()) || skuPath.getWarehouseNo() == null){
                throw new BizException("sku和仓库编号不能为空");
            }
        }
        List<PathQuery> queries = new ArrayList<>();
        for (SkuPathMappingQueryReq req : list) {
            queries.add(PathQuery.builder()
                    .sku(req.getSku())
                    .warehouseNo(req.getWarehouseNo()).build());
        }
        //查询数据信息
        List<PathDTO> pathDTOList = pathService.querySkuPathMappingList(queries);

        return DubboResponse.getOK(PathDubboConverter.pathDTO2SkuPathMappingResp(pathDTOList));
    }

    @Override
    public DubboResponse<List<ReallocatePathConfigResp>> queryExpectLastTimeReallocatePath(@Valid ExpectLastTimeReallocatePathReq req) {
        List<PathDTO> pathDTOList = pathService.queryExpectLastTimeReallocatePath(PathQuery.builder()
                .beginOutNo(req.getBeginOutNo())
                .endOutNo(req.getEndOutNo())
                .businseeType(PathConfigEnums.BusinseeType.REALLOCATE.getValue())
                .expectLastTime(req.getExpectLastTime())
                .build());

        List<ReallocatePathConfigResp> reallocatePathConfigResps = pathDTOList.stream().map(PathDubboConverter::pathDTO2ReallocatePathConfigResp).collect(Collectors.toList());
        return DubboResponse.getOK(reallocatePathConfigResps);
    }

    @Override
    public DubboResponse<List<SkuPathMappingResp>> queryBelowSkuPathMappingList(List<BelowSkuPathMappingReq> list) {
        if(CollectionUtil.isEmpty(list)){
            return DubboResponse.getOK(Collections.emptyList());
        }
        if(list.size() > 100){
            return DubboResponse.getDefaultError("请求数量不能超过100条");
        }
        List<PathQuery> queries = new ArrayList<>();
        for (BelowSkuPathMappingReq req : list) {
            queries.add(PathQuery.builder()
                    .sku(req.getSku())
                    .supWarehouseNo(req.getSupWarehouseNo()).build());
        }

        List<SkuPathMappingDTO> skuPathMappingDTOList = pathService.queryBelowSkuPathMappingList(queries);
        return DubboResponse.getOK(skuPathMappingDTOList.stream().map(PathDubboConverter::mapping2SkuPathMappingResp).collect(Collectors.toList()));
    }
   /* @PostConstruct
    void testQueryReallocatePathConfig() {
        ReallocatePathConfigReq req = new ReallocatePathConfigReq();
       *//* req.setFrequentMethod(1);
        req.setBeginOutNo(5);*//*
        DubboResponse<List<ReallocatePathConfigResp>> result = this.queryReallocatePathConfig(req);
        System.out.println(result);
    }*/
    /*@PostConstruct
    void testQueryReallocatePathConfig() {
        *//*ExpectLastTimeReallocatePathReq req = new ExpectLastTimeReallocatePathReq();
        req.setExpectLastTime(LocalDate.now().plusDays(3));
        DubboResponse<List<ReallocatePathConfigResp>> listDubboResponse = this.queryExpectLastTimeReallocatePath(req);

        System.out.println(listDubboResponse);*//*

      *//* DubboResponse<ReallocatePathConfigResp> reallocatePathConfigRespDubboResponse = queryReallocatePathConfigDetailById(1L);
        System.out.println(reallocatePathConfigRespDubboResponse);*//*
        ReallocatePathConfigPageReq req = new ReallocatePathConfigPageReq();
        req.setBeginOutNo(2);
        req.setEndOutNo(3);
        DubboResponse<PageInfo<ReallocatePathConfigResp>> pageInfoDubboResponse = queryPageReallocatePathConfig(req);
        System.out.println(pageInfoDubboResponse);
    }*/

}
