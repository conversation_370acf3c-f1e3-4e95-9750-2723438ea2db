package net.summerfarm.wnc.inbound.provider.fence.converter;

import net.summerfarm.wnc.client.req.AddressQueryReq;
import net.summerfarm.wnc.common.query.fence.AddressQuery;

/**
 * Description:地址查询转换器
 * date: 2023/11/14 18:30
 *
 * <AUTHOR>
 */
public class AddressQueryConverter {

    public static AddressQuery req2query(AddressQueryReq addressQueryReq){
        if (addressQueryReq == null){
            return null;
        }
        AddressQuery addressQuery = new AddressQuery();
        addressQuery.setProvince(addressQueryReq.getProvince());
        addressQuery.setCity(addressQueryReq.getCity());
        addressQuery.setArea(addressQueryReq.getArea());
        addressQuery.setAddress(addressQueryReq.getAddress());
        return addressQuery;
    }
}
