package net.summerfarm.wnc.inbound.provider.warehouse.converter;

import net.summerfarm.wnc.api.warehouse.dto.WarehouseLogisticsCenterDTO;
import net.summerfarm.wnc.client.resp.WarehousLogisticsCenterResp;

/**
 * Description: 城配仓转化类
 * date: 2023/10/19 16:50<br/>
 *
 * <AUTHOR> />
 */
public class WarehouseLogisticsDTOConverter {

    public static WarehousLogisticsCenterResp dto2WarehousLogisticsCenterResp(WarehouseLogisticsCenterDTO dto){
        if(dto == null){
            return null;
        }
        WarehousLogisticsCenterResp resp = new WarehousLogisticsCenterResp();
        resp.setId(dto.getId());
        resp.setStoreNo(dto.getStoreNo());
        resp.setStoreName(dto.getStoreName());
        resp.setStatus(dto.getStatus());
        resp.setManageAdminId(dto.getManageAdminId());
        resp.setPoiNote(dto.getPoiNote());
        resp.setAddress(dto.getAddress());
        resp.setCloseOrderType(dto.getCloseOrderType());
        resp.setOriginStoreNo(dto.getOriginStoreNo());
        resp.setSotFinishTime(dto.getSotFinishTime());
        resp.setCreator(dto.getCreator());
        resp.setUpdater(dto.getUpdater());
        resp.setUpdateTime(dto.getUpdateTime());
        resp.setCreateTime(dto.getCreateTime());
        resp.setCloseTime(dto.getCloseTime());
        resp.setUpdateCloseTime(dto.getUpdateCloseTime());
        resp.setPersonContact(dto.getPersonContact());
        resp.setPhone(dto.getPhone());
        resp.setRegion(dto.getRegion());
        resp.setStorePic(dto.getStorePic());
        resp.setFulfillmentType(dto.getFulfillmentType());

        return resp;
    }
}
