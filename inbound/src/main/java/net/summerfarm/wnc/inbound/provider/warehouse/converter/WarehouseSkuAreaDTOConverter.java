package net.summerfarm.wnc.inbound.provider.warehouse.converter;

import net.summerfarm.wnc.api.warehouse.dto.WarehouseBaseDTO;
import net.summerfarm.wnc.api.warehouse.dto.WarehouseBySkuAreaNoDTO;
import net.summerfarm.wnc.client.req.WarehouseBySkuAreaNoDataReq;
import net.summerfarm.wnc.client.resp.WarehouseBaseInfoByNoResp;
import net.summerfarm.wnc.client.resp.WarehouseBySkuAreaNoResp;
import net.summerfarm.wnc.common.constants.AppConsts;
import net.summerfarm.wnc.common.query.warehouse.WarehouseBySkuAreaNoQuery;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-07-04
 **/
@Mapper(componentModel = AppConsts.MapStructConstants.COMPONENT_MODEL_SPRING)
public interface WarehouseSkuAreaDTOConverter {

	/**
	 * dubboQuery2Query
	 *
	 * @param request
	 * @return
	 */
	List<WarehouseBySkuAreaNoQuery> dubboQuery2Query(List<WarehouseBySkuAreaNoDataReq> request);

	/**
	 * response2Dubbo
	 *
	 * @param warehouseBySkuAreaNoDTOS
	 * @return
	 */
	List<WarehouseBySkuAreaNoResp> response2Dubbo(List<WarehouseBySkuAreaNoDTO> warehouseBySkuAreaNoDTOS);

	/**
	 * baseInfo2DubboResponse
	 *
	 * @param warehouseBaseDTOS
	 * @return
	 */
	List<WarehouseBaseInfoByNoResp> baseInfo2DubboResponse(List<WarehouseBaseDTO> warehouseBaseDTOS);
}
