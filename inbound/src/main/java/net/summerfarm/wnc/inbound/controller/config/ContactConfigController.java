package net.summerfarm.wnc.inbound.controller.config;

import net.summerfarm.wnc.api.config.input.ContactConfigCreateCommandInput;
import net.summerfarm.wnc.api.config.input.ContactConfigRemoveCommandInput;
import net.summerfarm.wnc.api.config.service.ContactConfigCommandService;
import net.xianmu.common.result.CommonResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * Description:联系人配置相关接口
 * date: 2023/9/25 11:19
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/contact-config")
public class ContactConfigController {

    @Resource
    private ContactConfigCommandService contactConfigCommandService;

    /**
     * 创建联系人配置
     */
    @PostMapping("/upsert/create")
    public CommonResult<Void> createConfig(@RequestBody @Validated ContactConfigCreateCommandInput contactConfigCreateCommandInput) {
        contactConfigCommandService.createConfig(contactConfigCreateCommandInput);
        return CommonResult.ok();
    }

    /**
     * 批量创建联系人配置
     */
    @PostMapping("/upsert/batch-create")
    public CommonResult<Void> createConfig(@RequestBody @Validated List<ContactConfigCreateCommandInput> contactConfigCreateCommandInputs) {
        contactConfigCreateCommandInputs.forEach(contactConfigCommandService::createConfig);
        return CommonResult.ok();
    }

    /**
     * 删除联系人配置
     */
    @PostMapping("/upsert/remove")
    public CommonResult<Void> removeConfig(@RequestBody @Validated ContactConfigRemoveCommandInput contactConfigRemoveCommandInput) {
        contactConfigCommandService.removeConfig(contactConfigRemoveCommandInput);
        return CommonResult.ok();
    }
}
