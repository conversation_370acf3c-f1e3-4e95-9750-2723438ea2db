package net.summerfarm.wnc.inbound.controller.fastmall;

import net.summerfarm.wnc.api.fastmall.dto.WncFastMallDTO;
import net.summerfarm.wnc.api.fastmall.service.WncFastMallService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 快递查询
 * <AUTHOR> />
 */
@RestController
@RequestMapping("/fastmall")
public class FastmallController{

    @Resource
    private WncFastMallService wncFastMallService;

    /**
     * 获取快递信息
     */
    @PostMapping("/query/warehouse")
    public CommonResult<List<WncFastMallDTO>> queryWarehouseList() {
        return CommonResult.ok(wncFastMallService.queryFastMallList());
    }
}
