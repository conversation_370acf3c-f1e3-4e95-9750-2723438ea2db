package net.summerfarm.wnc.inbound.scheduler.mapping;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.fence.dto.FenceAreaFilterDTO;
import net.summerfarm.wnc.api.fence.dto.FenceDTO;
import net.summerfarm.wnc.api.fence.service.FenceService;
import net.summerfarm.wnc.api.fence.service.WarehouseInventoryMappingService;
import net.summerfarm.wnc.common.enums.AdCodeMsgEnums;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * Description: 围栏运营区域商品映射巡检 <br/>
 * date: 2023/12/18 13:42<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Component
public class WarehouseFenceInventoryMappingJob extends XianMuJavaProcessorV2 {

    @Resource
    private FenceService fenceService;

    @Resource
    private WarehouseInventoryMappingService warehouseInventoryMappingService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        List<FenceDTO> fenceDTOList = fenceService.queryListByStatus(Lists.newArrayList(AdCodeMsgEnums.Status.VALID.getValue()));
        for (FenceDTO fenceDTO : fenceDTOList) {
            if(fenceDTO.getAreaNo() == null || fenceDTO.getStoreNo() == null || fenceDTO.getPackId() == null){
                continue;
            }
            warehouseInventoryMappingService.mappingHandleInit(fenceDTO.getAreaNo(), fenceDTO.getStoreNo(), fenceDTO.getPackId());
        }
        return new ProcessResult(true);
    }
}
