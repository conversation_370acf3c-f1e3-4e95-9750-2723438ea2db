package net.summerfarm.wnc.inbound.scheduler.path;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.path.service.PathService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * Description: 路线下次执行时间更新<br/>
 * date: 2023/12/1 13:37<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Component
public class PathLastTimeJob extends XianMuJavaProcessorV2 {

    @Resource
    private PathService pathService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        pathService.updatePathLastTime();
        return new ProcessResult(true);
    }

    /*@PostConstruct
    public void test(){
        pathService.updatePathLastTime();
    }*/
}
