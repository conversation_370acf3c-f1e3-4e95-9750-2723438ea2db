package net.summerfarm.wnc.inbound.provider.deliveryRule;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.deliveryRule.input.ContactDeliveryRuleDelCommand;
import net.summerfarm.wnc.api.deliveryRule.input.ContactDeliveryRuleSaveUpdateCommand;
import net.summerfarm.wnc.api.deliveryRule.service.ContactDeliveryRuleService;
import net.summerfarm.wnc.client.provider.deliveryRule.ContactDeliveryRuleCommandProvider;
import net.summerfarm.wnc.client.req.deliveryRule.XmContactDeliveryRuleCommandReq;
import net.summerfarm.wnc.client.req.deliveryRule.XmContactDeliveryRuleDelCommandReq;
import net.summerfarm.wnc.common.constants.AppConsts;
import net.summerfarm.wnc.common.enums.WncContactDeliveryRuleEnums;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * Description: 地址配送规则操作
 * date: 2023/11/13 14:44<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@DubboService
public class ContactDeliveryRuleCommandProviderImpl implements ContactDeliveryRuleCommandProvider {

    @Resource
    private ContactDeliveryRuleService contactDeliveryRuleService;

    @Override
    public DubboResponse<Void> xmContactDeliveryRuleSaveOrUpdate(@Valid XmContactDeliveryRuleCommandReq req) {
        log.info("xmContactDeliveryRuleSaveOrUpdate req:{}", JSON.toJSONString(req));
        ContactDeliveryRuleSaveUpdateCommand command = ContactDeliveryRuleSaveUpdateCommand.builder()
                .outBusinessNo(req.getOutBusinessNo())
                .frequentMethod(req.getFrequentMethod())
                .weekDeliveryFrequent(req.getWeekDeliveryFrequent())
                .deliveryFrequentInterval(req.getDeliveryFrequentInterval())
                .beginCalculateDate(req.getBeginCalculateDate())
                .systemSource(WncContactDeliveryRuleEnums.SystemSource.XM.getValue())
                .tenantId(AppConsts.Tenant.XM_TENANT_ID)
                .build();
        contactDeliveryRuleService.saveOrUpdate(command);
        return DubboResponse.getOK();
    }

    @Override
    public DubboResponse<Void> xmContactDeliveryRuleDelete(@Valid XmContactDeliveryRuleDelCommandReq req) {
        log.info("xmContactDeliveryRuleDelete req:{}", JSON.toJSONString(req));
        contactDeliveryRuleService.delteDeliveryRule(ContactDeliveryRuleDelCommand.builder()
                .outBusinessNo(req.getOutBusinessNo())
                .tenantId(AppConsts.Tenant.XM_TENANT_ID)
                .systemSource(WncContactDeliveryRuleEnums.SystemSource.XM.getValue()).build());
        return DubboResponse.getOK();
    }

}
