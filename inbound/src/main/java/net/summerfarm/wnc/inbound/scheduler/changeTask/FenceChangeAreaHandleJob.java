package net.summerfarm.wnc.inbound.scheduler.changeTask;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.changeTask.service.FenceChangeTaskService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Description:围栏切仓区域处理任务
 * date: 2023/8/30 11:21
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class FenceChangeAreaHandleJob extends XianMuJavaProcessorV2 {

    @Resource
    private FenceChangeTaskService fenceChangeTaskService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        //城配仓截单时间晚20点、20点半、21点、21点半、22点、22点半、23点、23点半、次日0点、0点半执行
        log.info("围栏切仓区域处理任务开始执行");
        fenceChangeTaskService.executeFenceChangeAreaHandle();
        return new ProcessResult(true);
    }
}
