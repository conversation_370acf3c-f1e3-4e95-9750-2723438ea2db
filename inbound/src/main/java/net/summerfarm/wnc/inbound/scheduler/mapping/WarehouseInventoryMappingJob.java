package net.summerfarm.wnc.inbound.scheduler.mapping;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.fence.service.WarehouseInventoryMappingService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 补齐映射关系任务
 */
@Slf4j
@Component
public class WarehouseInventoryMappingJob extends XianMuJavaProcessorV2 {

    @Resource
    private WarehouseInventoryMappingService warehouseInventoryMappingService;


    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("补齐映射关系任务开始");
        //查询所有的有效的运营区域信息
        List<Integer> areaList = warehouseInventoryMappingService.queryOpenAreaList();
        //单个处理运营服务区映射问题
        for (Integer areaNo : areaList) {
            warehouseInventoryMappingService.handleAreaMappingQuestion(areaNo, false);
        }
        return new ProcessResult(true);
    }
}
