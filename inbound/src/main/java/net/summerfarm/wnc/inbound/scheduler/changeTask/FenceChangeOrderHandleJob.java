package net.summerfarm.wnc.inbound.scheduler.changeTask;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.changeTask.service.FenceChangeTaskService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Description: 围栏切仓订单处理任务
 * date: 2023/8/30 11:22
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class FenceChangeOrderHandleJob extends XianMuJavaProcessorV2 {

    @Resource
    private FenceChangeTaskService fenceChangeTaskService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        //切仓次日凌晨3/4/5点执行
        log.info("围栏切仓订单处理任务开始执行");
        fenceChangeTaskService.executeFenceChangeOrderHandle();
        return new ProcessResult(true);
    }
}
