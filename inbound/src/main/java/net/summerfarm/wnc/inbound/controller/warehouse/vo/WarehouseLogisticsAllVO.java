package net.summerfarm.wnc.inbound.controller.warehouse.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 城配仓详情
 */
@Data
public class WarehouseLogisticsAllVO implements Serializable {

    /**
     * 主键ID
     */
    private Integer id;
    /**
     * 物流中心编号（配送仓编号）
     */
    private Integer storeNo;

    /**
     * 名称
     */
    private String storeName;

    /**
     * 区域
     */
    private String region;

    /**
     * 截单时间
     */
    private String closeTime;

    /**
     * 配送中心状态：0、失效 1、有效
     */
    private Integer status;


    /**
     * 是否需要打卡0 不需要 1需要
     */
    private Integer punchState;

    /**
     * 是否支持提前截单：0、false 1、true
     */
    private Integer closeOrderType;

    /**
     * 打卡距离
     */
    private BigDecimal punchDistance;

    /**
     * 城配仓地址
     */
    private String address;

    /**
     * 城配仓poi
     */
    private String poiNote;

    /**
     * 出仓时间 hh:mm:ss
     */
    private String outTime;

    /**
     * 库存使用仓
     */
    private List<LogisticsStorageAllVO> warehouseList;
    /**
     * 0智能排线 1手动排线
     */
    private Integer intelligencePath;
    /**
     * 联系人
     */
    private String personContact;

    /**
     * 联系方式
     */
    private String phone;

    /**
     * 履约类型，0：城配履约，1：快递履约
     */
    private Integer fulfillmentType;

    /**
     * 履约类型描述
     */
    private String fulfillmentTypeDesc;
}
