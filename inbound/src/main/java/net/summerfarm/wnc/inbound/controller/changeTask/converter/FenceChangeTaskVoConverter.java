package net.summerfarm.wnc.inbound.controller.changeTask.converter;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.api.changeTask.dto.FenceChangeTaskDTO;
import net.summerfarm.wnc.inbound.controller.changeTask.vo.FenceChangeTaskVO;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Description:围栏切仓任务视图转换器
 * date: 2023/8/25 18:20
 *
 * <AUTHOR>
 */
public class FenceChangeTaskVoConverter {

    public static FenceChangeTaskVO dto2Vo(FenceChangeTaskDTO fenceChangeTaskDTO){
        if (fenceChangeTaskDTO == null) {
            return null;
        }
        FenceChangeTaskVO fenceChangeTaskVO = new FenceChangeTaskVO();
        fenceChangeTaskVO.setId(fenceChangeTaskDTO.getId());
        fenceChangeTaskVO.setFenceName(fenceChangeTaskDTO.getFenceName());
        fenceChangeTaskVO.setType(fenceChangeTaskDTO.getType());
        fenceChangeTaskVO.setTypeDesc(fenceChangeTaskDTO.getTypeDesc());
        fenceChangeTaskVO.setFenceChangeRemarkDTO(fenceChangeTaskDTO.getFenceChangeRemarkDTO());
        fenceChangeTaskVO.setStatus(fenceChangeTaskDTO.getStatus());
        fenceChangeTaskVO.setStatusDesc(fenceChangeTaskDTO.getStatusDesc());
        fenceChangeTaskVO.setFenceAreaDTOList(fenceChangeTaskDTO.getFenceAreaDTOList());
        fenceChangeTaskVO.setExeTime(fenceChangeTaskDTO.getExeTime());
        fenceChangeTaskVO.setCreateTime(fenceChangeTaskDTO.getCreateTime());
        fenceChangeTaskVO.setUpdateTime(fenceChangeTaskDTO.getUpdateTime());
        fenceChangeTaskVO.setCreator(fenceChangeTaskDTO.getCreator());
        fenceChangeTaskVO.setUpdater(fenceChangeTaskDTO.getUpdater());
        return fenceChangeTaskVO;
    }

    public static PageInfo<FenceChangeTaskVO> dtoPage2VoPage(PageInfo<FenceChangeTaskDTO> dtoPageInfo) {
        List<FenceChangeTaskDTO> fenceChangeTaskDTOS = dtoPageInfo.getList();
        List<FenceChangeTaskVO> fenceChangeTaskVOS = fenceChangeTaskDTOS.stream().map(FenceChangeTaskVoConverter::dto2Vo).collect(Collectors.toList());

        PageInfo<FenceChangeTaskVO> voPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(dtoPageInfo, voPageInfo);
        voPageInfo.setList(fenceChangeTaskVOS);
        return voPageInfo;
    }
}
