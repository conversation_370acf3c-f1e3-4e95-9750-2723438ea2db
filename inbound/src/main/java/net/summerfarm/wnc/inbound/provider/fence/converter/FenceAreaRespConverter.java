package net.summerfarm.wnc.inbound.provider.fence.converter;

import net.summerfarm.wnc.api.fence.dto.FenceAreaDTO;
import net.summerfarm.wnc.client.resp.fence.FenceAreaResp;

/**
 * Description:围栏配送区域转换器
 * date: 2024/1/9 18:13
 *
 * <AUTHOR>
 */
public class FenceAreaRespConverter {

    public static FenceAreaResp dto2Resp(FenceAreaDTO fenceAreaDTO){
        if(fenceAreaDTO == null){
            return null;
        }
        FenceAreaResp fenceAreaResp = new FenceAreaResp();
        fenceAreaResp.setProvince(fenceAreaDTO.getProvince());
        fenceAreaResp.setCity(fenceAreaDTO.getCity());
        fenceAreaResp.setArea(fenceAreaDTO.getArea());
        return fenceAreaResp;
    }
}
