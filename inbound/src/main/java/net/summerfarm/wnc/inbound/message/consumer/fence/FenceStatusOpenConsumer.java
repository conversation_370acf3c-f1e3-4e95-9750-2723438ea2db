package net.summerfarm.wnc.inbound.message.consumer.fence;

import com.alibaba.fastjson.JSON;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.fence.service.WarehouseInventoryMappingService;
import net.summerfarm.wnc.client.mq.WncMqConstants;
import net.summerfarm.wnc.common.message.in.FenceStatusOpenMessage;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Description: <br/>
 * date: 2023/2/17 10:23<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@MqListener(topic = WncMqConstants.Topic.TOPIC_WNC_FENCE, tag = "tag_fence_status_open",
        consumerGroup = "GID_wnc_fence_status_open")
public class FenceStatusOpenConsumer extends AbstractMqListener<FenceStatusOpenMessage> {

    @Resource
    private WarehouseInventoryMappingService warehouseInventoryMappingService;

    @Override
    public void process(FenceStatusOpenMessage msg) {
        log.info("接收到围栏开启消息通知:{}", JSON.toJSONString(msg));
        if(msg.getPackId() == null || msg.getStoreNo() == null || msg.getAreaNo() == null){
            return;
        }
        warehouseInventoryMappingService.mappingHandleInit(msg.getAreaNo(),msg.getStoreNo(),msg.getPackId());
    }
}
