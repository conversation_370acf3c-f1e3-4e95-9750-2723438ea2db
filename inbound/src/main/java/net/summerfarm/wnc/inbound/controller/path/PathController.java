package net.summerfarm.wnc.inbound.controller.path;

import net.summerfarm.wnc.api.path.service.PathService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Description: 路线相关接口<br/>
 * date: 2023/12/1 15:14<br/>
 *
 * <AUTHOR> />
 */
@RestController
@RequestMapping("/path")
public class PathController {

    @Resource
    private PathService pathService;

    /**
     * 初始化数据
     */
    @PostMapping("/upsert/init")
    public void initData() {
        pathService.initData();
    }
}
