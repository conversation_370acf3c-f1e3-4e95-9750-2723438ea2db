package net.summerfarm.wnc.inbound.controller.warehouse;

import net.summerfarm.wnc.api.fence.service.WarehouseInventoryMappingService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Description: <br/>
 * date: 2024/1/26 11:09<br/>
 *
 * <AUTHOR> />
 */
@RestController
@RequestMapping("/warehouse-invertory-mapping")
public class WarehouseInvertoryMappingController {

    @Resource
    private WarehouseInventoryMappingService warehouseInventoryMappingService;

    @GetMapping("/query/area-sku-many-warehouse-warning-job")
    public CommonResult<Void> areaSkuManyWarehouseWarningJob() {
        warehouseInventoryMappingService.areaSkuManyWarehouseWarningJob();
        return CommonResult.ok();
    }
}
