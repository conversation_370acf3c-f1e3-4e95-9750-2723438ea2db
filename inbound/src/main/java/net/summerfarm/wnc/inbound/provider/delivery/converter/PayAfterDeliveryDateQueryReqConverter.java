package net.summerfarm.wnc.inbound.provider.delivery.converter;

import net.summerfarm.wnc.client.req.DeliveryRuleQueryReq;
import net.summerfarm.wnc.client.req.delivery.PayAfterDeliveryDateQueryReq;

/**
 * Description: 转化类<br/>
 * date: 2024/7/10 15:12<br/>
 *
 * <AUTHOR> />
 */
public class PayAfterDeliveryDateQueryReqConverter {

    public static DeliveryRuleQueryReq pay2DeliveryRuleQueryReq(PayAfterDeliveryDateQueryReq req) {
        if(req == null){
            return null;
        }

        DeliveryRuleQueryReq ruleQueryReq = new DeliveryRuleQueryReq();
        ruleQueryReq.setOrderTime(req.getOrderTime());
        ruleQueryReq.setMerchantId(req.getMerchantId());
        ruleQueryReq.setContactId(req.getContactId());
        ruleQueryReq.setCity(req.getCity());
        ruleQueryReq.setArea(req.getArea());
        ruleQueryReq.setSource(req.getSource());
        ruleQueryReq.setSkus(req.getSkus());
        ruleQueryReq.setTenantId(req.getTenantId());
        ruleQueryReq.setNoNeedToStockUp(req.getNoNeedToStockUp());
        ruleQueryReq.setAddOrderFlag(req.getAddOrderFlag());

        return ruleQueryReq;
    }
}
