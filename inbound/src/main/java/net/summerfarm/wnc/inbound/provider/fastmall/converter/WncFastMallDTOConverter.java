package net.summerfarm.wnc.inbound.provider.fastmall.converter;

import net.summerfarm.wnc.api.fastmall.dto.WncFastMallDTO;
import net.summerfarm.wnc.client.resp.FastMallResp;

/**
 * Description: <br/>
 * date: 2023/4/11 16:56<br/>
 *
 * <AUTHOR> />
 */
public class WncFastMallDTOConverter {

    public static FastMallResp dto2Resp(WncFastMallDTO wncFastMallDTO){
        if(wncFastMallDTO == null){
            return null;
        }
        FastMallResp fastMallResp = new FastMallResp();

        fastMallResp.setFastName(wncFastMallDTO.getName());
        fastMallResp.setId(wncFastMallDTO.getId());

        return fastMallResp;
    }
}
