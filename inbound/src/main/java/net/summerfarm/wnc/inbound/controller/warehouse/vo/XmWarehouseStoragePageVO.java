package net.summerfarm.wnc.inbound.controller.warehouse.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/3/30 16:43<br/>
 *
 * <AUTHOR> />
 */
@Data
public class XmWarehouseStoragePageVO {
    /**
     * id
     */
    private Integer id;
    /**
     * 仓库编号
     */
    private Integer warehouseNo;
    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 仓库类型：0、本部仓 1、外部仓 2、合伙人仓
     */
    private Integer type;

    /**
     * 库位管理状态（0：未开启库位管理，1：已开启库位管理，2：开启中）
     */
    private String cabinetStatus;

    /**
     * 仓库负责人
     */
    private String manageAdminName;

    /**
     * 开放状态：0、不开放 1、开放
     */
    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
