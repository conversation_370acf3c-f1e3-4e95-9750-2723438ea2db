package net.summerfarm.wnc.inbound.provider.path.converter;

import net.summerfarm.wnc.api.path.dto.PathDTO;
import net.summerfarm.wnc.api.path.dto.SkuPathMappingDTO;
import net.summerfarm.wnc.client.req.path.SkuPathMappingDeleteReq;
import net.summerfarm.wnc.client.req.path.SkuPathSaveOrUpdateCommandReq;
import net.summerfarm.wnc.client.resp.path.ReallocatePathConfigResp;
import net.summerfarm.wnc.client.resp.path.SkuPathMappingResp;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: 路线dubbo转换类<br/>
 * date: 2023/11/29 11:00<br/>
 *
 * <AUTHOR> />
 */
public class PathDubboConverter {

    public static ReallocatePathConfigResp pathDTO2ReallocatePathConfigResp(PathDTO pathDTO){
        if(pathDTO == null){
            return null;
        }
        ReallocatePathConfigResp resp = new ReallocatePathConfigResp();

        resp.setId(pathDTO.getId());
        resp.setCreateTime(pathDTO.getCreateTime());
        resp.setUpdateTime(pathDTO.getUpdateTime());
        resp.setBeginOutNo(pathDTO.getBeginOutNo());
        resp.setEndOutNo(pathDTO.getEndOutNo());
        resp.setFrequentMethod(pathDTO.getFrequentMethod());
        resp.setFrequent(pathDTO.getFrequent());
        resp.setTravelDuratio(pathDTO.getTravelDuratio());
        resp.setLastTime(pathDTO.getLastTime());
        resp.setCreator(pathDTO.getCreator());
        resp.setUpdater(pathDTO.getUpdater());

        return resp;
    }

    public static SkuPathMappingDTO SkuPathReq2DTO(SkuPathSaveOrUpdateCommandReq req){
        if(req == null){
            return null;
        }
        SkuPathMappingDTO dto = new SkuPathMappingDTO();
        dto.setWarehouseNo(req.getWarehouseNo());
        dto.setSku(req.getSku());
        dto.setSupWarehouseNo(req.getSupWarehouseNo());
        return dto;
    }

    public static List<SkuPathMappingResp> pathDTO2SkuPathMappingResp(List<PathDTO> pathDTOList){
        if(CollectionUtils.isEmpty(pathDTOList)){
            return null;
        }
        List<SkuPathMappingResp> resps = new ArrayList<>();
        for (PathDTO pathDTO : pathDTOList) {
            if(CollectionUtils.isEmpty(pathDTO.getSkuPathMappingDTOList())){
                continue;
            }
            for (SkuPathMappingDTO skuPathMappingDTO : pathDTO.getSkuPathMappingDTOList()) {
                SkuPathMappingResp skuPathMappingResp = new SkuPathMappingResp();
                skuPathMappingResp.setId(skuPathMappingDTO.getId());
                skuPathMappingResp.setWarehouseNo(skuPathMappingDTO.getWarehouseNo());
                skuPathMappingResp.setSku(skuPathMappingDTO.getSku());
                skuPathMappingResp.setSupWarehouseNo(pathDTO.getBeginOutNo());
                skuPathMappingResp.setPathId(skuPathMappingDTO.getPathId());
                resps.add(skuPathMappingResp);
            }
        }
        return resps;
    }


    public static SkuPathMappingResp mapping2SkuPathMappingResp(SkuPathMappingDTO skuPathMappingDTO){
        if(skuPathMappingDTO == null){
            return null;
        }
        SkuPathMappingResp skuPathMappingResp = new SkuPathMappingResp();
        skuPathMappingResp.setId(skuPathMappingDTO.getId());
        skuPathMappingResp.setWarehouseNo(skuPathMappingDTO.getWarehouseNo());
        skuPathMappingResp.setSku(skuPathMappingDTO.getSku());
        skuPathMappingResp.setSupWarehouseNo(skuPathMappingDTO.getSupWarehouseNo());
        skuPathMappingResp.setPathId(skuPathMappingDTO.getPathId());
        return skuPathMappingResp;
    }


    public static SkuPathMappingDTO skuPathBatchDelete2DTO(SkuPathMappingDeleteReq req){
        if(req == null){
            return null;
        }
        SkuPathMappingDTO dto = new SkuPathMappingDTO();

        dto.setWarehouseNo(req.getWarehouseNo());
        dto.setSku(req.getSku());

        return dto;
    }
}
