package net.summerfarm.wnc.inbound.controller.lbs;

import net.summerfarm.wnc.api.fence.input.GlobalFenceRuleUpdateCommand;
import net.summerfarm.wnc.api.lbs.service.LbsService;
import net.xianmu.common.result.CommonResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/12/13 17:29<br/>
 *
 * <AUTHOR> />
 */
@RestController
@RequestMapping("/lbs")
public class LbsController {

    @Resource
    private LbsService lbsService;

    @PostMapping("/upsert/address-poi")
    public CommonResult<Void> updateAddressPoi(@RequestBody List<Long> contactIds) {
        lbsService.updateAddressPoi(contactIds);
        return CommonResult.ok();
    }


    @PostMapping("/upsert/ningJi-address-poi")
    public CommonResult<Void> updateNingjiAddressPoi() {
        lbsService.updateNingJiAddressPoi();
        return CommonResult.ok();
    }
}
