package net.summerfarm.wnc.inbound.provider.warehouse.converter;

import net.summerfarm.wnc.api.warehouse.dto.WncWarehouseStorageTenantDTO;
import net.summerfarm.wnc.client.resp.RsWarehouseStorageTenantResp;

/**
 * Description: <br/>
 * date: 2023/4/6 14:08<br/>
 *
 * <AUTHOR> />
 */
public class RsWarehouseStorageTenantDTOConverter {

    public static RsWarehouseStorageTenantResp dto2Resp(WncWarehouseStorageTenantDTO rsWarehouseStorageTenantDTO){
        if(rsWarehouseStorageTenantDTO == null){
            return null;
        }
        RsWarehouseStorageTenantResp rsWarehouseStorageTenantResp = new RsWarehouseStorageTenantResp();

        rsWarehouseStorageTenantResp.setId(rsWarehouseStorageTenantDTO.getId());
        rsWarehouseStorageTenantResp.setCreateTime(rsWarehouseStorageTenantDTO.getCreateTime());
        rsWarehouseStorageTenantResp.setUpdateTime(rsWarehouseStorageTenantDTO.getUpdateTime());
        rsWarehouseStorageTenantResp.setWarehouseNo(rsWarehouseStorageTenantDTO.getWarehouseNo());
        rsWarehouseStorageTenantResp.setTenantId(rsWarehouseStorageTenantDTO.getTenantId());
        rsWarehouseStorageTenantResp.setWarehouseName(rsWarehouseStorageTenantDTO.getWarehouseName());

        return rsWarehouseStorageTenantResp;
    }
}
