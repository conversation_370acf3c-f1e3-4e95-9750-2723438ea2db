package net.summerfarm.wnc.inbound.scheduler.mapping;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.fence.service.WarehouseInventoryMappingService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 历史映射关系错误的处理
 */
@Slf4j
@Component
public class WarehouseInvertoryMappingDataFixJob extends XianMuJavaProcessorV2 {

    @Resource
    private WarehouseInventoryMappingService warehouseInventoryMappingService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("映射关系数据修复任务开始");
        //查询所有的有效的运营区域信息
        List<Integer> areaList = warehouseInventoryMappingService.queryOpenAreaList();
        //单个处理运营服务区映射问题
        for (Integer areaNo : areaList) {
            warehouseInventoryMappingService.mappingDataFix(areaNo);
        }
        return new ProcessResult(true);
    }
}
