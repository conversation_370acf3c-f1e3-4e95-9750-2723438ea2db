package net.summerfarm.wnc.inbound.controller.changeTask.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import net.summerfarm.wnc.api.changeTask.dto.FenceChangeRemarkDTO;
import net.summerfarm.wnc.api.fence.dto.FenceAreaDTO;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Description:切仓任务视图对象
 * date: 2023/8/23 11:44
 *
 * <AUTHOR>
 */
@Data
public class FenceChangeTaskVO implements Serializable {

    private static final long serialVersionUID = 485673409122425359L;

    /**
     * 切仓任务ID
     */
    private Long id;

    /**
     * 围栏名称
     */
    private String fenceName;

    /**
     * 类型,0:切仓,1:切围栏
     */
    private Integer type;

    /**
     * 类型描述
     */
    private String typeDesc;

    /**
     * 切仓说明
     */
    private FenceChangeRemarkDTO fenceChangeRemarkDTO;

    /**
     * 切仓任务状态，0：待处理，1：已取消，2：已完成，10：区域切换中，15：订单切换中，20：处理失败
     */
    private Integer status;

    /**
     * 切仓任务状态描述
     */
    private String statusDesc;

    /**
     * 切仓区域集合
     */
    private List<FenceAreaDTO> fenceAreaDTOList;

    /**
     * 切仓时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime exeTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新人
     */
    private String updater;
}
