package net.summerfarm.wnc.inbound.controller.warehouse.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/3/30 16:43<br/>
 *
 * <AUTHOR> />
 */
@Data
public class WarehouseStorageFencePageVO {
    /**
     * id
     */
    private Integer id;
    /**
     * 仓库编号
     */
    private Integer warehouseNo;
    /**
     * 仓库名称
     */
    private String warehouseName;
    /**
     * 1 鲜沐仓 2自营仓
     */
    private Integer warehouseSource;
    /**
     *配送区域
     */
    private List<WarehouseStorageFenceVO> warehouseStorageFenceList;

    /**
     * 修改人
     */
    private String updaterName;
    /**
     * 修改时间
     */
    private LocalDateTime updateTime;
}
