package net.summerfarm.wnc.inbound.converter;

import net.summerfarm.wnc.api.fence.dto.area.AreaDTO;
import net.summerfarm.wnc.api.fence.dto.area.SimpleAreaDTO;
import net.summerfarm.wnc.client.resp.AreaQueryResp;
import net.summerfarm.wnc.client.resp.QueryAreaLegitimacyResp;
import net.summerfarm.wnc.common.constants.AppConsts;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-08-15
 **/
@Mapper(componentModel = AppConsts.MapStructConstants.COMPONENT_MODEL_SPRING)
public interface DubboAreaQueryConverter {
	/**
	 * dtoToDubboResp
	 *
	 * @param areaDTO
	 * @return
	 */
	AreaQueryResp dtoToDubboResp(AreaDTO areaDTO);


	/**
	 * simpleDtoToResp
	 *
	 * @param simpleAreaDTOList
	 * @return
	 */
	List<QueryAreaLegitimacyResp> simpleDtoToResp(List<SimpleAreaDTO> simpleAreaDTOList);
}
