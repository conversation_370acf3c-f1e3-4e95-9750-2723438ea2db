package net.summerfarm.wnc.inbound.provider.warehouse.converter;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.api.warehouse.dto.*;
import net.summerfarm.wnc.client.enums.WarehouseSourceEnum;
import net.summerfarm.wnc.client.resp.*;
import net.summerfarm.wnc.client.resp.warehouse.WarehouseStorageCenterBaseInfoResp;
import net.summerfarm.wnc.inbound.controller.warehouse.vo.WarehouseStorageFencePageVO;
import net.summerfarm.wnc.inbound.controller.warehouse.vo.WarehouseStorageFenceVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/4/4 15:10<br/>
 *
 * <AUTHOR> />
 */
public class WarehouseStorageDTOConverter {

    public static WarehouseStorageResp warehouseStorageDTO2Resp(WarehouseStorageDTO warehouseStorageDTO){
        if(warehouseStorageDTO == null){
            return null;
        }
        WarehouseStorageResp warehouseStorageResp = new WarehouseStorageResp();

        warehouseStorageResp.setId(warehouseStorageDTO.getId());
        warehouseStorageResp.setWarehouseNo(warehouseStorageDTO.getWarehouseNo());
        warehouseStorageResp.setWarehouseName(warehouseStorageDTO.getWarehouseName());
        warehouseStorageResp.setManageAdminId(warehouseStorageDTO.getManageAdminId());
        warehouseStorageResp.setType(warehouseStorageDTO.getType());
        warehouseStorageResp.setAreaManageId(warehouseStorageDTO.getAreaManageId());
        warehouseStorageResp.setStatus(warehouseStorageDTO.getStatus());
        warehouseStorageResp.setAddress(warehouseStorageDTO.getAddress());
        warehouseStorageResp.setPoiNote(warehouseStorageDTO.getPoiNote());
        warehouseStorageResp.setMailToAddress(warehouseStorageDTO.getMailToAddress());
        warehouseStorageResp.setUpdater(warehouseStorageDTO.getUpdater());
        warehouseStorageResp.setUpdateTime(warehouseStorageDTO.getUpdateTime());
        warehouseStorageResp.setCreator(warehouseStorageDTO.getCreator());
        warehouseStorageResp.setCreateTime(warehouseStorageDTO.getCreateTime());
        warehouseStorageResp.setPersonContact(warehouseStorageDTO.getPersonContact());
        warehouseStorageResp.setPhone(warehouseStorageDTO.getPhone());
        warehouseStorageResp.setTenantId(warehouseStorageDTO.getTenantId());
        warehouseStorageResp.setWarehouseServiceName(warehouseStorageDTO.getWarehouseServiceName());
        if(Objects.equals(warehouseStorageDTO.getTenantId(),1L)){
            warehouseStorageResp.setWarehouseSourceEnum(WarehouseSourceEnum.SUMMERFARM_WAREHOUSE);
        }else{
            warehouseStorageResp.setWarehouseSourceEnum(WarehouseSourceEnum.SAAS_WAREHOUSE);
        }
        if(warehouseStorageDTO.getWarehouseStorageCenterBusDTO() != null){
            warehouseStorageResp.setWarehouseStorageBusinessResp(WarehouseStorageDTOConverter.dto2WsbResp(warehouseStorageDTO.getWarehouseStorageCenterBusDTO()));
        }
        if(!CollectionUtils.isEmpty(warehouseStorageDTO.getWarehouseTakeStandardDTOs())) {
            warehouseStorageResp.setWarehouseTakeStandardResps(warehouseStorageDTO.getWarehouseTakeStandardDTOs().stream()
                    .map(WarehouseStorageDTOConverter::dto2TakeStandResp).collect(Collectors.toList()));
        }
        if(!CollectionUtils.isEmpty(warehouseStorageDTO.getRcWarehouseStorageFenceDTOs())) {
            warehouseStorageResp.setRcWarehouseStorageFenceResps(warehouseStorageDTO.getRcWarehouseStorageFenceDTOs().stream()
            .map(WarehouseStorageDTOConverter::dto2fenceResp).collect(Collectors.toList()));
        }
        return warehouseStorageResp;
    }

    public static RcWarehouseStorageFenceResp dto2fenceResp(WncWarehouseStorageFenceDTO rcWarehouseStorageFenceDTO){
        if(rcWarehouseStorageFenceDTO == null){
            return null;
        }
        RcWarehouseStorageFenceResp rcWarehouseStorageFenceResp = new RcWarehouseStorageFenceResp();
        rcWarehouseStorageFenceResp.setId(rcWarehouseStorageFenceDTO.getId());
        rcWarehouseStorageFenceResp.setCreateTime(rcWarehouseStorageFenceDTO.getCreateTime());
        rcWarehouseStorageFenceResp.setUpdateTime(rcWarehouseStorageFenceDTO.getUpdateTime());
        rcWarehouseStorageFenceResp.setProvince(rcWarehouseStorageFenceDTO.getProvince());
        rcWarehouseStorageFenceResp.setCity(rcWarehouseStorageFenceDTO.getCity());
        rcWarehouseStorageFenceResp.setArea(rcWarehouseStorageFenceDTO.getArea());
        rcWarehouseStorageFenceResp.setWarehouseNo(rcWarehouseStorageFenceDTO.getWarehouseNo() != null ?
                Long.parseLong(String.valueOf(rcWarehouseStorageFenceDTO.getWarehouseNo())) : null);
        rcWarehouseStorageFenceResp.setTenantId(rcWarehouseStorageFenceDTO.getTenantId());
        rcWarehouseStorageFenceResp.setLastOperatorName(rcWarehouseStorageFenceDTO.getLastOperatorName());
        return rcWarehouseStorageFenceResp;
    }

    public static WarehouseTakeStandardResp dto2TakeStandResp(WarehouseTakeStandardDTO warehouseTakeStandardDTO){
        if(warehouseTakeStandardDTO == null){
            return null;
        }
        WarehouseTakeStandardResp warehouseTakeStandardResp = new WarehouseTakeStandardResp();
        warehouseTakeStandardResp.setId(warehouseTakeStandardDTO.getId());
        warehouseTakeStandardResp.setCreateTime(warehouseTakeStandardDTO.getCreateTime());
        warehouseTakeStandardResp.setUpdateTime(warehouseTakeStandardDTO.getUpdateTime());
        warehouseTakeStandardResp.setStandardType(warehouseTakeStandardDTO.getStandardType());
        warehouseTakeStandardResp.setStorageLocation(warehouseTakeStandardDTO.getStorageLocation());
        warehouseTakeStandardResp.setWarehouseNo(warehouseTakeStandardDTO.getWarehouseNo());
        warehouseTakeStandardResp.setProveType(warehouseTakeStandardDTO.getProveType());
        warehouseTakeStandardResp.setCreateAdminId(warehouseTakeStandardDTO.getCreateAdminId());

        return warehouseTakeStandardResp;
    }

    public static WarehouseStorageBusinessResp dto2WsbResp(WarehouseStorageCenterBusDTO warehouseStorageCenterBusDTO){
        if(warehouseStorageCenterBusDTO == null){
            return null;
        }
        WarehouseStorageBusinessResp warehouseStorageBusinessResp = new WarehouseStorageBusinessResp();
        warehouseStorageBusinessResp.setId(warehouseStorageCenterBusDTO.getId());
        warehouseStorageBusinessResp.setWarehouseNo(warehouseStorageCenterBusDTO.getWarehouseNo());
        warehouseStorageBusinessResp.setCapacity(warehouseStorageCenterBusDTO.getCapacity());
        warehouseStorageBusinessResp.setAdvanceDay(warehouseStorageCenterBusDTO.getAdvanceDay());

        return warehouseStorageBusinessResp;
    }

    public static WarehouseStorageFencePageVO dto2WarehouseStorageFencePage(WarehouseStorageDTO warehouseStorageDTO){
        if(warehouseStorageDTO == null){
            return null;
        }
        WarehouseStorageFencePageVO warehouseStorageFencePageVO = new WarehouseStorageFencePageVO();

        warehouseStorageFencePageVO.setId(warehouseStorageDTO.getId());
        warehouseStorageFencePageVO.setWarehouseNo(warehouseStorageDTO.getWarehouseNo());
        warehouseStorageFencePageVO.setWarehouseName(warehouseStorageDTO.getWarehouseName());
        warehouseStorageFencePageVO.setWarehouseSource(warehouseStorageDTO.getTenantId() == null || warehouseStorageDTO.getTenantId() > WarehouseSourceEnum.SUMMERFARM_WAREHOUSE.getCode() ?
                WarehouseSourceEnum.SAAS_WAREHOUSE.getCode() : WarehouseSourceEnum.SUMMERFARM_WAREHOUSE.getCode());
        if(!CollectionUtils.isEmpty(warehouseStorageDTO.getRcWarehouseStorageFenceDTOs())){
            List<WarehouseStorageFenceVO> warehouseStorageFenceVOS = warehouseStorageDTO.getRcWarehouseStorageFenceDTOs().stream().map(WarehouseStorageDTOConverter::fenceVO2DTO).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(warehouseStorageFenceVOS)){
                warehouseStorageFencePageVO.setWarehouseStorageFenceList(warehouseStorageFenceVOS.stream().filter(vo -> vo != null).collect(Collectors.toList()));
            }

            List<WncWarehouseStorageFenceDTO> rcWarehouseStorageFenceDTOs = warehouseStorageDTO.getRcWarehouseStorageFenceDTOs();
            if(!CollectionUtils.isEmpty(rcWarehouseStorageFenceDTOs)){
                rcWarehouseStorageFenceDTOs.sort(Comparator.comparing(WncWarehouseStorageFenceDTO::getUpdateTime));

                warehouseStorageFencePageVO.setUpdaterName(rcWarehouseStorageFenceDTOs.get(0).getLastOperatorName());
                warehouseStorageFencePageVO.setUpdateTime(rcWarehouseStorageFenceDTOs.get(0).getUpdateTime());
            }
        }


        return warehouseStorageFencePageVO;
    }


    public static WarehouseStorageFenceVO fenceVO2DTO( WncWarehouseStorageFenceDTO warehouseStorageFenceDTO){
        if(warehouseStorageFenceDTO == null || (StringUtils.isBlank(warehouseStorageFenceDTO.getProvince()) &&
                StringUtils.isBlank(warehouseStorageFenceDTO.getCity()) && StringUtils.isBlank(warehouseStorageFenceDTO.getCity()))){
            return null;
        }
        WarehouseStorageFenceVO warehouseStorageFenceVO = new WarehouseStorageFenceVO();
        warehouseStorageFenceVO.setProvince(warehouseStorageFenceDTO.getProvince());
        warehouseStorageFenceVO.setCity(warehouseStorageFenceDTO.getCity());
        warehouseStorageFenceVO.setArea(warehouseStorageFenceDTO.getArea());
        return warehouseStorageFenceVO;
    }




    public static WarehouseStorageDetailResp warehouseStorageDTO2DetailResp(WarehouseStorageDTO warehouseStorageDTO){
        if(warehouseStorageDTO == null){
            return null;
        }
        WarehouseStorageDetailResp warehouseStorageResp = new WarehouseStorageDetailResp();

        warehouseStorageResp.setId(warehouseStorageDTO.getId());
        warehouseStorageResp.setWarehouseNo(warehouseStorageDTO.getWarehouseNo());
        warehouseStorageResp.setWarehouseName(warehouseStorageDTO.getWarehouseName());
        warehouseStorageResp.setManageAdminId(warehouseStorageDTO.getManageAdminId());
        warehouseStorageResp.setType(warehouseStorageDTO.getType());
        warehouseStorageResp.setAreaManageId(warehouseStorageDTO.getAreaManageId());
        warehouseStorageResp.setStatus(warehouseStorageDTO.getStatus());
        warehouseStorageResp.setAddress(warehouseStorageDTO.getAddress());
        warehouseStorageResp.setPoiNote(warehouseStorageDTO.getPoiNote());
        warehouseStorageResp.setMailToAddress(warehouseStorageDTO.getMailToAddress());
        warehouseStorageResp.setUpdater(warehouseStorageDTO.getUpdater());
        warehouseStorageResp.setUpdateTime(warehouseStorageDTO.getUpdateTime());
        warehouseStorageResp.setCreator(warehouseStorageDTO.getCreator());
        warehouseStorageResp.setCreateTime(warehouseStorageDTO.getCreateTime());
        warehouseStorageResp.setPersonContact(warehouseStorageDTO.getPersonContact());
        warehouseStorageResp.setPhone(warehouseStorageDTO.getPhone());
        warehouseStorageResp.setTenantId(warehouseStorageDTO.getTenantId());
        warehouseStorageResp.setWarehouseServiceName(warehouseStorageDTO.getWarehouseServiceName());
        if(Objects.equals(warehouseStorageDTO.getTenantId(),1L)){
            warehouseStorageResp.setWarehouseSourceEnum(WarehouseSourceEnum.SUMMERFARM_WAREHOUSE);
        }else{
            warehouseStorageResp.setWarehouseSourceEnum(WarehouseSourceEnum.SAAS_WAREHOUSE);
        }
        if(warehouseStorageDTO.getWarehouseStorageCenterBusDTO() != null){
            warehouseStorageResp.setWarehouseStorageBusinessResp(WarehouseStorageDTOConverter.dto2WsbResp(warehouseStorageDTO.getWarehouseStorageCenterBusDTO()));
        }
        if(!CollectionUtils.isEmpty(warehouseStorageDTO.getWarehouseTakeStandardDTOs())) {
            warehouseStorageResp.setWarehouseTakeStandardResps(warehouseStorageDTO.getWarehouseTakeStandardDTOs().stream()
                    .map(WarehouseStorageDTOConverter::dto2TakeStandResp).collect(Collectors.toList()));
        }
        if(!CollectionUtils.isEmpty(warehouseStorageDTO.getWarehouseStorageCenterWorkDTOs())) {
            warehouseStorageResp.setWarehouseWorkTimeResps(warehouseStorageDTO.getWarehouseStorageCenterWorkDTOs().stream()
                    .map(WarehouseStorageDTOConverter::dto2WorkTimeResp).collect(Collectors.toList()));
        }
        return warehouseStorageResp;
    }

    public static WarehouseWorkTimeResp dto2WorkTimeResp(WarehouseStorageCenterWorkDTO warehouseStorageCenterWorkDTO){
        if(warehouseStorageCenterWorkDTO == null){
            return null;
        }
        WarehouseWorkTimeResp warehouseWorkTimeResp = new WarehouseWorkTimeResp();
        warehouseWorkTimeResp.setWorkStartTime(warehouseStorageCenterWorkDTO.getWorkStartTime());
        warehouseWorkTimeResp.setWorkEndTime(warehouseStorageCenterWorkDTO.getWorkEndTime());

        return warehouseWorkTimeResp;
    }

    public static WarehouseStorageCenterResp dto2WarehouseStorageCenterResp(WarehouseStorageDTO warehouseStorageDTO){
        if(warehouseStorageDTO == null){
            return null;
        }
        WarehouseStorageCenterResp warehouseStorageCenterResp = new WarehouseStorageCenterResp();

        warehouseStorageCenterResp.setId(warehouseStorageDTO.getId());
        warehouseStorageCenterResp.setWarehouseNo(warehouseStorageDTO.getWarehouseNo());
        warehouseStorageCenterResp.setWarehouseName(warehouseStorageDTO.getWarehouseName());
        warehouseStorageCenterResp.setManageAdminId(warehouseStorageDTO.getManageAdminId());
        warehouseStorageCenterResp.setType(warehouseStorageDTO.getType());
        warehouseStorageCenterResp.setAreaManageId(warehouseStorageDTO.getAreaManageId());
        warehouseStorageCenterResp.setStatus(warehouseStorageDTO.getStatus());
        warehouseStorageCenterResp.setAddress(warehouseStorageDTO.getAddress());
        warehouseStorageCenterResp.setPoiNote(warehouseStorageDTO.getPoiNote());
        warehouseStorageCenterResp.setMailToAddress(warehouseStorageDTO.getMailToAddress());
        warehouseStorageCenterResp.setUpdater(warehouseStorageDTO.getUpdater());
        warehouseStorageCenterResp.setUpdateTime(warehouseStorageDTO.getUpdateTime());
        warehouseStorageCenterResp.setCreator(warehouseStorageDTO.getCreator());
        warehouseStorageCenterResp.setCreateTime(warehouseStorageDTO.getCreateTime());
        warehouseStorageCenterResp.setPersonContact(warehouseStorageDTO.getPersonContact());
        warehouseStorageCenterResp.setPhone(warehouseStorageDTO.getPhone());
        warehouseStorageCenterResp.setTenantId(warehouseStorageDTO.getTenantId());
        warehouseStorageCenterResp.setWarehousePic(warehouseStorageDTO.getWarehousePic());
        warehouseStorageCenterResp.setSelfWarehouseFlag(warehouseStorageDTO.getSelfWarehouseFlag());

        return warehouseStorageCenterResp;
    }

    public static PageInfo<WarehouseStorageCenterBaseInfoResp> dto2BasePage(PageInfo<WarehouseStorageDTO> dtoPageInfo){
        PageInfo<WarehouseStorageCenterBaseInfoResp> page = new PageInfo<>();
        if(dtoPageInfo == null){
            return page;
        }
        BeanUtils.copyProperties(dtoPageInfo, page);

        List<WarehouseStorageDTO> list = dtoPageInfo.getList();
        if(CollectionUtils.isEmpty(list)){
            return page;
        }
        List<WarehouseStorageCenterBaseInfoResp> respList = list.stream().map(warehouseStorageDTO -> dto2BaseInfo(warehouseStorageDTO)).collect(Collectors.toList());
        page.setList(respList);
        return page;
    }

    public static WarehouseStorageCenterBaseInfoResp dto2BaseInfo(WarehouseStorageDTO warehouseStorageDTO){
        if(warehouseStorageDTO == null){
            return null;
        }
        WarehouseStorageCenterBaseInfoResp resp = new WarehouseStorageCenterBaseInfoResp();
        resp.setId(warehouseStorageDTO.getId());
        resp.setWarehouseNo(warehouseStorageDTO.getWarehouseNo());
        resp.setWarehouseName(warehouseStorageDTO.getWarehouseName());
        resp.setManageAdminId(warehouseStorageDTO.getManageAdminId());
        resp.setType(warehouseStorageDTO.getType());
        resp.setAreaManageId(warehouseStorageDTO.getAreaManageId());
        resp.setStatus(warehouseStorageDTO.getStatus());
        resp.setAddress(warehouseStorageDTO.getAddress());
        resp.setPoiNote(warehouseStorageDTO.getPoiNote());
        resp.setMailToAddress(warehouseStorageDTO.getMailToAddress());
        resp.setUpdater(warehouseStorageDTO.getUpdater());
        resp.setUpdateTime(warehouseStorageDTO.getUpdateTime());
        resp.setCreator(warehouseStorageDTO.getCreator());
        resp.setCreateTime(warehouseStorageDTO.getCreateTime());
        resp.setPersonContact(warehouseStorageDTO.getPersonContact());
        resp.setPhone(warehouseStorageDTO.getPhone());
        resp.setTenantId(warehouseStorageDTO.getTenantId());
        resp.setWarehousePic(warehouseStorageDTO.getWarehousePic());
        resp.setSelfWarehouseFlag(warehouseStorageDTO.getSelfWarehouseFlag());

        return resp;
    }
}
