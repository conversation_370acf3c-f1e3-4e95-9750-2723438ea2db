package net.summerfarm.wnc.inbound.controller.warehouse.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/8/23 14:40<br/>
 *
 * <AUTHOR> />
 */
@Data
public class XmWarehouseStorageVO {
    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 仓库负责人
     */
    private Integer manageAdminId;

    /**
     * 仓库类型：0、本部仓 1、外部仓 2、合伙人仓
     */
    private Integer type;

    /**
     * 仓库所属合伙人
     */
    private Integer areaManageId;

    /**
     * 开放状态：0、不开放 1、开放
     */
    private Integer status;
    /**
     * 仓库地址
     */
    private String address;

    /**
     * 高德poi
     */
    private String poiNote;

    /**
     * 邮件接收人
     */
    private String mailToAddress;

    /**
     * 修改人
     */
    private Integer updater;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 联系人
     */
    private String personContact;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 所属租户0鲜沐
     */
    private Long tenantId;

    /**
     * 仓库负责人
     */
    private String manageAdminName;

    /**
     * 产能
     */
    private BigDecimal capacity;

    /**
     * 预约提前期
     */
    private Integer advanceDay;

    /**
     * 仓库作业时间
     */
    private List<WorkTimeVO> workTimes;

    /**
     * 收货标准
     */
    private List<WarehouseTakeStandardVO> warehouseTakeStandards;

    /**
     * 仓库照片
     */
    private String warehousePic;

    /**
     * 库位管理状态（0：未开启库位管理，1：已开启库位管理，2：开启中）
     */
    private String cabinetStatus;
}
