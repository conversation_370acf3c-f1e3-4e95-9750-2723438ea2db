package net.summerfarm.wnc.inbound.provider.delivery.converter;

import net.summerfarm.wnc.api.fence.dto.DeliveryFenceDTO;
import net.summerfarm.wnc.api.fence.dto.SkuDeliveryDateDTO;
import net.summerfarm.wnc.client.resp.CategoryDeliveryDateResp;
import net.summerfarm.wnc.client.resp.DeliveryRuleResp;
import net.summerfarm.wnc.client.resp.SkuDeliveryDate;
import net.summerfarm.wnc.client.resp.delivery.PayAfterDeliveryDateResp;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/9/27 15:15<br/>
 *
 * <AUTHOR> />
 */
public class DeliveryFenceDTOConverter {

    public static DeliveryRuleResp dto2Resp(DeliveryFenceDTO deliveryFenceDTO){
        if(deliveryFenceDTO == null){
            return null;
        }
        DeliveryRuleResp deliveryRuleResp = new DeliveryRuleResp();

        deliveryRuleResp.setDeliveryTimes(deliveryFenceDTO.getDeliveryDateList());
        deliveryRuleResp.setCloseTime(deliveryFenceDTO.getCloseTime());
        deliveryRuleResp.setDeliveryCloseTime(deliveryFenceDTO.getDeliveryCloseTime());
        deliveryRuleResp.setIsEveryDayFlag(deliveryFenceDTO.getIsEveryDayFlag());
        deliveryRuleResp.setFulfillmentType(deliveryFenceDTO.getFulfillmentMethod());
        deliveryRuleResp.setAddOrderHourDuration(deliveryFenceDTO.getAddOrderHourDuration());
        deliveryRuleResp.setFirstMerchantDeliveryTime(deliveryFenceDTO.getFirstMerchantDeliveryTime());
        deliveryRuleResp.setNextMerchantDeliveryTime(deliveryFenceDTO.getNextMerchantDeliveryTime());
        if(!CollectionUtils.isEmpty(deliveryFenceDTO.getSkuDeliveryDates())){
            deliveryRuleResp.setSkuDeliveryDateList(deliveryFenceDTO.getSkuDeliveryDates().stream()
                    .map(DeliveryFenceDTOConverter::skuDateDto2Resp).collect(Collectors.toList()));
        }

        return deliveryRuleResp;
    }

    public static SkuDeliveryDate skuDateDto2Resp(SkuDeliveryDateDTO skuDeliveryDateDTO){
        if(skuDeliveryDateDTO == null){
            return null;
        }

        SkuDeliveryDate skuDeliveryDate = new SkuDeliveryDate();

        skuDeliveryDate.setDeliveryDate(skuDeliveryDateDTO.getDeliveryDate());
        skuDeliveryDate.setSku(skuDeliveryDateDTO.getSku());
        skuDeliveryDate.setDeliveryCloseTime(skuDeliveryDateDTO.getDeliveryCloseTime());

        return skuDeliveryDate;
    }

    public static CategoryDeliveryDateResp dto2CategoryResp(DeliveryFenceDTO deliveryFenceDTO){
        if(deliveryFenceDTO == null){
            return null;
        }
        CategoryDeliveryDateResp resp = new CategoryDeliveryDateResp();

        resp.setCloseTime(deliveryFenceDTO.getCloseTime());
        resp.setIsEveryDayFlag(deliveryFenceDTO.getIsEveryDayFlag());
        resp.setSkuFullCategoryDeliveryDate(deliveryFenceDTO.getFullCategoryDeliveryDay());
        resp.setSkuNoFullCategoryDeliveryDate(CollectionUtils.isEmpty(deliveryFenceDTO.getDeliveryDateList()) ? null : deliveryFenceDTO.getDeliveryDateList().get(0));

        return resp;
    }

    public static PayAfterDeliveryDateResp dto2PayAfterResp(DeliveryFenceDTO dto){
        if(dto == null){
            return null;
        }
        PayAfterDeliveryDateResp resp = new PayAfterDeliveryDateResp();
        resp.setIsEveryDayFlag(dto.getIsEveryDayFlag());
        List<SkuDeliveryDateDTO> skuDeliveryDates = dto.getSkuDeliveryDates();
        if(!CollectionUtils.isEmpty(skuDeliveryDates)){
            resp.setSkuDeliveryDateList(skuDeliveryDates.stream().map(DeliveryFenceDTOConverter::skuDateDto2Resp).collect(Collectors.toList()));

            // 最晚配送日期
            LocalDate lastDeliveryDate = skuDeliveryDates.stream()
                    .map(SkuDeliveryDateDTO::getDeliveryDate)
                    .max(Comparator.naturalOrder())
                    .orElse(null);
            resp.setSkuLastDeliveryDate(lastDeliveryDate);
        }
        return resp;
    }
}
