package net.summerfarm.wnc.inbound.provider.warehouse;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.warehouse.dto.WarehouseStorageDTO;
import net.summerfarm.wnc.api.warehouse.dto.WncWarehouseStorageTenantDTO;
import net.summerfarm.wnc.api.warehouse.service.warehouse.WarehouseStorageCenterService;
import net.summerfarm.wnc.api.warehouse.service.warehouse.WncWarehouseStorageTenantService;
import net.summerfarm.wnc.common.base.WncAssert;
import net.summerfarm.wnc.client.provider.warehouse.RsWarehouseStorageTenantQueryProvider;
import net.summerfarm.wnc.client.req.RsWarehouseStorageTenantQueryReq;
import net.summerfarm.wnc.client.resp.RsWarehouseStorageTenantResp;
import net.summerfarm.wnc.common.query.warehouse.WarehouseStorageQuery;
import net.summerfarm.wnc.common.query.warehouse.WncWarehouseStorageTenantQuery;
import net.summerfarm.wnc.inbound.provider.warehouse.converter.RsWarehouseStorageTenantDTOConverter;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/4/4 14:17<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@DubboService
public class RsWarehouseStorageTenantQueryProviderImpl implements RsWarehouseStorageTenantQueryProvider {

    @Resource
    private WncWarehouseStorageTenantService rsWarehouseStorageTenantService;
    @Resource
    private WarehouseStorageCenterService warehouseStorageCenterService;


    /**
     * 根据租户查询仓库信息
     * @param rsWarehouseStorageTenantQueryReq 请求参数
     * @return 结果
     */
    @Override
    public DubboResponse<List<RsWarehouseStorageTenantResp>> queryWarehouseStorageTenant(RsWarehouseStorageTenantQueryReq rsWarehouseStorageTenantQueryReq) {
        log.info("根据租户查询仓库信息:{}", JSON.toJSONString(rsWarehouseStorageTenantQueryReq));
        WncAssert.notNull(rsWarehouseStorageTenantQueryReq.getTenantId(),"tenantId请求参数不能为空");
        List<WncWarehouseStorageTenantDTO> rsWarehouseStorageTenantDTOS = rsWarehouseStorageTenantService.queryTenantWarehouseList(WncWarehouseStorageTenantQuery.builder()
                .tenantId(rsWarehouseStorageTenantQueryReq.getTenantId()).build());

        List<Long> warehouseNos = rsWarehouseStorageTenantDTOS.stream().map(WncWarehouseStorageTenantDTO::getWarehouseNo).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(warehouseNos)){
            List<WarehouseStorageDTO> warehouseStorageDTOS = warehouseStorageCenterService.queryWarehouseList(WarehouseStorageQuery.builder()
                    .warehouseNos(JSON.parseArray(warehouseNos.toString(), Integer.class))
                    .build());
            if(!CollectionUtils.isEmpty(warehouseStorageDTOS)){
                Map<Integer, List<WarehouseStorageDTO>> warehouseStorageDTOMap = warehouseStorageDTOS.stream()
                        .collect(Collectors.groupingBy(WarehouseStorageDTO::getWarehouseNo));
                rsWarehouseStorageTenantDTOS.forEach(rsWarehouseStorage ->{
                    if(!CollectionUtils.isEmpty(warehouseStorageDTOMap.get(Integer.parseInt(rsWarehouseStorage.getWarehouseNo()+"")))){
                        rsWarehouseStorage.setWarehouseName(warehouseStorageDTOMap.get(Integer.parseInt(rsWarehouseStorage.getWarehouseNo()+"")).get(0).getWarehouseName());
                    }
                });
            }
        }


        return DubboResponse.getOK(rsWarehouseStorageTenantDTOS.stream().map(RsWarehouseStorageTenantDTOConverter::dto2Resp).collect(Collectors.toList()));
    }
}
