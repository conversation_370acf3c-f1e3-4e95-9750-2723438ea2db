package net.summerfarm.wnc.inbound.provider.fence.converter;

import net.summerfarm.wnc.api.fence.dto.WarehouseStorageFenceRuleDTO;
import net.summerfarm.wnc.api.fence.dto.WncSkuStoreNoDTO;
import net.summerfarm.wnc.api.fence.dto.WncWarehouseStorageFenceRuleDTO;
import net.summerfarm.wnc.client.resp.WarehouseStorageFenceRuleResp;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/4/10 18:51<br/>
 *
 * <AUTHOR> />
 */
public class WncWarehouseStorageFenceRuleDTOConverter {

    public static List<WarehouseStorageFenceRuleResp> dto2WarehouseStorageFenceRuleRespList(WncWarehouseStorageFenceRuleDTO wncWarehouseStorageFenceRuleDTO){
        if(wncWarehouseStorageFenceRuleDTO == null){
            return null;
        }
        List<WarehouseStorageFenceRuleResp> warehouseStorageFenceRuleRespList = new ArrayList<>();

        List<WncSkuStoreNoDTO> wncSkuStoreNoDTOS = wncWarehouseStorageFenceRuleDTO.getWncSkuStoreNoDTOS();
        if(CollectionUtils.isEmpty(wncSkuStoreNoDTOS)){
            WarehouseStorageFenceRuleResp warehouseStorageFenceRuleResp = new WarehouseStorageFenceRuleResp();

            warehouseStorageFenceRuleResp.setDistance(wncWarehouseStorageFenceRuleDTO.getDistance());
            warehouseStorageFenceRuleResp.setWarehouseNo(wncWarehouseStorageFenceRuleDTO.getWarehouseNo());
            warehouseStorageFenceRuleResp.setTenantId(wncWarehouseStorageFenceRuleDTO.getTenantId());
            warehouseStorageFenceRuleResp.setDeliveryTime(wncWarehouseStorageFenceRuleDTO.getDeliveryTime());

            warehouseStorageFenceRuleRespList.add(warehouseStorageFenceRuleResp);
        }else{
            for (WncSkuStoreNoDTO wncSkuStoreNoDTO : wncSkuStoreNoDTOS) {
                WarehouseStorageFenceRuleResp warehouseStorageFenceRuleResp = new WarehouseStorageFenceRuleResp();

                warehouseStorageFenceRuleResp.setDistance(wncWarehouseStorageFenceRuleDTO.getDistance());
                warehouseStorageFenceRuleResp.setWarehouseNo(wncWarehouseStorageFenceRuleDTO.getWarehouseNo());
                warehouseStorageFenceRuleResp.setTenantId(wncWarehouseStorageFenceRuleDTO.getTenantId());
                warehouseStorageFenceRuleResp.setDeliveryTime(wncWarehouseStorageFenceRuleDTO.getDeliveryTime());
                warehouseStorageFenceRuleResp.setSku(wncSkuStoreNoDTO.getSku());
                warehouseStorageFenceRuleResp.setStoreNo(wncSkuStoreNoDTO.getStoreNo());

                warehouseStorageFenceRuleRespList.add(warehouseStorageFenceRuleResp);
            }
        }


        return warehouseStorageFenceRuleRespList;
    }

    public static List<WarehouseStorageFenceRuleResp> dto2WarehouseStorageFenceRuleRespList(WarehouseStorageFenceRuleDTO warehouseStorageFenceRuleDTO){
        if(warehouseStorageFenceRuleDTO == null){
            return null;
        }
        List<WarehouseStorageFenceRuleResp> warehouseStorageFenceRuleRespList = new ArrayList<>();

        List<WncSkuStoreNoDTO> wncSkuStoreNoDtos = warehouseStorageFenceRuleDTO.getWncSkuStoreNoDTOS();
        if(CollectionUtils.isEmpty(wncSkuStoreNoDtos)){
            WarehouseStorageFenceRuleResp warehouseStorageFenceRuleResp = new WarehouseStorageFenceRuleResp();

            warehouseStorageFenceRuleResp.setDistance(warehouseStorageFenceRuleDTO.getDistance());
            warehouseStorageFenceRuleResp.setWarehouseNo(warehouseStorageFenceRuleDTO.getWarehouseNo());
            warehouseStorageFenceRuleResp.setTenantId(warehouseStorageFenceRuleDTO.getTenantId());
            warehouseStorageFenceRuleResp.setDeliveryTime(warehouseStorageFenceRuleDTO.getDeliveryTime());

            warehouseStorageFenceRuleRespList.add(warehouseStorageFenceRuleResp);
        }else{
            for (WncSkuStoreNoDTO wncSkuStoreNoDTO : wncSkuStoreNoDtos) {
                WarehouseStorageFenceRuleResp warehouseStorageFenceRuleResp = new WarehouseStorageFenceRuleResp();

                warehouseStorageFenceRuleResp.setDistance(warehouseStorageFenceRuleDTO.getDistance());
                warehouseStorageFenceRuleResp.setWarehouseNo(warehouseStorageFenceRuleDTO.getWarehouseNo());
                warehouseStorageFenceRuleResp.setTenantId(warehouseStorageFenceRuleDTO.getTenantId());
                if(wncSkuStoreNoDTO.getDeliveryCloseTime() != null){
                    warehouseStorageFenceRuleResp.setCloseTime(wncSkuStoreNoDTO.getDeliveryCloseTime().toLocalTime());
                }else{
                    warehouseStorageFenceRuleResp.setCloseTime(warehouseStorageFenceRuleDTO.getCloseTime());
                }
                warehouseStorageFenceRuleResp.setIsEveryDayFlag(warehouseStorageFenceRuleDTO.getIsEveryDayFlag());

                warehouseStorageFenceRuleResp.setSku(wncSkuStoreNoDTO.getSku());
                warehouseStorageFenceRuleResp.setStoreNo(wncSkuStoreNoDTO.getStoreNo());
                warehouseStorageFenceRuleResp.setSkuSubType(wncSkuStoreNoDTO.getSubType());
                warehouseStorageFenceRuleResp.setDeliveryTime(wncSkuStoreNoDTO.getDeliveryTime());
                warehouseStorageFenceRuleResp.setDeliveryCloseTime(wncSkuStoreNoDTO.getDeliveryCloseTime());

                warehouseStorageFenceRuleRespList.add(warehouseStorageFenceRuleResp);
            }
        }


        return warehouseStorageFenceRuleRespList;
    }
}
