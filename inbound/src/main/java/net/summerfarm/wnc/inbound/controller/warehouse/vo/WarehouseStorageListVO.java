package net.summerfarm.wnc.inbound.controller.warehouse.vo;

import lombok.Data;
import net.summerfarm.wnc.client.enums.WarehouseSourceEnum;

import java.io.Serializable;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/3/29 18:11<br/>
 *
 * <AUTHOR> />
 */
@Data
public class WarehouseStorageListVO implements Serializable {

    /**
     * 主键、自增
     */
    private Integer id;
    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 租户ID 鲜沐默认为0
     */
    private Long tenantId;

    /**
     * 开放状态：0、不开放 1、开放
     */
    private Integer status;

    /**
     * 仓库来源类型 1 鲜沐仓 2saas自营仓
     */
    private Integer warehouseSource;

    /**
     * 联系人
     */
    private String personContact;

    /**
     * 手机号
     */
    private String phone;
}
