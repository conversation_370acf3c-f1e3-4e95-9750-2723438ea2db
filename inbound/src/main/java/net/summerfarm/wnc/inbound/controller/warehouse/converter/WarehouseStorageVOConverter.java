package net.summerfarm.wnc.inbound.controller.warehouse.converter;

import lombok.Data;
import net.summerfarm.wnc.api.warehouse.dto.WarehouseStorageCenterWorkDTO;
import net.summerfarm.wnc.api.warehouse.dto.WncWarehouseStorageFenceDTO;
import net.summerfarm.wnc.api.warehouse.dto.WarehouseStorageDTO;
import net.summerfarm.wnc.api.warehouse.dto.WarehouseTakeStandardDTO;
import net.summerfarm.wnc.client.enums.WarehouseSourceEnum;
import net.summerfarm.wnc.inbound.controller.warehouse.vo.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/4/3 19:44<br/>
 *
 * <AUTHOR> />
 */
@Data
public class WarehouseStorageVOConverter {

    public static WarehouseStorageVO warehouseStorageDto2WarehouseStorageVO(WarehouseStorageDTO warehouseStorageDTO) {
        if (warehouseStorageDTO == null) {
            return null;
        }
        WarehouseStorageVO warehouseStorageVO = new WarehouseStorageVO();
        warehouseStorageVO.setWarehouseNo(warehouseStorageDTO.getWarehouseNo());
        warehouseStorageVO.setWarehouseName(warehouseStorageDTO.getWarehouseName());
        warehouseStorageVO.setAddress(warehouseStorageDTO.getAddress());
        warehouseStorageVO.setPoiNote(warehouseStorageDTO.getPoiNote());
        warehouseStorageVO.setPersonContact(warehouseStorageDTO.getPersonContact());
        warehouseStorageVO.setPhone(warehouseStorageDTO.getPhone());
        warehouseStorageVO.setTenantId(warehouseStorageDTO.getTenantId());
        warehouseStorageVO.setStatus(warehouseStorageDTO.getStatus());
        warehouseStorageVO.setManageAdminName(warehouseStorageDTO.getManageAdminName());
        if (warehouseStorageDTO.getWarehouseStorageCenterBusDTO() != null) {
            warehouseStorageVO.setCapacity(warehouseStorageDTO.getWarehouseStorageCenterBusDTO().getCapacity());
            warehouseStorageVO.setAdvanceDay(warehouseStorageDTO.getWarehouseStorageCenterBusDTO().getAdvanceDay());
        }
        if (!CollectionUtils.isEmpty(warehouseStorageDTO.getWarehouseTakeStandardDTOs())) {
            warehouseStorageVO.setWarehouseTakeStandards(WarehouseStorageVOConverter.takeStandardDTO2Vo(warehouseStorageDTO.getWarehouseTakeStandardDTOs()));
        }
        if (!CollectionUtils.isEmpty(warehouseStorageDTO.getRcWarehouseStorageFenceDTOs())) {
            List<WarehouseStorageFenceVO> fenceVOS = warehouseStorageDTO.getRcWarehouseStorageFenceDTOs().stream()
                    .map(WarehouseStorageVOConverter::warehouseFenceDTO2Vo).collect(Collectors.toList());

            warehouseStorageVO.setSendFenceList(fenceVOS.stream().filter(fence -> fence != null).collect(Collectors.toList()));
        }
        return warehouseStorageVO;
    }

    public static WarehouseStorageListVO warehouseStorageDto2WarehouseListStorageVO(WarehouseStorageDTO warehouseStorageDTO) {
        if (warehouseStorageDTO == null) {
            return null;
        }
        WarehouseStorageListVO warehouseStorageListVO = new WarehouseStorageListVO();
        warehouseStorageListVO.setId(warehouseStorageDTO.getId());
        warehouseStorageListVO.setWarehouseName(warehouseStorageDTO.getWarehouseName());
        warehouseStorageListVO.setTenantId(warehouseStorageDTO.getTenantId());
        warehouseStorageListVO.setWarehouseNo(warehouseStorageDTO.getWarehouseNo());
        warehouseStorageListVO.setStatus(warehouseStorageDTO.getStatus());
        warehouseStorageListVO.setPhone(warehouseStorageDTO.getPhone());
        warehouseStorageListVO.setPersonContact(warehouseStorageDTO.getPersonContact());
        if (Objects.equals(warehouseStorageDTO.getTenantId().intValue(), WarehouseSourceEnum.SUMMERFARM_WAREHOUSE.getCode())) {
            warehouseStorageListVO.setWarehouseSource(WarehouseSourceEnum.SUMMERFARM_WAREHOUSE.getCode());
        } else {
            warehouseStorageListVO.setWarehouseSource(WarehouseSourceEnum.SAAS_WAREHOUSE.getCode());
        }
        return warehouseStorageListVO;
    }

    public static WarehouseStorageFenceVO warehouseFenceDTO2Vo(WncWarehouseStorageFenceDTO rcWarehouseStorageFenceDTO) {
        if (rcWarehouseStorageFenceDTO == null) {
            return null;
        }
        WarehouseStorageFenceVO warehouseStorageFenceVO = new WarehouseStorageFenceVO();
        if (StringUtils.isNotBlank(rcWarehouseStorageFenceDTO.getProvince()) || StringUtils.isNotBlank(rcWarehouseStorageFenceDTO.getCity())
                || StringUtils.isNotBlank(rcWarehouseStorageFenceDTO.getArea())) {
            warehouseStorageFenceVO.setProvince(rcWarehouseStorageFenceDTO.getProvince());
            warehouseStorageFenceVO.setCity(rcWarehouseStorageFenceDTO.getCity());
            warehouseStorageFenceVO.setArea(rcWarehouseStorageFenceDTO.getArea());
            return warehouseStorageFenceVO;
        } else {
            return null;
        }
    }

    public static List<WarehouseTakeStandardVO> takeStandardDTO2Vo(List<WarehouseTakeStandardDTO> warehouseTakeStandardDTOs) {
        if (CollectionUtils.isEmpty(warehouseTakeStandardDTOs)) {
            return null;
        }

        Map<Integer, List<WarehouseTakeStandardDTO>> takeMap = warehouseTakeStandardDTOs.stream().collect(Collectors.groupingBy(WarehouseTakeStandardDTO::getStandardType));
        Iterator<Map.Entry<Integer, List<WarehouseTakeStandardDTO>>> iterator = takeMap.entrySet().iterator();
        List<WarehouseTakeStandardVO> warehouseTakeStandardVos = new ArrayList<>();
        while (iterator.hasNext()) {
            Map.Entry<Integer, List<WarehouseTakeStandardDTO>> entry = iterator.next();
            Integer standardType = entry.getKey();
            List<WarehouseTakeStandardDTO> standards = entry.getValue();
            List<StorageProveVO> storageProve = new ArrayList<>();
            Map<Integer, List<WarehouseTakeStandardDTO>> storageMap = standards.stream().collect(Collectors.groupingBy(WarehouseTakeStandardDTO::getStorageLocation));
            Iterator<Map.Entry<Integer, List<WarehouseTakeStandardDTO>>> storageIterator = storageMap.entrySet().iterator();
            while (storageIterator.hasNext()) {
                Map.Entry<Integer, List<WarehouseTakeStandardDTO>> storageEntry = storageIterator.next();
                Integer storageLocation = storageEntry.getKey();
                List<WarehouseTakeStandardDTO> standardList = storageEntry.getValue();
                List<Integer> proveTypes = standardList.stream().map(WarehouseTakeStandardDTO::getProveType).collect(Collectors.toList());
                StorageProveVO storageProveVO = new StorageProveVO();
                storageProveVO.setStorageLocation(storageLocation);
                storageProveVO.setProveType(proveTypes);
                storageProve.add(storageProveVO);
            }
            WarehouseTakeStandardVO warehouseTakeStandardVO = new WarehouseTakeStandardVO();
            warehouseTakeStandardVO.setStandardType(standardType);
            warehouseTakeStandardVO.setStorageProve(storageProve);
            warehouseTakeStandardVos.add(warehouseTakeStandardVO);
        }
        return warehouseTakeStandardVos;
    }


    public static XmWarehouseStorageVO warehouseStorageDto2XmVO(WarehouseStorageDTO warehouseStorageDTO){
        if(warehouseStorageDTO == null){
            return null;
        }
        XmWarehouseStorageVO warehouseStorageVO = new XmWarehouseStorageVO();
        warehouseStorageVO.setId(warehouseStorageDTO.getId());
        warehouseStorageVO.setAddress(warehouseStorageDTO.getAddress());
        warehouseStorageVO.setPoiNote(warehouseStorageDTO.getPoiNote());
        warehouseStorageVO.setWarehouseNo(warehouseStorageDTO.getWarehouseNo());
        warehouseStorageVO.setWarehouseName(warehouseStorageDTO.getWarehouseName());
        warehouseStorageVO.setPersonContact(warehouseStorageDTO.getPersonContact());
        warehouseStorageVO.setPhone(warehouseStorageDTO.getPhone());
        warehouseStorageVO.setTenantId(warehouseStorageDTO.getTenantId());
        warehouseStorageVO.setStatus(warehouseStorageDTO.getStatus());
        warehouseStorageVO.setManageAdminName(warehouseStorageDTO.getManageAdminName());
        warehouseStorageVO.setManageAdminId(warehouseStorageDTO.getManageAdminId());
        warehouseStorageVO.setType(warehouseStorageDTO.getType());
        warehouseStorageVO.setMailToAddress(warehouseStorageDTO.getMailToAddress());
        warehouseStorageVO.setAreaManageId(warehouseStorageDTO.getAreaManageId());
        warehouseStorageVO.setWarehousePic(warehouseStorageDTO.getWarehousePic());
        if(warehouseStorageDTO.getWarehouseStorageCenterBusDTO() != null){
            warehouseStorageVO.setCapacity((new BigDecimal(warehouseStorageDTO.getWarehouseStorageCenterBusDTO().getCapacity())).divide(new BigDecimal(1000), 2, RoundingMode.UP));
            warehouseStorageVO.setAdvanceDay(warehouseStorageDTO.getWarehouseStorageCenterBusDTO().getAdvanceDay());
        }
        if(!CollectionUtils.isEmpty(warehouseStorageDTO.getWarehouseTakeStandardDTOs())){
            warehouseStorageVO.setWarehouseTakeStandards(WarehouseStorageVOConverter.takeStandardDTO2Vo(warehouseStorageDTO.getWarehouseTakeStandardDTOs()));
        }
        if(!CollectionUtils.isEmpty(warehouseStorageDTO.getWarehouseStorageCenterWorkDTOs())){
            warehouseStorageVO.setWorkTimes(warehouseStorageDTO.getWarehouseStorageCenterWorkDTOs().stream().map(WarehouseStorageVOConverter::workTimeDTO2VO).collect(Collectors.toList()));
        }
        return warehouseStorageVO;
    }

    public static WorkTimeVO workTimeDTO2VO(WarehouseStorageCenterWorkDTO workDTO){
        if(workDTO == null){
           return null;
        }
        WorkTimeVO workTimeVO = new WorkTimeVO();

        workTimeVO.setStartTime(workDTO.getWorkStartTime());
        workTimeVO.setEndTime(workDTO.getWorkEndTime());

        return workTimeVO;
    }

    public static List<XmThirdAllWarehouseVO> dto2XmThirdVO(List<WarehouseStorageDTO> warehouseStorages) {
        if(CollectionUtils.isEmpty(warehouseStorages)){
            return Collections.emptyList();
        }
        List<XmThirdAllWarehouseVO>  xmThirdAllWarehouseVos = new ArrayList<>();
        Map<Long, List<WarehouseStorageDTO>> tenantIdWarehouseMap = warehouseStorages.stream().collect(Collectors.groupingBy(WarehouseStorageDTO::getTenantId));
        TreeMap<Long, List<WarehouseStorageDTO>> sortMap = new TreeMap<>(Comparator.comparingLong(o -> o));
        sortMap.putAll(tenantIdWarehouseMap);
        for (Long tenantId : sortMap.keySet()) {
            List<WarehouseStorageDTO> warehouseStorageDtos = tenantIdWarehouseMap.get(tenantId);
            XmThirdAllWarehouseVO xmThirdAllWarehouseVO = new XmThirdAllWarehouseVO();
            xmThirdAllWarehouseVO.setTenantId(tenantId);
            xmThirdAllWarehouseVO.setTenantName(warehouseStorageDtos.get(0).getTenantName());
            xmThirdAllWarehouseVO.setWarehouseDetails(warehouseStorageDtos.stream().map(WarehouseStorageVOConverter::dto2XmThirdDetail).collect(Collectors.toList()));
            xmThirdAllWarehouseVos.add(xmThirdAllWarehouseVO);
        }

        return xmThirdAllWarehouseVos;
    }

    public static XmThirdAllWarehouseDetailVO dto2XmThirdDetail(WarehouseStorageDTO warehouseStorageDTO){
        if(warehouseStorageDTO == null){
            return null;
        }
        XmThirdAllWarehouseDetailVO xmThirdAllWarehouseDetailVO = new XmThirdAllWarehouseDetailVO();
        xmThirdAllWarehouseDetailVO.setTenantId(warehouseStorageDTO.getTenantId());
        xmThirdAllWarehouseDetailVO.setWarehouseNo(warehouseStorageDTO.getWarehouseNo());
        xmThirdAllWarehouseDetailVO.setWarehouseName(warehouseStorageDTO.getWarehouseName());

        return xmThirdAllWarehouseDetailVO;
    }

    public static LogisticsStorageAllVO dto2LogisticsStorage(WarehouseStorageDTO warehouseStorageDTO){
        if(warehouseStorageDTO == null){
            return null;
        }
        LogisticsStorageAllVO vo = new LogisticsStorageAllVO();

        vo.setId(warehouseStorageDTO.getId());
        vo.setWarehouseNo(warehouseStorageDTO.getWarehouseNo());
        vo.setWarehouseName(warehouseStorageDTO.getWarehouseName());
        vo.setManageAdminId(warehouseStorageDTO.getManageAdminId());
        vo.setType(warehouseStorageDTO.getType());
        vo.setAreaManageId(warehouseStorageDTO.getAreaManageId());
        vo.setStatus(warehouseStorageDTO.getStatus());
        vo.setAddress(warehouseStorageDTO.getAddress());
        vo.setPoiNote(warehouseStorageDTO.getPoiNote());
        vo.setMailToAddress(warehouseStorageDTO.getMailToAddress());
        vo.setUpdater(warehouseStorageDTO.getUpdater());
        vo.setUpdateTime(warehouseStorageDTO.getUpdateTime());
        vo.setCreator(warehouseStorageDTO.getCreator());
        vo.setCreateTime(warehouseStorageDTO.getCreateTime());
        vo.setPersonContact(warehouseStorageDTO.getPersonContact());
        vo.setPhone(warehouseStorageDTO.getPhone());
        vo.setTenantId(warehouseStorageDTO.getTenantId());

        return vo;
    }


    public static XmAllWarehouseStorageVO warehouseStorageDto2XmAllVO(WarehouseStorageDTO warehouseStorageDTO){
        if(warehouseStorageDTO == null){
            return null;
        }
        XmAllWarehouseStorageVO warehouseStorageVO = new XmAllWarehouseStorageVO();
        warehouseStorageVO.setId(warehouseStorageDTO.getId());
        warehouseStorageVO.setAddress(warehouseStorageDTO.getAddress());
        warehouseStorageVO.setPoiNote(warehouseStorageDTO.getPoiNote());
        warehouseStorageVO.setWarehouseNo(warehouseStorageDTO.getWarehouseNo());
        warehouseStorageVO.setWarehouseName(warehouseStorageDTO.getWarehouseName());
        warehouseStorageVO.setPersonContact(warehouseStorageDTO.getPersonContact());
        warehouseStorageVO.setPhone(warehouseStorageDTO.getPhone());
        warehouseStorageVO.setTenantId(warehouseStorageDTO.getTenantId());
        warehouseStorageVO.setStatus(warehouseStorageDTO.getStatus());
        warehouseStorageVO.setManageAdminName(warehouseStorageDTO.getManageAdminName());
        warehouseStorageVO.setManageAdminId(warehouseStorageDTO.getManageAdminId());
        warehouseStorageVO.setType(warehouseStorageDTO.getType());
        warehouseStorageVO.setMailToAddress(warehouseStorageDTO.getMailToAddress());
        warehouseStorageVO.setAreaManageId(warehouseStorageDTO.getAreaManageId());
        warehouseStorageVO.setWarehousePic(warehouseStorageDTO.getWarehousePic());
        warehouseStorageVO.setCabinetStatus(warehouseStorageDTO.getCabinetStatus());
        warehouseStorageVO.setExternalStatus(warehouseStorageDTO.getExternalStatus());
        warehouseStorageVO.setCreator(warehouseStorageDTO.getCreator());
        warehouseStorageVO.setCreateTime(warehouseStorageDTO.getCreateTime());
        warehouseStorageVO.setUpdateTime(warehouseStorageDTO.getUpdateTime());
        warehouseStorageVO.setUpdater(warehouseStorageDTO.getUpdater());

        if(!CollectionUtils.isEmpty(warehouseStorageDTO.getWarehouseTakeStandardDTOs())){
            warehouseStorageVO.setWarehouseTakeStandardVO(WarehouseStorageVOConverter.takeStandardDTO2Vo(warehouseStorageDTO.getWarehouseTakeStandardDTOs()));
        }else{
            warehouseStorageVO.setWarehouseTakeStandardVO(Collections.emptyList());
        }

        WarehouseStorageCenterBusinessVO businessVO = new WarehouseStorageCenterBusinessVO();
        if(warehouseStorageDTO.getWarehouseStorageCenterBusDTO() != null){
            businessVO.setCapacity((new BigDecimal(warehouseStorageDTO.getWarehouseStorageCenterBusDTO().getCapacity())).divide(new BigDecimal(1000), 2, RoundingMode.UP));
            businessVO.setAdvanceDay(warehouseStorageDTO.getWarehouseStorageCenterBusDTO().getAdvanceDay());
            warehouseStorageVO.setWarehouseStorageCenterBusinessVO(businessVO);
        }

        if(!CollectionUtils.isEmpty(warehouseStorageDTO.getWarehouseStorageCenterWorkDTOs())){
            businessVO.setWorkTimeDTO(warehouseStorageDTO.getWarehouseStorageCenterWorkDTOs().stream().map(WarehouseStorageVOConverter::workTimeDTO2VO).collect(Collectors.toList()));
        }
        return warehouseStorageVO;
    }
}