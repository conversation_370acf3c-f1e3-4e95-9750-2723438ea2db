package net.summerfarm.wnc.inbound.provider.fence.converter;

import net.summerfarm.wnc.api.fence.dto.LocationDTO;
import net.summerfarm.wnc.api.fence.dto.GeoDTO;
import net.summerfarm.wnc.client.resp.AddressQueryPoiResp;
import net.summerfarm.wnc.client.resp.GeoResp;
import net.summerfarm.wnc.client.resp.LocationResp;

/**
 * Description:位置查询转换器
 * date: 2023/11/14 18:30
 *
 * <AUTHOR>
 */
public class LocationDTOConverter {

    public static LocationResp dto2resp(LocationDTO locationDTO){
        if (locationDTO == null){
            return null;
        }
        LocationResp locationResp = new LocationResp();
        locationResp.setCompleteAddress(locationDTO.getCompleteAddress());
        locationResp.setPoi(locationDTO.getPoi());
        GeoDTO geoDTO = locationDTO.getGeoDTO();
        if (geoDTO != null){
            GeoResp geoResp = new GeoResp();
            geoResp.setProvince(geoDTO.getProvince());
            geoResp.setCity(geoDTO.getCity());
            geoResp.setArea(geoDTO.getArea());
            geoResp.setTownship(geoDTO.getTownship());
            geoResp.setStreet(geoDTO.getStreet());
            geoResp.setNumber(geoDTO.getNumber());
            geoResp.setAddress(geoDTO.getAddress());
            locationResp.setGeoResp(geoResp);
        }
        return locationResp;
    }

    public static AddressQueryPoiResp dto2poiResp(LocationDTO locationDTO){
        if (locationDTO == null){
            return null;
        }
        AddressQueryPoiResp addressQueryPoiResp = new AddressQueryPoiResp();
        addressQueryPoiResp.setCompleteAddress(locationDTO.getCompleteAddress());
        addressQueryPoiResp.setPoi(locationDTO.getPoi());
        return addressQueryPoiResp;
    }
}
