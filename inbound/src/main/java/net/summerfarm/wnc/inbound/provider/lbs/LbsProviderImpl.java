package net.summerfarm.wnc.inbound.provider.lbs;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.fence.dto.LocationDTO;
import net.summerfarm.wnc.api.lbs.service.LbsService;
import net.summerfarm.wnc.client.provider.lbs.LbsProvider;
import net.summerfarm.wnc.client.req.AddressBatchQueryReq;
import net.summerfarm.wnc.client.req.AddressQueryReq;
import net.summerfarm.wnc.client.resp.AddressBatchQueryPoiResp;
import net.summerfarm.wnc.client.resp.AddressBatchQueryResp;
import net.summerfarm.wnc.client.resp.AddressQueryPoiResp;
import net.summerfarm.wnc.client.resp.LocationResp;
import net.summerfarm.wnc.common.query.fence.AddressQuery;
import net.summerfarm.wnc.inbound.provider.fence.converter.LocationDTOConverter;
import net.summerfarm.wnc.inbound.provider.fence.converter.AddressQueryConverter;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description:位置服务提供者实现类
 * date: 2023/11/15 10:25
 *
 * <AUTHOR>
 */
@Slf4j
@DubboService
public class LbsProviderImpl implements LbsProvider {

    @Resource
    private LbsService lbsService;

    @Override
    public DubboResponse<AddressQueryPoiResp> queryPoiByAddress(@Valid AddressQueryReq addressQueryReq) {
        AddressQuery addressQuery = AddressQueryConverter.req2query(addressQueryReq);
        LocationDTO locationDTO = lbsService.queryPoi(addressQuery);
        AddressQueryPoiResp resp = new AddressQueryPoiResp();
        resp.setPoi(locationDTO.getPoi());
        return DubboResponse.getOK(resp);
    }

    @Override
    public DubboResponse<AddressBatchQueryPoiResp> batchQueryPoiByAddress(@Valid AddressBatchQueryReq addressBatchQueryReq) {
        List<AddressQuery> addressQueryList = addressBatchQueryReq.getAddressQueryReqList().stream().map(AddressQueryConverter::req2query).collect(Collectors.toList());
        List<LocationDTO> locationDTOList = lbsService.batchQueryPoi(addressQueryList);
        List<AddressQueryPoiResp> addressQueryPoiRespList = locationDTOList.stream().map(LocationDTOConverter::dto2poiResp).collect(Collectors.toList());
        AddressBatchQueryPoiResp resp = new AddressBatchQueryPoiResp();
        resp.setAddressQueryPoiRespList(addressQueryPoiRespList);
        return DubboResponse.getOK(resp);
    }

    @Override
    public DubboResponse<AddressBatchQueryResp> batchQueryLocationByAddress(@Valid AddressBatchQueryReq addressBatchQueryReq) {
        List<AddressQuery> addressQueryList = addressBatchQueryReq.getAddressQueryReqList().stream().map(AddressQueryConverter::req2query).collect(Collectors.toList());
        List<LocationDTO> locationDTOList = lbsService.batchQueryLocation(addressQueryList);
        List<LocationResp> locationRespList = locationDTOList.stream().map(LocationDTOConverter::dto2resp).collect(Collectors.toList());
        AddressBatchQueryResp resp = new AddressBatchQueryResp();
        resp.setLocationRespList(locationRespList);
        return DubboResponse.getOK(resp);
    }
}
