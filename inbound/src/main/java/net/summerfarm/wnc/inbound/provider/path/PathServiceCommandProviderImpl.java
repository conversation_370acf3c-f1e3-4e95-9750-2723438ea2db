package net.summerfarm.wnc.inbound.provider.path;

import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.path.dto.PathDTO;
import net.summerfarm.wnc.api.path.dto.SkuPathMappingDTO;
import net.summerfarm.wnc.api.path.service.PathService;
import net.summerfarm.wnc.client.provider.path.PathServiceCommandProvider;
import net.summerfarm.wnc.client.req.path.PathDeleteReq;
import net.summerfarm.wnc.client.req.path.ReallocatePathSaveOrUpdateCommandReq;
import net.summerfarm.wnc.client.req.path.SkuPathMappingDeleteReq;
import net.summerfarm.wnc.client.req.path.SkuPathSaveOrUpdateCommandReq;
import net.summerfarm.wnc.common.enums.PathConfigEnums;
import net.summerfarm.wnc.inbound.provider.path.converter.PathDubboConverter;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: 路线配置操作<br/>
 * date: 2023/11/29 10:23<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@DubboService
public class PathServiceCommandProviderImpl implements PathServiceCommandProvider {

    @Resource
    private PathService pathService;

    @Override
    public DubboResponse<Void> reallocatePathSaveOrUpdateCommand(@Valid ReallocatePathSaveOrUpdateCommandReq req) {
        pathService.saveOrUpdateConfig(PathDTO.builder()
                .beginOutNo(req.getBeginOutNo())
                .endOutNo(req.getEndOutNo())
                .businseeType(PathConfigEnums.BusinseeType.REALLOCATE.getValue())
                .frequent(req.getFrequent())
                .frequentMethod(req.getFrequentMethod())
                .travelDuratio(req.getTravelDuratio())
                .lastTime(req.getLastTime())
                .build());
        return DubboResponse.getOK();
    }

    @Override
    public DubboResponse<Void> skuPathBatchSaveOrUpdateCommand(@Valid List<SkuPathSaveOrUpdateCommandReq> list) {
        //参数校验
        for (SkuPathSaveOrUpdateCommandReq skuPathReq : list) {
            String sku = skuPathReq.getSku();
            Integer supWarehouseNo = skuPathReq.getSupWarehouseNo();
            Integer warehouseNo = skuPathReq.getWarehouseNo();
            if(StringUtil.isBlank(sku) || supWarehouseNo == null || warehouseNo == null){
                log.info("新增sku和仓库和上级支援仓缺失必传条件，sku:{},supWarehouseNo:{},warehouseNo:{}",sku,supWarehouseNo,warehouseNo);
                throw new BizException("sku和上级支援仓和仓库编号不能为空");
            }
        }
        pathService.skuPathBatchSaveOrUpdate(list.stream().map(PathDubboConverter::SkuPathReq2DTO).collect(Collectors.toList()));
        return DubboResponse.getOK();
    }

    @Override
    public DubboResponse<Void> pathDeleteCommand(@Valid PathDeleteReq pathDeleteReq) {
        pathService.pathDelete(pathDeleteReq.getId());
        return DubboResponse.getOK();
    }

    @Override
    public DubboResponse<Void> skuPathMappingBatchDeleteCommand(@Valid List<SkuPathMappingDeleteReq> list) {
        if(CollectionUtils.isEmpty(list)){
            return DubboResponse.getDefaultError("删除路线信息不能为空");
        }
        if(list.stream().anyMatch(item -> StringUtil.isBlank(item.getSku()) || item.getWarehouseNo() == null)){
            return DubboResponse.getDefaultError("sku和仓库编号不能为空");
        }
        pathService.skuPathMappingBatchDelete(list.stream().map(PathDubboConverter::skuPathBatchDelete2DTO).collect(Collectors.toList()));
        return DubboResponse.getOK();
    }
}
