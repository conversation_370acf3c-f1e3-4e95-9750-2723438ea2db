package net.summerfarm.wnc.inbound.provider.delivery.refactor.context;

import lombok.Builder;
import lombok.Data;
import net.summerfarm.wnc.api.fence.dto.DeliveryFenceDTO;
import net.summerfarm.wnc.client.req.DeliveryRuleQueryReq;
import net.summerfarm.wnc.client.req.delivery.PayAfterDeliveryDateQueryReq;
import net.summerfarm.wnc.common.query.fence.DeliveryFenceDateQuery;
import net.summerfarm.wnc.domain.fence.entity.DeliveryFenceEntity;
import net.summerfarm.wnc.domain.outLandContact.entity.OutLandContactEntity;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 配送查询上下文
 * 
 * 作用：
 * 1. 封装查询过程中的所有数据和状态
 * 2. 在处理链中传递数据
 * 3. 避免方法参数过多的问题
 * 4. 便于扩展新的上下文信息
 * 
 * <AUTHOR>
 */
@Data
@Builder
public class DeliveryQueryContext {
    
    /**
     * 查询类型枚举
     */
    public enum QueryType {
        NORMAL("普通查询"),
        CACHED("缓存查询"), 
        CATEGORY("全品类查询"),
        PAY_AFTER("支付后查询");
        
        private final String description;
        
        QueryType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    // ========== 请求信息 ==========
    /**
     * 标准查询请求
     */
    private DeliveryRuleQueryReq request;
    
    /**
     * 支付后查询请求
     */
    private PayAfterDeliveryDateQueryReq payAfterRequest;
    
    /**
     * 查询类型
     */
    private QueryType queryType;
    
    // ========== 处理过程数据 ==========
    /**
     * 转换后的查询对象
     */
    private DeliveryFenceDateQuery deliveryFenceDateQuery;
    
    /**
     * 联系人信息
     */
    private OutLandContactEntity contactEntity;
    
    /**
     * 配送围栏实体
     */
    private DeliveryFenceEntity deliveryFenceEntity;
    
    /**
     * 首配日期
     */
    private LocalDate firstDeliveryDate;
    
    /**
     * 配送日期列表
     */
    private List<LocalDate> deliveryDateList;
    
    /**
     * 全品类配送日期
     */
    private LocalDate fullCategoryDeliveryDate;
    
    /**
     * 下一个配送日期
     */
    private LocalDate nextDeliveryDate;
    
    // ========== 结果数据 ==========
    /**
     * 配送围栏DTO
     */
    private DeliveryFenceDTO deliveryFenceDTO;
    
    // ========== 扩展信息 ==========
    /**
     * 处理开始时间
     */
    @Builder.Default
    private LocalDateTime processStartTime = LocalDateTime.now();
    
    /**
     * 扩展属性，用于传递额外信息
     */
    private Map<String, Object> extensions;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 是否处理成功
     */
    @Builder.Default
    private boolean success = true;
    
    // ========== 便利方法 ==========
    
    /**
     * 获取联系人ID
     */
    public Long getContactId() {
        if (request != null) {
            return request.getContactId();
        }
        if (payAfterRequest != null) {
            return payAfterRequest.getContactId();
        }
        return null;
    }
    
    /**
     * 获取商户ID
     */
    public Long getMerchantId() {
        if (request != null) {
            return request.getMerchantId();
        }
        if (payAfterRequest != null) {
            return payAfterRequest.getMerchantId();
        }
        return null;
    }
    
    /**
     * 获取城市
     */
    public String getCity() {
        if (request != null) {
            return request.getCity();
        }
        if (payAfterRequest != null) {
            return payAfterRequest.getCity();
        }
        return null;
    }
    
    /**
     * 获取区域
     */
    public String getArea() {
        if (request != null) {
            return request.getArea();
        }
        if (payAfterRequest != null) {
            return payAfterRequest.getArea();
        }
        return null;
    }
    
    /**
     * 获取订单时间
     */
    public LocalDateTime getOrderTime() {
        if (request != null) {
            return request.getOrderTime();
        }
        if (payAfterRequest != null) {
            return payAfterRequest.getOrderTime();
        }
        return null;
    }
    
    /**
     * 获取SKU列表
     */
    public List<String> getSkus() {
        if (request != null) {
            return request.getSkus();
        }
        if (payAfterRequest != null) {
            return payAfterRequest.getSkus();
        }
        return null;
    }
    
    /**
     * 设置错误信息并标记失败
     */
    public void setError(String errorMessage) {
        this.errorMessage = errorMessage;
        this.success = false;
    }
    
    /**
     * 添加扩展属性
     */
    public void addExtension(String key, Object value) {
        if (extensions == null) {
            extensions = new java.util.HashMap<>();
        }
        extensions.put(key, value);
    }
    
    /**
     * 获取扩展属性
     */
    @SuppressWarnings("unchecked")
    public <T> T getExtension(String key, Class<T> type) {
        if (extensions == null) {
            return null;
        }
        Object value = extensions.get(key);
        if (value != null && type.isInstance(value)) {
            return (T) value;
        }
        return null;
    }
}
