package net.summerfarm.wnc.inbound.controller.warehouse.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalTime;

/**
 * Description: <br/>
 * date: 2023/8/23 14:44<br/>
 *
 * <AUTHOR> />
 */
@Data
public class WorkTimeVO {

    /**
     *仓库作业时间开始时间
     */
    @JsonFormat(pattern = "HH:mm")
    private LocalTime startTime;

    /**
     * 仓库作业时间结束时间
     */
    @JsonFormat(pattern = "HH:mm")
    private LocalTime endTime;
}
