package net.summerfarm.wnc.inbound.provider.fastmall;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.fastmall.dto.WncFastMallDTO;
import net.summerfarm.wnc.api.fastmall.service.WncFastMallService;
import net.summerfarm.wnc.client.provider.fms.FastMallServiceQueryProvider;
import net.summerfarm.wnc.client.resp.FastMallResp;
import net.summerfarm.wnc.inbound.provider.fastmall.converter.WncFastMallDTOConverter;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/4/11 16:26<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@DubboService
public class FastMallServiceQueryProviderImpl implements FastMallServiceQueryProvider {

    @Resource
    private WncFastMallService wncFastMallService;

    @Override
    public DubboResponse<List<FastMallResp>> queryFastMallList() {
        List<WncFastMallDTO> wncFastMallDTOS = wncFastMallService.queryFastMallList();
        return DubboResponse.getOK(wncFastMallDTOS.stream().map(WncFastMallDTOConverter::dto2Resp)
                .collect(Collectors.toList()));
    }

}
