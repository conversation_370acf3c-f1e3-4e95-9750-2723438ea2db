package net.summerfarm.wnc.inbound.provider.fence.converter;

import net.summerfarm.wnc.api.fence.dto.DeliveryFenceCloseTimeQuery;
import net.summerfarm.wnc.client.req.FenceCloseTimeQueryReq;
import net.summerfarm.wnc.common.constants.AppConsts;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @Date 2023-07-21
 **/
@Mapper(componentModel = AppConsts.MapStructConstants.COMPONENT_MODEL_SPRING)
public interface DeliveryFenceQueryConverter {

	/**
	 * dubboDTOToQueryDTO
	 *
	 * @param fenceCloseTimeQueryReq
	 * @return
	 */
	DeliveryFenceCloseTimeQuery dubboDTOToQueryDTO(FenceCloseTimeQueryReq fenceCloseTimeQueryReq);
}
