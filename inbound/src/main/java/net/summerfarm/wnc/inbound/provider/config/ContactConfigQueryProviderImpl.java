package net.summerfarm.wnc.inbound.provider.config;

import net.summerfarm.wnc.api.config.dto.ContactConfigDTO;
import net.summerfarm.wnc.api.config.service.ContactConfigQueryService;
import net.summerfarm.wnc.client.provider.config.ContactConfigQueryProvider;
import net.summerfarm.wnc.client.req.ContactConfigQueryReq;
import net.summerfarm.wnc.client.req.storeConfig.ContactConfigBatchQueryReq;
import net.summerfarm.wnc.client.resp.ContactConfigResp;
import net.summerfarm.wnc.common.query.config.ContactConfigQuery;
import net.summerfarm.wnc.inbound.provider.config.converter.ContactConfigDTOConverter;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Description:联系人配置查询提供者实现
 * date: 2023/9/22 17:04
 *
 * <AUTHOR>
 */
@DubboService
public class ContactConfigQueryProviderImpl implements ContactConfigQueryProvider {

    @Resource
    private ContactConfigQueryService contactConfigQueryService;

    @Override
    public DubboResponse<ContactConfigResp> queryContactConfig(@Valid ContactConfigQueryReq queryReq) {
        ContactConfigQuery query = ContactConfigQuery.builder().outerContactId(queryReq.getContactId()).tenantId(queryReq.getTenantId()).build();
        ContactConfigDTO contactConfigDTO = contactConfigQueryService.queryDetail(query);
        return DubboResponse.getOK(ContactConfigDTOConverter.dto2Resp(contactConfigDTO));
    }

    @Override
    public DubboResponse<List<ContactConfigResp>> batchQueryContactConfig(@Valid ContactConfigBatchQueryReq batchQueryReq) {
        List<Long> contactIds =  batchQueryReq.getQueryReqs().stream().map(ContactConfigQueryReq::getContactId).collect(Collectors.toList());

        List<ContactConfigDTO> contactConfigDTOList = contactConfigQueryService.queryConfigList(ContactConfigQuery.builder().outerContactIds(contactIds).build());
        //根据门店ID+租户进行过滤一下
        Set<String> contactUkSet = batchQueryReq.getQueryReqs().stream().map(e -> String.format("%s#%s", e.getTenantId(), e.getContactId())).collect(Collectors.toSet());
        contactConfigDTOList = contactConfigDTOList.stream().filter(e -> contactUkSet.contains(String.format("%s#%s", e.getTenantId(), e.getOuterContactId()))).collect(Collectors.toList());

        List<ContactConfigResp> contactConfigRespList = contactConfigDTOList.stream().map(ContactConfigDTOConverter::dto2Resp).collect(Collectors.toList());
        return DubboResponse.getOK(contactConfigRespList);
    }
}
