package net.summerfarm.wnc.inbound.provider.delivery.refactor;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider;
import net.summerfarm.wnc.client.req.CategoryDeliveryRuleQueryReq;
import net.summerfarm.wnc.client.req.DeliveryRuleQueryReq;
import net.summerfarm.wnc.client.req.delivery.PayAfterDeliveryDateQueryReq;
import net.summerfarm.wnc.client.resp.CategoryDeliveryDateResp;
import net.summerfarm.wnc.client.resp.DeliveryRuleResp;
import net.summerfarm.wnc.client.resp.delivery.PayAfterDeliveryDateResp;
import net.summerfarm.wnc.inbound.provider.delivery.refactor.orchestrator.DeliveryDateQueryOrchestrator;
import net.summerfarm.wnc.inbound.provider.delivery.refactor.context.DeliveryQueryContext;
import net.summerfarm.wnc.inbound.provider.delivery.refactor.converter.DeliveryQueryConverter;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 重构后的配送规则查询提供者
 * 
 * 设计原则：
 * 1. 单一职责：每个类只负责一个明确的功能
 * 2. 开闭原则：通过策略模式支持新的配送规则扩展
 * 3. 依赖倒置：依赖抽象而非具体实现
 * 4. 接口隔离：细化接口，避免臃肿
 * 
 * <AUTHOR>
 */
@Slf4j
@DubboService
public class DeliveryRuleQueryProviderRefactored implements DeliveryRuleQueryProvider {

    @Resource
    private DeliveryDateQueryOrchestrator deliveryDateQueryOrchestrator;
    
    @Resource
    private DeliveryQueryConverter deliveryQueryConverter;

    /**
     * 查询配送日期信息
     * 重构要点：
     * 1. 将复杂逻辑委托给编排器处理
     * 2. 统一异常处理
     * 3. 规范化日志记录
     */
    @Override
    public DubboResponse<DeliveryRuleResp> queryDeliveryDateInfo(DeliveryRuleQueryReq deliveryRuleQueryReq) {
        try {
            log.info("开始查询配送日期，请求参数: {}", deliveryRuleQueryReq);
            
            // 构建查询上下文
            DeliveryQueryContext context = DeliveryQueryContext.builder()
                    .request(deliveryRuleQueryReq)
                    .queryType(DeliveryQueryContext.QueryType.NORMAL)
                    .build();
            
            // 执行查询编排
            DeliveryRuleResp response = deliveryDateQueryOrchestrator.execute(context);
            
            log.info("配送日期查询完成，联系人ID: {}, 商户ID: {}, 配送日期数量: {}", 
                    deliveryRuleQueryReq.getContactId(), 
                    deliveryRuleQueryReq.getMerchantId(),
                    response.getDeliveryDateList() != null ? response.getDeliveryDateList().size() : 0);
            
            return DubboResponse.getOK(response);
            
        } catch (Exception e) {
            log.error("查询配送日期失败，请求参数: {}", deliveryRuleQueryReq, e);
            return DubboResponse.getFail(e.getMessage());
        }
    }

    /**
     * 查询缓存配送日期信息
     */
    @Override
    public DubboResponse<DeliveryRuleResp> queryCacheDeliveryDateInfo(DeliveryRuleQueryReq deliveryRuleQueryReq) {
        try {
            log.info("开始查询缓存配送日期，请求参数: {}", deliveryRuleQueryReq);
            
            DeliveryQueryContext context = DeliveryQueryContext.builder()
                    .request(deliveryRuleQueryReq)
                    .queryType(DeliveryQueryContext.QueryType.CACHED)
                    .build();
            
            DeliveryRuleResp response = deliveryDateQueryOrchestrator.execute(context);
            
            log.info("缓存配送日期查询完成");
            return DubboResponse.getOK(response);
            
        } catch (Exception e) {
            log.error("查询缓存配送日期失败", e);
            return DubboResponse.getFail(e.getMessage());
        }
    }

    /**
     * 查询商城全品类配送日期
     */
    @Override
    public DubboResponse<CategoryDeliveryDateResp> queryMallCategoryDeliveryDate(@Valid CategoryDeliveryRuleQueryReq categoryDeliveryRuleQueryReq) {
        try {
            log.info("开始查询全品类配送日期，请求参数: {}", categoryDeliveryRuleQueryReq);
            
            // 转换为标准查询请求
            DeliveryRuleQueryReq standardReq = deliveryQueryConverter.categoryToStandard(categoryDeliveryRuleQueryReq);
            
            DeliveryQueryContext context = DeliveryQueryContext.builder()
                    .request(standardReq)
                    .queryType(DeliveryQueryContext.QueryType.CATEGORY)
                    .build();
            
            DeliveryRuleResp deliveryResp = deliveryDateQueryOrchestrator.execute(context);
            CategoryDeliveryDateResp response = deliveryQueryConverter.standardToCategory(deliveryResp);
            
            log.info("全品类配送日期查询完成");
            return DubboResponse.getOK(response);
            
        } catch (Exception e) {
            log.error("查询全品类配送日期失败", e);
            return DubboResponse.getFail(e.getMessage());
        }
    }

    /**
     * 查询支付后配送日期
     */
    @Override
    public DubboResponse<PayAfterDeliveryDateResp> queryPayAfterDeliveryDate(@Valid PayAfterDeliveryDateQueryReq req) {
        try {
            log.info("开始查询支付后配送日期，请求参数: {}", req);
            
            DeliveryQueryContext context = DeliveryQueryContext.builder()
                    .payAfterRequest(req)
                    .queryType(DeliveryQueryContext.QueryType.PAY_AFTER)
                    .build();
            
            PayAfterDeliveryDateResp response = deliveryDateQueryOrchestrator.executePayAfter(context);
            
            log.info("支付后配送日期查询完成");
            return DubboResponse.getOK(response);
            
        } catch (Exception e) {
            log.error("查询支付后配送日期失败", e);
            return DubboResponse.getFail(e.getMessage());
        }
    }
}
