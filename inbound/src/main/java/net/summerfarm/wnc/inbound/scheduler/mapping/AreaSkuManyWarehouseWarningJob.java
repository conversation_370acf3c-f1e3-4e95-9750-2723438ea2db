package net.summerfarm.wnc.inbound.scheduler.mapping;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.fence.service.WarehouseInventoryMappingService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Description: 区域sku多仓映射告警<br/>
 * date: 2023/12/26 14:47<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Component
public class AreaSkuManyWarehouseWarningJob extends XianMuJavaProcessorV2 {

    @Resource
    private WarehouseInventoryMappingService warehouseInventoryMappingService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("相同运营区域sku多仓映射关系告警");
        warehouseInventoryMappingService.areaSkuManyWarehouseWarningJob();
        return new ProcessResult(true);
    }
}
