package net.summerfarm.wnc.inbound.controller.stop;

import net.summerfarm.wnc.api.stop.input.StopDeliveryAddInput;
import net.summerfarm.wnc.api.stop.input.StopDeliveryCancelInput;
import net.summerfarm.wnc.api.stop.service.StopDeliveryService;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 停配
 */
@RestController
@RequestMapping("/stop")
public class StopDeliveryController {

    @Resource
    private StopDeliveryService stopDeliveryService;

    /**
     * 城配仓停配时间的设置
     */
    @PostMapping("/upsert/stop-delivery-add")
    @RequiresPermissions(value = {"wnc:store_stop_delivery:update", "SUPER_ADMIN"}, logical = Logical.OR)
    public CommonResult<Void> stopDeliveryAdd(@RequestBody @Validated StopDeliveryAddInput input) {
        stopDeliveryService.stopDeliveryAdd(input);
        return CommonResult.ok();
    }

    /**
     * 取消城配仓的停配配置
     */
    @PostMapping("/upsert/stop-delivery-cancel")
    public CommonResult<Void> stopDeliveryCancel(@RequestBody @Validated StopDeliveryCancelInput input) {
        stopDeliveryService.stopDeliveryCancel(input);
        return CommonResult.ok();
    }
}
