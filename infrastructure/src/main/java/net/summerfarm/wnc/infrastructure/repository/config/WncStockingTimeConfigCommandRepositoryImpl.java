package net.summerfarm.wnc.infrastructure.repository.config;

import net.summerfarm.wnc.infrastructure.model.config.WncStockingTimeConfig;
import net.summerfarm.wnc.infrastructure.mapper.config.WncStockingTimeConfigMapper;
import net.summerfarm.wnc.infrastructure.converter.config.WncStockingTimeConfigConverter;
import net.summerfarm.wnc.domain.config.repository.WncStockingTimeConfigCommandRepository;
import net.summerfarm.wnc.domain.config.entity.WncStockingTimeConfigEntity;
import net.summerfarm.wnc.domain.config.param.command.WncStockingTimeConfigCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2025-04-15 14:51:44
* @version 1.0
*
*/
@Repository
public class WncStockingTimeConfigCommandRepositoryImpl implements WncStockingTimeConfigCommandRepository {

    @Autowired
    private WncStockingTimeConfigMapper wncStockingTimeConfigMapper;
    @Override
    public WncStockingTimeConfigEntity insertSelective(WncStockingTimeConfigCommandParam param) {
        WncStockingTimeConfig wncStockingTimeConfig = WncStockingTimeConfigConverter.toWncStockingTimeConfig(param);
        wncStockingTimeConfigMapper.insertSelective(wncStockingTimeConfig);
        return WncStockingTimeConfigConverter.toWncStockingTimeConfigEntity(wncStockingTimeConfig);
    }

    @Override
    public int updateSelectiveById(WncStockingTimeConfigCommandParam param){
        return wncStockingTimeConfigMapper.updateSelectiveById(WncStockingTimeConfigConverter.toWncStockingTimeConfig(param));
    }


    @Override
    public int remove(Long id) {
        return wncStockingTimeConfigMapper.remove(id);
    }
}