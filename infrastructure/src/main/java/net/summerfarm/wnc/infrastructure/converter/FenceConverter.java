package net.summerfarm.wnc.infrastructure.converter;
import com.google.common.collect.Lists;
import net.summerfarm.wnc.common.enums.FenceEnums.Status;
import java.time.LocalDateTime;
import net.summerfarm.wnc.common.enums.FenceEnums.Type;

import lombok.Data;
import net.summerfarm.wnc.common.enums.FenceDeliveryEnums;
import net.summerfarm.wnc.common.enums.FenceEnums;
import net.summerfarm.wnc.domain.fence.entity.FenceDeliveryEntity;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.summerfarm.wnc.infrastructure.model.Fence;
import net.summerfarm.wnc.infrastructure.model.FenceDelivery;
import net.summerfarm.wnc.infrastructure.model.FenceDeliveryLog;

/**
 * Description: <br/>
 * date: 2023/3/8 13:43<br/>
 *
 * <AUTHOR> />
 */
@Data
public class FenceConverter {

    public static FenceEntity fence2Entity(Fence fence){
        if(fence == null){
            return null;
        }
        FenceEntity fenceEntity = new FenceEntity();
        fenceEntity.setId(fence.getId());
        fenceEntity.setFenceName(fence.getFenceName());
        fenceEntity.setStoreNo(fence.getStoreNo());
        fenceEntity.setAreaNo(fence.getAreaNo());
        fenceEntity.setStatus(FenceEnums.Status.getStatusByValue(fence.getStatus()));
        fenceEntity.setType(FenceEnums.Type.getTypeByValue(fence.getType()));
        //entity与po字段差异
        fenceEntity.setCreateTime(fence.getAddTime());
        fenceEntity.setUpdateTime(fence.getUpdateTime());
        //entity与po字段差异
        fenceEntity.setUpdaterId(fence.getAdminId());
        fenceEntity.setPackId(fence.getPackId());

        fenceEntity.setAreaName(fence.getAreaName());
        fenceEntity.setStoreName(fence.getStoreName());
        fenceEntity.setCityName(fence.getCityName());
        fenceEntity.setUpdater(fence.getAdminName());
        fenceEntity.setOrderChannelType(fence.getOrderChannelType());

        return fenceEntity;
    }

    public static Fence entity2Fence(FenceEntity fenceEntity){
        if(fenceEntity == null){
            return null;
        }
        Fence fence = new Fence();

        fence.setId(fenceEntity.getId());
        fence.setFenceName(fenceEntity.getFenceName());
        fence.setStoreNo(fenceEntity.getStoreNo());
        fence.setAreaNo(fenceEntity.getAreaNo());
        if (fenceEntity.getStatus() != null){
            fence.setStatus(fenceEntity.getStatus().getValue());
        }
        if (fenceEntity.getType() != null){
            fence.setType(fenceEntity.getType().getValue());
        }
        //entity与po字段差异
        fence.setAddTime(fenceEntity.getCreateTime());
        fence.setUpdateTime(fenceEntity.getUpdateTime());
        //entity与po字段差异
        fence.setAdminId(fenceEntity.getUpdaterId());
        fence.setPackId(fenceEntity.getPackId());

        fence.setOrderChannelType(fenceEntity.getOrderChannelType());
        return fence;
    }

    public static FenceDeliveryEntity fenceDelivery2Entity(FenceDelivery fenceDelivery){
        if(fenceDelivery == null){
            return null;
        }

        FenceDeliveryEntity fenceDeliveryEntity = new FenceDeliveryEntity();

        fenceDeliveryEntity.setId(fenceDelivery.getId());
        fenceDeliveryEntity.setFenceId(fenceDelivery.getFenceId());
        fenceDeliveryEntity.setDeliveryFrequent(fenceDelivery.getDeliveryFrequent());
        fenceDeliveryEntity.setNextDeliveryDate(fenceDelivery.getNextDeliveryDate());
        fenceDeliveryEntity.setDeleteFlag(fenceDelivery.getDeleteFlag());
        fenceDeliveryEntity.setCreator(fenceDelivery.getCreator());
        fenceDeliveryEntity.setCreateTime(fenceDelivery.getCreateTime());
        fenceDeliveryEntity.setUpdater(fenceDelivery.getUpdater());
        fenceDeliveryEntity.setUpdateTime(fenceDelivery.getUpdateTime());
        fenceDeliveryEntity.setFrequentMethod(fenceDelivery.getFrequentMethod() == null ?
                FenceDeliveryEnums.FrequentMethod.WEEK_CALC.getValue() : fenceDelivery.getFrequentMethod());
        fenceDeliveryEntity.setDeliveryFrequentInterval(fenceDelivery.getDeliveryFrequentInterval());
        fenceDeliveryEntity.setBeginCalculateDate(fenceDelivery.getBeginCalculateDate());

        return fenceDeliveryEntity;
    }

    public static FenceDelivery entity2FenceDelivery(FenceDeliveryEntity fenceDeliveryEntity){
        if (fenceDeliveryEntity == null){
            return null;
        }
        FenceDelivery fenceDelivery = new FenceDelivery();
        fenceDelivery.setId(fenceDeliveryEntity.getId());
        fenceDelivery.setFenceId(fenceDeliveryEntity.getFenceId());
        fenceDelivery.setDeliveryFrequent(fenceDeliveryEntity.getDeliveryFrequent());
        fenceDelivery.setNextDeliveryDate(fenceDeliveryEntity.getNextDeliveryDate());
        fenceDelivery.setDeleteFlag(fenceDeliveryEntity.getDeleteFlag());
        fenceDelivery.setCreator(fenceDeliveryEntity.getCreator());
        fenceDelivery.setCreateTime(fenceDeliveryEntity.getCreateTime());
        fenceDelivery.setUpdater(fenceDeliveryEntity.getUpdater());
        fenceDelivery.setUpdateTime(fenceDeliveryEntity.getUpdateTime());
        fenceDelivery.setFrequentMethod(fenceDeliveryEntity.getFrequentMethod());
        fenceDelivery.setDeliveryFrequentInterval(fenceDeliveryEntity.getDeliveryFrequentInterval());
        fenceDelivery.setBeginCalculateDate(fenceDeliveryEntity.getBeginCalculateDate());
        return fenceDelivery;
    }

    public static FenceDeliveryLog fenceDelivery2Log(FenceDelivery fenceDelivery){
        if (fenceDelivery == null){
            return null;
        }
        FenceDeliveryLog fenceDeliveryLog = new FenceDeliveryLog();
        //日志新增 无需设置ID
//        fenceDeliveryLog.setId(fenceDelivery.getId());
        fenceDeliveryLog.setFenceId(fenceDelivery.getFenceId());
        fenceDeliveryLog.setDeliveryFrequent(fenceDelivery.getDeliveryFrequent());
        fenceDeliveryLog.setNextDeliveryDate(fenceDelivery.getNextDeliveryDate());
        fenceDeliveryLog.setCreator(fenceDelivery.getCreator());
        fenceDeliveryLog.setCreateTime(fenceDelivery.getCreateTime());
        fenceDeliveryLog.setUpdater(fenceDelivery.getUpdater());
        fenceDeliveryLog.setUpdateTime(fenceDelivery.getUpdateTime());
        fenceDeliveryLog.setFrequentMethod(fenceDelivery.getFrequentMethod());
        fenceDeliveryLog.setDeliveryFrequentInterval(fenceDelivery.getDeliveryFrequentInterval());
        fenceDeliveryLog.setBeginCalculateDate(fenceDelivery.getBeginCalculateDate());
        return fenceDeliveryLog;
    }
}
