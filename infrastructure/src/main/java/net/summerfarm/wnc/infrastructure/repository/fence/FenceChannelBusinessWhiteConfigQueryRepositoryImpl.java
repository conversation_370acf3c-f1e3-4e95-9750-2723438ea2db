package net.summerfarm.wnc.infrastructure.repository.fence;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.summerfarm.wnc.infrastructure.model.fence.FenceChannelBusinessWhiteConfig;
import net.summerfarm.wnc.infrastructure.mapper.fence.FenceChannelBusinessWhiteConfigMapper;
import net.summerfarm.wnc.infrastructure.converter.fence.FenceChannelBusinessWhiteConfigConverter;
import net.summerfarm.wnc.domain.fence.repository.FenceChannelBusinessWhiteConfigQueryRepository;
import net.summerfarm.wnc.domain.fence.entity.FenceChannelBusinessWhiteConfigEntity;
import net.summerfarm.wnc.domain.fence.param.query.FenceChannelBusinessWhiteConfigQueryParam;
import net.summerfarm.wnc.common.converter.PageInfoConverter;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


/**
*
* <AUTHOR>
* @date 2024-10-12 16:29:33
* @version 1.0
*
*/
@Repository
public class FenceChannelBusinessWhiteConfigQueryRepositoryImpl implements FenceChannelBusinessWhiteConfigQueryRepository {

    @Autowired
    private FenceChannelBusinessWhiteConfigMapper fenceChannelBusinessWhiteConfigMapper;


    @Override
    public PageInfo<FenceChannelBusinessWhiteConfigEntity> getPage(FenceChannelBusinessWhiteConfigQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<FenceChannelBusinessWhiteConfigEntity> entities = fenceChannelBusinessWhiteConfigMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public FenceChannelBusinessWhiteConfigEntity selectById(Long id) {
        return FenceChannelBusinessWhiteConfigConverter.toFenceChannelBusinessWhiteConfigEntity(fenceChannelBusinessWhiteConfigMapper.selectById(id));
    }


    @Override
    public List<FenceChannelBusinessWhiteConfigEntity> selectByCondition(FenceChannelBusinessWhiteConfigQueryParam param) {
        return FenceChannelBusinessWhiteConfigConverter.toFenceChannelBusinessWhiteConfigEntityList(fenceChannelBusinessWhiteConfigMapper.selectByCondition(param));
    }

    @Override
    public List<Integer> queryAllFenceIdList() {
        List<FenceChannelBusinessWhiteConfig> fenceChannelBusinessWhiteConfigList = fenceChannelBusinessWhiteConfigMapper.selectList(new LambdaQueryWrapper<FenceChannelBusinessWhiteConfig>()
                .groupBy(FenceChannelBusinessWhiteConfig::getFenceId));
        if(CollectionUtils.isEmpty(fenceChannelBusinessWhiteConfigList)){
            return Collections.emptyList();
        }
        return fenceChannelBusinessWhiteConfigList.stream().map(FenceChannelBusinessWhiteConfig::getFenceId).collect(Collectors.toList());
    }
}