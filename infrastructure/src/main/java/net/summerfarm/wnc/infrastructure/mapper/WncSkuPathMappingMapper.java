package net.summerfarm.wnc.infrastructure.mapper;

import net.summerfarm.wnc.domain.path.entity.SkuPathMappingEntity;
import net.summerfarm.wnc.infrastructure.model.WncSkuPathMapping;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 商品路线映射表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-332 14:24:16
 */
public interface WncSkuPathMappingMapper extends BaseMapper<WncSkuPathMapping> {

    /**
     * 根据仓库编号和sku删除
     * @param skuPathMappingEntities 实体
     */
    void batchDeleteByWarehouseNosSkus(@Param("list") List<SkuPathMappingEntity> skuPathMappingEntities);
}
