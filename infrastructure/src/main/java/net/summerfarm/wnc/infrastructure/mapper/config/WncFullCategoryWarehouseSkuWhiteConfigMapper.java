package net.summerfarm.wnc.infrastructure.mapper.config;

import net.summerfarm.wnc.infrastructure.model.config.WncFullCategoryWarehouseSkuWhiteConfig;
import net.summerfarm.wnc.domain.config.param.query.WncFullCategoryWarehouseSkuWhiteConfigQueryParam;
import net.summerfarm.wnc.domain.config.entity.WncFullCategoryWarehouseSkuWhiteConfigEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025-01-06 15:31:26
 * @version 1.0
 *
 */
@Mapper
public interface WncFullCategoryWarehouseSkuWhiteConfigMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(WncFullCategoryWarehouseSkuWhiteConfig record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(WncFullCategoryWarehouseSkuWhiteConfig record);

    /**
     * 批量插入
     *
     * @param records
     * @return
     */
    int batchInsert(@Param("items") List<WncFullCategoryWarehouseSkuWhiteConfig> records);

    /**
     * @Describe: 通过主键删除
     * @param
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    WncFullCategoryWarehouseSkuWhiteConfig selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param param
     * @return
     */
    List<WncFullCategoryWarehouseSkuWhiteConfig> selectByCondition(WncFullCategoryWarehouseSkuWhiteConfigQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param param
     * @return
     */
    List<WncFullCategoryWarehouseSkuWhiteConfigEntity> getPage(WncFullCategoryWarehouseSkuWhiteConfigQueryParam param);
}

