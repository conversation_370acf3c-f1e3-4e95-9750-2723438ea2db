package net.summerfarm.wnc.infrastructure.mapper;

import net.summerfarm.wnc.common.query.warehouse.SkuWarehouseMappingQuery;
import net.summerfarm.wnc.infrastructure.model.WarehouseInventoryMapping;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * sku配送中心仓库映射表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-96 15:43:38
 */
public interface WarehouseInventoryMappingMapper extends BaseMapper<WarehouseInventoryMapping> {

    /**
     * 查询SKU库存仓映射关系
     * @param skuWarehouseMappingQueries 查询
     * @return SKU库存仓映射关系集合
     */
    List<WarehouseInventoryMapping> selectListBySkuStoreNo(@Param(value = "list") List<SkuWarehouseMappingQuery> skuWarehouseMappingQueries);
}
