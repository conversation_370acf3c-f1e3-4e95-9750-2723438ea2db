package net.summerfarm.wnc.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.common.enums.InventoryEnums;
import net.summerfarm.wnc.domain.invertory.InventoryRepository;
import net.summerfarm.wnc.infrastructure.mapper.InventoryMapper;
import net.summerfarm.wnc.infrastructure.model.Inventory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/8/22 13:38<br/>
 *
 * <AUTHOR> />
 */
@Service
@Slf4j
public class InventoryRepositoryImpl implements InventoryRepository {

    @Resource
    private InventoryMapper inventoryMapper;

    @Override
    public List<String> queryProxyListBySkus(List<String> skuList) {
        if(CollectionUtils.isEmpty(skuList)){
            return Collections.emptyList();
        }
        List<Inventory> inventories = inventoryMapper.selectList(new LambdaQueryWrapper<Inventory>()
                .in(Inventory::getSku,skuList)
                .eq(Inventory::getCreateType, InventoryEnums.CreateTypeEnum.SAAS_SELF_AND_AGENT.getType())
        );

        return inventories.stream().map(Inventory::getSku).collect(Collectors.toList());
    }
}
