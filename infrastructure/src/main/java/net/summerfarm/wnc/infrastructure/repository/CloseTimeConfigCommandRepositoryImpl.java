package net.summerfarm.wnc.infrastructure.repository;

import lombok.RequiredArgsConstructor;
import net.summerfarm.wnc.domain.closeTime.CloseTimeConfigCommandRepository;
import net.summerfarm.wnc.domain.closeTime.param.CloseTimeAreaConfigCommandParam;
import net.summerfarm.wnc.infrastructure.converter.WncCloseTimeAreaConfigConverter;
import net.summerfarm.wnc.infrastructure.mapper.WncCloseTimeAreaConfigMapper;
import net.summerfarm.wnc.infrastructure.model.WncCloseTimeAreaConfig;
import net.summerfarm.wnc.infrastructure.util.MybatisPlusUtil;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Description:截单时间配置仓库操作接口实现
 * date: 2024/3/20 13:56
 *
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class CloseTimeConfigCommandRepositoryImpl implements CloseTimeConfigCommandRepository {

    private final WncCloseTimeAreaConfigMapper wncCloseTimeAreaConfigMapper;

    @Override
    public int batchRemoveAreaConfig(List<Long> deleteIds) {
        if (CollectionUtils.isEmpty(deleteIds)){
            return 0;
        }
        return wncCloseTimeAreaConfigMapper.deleteBatchIds(deleteIds);
    }

    @Override
    public int updateAreaConfig(CloseTimeAreaConfigCommandParam commandParam) {
        if (commandParam == null){
            return 0;
        }
        WncCloseTimeAreaConfig wncCloseTimeAreaConfig = WncCloseTimeAreaConfigConverter.param2Do(commandParam);
        return wncCloseTimeAreaConfigMapper.updateById(wncCloseTimeAreaConfig);
    }

    @Override
    public void batchSaveAreaConfig(List<CloseTimeAreaConfigCommandParam> commandParams) {
        if (CollectionUtils.isEmpty(commandParams)){
            return;
        }
        List<WncCloseTimeAreaConfig> wncCloseTimeAreaConfigs = commandParams.stream().map(WncCloseTimeAreaConfigConverter::param2Do).collect(Collectors.toList());
        MybatisPlusUtil.createBatch(wncCloseTimeAreaConfigs, WncCloseTimeAreaConfig.class);
    }
}
