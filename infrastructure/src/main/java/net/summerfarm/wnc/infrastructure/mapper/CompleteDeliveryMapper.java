package net.summerfarm.wnc.infrastructure.mapper;

import net.summerfarm.wnc.common.query.alert.CompleteDeliveryTimeQueryParam;
import net.summerfarm.wnc.infrastructure.model.CompleteDelivery;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * complete_delivery完成配送提醒表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-99 15:30:32
 */
public interface CompleteDeliveryMapper extends BaseMapper<CompleteDelivery> {

    /**
     * 查询最晚配送时效
     * @param query 查询
     * @return 结果
     */
    List<String> queryListAreaLastTime(@Param("query") CompleteDeliveryTimeQueryParam query);
}
