package net.summerfarm.wnc.infrastructure.converter;

import net.summerfarm.wnc.domain.deliveryRule.entity.ContactDeliveryRuleEntity;
import net.summerfarm.wnc.infrastructure.model.WncContactDeliveryRule;

/**
 * Description: <br/>
 * date: 2023/11/13 11:43<br/>
 *
 * <AUTHOR> />
 */
public class WncContactDeliveryRuleConverter {

    public static ContactDeliveryRuleEntity do2Entity(WncContactDeliveryRule model){
        if(model == null){
            return null;
        }
        ContactDeliveryRuleEntity entity = new ContactDeliveryRuleEntity();

        entity.setId(model.getId());
        entity.setCreateTime(model.getCreateTime());
        entity.setUpdateTime(model.getUpdateTime());
        entity.setOutBusinessNo(model.getOutBusinessNo());
        entity.setSystemSource(model.getSystemSource());
        entity.setFrequentMethod(model.getFrequentMethod());
        entity.setWeekDeliveryFrequent(model.getWeekDeliveryFrequent());
        entity.setDeliveryFrequentInterval(model.getDeliveryFrequentInterval());
        entity.setBeginCalculateDate(model.getBeginCalculateDate());
        entity.setTenantId(model.getTenantId());

        return entity;
    }


    public static WncContactDeliveryRule entity2Do(ContactDeliveryRuleEntity entity){
        if(entity == null){
            return null;
        }
        WncContactDeliveryRule model = new WncContactDeliveryRule();

        model.setId(entity.getId());
        model.setCreateTime(entity.getCreateTime());
        model.setUpdateTime(entity.getUpdateTime());
        model.setOutBusinessNo(entity.getOutBusinessNo());
        model.setSystemSource(entity.getSystemSource());
        model.setFrequentMethod(entity.getFrequentMethod());
        model.setWeekDeliveryFrequent(entity.getWeekDeliveryFrequent());
        model.setDeliveryFrequentInterval(entity.getDeliveryFrequentInterval());
        model.setBeginCalculateDate(entity.getBeginCalculateDate());
        model.setTenantId(entity.getTenantId());

        return model;
    }
}
