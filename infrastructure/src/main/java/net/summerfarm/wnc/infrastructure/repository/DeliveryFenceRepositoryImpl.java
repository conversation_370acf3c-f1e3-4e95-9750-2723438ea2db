package net.summerfarm.wnc.infrastructure.repository;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.common.constants.AppConsts;
import net.summerfarm.wnc.common.enums.AdCodeMessageEnums;
import net.summerfarm.wnc.common.enums.AdCodeMsgEnums;
import net.summerfarm.wnc.common.enums.FenceEnums;
import net.summerfarm.wnc.common.query.fence.AdCodeMsgQuery;
import net.summerfarm.wnc.common.query.fence.AddressQuery;
import net.summerfarm.wnc.common.query.fence.FenceQuery;
import net.summerfarm.wnc.domain.ConfigRepository;
import net.summerfarm.wnc.domain.config.repository.ContactConfigQueryRepository;
import net.summerfarm.wnc.domain.config.entity.ContactConfigEntity;
import net.summerfarm.wnc.domain.deliveryRule.entity.ContactDeliveryRuleEntity;
import net.summerfarm.wnc.domain.fence.DeliveryFenceRepository;
import net.summerfarm.wnc.domain.fence.entity.*;
import net.summerfarm.wnc.domain.warehouse.WarehouseLogisticsCenterDomainService;
import net.summerfarm.wnc.domain.warehouse.WarehouseLogisticsCenterRepository;
import net.summerfarm.wnc.infrastructure.converter.AdCodeMsgConverter;
import net.summerfarm.wnc.infrastructure.converter.FenceConverter;
import net.summerfarm.wnc.infrastructure.converter.TmsStopDeliveryConverter;
import net.summerfarm.wnc.infrastructure.mapper.*;
import net.summerfarm.wnc.infrastructure.model.AdCodeMsg;
import net.summerfarm.wnc.infrastructure.model.Fence;
import net.summerfarm.wnc.infrastructure.model.FenceDelivery;
import net.summerfarm.wnc.infrastructure.model.TmsStopDelivery;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/3/7 17:54<br/>
 *
 * <AUTHOR> />
 */
@Service
@Slf4j
public class DeliveryFenceRepositoryImpl implements DeliveryFenceRepository {

    @Resource
    private FenceMapper fenceMapper;
    @Resource
    private AdCodeMsgMapper adCodeMsgMapper;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private OutLandContactMapper outLandContactMapper;
    @Resource
    private OutLandMerchantMapper outLandMerchantMapper;
    @Resource
    private ConfigRepository configRepository;
    @Resource
    private FenceDeliveryMapper fenceDeliveryMapper;
    @Resource
    private TmsStopDeliveryMapper tmsStopDeliveryMapper;
    @Resource
    @Lazy
    private WarehouseLogisticsCenterDomainService warehouseLogisticsCenterDomainService;
    @Resource
    private ContactConfigQueryRepository contactConfigQueryRepository;
    @Resource
    private WarehouseLogisticsCenterRepository warehouseLogisticsCenterRepository;

    @Override
    public List<FenceEntity> queryFenceByProvinceCityArea(FenceQuery fenceQuery) {
        List<AdCodeMsgQuery> adCodeMsgList = fenceQuery.getAdCodeMsgList();
        if (CollectionUtils.isEmpty(adCodeMsgList)) {
            return Collections.EMPTY_LIST;
        }
        //过滤没有区域的城市
        List<String> noAreaCityList = configRepository.queryNoAreaCity();
        adCodeMsgList.forEach(adCodeMsg -> {
            if (noAreaCityList.contains(adCodeMsg.getCity())) {
                adCodeMsg.setArea(null);
            }
        });

        List<String> provinces = adCodeMsgList.stream().filter(ad -> StringUtils.isNotBlank(ad.getProvince())).map(AdCodeMsgQuery::getProvince).collect(Collectors.toList());
        List<String> citys = adCodeMsgList.stream().filter(ad -> StringUtils.isNotBlank(ad.getCity())).map(AdCodeMsgQuery::getCity).collect(Collectors.toList());
        List<String> areas = adCodeMsgList.stream().filter(adCodeMsgQuery -> StringUtils.isNotBlank(adCodeMsgQuery.getArea()))
                .map(AdCodeMsgQuery::getArea).collect(Collectors.toList());

        List<AdCodeMsg> adCodeMsgs = adCodeMsgMapper.selectList(new LambdaQueryWrapper<AdCodeMsg>()
                .in(!CollectionUtils.isEmpty(provinces), AdCodeMsg::getProvince, provinces)
                .in(!CollectionUtils.isEmpty(citys), AdCodeMsg::getCity, citys)
                .in(!CollectionUtils.isEmpty(areas), AdCodeMsg::getArea, areas)
                .eq(AdCodeMsg::getStatus, AdCodeMsgEnums.Status.VALID.getValue())
        );
        if (CollectionUtils.isEmpty(adCodeMsgs)) {
            return Collections.EMPTY_LIST;
        }
        List<Integer> fenceIds = adCodeMsgs.stream().map(AdCodeMsg::getFenceId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(fenceIds)){
            return Collections.EMPTY_LIST;
        }
        List<Fence> fences = fenceMapper.selectList(new LambdaQueryWrapper<Fence>().in(Fence::getId, fenceIds));
        if(CollectionUtils.isEmpty(fences)){
            return Collections.EMPTY_LIST;
        }
        List<FenceEntity> fenceEntities = fences.stream().map(FenceConverter::fence2Entity).collect(Collectors.toList());
        List<AdCodeMsgEntity> adCodeMsgEntities = adCodeMsgs.stream().map(AdCodeMsgConverter::adCodeMsg2Entity).collect(Collectors.toList());
        Map<Integer, List<AdCodeMsgEntity>> fenceIdAdCodeMsgListMap = adCodeMsgEntities.stream().collect(Collectors.groupingBy(AdCodeMsgEntity::getFenceId));
        fenceEntities.forEach(fence ->{
            if(fenceIdAdCodeMsgListMap.get(fence.getId()) != null){
                fence.setAdCodeMsgEntities(fenceIdAdCodeMsgListMap.get(fence.getId()));
            }
        });
        return fenceEntities;
    }

    @Override
    public FenceEntity queryOneFenceByProvinceCityArea(FenceQuery fenceQuery) {
        //过滤没有区域的城市
        List<String> noAreaCityList = configRepository.queryNoAreaCity();
        if(noAreaCityList.contains(fenceQuery.getCity())) {
            fenceQuery.setArea(null);
        }
        AdCodeMsg adCodeMsg = adCodeMsgMapper.selectOne(new LambdaQueryWrapper<AdCodeMsg>()
                .eq(StringUtils.isNotBlank(fenceQuery.getProvince()), AdCodeMsg::getProvince, fenceQuery.getProvince())
                .eq(StringUtils.isNotBlank(fenceQuery.getCity()), AdCodeMsg::getCity, fenceQuery.getCity())
                .eq(StringUtils.isNotBlank(fenceQuery.getArea()), AdCodeMsg::getArea, fenceQuery.getArea())
                .eq(AdCodeMsg::getStatus, AdCodeMsgEnums.Status.VALID.getValue())
                .last("limit 1")
        );
        if(adCodeMsg == null){
            return null;
        }
        if(adCodeMsg.getFenceId() == null){
            return null;
        }
        Fence fence = fenceMapper.selectById(adCodeMsg.getFenceId());
        if(fence == null){
            return null;
        }

        FenceEntity fenceEntity = FenceConverter.fence2Entity(fence);
        fenceEntity.setAdCodeMsgEntities(Arrays.asList(AdCodeMsgConverter.adCodeMsg2Entity(adCodeMsg)));
        return fenceEntity;
    }

    

    @Override
    public List<FenceEntity> queryFenceByStoreNoList(List<Integer> storeNoList) {
        if(CollectionUtils.isEmpty(storeNoList)){
            return null;
        }
        List<Fence> fences = fenceMapper.selectList(new LambdaQueryWrapper<Fence>()
                .in(Fence::getStoreNo, storeNoList)
                .eq(Fence::getStatus, FenceEnums.Status.VALID.getValue())
        );
        if(CollectionUtils.isEmpty(fences)){
            return null;
        }
        List<Integer> fenceIds = fences.stream().map(Fence::getId).collect(Collectors.toList());

        List<AdCodeMsg> adCodeMsgs = adCodeMsgMapper.selectList(new LambdaQueryWrapper<AdCodeMsg>()
                .in(AdCodeMsg::getFenceId, fenceIds)
                .eq(AdCodeMsg::getStatus, AdCodeMsgEnums.Status.VALID.getValue())
        );

        List<AdCodeMsgEntity> adCodeMsgEntities = adCodeMsgs.stream().map(AdCodeMsgConverter::adCodeMsg2Entity).collect(Collectors.toList());
        Map<Integer, List<AdCodeMsgEntity>> fenceIdAdCodeMsgListMap = adCodeMsgEntities.stream().collect(Collectors.groupingBy(AdCodeMsgEntity::getFenceId));

        List<FenceEntity> fenceEntities = fences.stream().map(FenceConverter::fence2Entity).collect(Collectors.toList());
        fenceEntities.forEach(fenceEntity -> {
            fenceEntity.setAdCodeMsgEntities(fenceIdAdCodeMsgListMap.get(fenceEntity.getId()));
        });

        return fenceEntities;
    }

    @Override
    public List<FenceEntity> queryListFenceByCity(String city) {
        List<AdCodeMsg> adCodeMsgs = adCodeMsgMapper.selectList(new LambdaQueryWrapper<AdCodeMsg>()
                .eq( AdCodeMsg::getCity, city)
                .eq(AdCodeMsg::getStatus, AdCodeMsgEnums.Status.VALID.getValue())
        );
        if(CollectionUtils.isEmpty(adCodeMsgs)){
            return null;
        }
        adCodeMsgs = adCodeMsgs.stream().filter(adCodeMsg -> adCodeMsg.getFenceId() != null).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(adCodeMsgs)){
            return null;
        }
        List<Fence> fences = fenceMapper.selectList(new LambdaQueryWrapper<Fence>()
                .eq(Fence::getStatus, FenceEnums.Status.VALID.getValue())
                .in(Fence::getId, adCodeMsgs.stream().map(AdCodeMsg::getFenceId).collect(Collectors.toList()))
        );

        if (CollectionUtils.isEmpty(fences)) {
            return null;
        }
        return fences.stream().map(FenceConverter::fence2Entity).collect(Collectors.toList());
    }

    @Override
    public List<FenceEntity> queryListFenceByAreaNo(Collection<Integer> areaNoList) {
        if (CollectionUtils.isEmpty(areaNoList)) {
            return null;
        }
        List<Fence> fences = fenceMapper.selectList(new LambdaQueryWrapper<Fence>().in(Fence::getAreaNo, areaNoList)
                .eq(Fence::getStatus, FenceEnums.Status.VALID.getValue()));
        return fences.stream().map(FenceConverter::fence2Entity).collect(Collectors.toList());
    }


    @Override
    public DeliveryFenceEntity queryFrequentRule(OutLandContactEntity outLandContactEntity) {
        DeliveryFenceEntity deliveryFenceEntity = new DeliveryFenceEntity();
        //判断客户是否有指定城配仓
        ContactConfigEntity contactConfigEntity = outLandContactEntity.getContactConfigEntity();
        ContactDeliveryRuleEntity contactDeliveryRuleEntity = outLandContactEntity.getContactDeliveryRuleEntity();
        //指定了城配仓和配送周期
        if(contactConfigEntity != null && contactDeliveryRuleEntity != null){
            //将客户的配送规则赋值给配送规则实体
            FenceDeliveryEntity fenceDeliveryEntity = new FenceDeliveryEntity();
            fenceDeliveryEntity.setFrequentMethod(contactDeliveryRuleEntity.getFrequentMethod());
            fenceDeliveryEntity.setDeliveryFrequent(contactDeliveryRuleEntity.getWeekDeliveryFrequent());
            fenceDeliveryEntity.setDeliveryFrequentInterval(contactDeliveryRuleEntity.getDeliveryFrequentInterval());
            fenceDeliveryEntity.setBeginCalculateDate(contactDeliveryRuleEntity.getBeginCalculateDate());
            fenceDeliveryEntity.setNextDeliveryDate(contactDeliveryRuleEntity.getBeginCalculateDate());
            deliveryFenceEntity.setFenceDeliveryEntity(fenceDeliveryEntity);
            StopDeliveryEntity stopDeliveryEntity = this.queryStopDelivery(contactConfigEntity.getStoreNo());
            deliveryFenceEntity.setStopDeliveryEntitys(stopDeliveryEntity != null ? Collections.singletonList(stopDeliveryEntity) : Collections.emptyList());
            deliveryFenceEntity.setWarehouseLogisticsCenterEntity(warehouseLogisticsCenterRepository.queryByUk(contactConfigEntity.getStoreNo()));
            return deliveryFenceEntity;
        }
        //查询围栏
        Fence fence = this.queryFence(outLandContactEntity.getCity(), outLandContactEntity.getArea());
        if (fence != null && Objects.isNull(outLandContactEntity.getStoreNo())) {
            outLandContactEntity.setStoreNo(fence.getStoreNo());
        }
        deliveryFenceEntity.setFenceEntity(FenceConverter.fence2Entity(fence));
        //查询围栏配送周期
        deliveryFenceEntity.setFenceDeliveryEntity(FenceConverter.fenceDelivery2Entity(queryFenceDelivery(fence)));
        //停运
        StopDeliveryEntity stopDeliveryEntity = null;
        if(contactConfigEntity != null){
            stopDeliveryEntity = this.queryStopDelivery(contactConfigEntity.getStoreNo());
            deliveryFenceEntity.setWarehouseLogisticsCenterEntity(warehouseLogisticsCenterRepository.queryByUk(contactConfigEntity.getStoreNo()));
        }else{
            stopDeliveryEntity = this.queryStopDelivery(fence);
            deliveryFenceEntity.setWarehouseLogisticsCenterEntity(fence != null ? warehouseLogisticsCenterRepository.queryByUk(fence.getStoreNo()) : null);
        }
        deliveryFenceEntity.setStopDeliveryEntitys(stopDeliveryEntity != null ? Collections.singletonList(stopDeliveryEntity) : Collections.emptyList());
        return deliveryFenceEntity;
    }

    @Override
    public LocalTime queryCloseTime(String city, String area) {
        //查询围栏
        Fence fence = queryFence(city, area);
        if (Objects.isNull(fence)) {
            return null;
        }
        //查询城配仓截单时间
        return queryStoreCloseTime(fence.getStoreNo());
    }

    @Override
    public LocalTime queryStoreCloseTime(Integer storeNo) {
        if (storeNo == null) {
            return null;
        }
        return warehouseLogisticsCenterDomainService.selectCloseTime(storeNo);
    }


    private Fence queryFence(String city, String area) {
        AdCodeMsg adCodeMsg = adCodeMsgMapper.selectOne(new LambdaQueryWrapper<AdCodeMsg>()
                .eq(AdCodeMsg::getCity, city)
                .eq(StringUtils.isNotBlank(area), AdCodeMsg::getArea, area)
                .eq(AdCodeMsg::getStatus, AdCodeMessageEnums.Status.NORMAL.getValue())
                .last("limit 1")
        );
        if (adCodeMsg == null || adCodeMsg.getFenceId() == null) {
            return null;
        }
        return fenceMapper.selectById(adCodeMsg.getFenceId());
    }

    @Override
    public FenceEntity queryFenceEntity(@NonNull String city, String area) {
        if (configRepository.queryNoAreaCity().contains(city)) {
            area = null;
        }
        AdCodeMsg adCodeMsg = adCodeMsgMapper.selectOne(new LambdaQueryWrapper<AdCodeMsg>()
                .eq(AdCodeMsg::getCity, city)
                .eq(StringUtils.isNotBlank(area), AdCodeMsg::getArea, area)
                .eq(AdCodeMsg::getStatus, AdCodeMessageEnums.Status.NORMAL.getValue())
                .last("limit 1")
        );
        if (adCodeMsg == null || adCodeMsg.getFenceId() == null) {
            return null;
        }
        return FenceConverter.fence2Entity(fenceMapper.selectById(adCodeMsg.getFenceId()));
    }

    @Override
    public List<FenceEntity> queryFenceByCityList(List<String> cityList) {
        if (CollectionUtils.isEmpty(cityList)){
            return Collections.emptyList();
        }
        List<AdCodeMsg> adCodeMsgList = adCodeMsgMapper.selectList(new LambdaQueryWrapper<AdCodeMsg>()
                .in(AdCodeMsg::getCity, cityList)
                .eq(AdCodeMsg::getStatus, AdCodeMessageEnums.Status.NORMAL.getValue()));
        if (CollectionUtils.isEmpty(adCodeMsgList)){
            return Collections.emptyList();
        }
        Map<Integer/*FenceId*/, String/*City*/> fenceIdMap = adCodeMsgList.stream().collect(Collectors.toMap(AdCodeMsg::getFenceId, AdCodeMsg::getCity, (oldData, newData) -> oldData));
        List<Integer> fenceIds = adCodeMsgList.stream().map(AdCodeMsg::getFenceId).collect(Collectors.toList());
        List<Fence> fences = fenceMapper.selectList(new LambdaQueryWrapper<Fence>()
                .in(Fence::getId, fenceIds)
                .eq(Fence::getStatus, FenceEnums.Status.VALID.getValue()));
        List<FenceEntity> fenceEntities = fences.stream().map(FenceConverter::fence2Entity).collect(Collectors.toList());
        fenceEntities.forEach(e -> e.setCityName(fenceIdMap.get(e.getId())));
        return fenceEntities;
    }

    @Override
    public List<AddressBelongFenceEntity> queryAddressBelongFence(List<AddressQuery> addressQueryList) {
        if (CollectionUtils.isEmpty(addressQueryList)){
            return Collections.emptyList();
        }
        //查询没有区域的城市
        List<String> noAreaCityList = Optional.ofNullable(configRepository.queryNoAreaCity()).orElse(Lists.newArrayList());
        List<AdCodeMsg> allFenceAreas = queryBatchAdCodeMsgListByCityArea(addressQueryList, noAreaCityList);

        if (CollectionUtils.isEmpty(allFenceAreas)){
            return Collections.emptyList();
        }
        Set<Integer> fenceIds = allFenceAreas.stream().map(AdCodeMsg::getFenceId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(fenceIds)){
            return Collections.emptyList();
        }
        List<Fence> fences = fenceMapper.selectBatchIds(fenceIds);
        if (CollectionUtils.isEmpty(fenceIds)){
            return Collections.emptyList();
        }

        Map<String/*city#area*/, AdCodeMsg> adCodeMsgMap = allFenceAreas.stream()
                .collect(Collectors.toMap(e -> e.getCity() + AppConsts.Symbol.HASH_TAG + (noAreaCityList.contains(e.getCity()) ? null : e.getArea()), Function.identity(), (oldData, newData) -> newData));
        Map<Integer/*fenceId*/, Fence> fenceMap = fences.stream().collect(Collectors.toMap(Fence::getId, Function.identity(), (oldData, newData) -> newData));

        List<AddressBelongFenceEntity> result = Lists.newArrayList();
        for (AddressQuery addressQuery : addressQueryList) {
            AddressBelongFenceEntity addressBelongFenceEntity = new AddressBelongFenceEntity();
            addressBelongFenceEntity.setCity(addressQuery.getCity());
            addressBelongFenceEntity.setArea(addressQuery.getArea());
            result.add(addressBelongFenceEntity);
            String realArea = noAreaCityList.contains(addressQuery.getCity()) ? null : addressQuery.getArea();
            String cityAreaStr = addressQuery.getCity() + AppConsts.Symbol.HASH_TAG + realArea;
            AdCodeMsg adCodeMsg = adCodeMsgMap.get(cityAreaStr);
            if (adCodeMsg == null){
                continue;
            }
            Fence fence = fenceMap.get(adCodeMsg.getFenceId());
            if (fence == null){
                continue;
            }
            FenceEntity fenceEntity = FenceConverter.fence2Entity(fence);
            fenceEntity.setAdCodeMsgEntities(Lists.newArrayList(AdCodeMsgConverter.adCodeMsg2Entity(adCodeMsg)));
            addressBelongFenceEntity.setFenceEntity(fenceEntity);
        }
        return result;
    }

    /**
     * 批量查询区域信息
     * @param addressQueryList 城市区域信息
     * @param noAreaCityList 没有区域的城市
     * @return 区域信息
     */
    private List<AdCodeMsg> queryBatchAdCodeMsgListByCityArea(List<AddressQuery> addressQueryList, List<String> noAreaCityList) {
        //处理查询入参
        List<AddressQuery> realQueries = addressQueryList.stream().map(e -> {
            AddressQuery addressQuery = new AddressQuery();
            addressQuery.setCity(e.getCity());
            addressQuery.setArea(e.getArea());
            if(noAreaCityList.contains(e.getCity())) {
                addressQuery.setArea(null);
            }
            return addressQuery;
        }).collect(Collectors.toList());
        List<String> cityList = realQueries.stream().filter(e -> StrUtil.isNotBlank(e.getCity()) && StrUtil.isNotBlank(e.getArea())).map(AddressQuery::getCity).distinct().collect(Collectors.toList());
        List<String> cityWithNoAreaList = realQueries.stream().filter(e -> StrUtil.isNotBlank(e.getCity()) && StrUtil.isBlank(e.getArea())).map(AddressQuery::getCity).distinct().collect(Collectors.toList());
        List<String> areaList = realQueries.stream().map(AddressQuery::getArea).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        //查询区域信息
        List<AdCodeMsg> allFenceAreas = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(cityList)){
            List<AdCodeMsg> fenceAreas = adCodeMsgMapper.selectList(new LambdaQueryWrapper<AdCodeMsg>()
                    .in(AdCodeMsg::getCity, cityList)
                    .in(AdCodeMsg::getArea, areaList)
                    .in(AdCodeMsg::getStatus, AdCodeMsgEnums.Status.getUsableStatus()));
            allFenceAreas.addAll(fenceAreas);
        }
        //查询区域信息
        if (CollectionUtils.isNotEmpty(cityWithNoAreaList)){
            List<AdCodeMsg> fenceAreas = adCodeMsgMapper.selectList(new LambdaQueryWrapper<AdCodeMsg>()
                    .in(AdCodeMsg::getCity, cityWithNoAreaList)
                    .in(AdCodeMsg::getStatus, AdCodeMsgEnums.Status.getUsableStatus()));
            allFenceAreas.addAll(fenceAreas);
        }
        return allFenceAreas;
    }

    /**
     * 查询围栏配送周期
     *
     * @param fence 围栏
     */
    private FenceDelivery queryFenceDelivery(Fence fence) {
        if (fence == null || fence.getStatus() != 0) {
            return null;
        }
        return fenceDeliveryMapper.selectOne(new LambdaQueryWrapper<FenceDelivery>()
                .eq(FenceDelivery::getFenceId, fence.getId())
                .eq(FenceDelivery::getDeleteFlag, 0)
                .last("limit 1")
        );
    }

    /**
     * 查询停运规则
     *
     * @param fence 围栏
     * @return 停运对象
     */
    private StopDeliveryEntity queryStopDelivery(Fence fence) {
        if (fence == null || fence.getStoreNo() == null) {
            return null;
        }
        return this.queryStopDelivery(fence.getStoreNo());
    }

    /**
     * 查询停运规则
     *
     * @param storeNo 城配仓编号
     * @return 停运对象
     */
    private StopDeliveryEntity queryStopDelivery(Integer storeNo) {
        if (storeNo == null) {
            return null;
        }
        TmsStopDelivery tmsStopDelivery = tmsStopDeliveryMapper.selectOne(new LambdaQueryWrapper<TmsStopDelivery>()
                .eq(TmsStopDelivery::getStoreNo, storeNo)
                .eq(TmsStopDelivery::getDeleteFlag, 0)
                .last("limit 1")
        );

        return TmsStopDeliveryConverter.do2Entity(tmsStopDelivery);
    }

    @Override
    public List<FenceEntity> queryBatchAddressBelongFences(List<AddressQuery> addressQueryList) {
        if (CollectionUtils.isEmpty(addressQueryList)) {
            return Collections.emptyList();
        }

        // 查询没有区域的城市，使用Set提高查找性能
        Set<String> noAreaCitySet = Optional.ofNullable(configRepository.queryNoAreaCity())
                .map(HashSet::new)
                .orElse(new HashSet<>());

        List<AdCodeMsg> allFenceAreas = this.queryBatchAdCodeMsgListByCityArea(addressQueryList, new ArrayList<>(noAreaCitySet));
        if (CollectionUtils.isEmpty(allFenceAreas)) {
            return Collections.emptyList();
        }

        // 构建查询条件的键集合，用于快速匹配
        Set<String> queryKeys = addressQueryList.stream()
                .map(query -> buildAreaKey(query.getCity(),
                        noAreaCitySet.contains(query.getCity()) ? null : query.getArea()))
                .collect(Collectors.toSet());

        // 过滤并分组：同时完成过滤和按围栏ID分组
        Map<Integer, List<AdCodeMsg>> fenceAreaMap = allFenceAreas.stream()
                .filter(area -> queryKeys.contains(buildAreaKey(area.getCity(), area.getArea())))
                .filter(area -> Objects.nonNull(area.getFenceId()))
                .collect(Collectors.groupingBy(AdCodeMsg::getFenceId));

        if (fenceAreaMap.isEmpty()) {
            return Collections.emptyList();
        }

        // 批量查询围栏信息
        List<Fence> fences = fenceMapper.selectBatchIds(fenceAreaMap.keySet());
        if (CollectionUtils.isEmpty(fences)) {
            return Collections.emptyList();
        }

        // 构建结果：直接将围栏和对应的区域组合
        return fences.stream()
                .map(fence -> {
                    FenceEntity fenceEntity = FenceConverter.fence2Entity(fence);
                    List<AdCodeMsgEntity> adCodeMsgEntities = fenceAreaMap.get(fence.getId()).stream()
                            .map(AdCodeMsgConverter::adCodeMsg2Entity)
                            .collect(Collectors.toList());
                    fenceEntity.setAdCodeMsgEntities(adCodeMsgEntities);
                    return fenceEntity;
                })
                .collect(Collectors.toList());
    }

    /**
     * 构建区域键
     * @param city 城市
     * @param area 区域
     * @return 区域键
     */
    private String buildAreaKey(String city, String area) {
        return city + AppConsts.Symbol.HASH_TAG + (area != null ? area : "");
    }
}
