package net.summerfarm.wnc.infrastructure.mapper;

import net.summerfarm.wnc.infrastructure.model.WarehouseLogisticsMapping;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 仓储物流中心对应 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-97 16:09:57
 */
public interface WarehouseLogisticsMappingMapper extends BaseMapper<WarehouseLogisticsMapping> {
    /**
     * 查询城配仓有效的对应的库存仓
     * @param storeNoList 城配仓编号
     * @return 结果
     */
    List<WarehouseLogisticsMapping> queryValidWarehouseMapping(@Param("storeNoList") List<Integer> storeNoList);
}
