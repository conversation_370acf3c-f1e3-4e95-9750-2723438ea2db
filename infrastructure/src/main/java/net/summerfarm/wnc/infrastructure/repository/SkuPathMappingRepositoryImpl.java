package net.summerfarm.wnc.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.summerfarm.wnc.domain.path.SkuPathMappingRepository;
import net.summerfarm.wnc.domain.path.entity.PathConfigEntity;
import net.summerfarm.wnc.domain.path.entity.SkuPathMappingEntity;
import net.summerfarm.wnc.infrastructure.converter.WncPathConfigConverter;
import net.summerfarm.wnc.infrastructure.converter.WncSkuPathMappingConverter;
import net.summerfarm.wnc.infrastructure.mapper.WncPathConfigMapper;
import net.summerfarm.wnc.infrastructure.mapper.WncSkuPathMappingMapper;
import net.summerfarm.wnc.infrastructure.model.WncPathConfig;
import net.summerfarm.wnc.infrastructure.model.WncSkuPathMapping;
import net.summerfarm.wnc.infrastructure.util.MybatisPlusUtil;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * Description: 路线sku配置<br/>
 * date: 2023/11/29 10:38<br/>
 *
 * <AUTHOR> />
 */
@Service
public class SkuPathMappingRepositoryImpl implements SkuPathMappingRepository {

    @Resource
    private WncSkuPathMappingMapper wncSkuPathMappingMapper;

    @Override
    public List<SkuPathMappingEntity> queryList(List<String> skuList, List<Integer> warehouseList) {
        if(CollectionUtils.isEmpty(skuList) || CollectionUtils.isEmpty(warehouseList)){
            throw new BizException("sku集合和仓库列表不能为空");
        }

        List<WncSkuPathMapping> wncSkuPathMappings = wncSkuPathMappingMapper.selectList(new LambdaQueryWrapper<WncSkuPathMapping>()
                .in(WncSkuPathMapping::getSku, skuList)
                .in(WncSkuPathMapping::getWarehouseNo, warehouseList)
        );

        if(CollectionUtils.isEmpty(wncSkuPathMappings)){
            return Collections.emptyList();
        }

        return wncSkuPathMappings.stream().map(WncSkuPathMappingConverter::model2Entity).collect(Collectors.toList());
    }

    @Override
    public void batchSave(List<SkuPathMappingEntity> skuPathMappingEntities) {
        if(CollectionUtils.isEmpty(skuPathMappingEntities)){
            return;
        }
        List<WncSkuPathMapping> wncSkuPathMappings = skuPathMappingEntities.stream().map(WncSkuPathMappingConverter::entity2Model).collect(Collectors.toList());
        MybatisPlusUtil.createBatch(wncSkuPathMappings, WncSkuPathMapping.class);
    }

    @Override
    public void batchUpdate(List<SkuPathMappingEntity> skuPathMappingEntities) {
        if(CollectionUtils.isEmpty(skuPathMappingEntities)){
            return;
        }
        List<WncSkuPathMapping> wncSkuPathMappings = skuPathMappingEntities.stream().map(WncSkuPathMappingConverter::entity2Model).collect(Collectors.toList());
        MybatisPlusUtil.updateBatch(wncSkuPathMappings, WncSkuPathMapping.class);
    }

    @Override
    public List<SkuPathMappingEntity> queryByPathId(Long id) {
        if(id == null){
            throw new BizException("路线ID不能为空");
        }

        List<WncSkuPathMapping> wncSkuPathMappings = wncSkuPathMappingMapper.selectList(new LambdaQueryWrapper<WncSkuPathMapping>()
               .eq(WncSkuPathMapping::getPathId, id)
        );
        return wncSkuPathMappings.stream().map(WncSkuPathMappingConverter::model2Entity).collect(Collectors.toList());
    }

    @Override
    public List<SkuPathMappingEntity> queryListBySkusPathIdList(List<String> skuList, List<Long> pathIdList) {
        if(CollectionUtils.isEmpty(skuList) || CollectionUtils.isEmpty(pathIdList)){
            throw new BizException("sku集合和路线信息不能为空");
        }
        List<WncSkuPathMapping> wncSkuPathMappings = wncSkuPathMappingMapper.selectList(new LambdaQueryWrapper<WncSkuPathMapping>()
                .in(WncSkuPathMapping::getSku, skuList)
                .in(WncSkuPathMapping::getPathId, pathIdList)
        );

        return wncSkuPathMappings.stream().map(WncSkuPathMappingConverter::model2Entity).collect(Collectors.toList());
    }

    @Override
    public void batchDeleteByWarehouseNosSkus(List<SkuPathMappingEntity> skuPathMappingEntities) {
        if(CollectionUtils.isEmpty(skuPathMappingEntities)){
            return;
        }
        wncSkuPathMappingMapper.batchDeleteByWarehouseNosSkus(skuPathMappingEntities);
    }
}
