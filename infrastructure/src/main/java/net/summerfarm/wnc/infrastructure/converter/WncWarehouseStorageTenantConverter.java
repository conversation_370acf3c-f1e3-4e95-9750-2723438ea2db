package net.summerfarm.wnc.infrastructure.converter;

import lombok.Data;
import net.summerfarm.wnc.domain.warehouse.entity.WncWarehouseStorageTenantEntity;
import net.summerfarm.wnc.infrastructure.model.WncWarehouseStorageTenant;

/**
 * Description: <br/>
 * date: 2023/4/6 11:40<br/>
 *
 * <AUTHOR> />
 */
@Data
public class WncWarehouseStorageTenantConverter {

    public static WncWarehouseStorageTenantEntity model2Entity(WncWarehouseStorageTenant wncWarehouseStorageTenant){
        if(wncWarehouseStorageTenant == null){
            return null;
        }

        WncWarehouseStorageTenantEntity rsWarehouseStorageTenantEntity = new WncWarehouseStorageTenantEntity();
        rsWarehouseStorageTenantEntity.setId(wncWarehouseStorageTenant.getId());
        rsWarehouseStorageTenantEntity.setCreateTime(wncWarehouseStorageTenant.getCreateTime());
        rsWarehouseStorageTenantEntity.setUpdateTime(wncWarehouseStorageTenant.getUpdateTime());
        rsWarehouseStorageTenantEntity.setWarehouseNo(wncWarehouseStorageTenant.getWarehouseNo());
        rsWarehouseStorageTenantEntity.setTenantId(wncWarehouseStorageTenant.getTenantId());

        return rsWarehouseStorageTenantEntity;
    }

    public static WncWarehouseStorageTenant entity2Model(WncWarehouseStorageTenantEntity rsWarehouseStorageTenantEntity){
        if(rsWarehouseStorageTenantEntity == null){
            return null;
        }

        WncWarehouseStorageTenant wncWarehouseStorageTenant = new WncWarehouseStorageTenant();
        wncWarehouseStorageTenant.setId(rsWarehouseStorageTenantEntity.getId());
        wncWarehouseStorageTenant.setCreateTime(rsWarehouseStorageTenantEntity.getCreateTime());
        wncWarehouseStorageTenant.setUpdateTime(rsWarehouseStorageTenantEntity.getUpdateTime());
        wncWarehouseStorageTenant.setWarehouseNo(rsWarehouseStorageTenantEntity.getWarehouseNo());
        wncWarehouseStorageTenant.setTenantId(rsWarehouseStorageTenantEntity.getTenantId());

        return wncWarehouseStorageTenant;
    }
}
