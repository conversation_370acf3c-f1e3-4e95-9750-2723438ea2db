package net.summerfarm.wnc.infrastructure.converter;

import net.summerfarm.wnc.domain.fence.entity.OutLandMerchantEntity;
import net.summerfarm.wnc.infrastructure.model.OutLandMerchant;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2023-06-20
 **/
public class OutLandMerchantConverter {

	/**
	 * do2Entity
	 *
	 * @param outLandMerchant
	 * @return
	 */
	public static OutLandMerchantEntity do2Entity(OutLandMerchant outLandMerchant) {
		if (Objects.isNull(outLandMerchant)) {
			return null;
		}
		OutLandMerchantEntity entity = new OutLandMerchantEntity();
		entity.setMId(outLandMerchant.getMId());
		entity.setRoleId(outLandMerchant.getRoleId());
		entity.setMname(outLandMerchant.getMname());
		entity.setMcontact(outLandMerchant.getMcontact());
		entity.setOpenid(outLandMerchant.getOpenid());
		entity.setPhone(outLandMerchant.getPhone());
		entity.setIslock(outLandMerchant.getIslock());
		entity.setRankId(outLandMerchant.getRankId());
		entity.setRegisterTime(outLandMerchant.getRegisterTime());
		entity.setLoginTime(outLandMerchant.getLoginTime());
		entity.setInvitecode(outLandMerchant.getInvitecode());
		entity.setChannelCode(outLandMerchant.getChannelCode());
		entity.setInviterChannelCode(outLandMerchant.getInviterChannelCode());
		entity.setAuditTime(outLandMerchant.getAuditTime());
		entity.setAuditUser(outLandMerchant.getAuditUser());
		entity.setBusinessLicense(outLandMerchant.getBusinessLicense());
		entity.setProvince(outLandMerchant.getProvince());
		entity.setCity(outLandMerchant.getCity());
		entity.setArea(outLandMerchant.getArea());
		entity.setAddress(outLandMerchant.getAddress());
		entity.setPoiNote(outLandMerchant.getPoiNote());
		entity.setRemark(outLandMerchant.getRemark());
		entity.setShopSign(outLandMerchant.getShopSign());
		entity.setOtherProof(outLandMerchant.getOtherProof());
		entity.setLastOrderTime(outLandMerchant.getLastOrderTime());
		entity.setAreaNo(outLandMerchant.getAreaNo());
		entity.setSize(outLandMerchant.getSize());
		entity.setType(outLandMerchant.getType());
		entity.setTradeArea(outLandMerchant.getTradeArea());
		entity.setTradeGroup(outLandMerchant.getTradeGroup());
		entity.setUnionid(outLandMerchant.getUnionid());
		entity.setMpOpenid(outLandMerchant.getMpOpenid());
		entity.setAdminId(outLandMerchant.getAdminId());
		entity.setDirect(outLandMerchant.getDirect());
		entity.setServer(outLandMerchant.getServer());
		entity.setPopView(outLandMerchant.getPopView());
		entity.setMemberIntegral(outLandMerchant.getMemberIntegral());
		entity.setGrade(outLandMerchant.getGrade());
		entity.setSkuShow(outLandMerchant.getSkuShow());
		entity.setRechargeAmount(outLandMerchant.getRechargeAmount());
		entity.setCashAmount(outLandMerchant.getCashAmount());
		entity.setCashUpdateTime(outLandMerchant.getCashUpdateTime());
		entity.setShowPrice(outLandMerchant.getShowPrice());
		entity.setMergeAdmin(outLandMerchant.getMergeAdmin());
		entity.setMergeTime(outLandMerchant.getMergeTime());
		entity.setFirstLoginPop(outLandMerchant.getFirstLoginPop());
		entity.setChangePop(outLandMerchant.getChangePop());
		entity.setPullBlackRemark(outLandMerchant.getPullBlackRemark());
		entity.setPullBlackOperator(outLandMerchant.getPullBlackOperator());
		entity.setHouseNumber(outLandMerchant.getHouseNumber());
		entity.setCompanyBrand(outLandMerchant.getCompanyBrand());
		entity.setCluePool(outLandMerchant.getCluePool());
		entity.setMerchantType(outLandMerchant.getMerchantType());
		entity.setEnterpriseScale(outLandMerchant.getEnterpriseScale());
		entity.setUpdateTime(outLandMerchant.getUpdateTime());
		entity.setExamineType(outLandMerchant.getExamineType());
		entity.setDisplayButton(outLandMerchant.getDisplayButton());
		entity.setOperateStatus(outLandMerchant.getOperateStatus());
		entity.setUpdater(outLandMerchant.getUpdater());
		return entity;
	}
}
