package net.summerfarm.wnc.infrastructure.mapper;

import net.summerfarm.wnc.common.query.changeTask.FenceChangeTaskOrderPageQuery;
import net.summerfarm.wnc.infrastructure.model.WncChangeFenceDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <p>
 * 切仓任务明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-236 10:46:18
 */
public interface WncChangeFenceDetailMapper extends BaseMapper<WncChangeFenceDetail> {

    /**
     * 分页查询切仓任务订单列表
     * @param fenceChangeTaskOrderPageQuery 查询
     * @return 结果
     */
    List<WncChangeFenceDetail> queryPage(FenceChangeTaskOrderPageQuery fenceChangeTaskOrderPageQuery);

    /**
     * 查询可重试切仓任务订单明细
     * @return 切仓任务订单明细集合
     */
    List<WncChangeFenceDetail> selectRetryableTaskDetails();
}
