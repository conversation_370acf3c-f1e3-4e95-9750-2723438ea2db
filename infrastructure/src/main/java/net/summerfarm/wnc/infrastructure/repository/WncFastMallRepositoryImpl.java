package net.summerfarm.wnc.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.summerfarm.wnc.domain.fastmall.WncFastMallRepository;
import net.summerfarm.wnc.domain.fastmall.entity.WncFastMallEntity;
import net.summerfarm.wnc.infrastructure.converter.WncFastMallConverter;
import net.summerfarm.wnc.infrastructure.mapper.WncFastMallMapper;
import net.summerfarm.wnc.infrastructure.model.WncFastMall;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/4/11 16:33<br/>
 *
 * <AUTHOR> />
 */
@Service
public class WncFastMallRepositoryImpl implements WncFastMallRepository {

    @Resource
    private WncFastMallMapper wncFastMallMapper;

    @Override
    public List<WncFastMallEntity> queryFastMallList() {
        List<WncFastMall> wncFastMalls = wncFastMallMapper.selectList(new LambdaQueryWrapper<WncFastMall>()
                .orderByAsc(WncFastMall::getName)
        );

        return wncFastMalls.stream().map(WncFastMallConverter::model2Entity)
                .collect(Collectors.toList());
    }
}
