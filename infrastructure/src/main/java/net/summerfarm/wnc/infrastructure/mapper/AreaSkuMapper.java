package net.summerfarm.wnc.infrastructure.mapper;

import net.summerfarm.wnc.domain.fence.entity.SkuManyWarehouseEntity;
import net.summerfarm.wnc.infrastructure.model.AreaSku;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-214 16:36:45
 */
public interface AreaSkuMapper extends BaseMapper<AreaSku> {

    /**
     * 根据运营区域编号和城配仓编号查询仓库集合
     * @param areaNo 运营区域编号
     * @param storeNos 城配仓编号集合
     * @return 仓库编号
     */
    List<Integer> queryWareNoByAreaNoStoreNo(@Param("areaNo") Integer areaNo,@Param("storeNos") List<Integer> storeNos);

    /**
     * 查询同一个运营区域存在多个仓库的sku
     * @param areaNo 运营区域编号
     * @param warehouseNos 仓库编号集合
     * @return 多个仓库的sku集合
     */
    List<SkuManyWarehouseEntity> querySkuManyWarehouse(@Param("areaNo")Integer areaNo,@Param("warehouseNos") List<Integer> warehouseNos);
}
