package net.summerfarm.wnc.infrastructure.converter.config;

import net.summerfarm.wnc.infrastructure.model.config.WncStockingTimeConfig;
import net.summerfarm.wnc.domain.config.entity.WncStockingTimeConfigEntity;
import net.summerfarm.wnc.domain.config.param.command.WncStockingTimeConfigCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2025-04-15 14:51:44
 * @version 1.0
 *
 */
public class WncStockingTimeConfigConverter {

    private WncStockingTimeConfigConverter() {
        // 无需实现
    }




    public static List<WncStockingTimeConfigEntity> toWncStockingTimeConfigEntityList(List<WncStockingTimeConfig> wncStockingTimeConfigList) {
        if (wncStockingTimeConfigList == null) {
            return Collections.emptyList();
        }
        List<WncStockingTimeConfigEntity> wncStockingTimeConfigEntityList = new ArrayList<>();
        for (WncStockingTimeConfig wncStockingTimeConfig : wncStockingTimeConfigList) {
            wncStockingTimeConfigEntityList.add(toWncStockingTimeConfigEntity(wncStockingTimeConfig));
        }
        return wncStockingTimeConfigEntityList;
}


    public static WncStockingTimeConfigEntity toWncStockingTimeConfigEntity(WncStockingTimeConfig wncStockingTimeConfig) {
        if (wncStockingTimeConfig == null) {
             return null;
        }
        WncStockingTimeConfigEntity wncStockingTimeConfigEntity = new WncStockingTimeConfigEntity();
        wncStockingTimeConfigEntity.setId(wncStockingTimeConfig.getId());
        wncStockingTimeConfigEntity.setCreateTime(wncStockingTimeConfig.getCreateTime());
        wncStockingTimeConfigEntity.setUpdateTime(wncStockingTimeConfig.getUpdateTime());
        wncStockingTimeConfigEntity.setBusinessNo(wncStockingTimeConfig.getBusinessNo());
        wncStockingTimeConfigEntity.setTenantId(wncStockingTimeConfig.getTenantId());
        wncStockingTimeConfigEntity.setFulfillmentType(wncStockingTimeConfig.getFulfillmentType());
        wncStockingTimeConfigEntity.setDeliveryRules(wncStockingTimeConfig.getDeliveryRules());
        wncStockingTimeConfigEntity.setStockingTime(wncStockingTimeConfig.getStockingTime());
        return wncStockingTimeConfigEntity;
    }








    public static WncStockingTimeConfig toWncStockingTimeConfig(WncStockingTimeConfigCommandParam param) {
        if (param == null) {
            return null;
        }
        WncStockingTimeConfig wncStockingTimeConfig = new WncStockingTimeConfig();
        wncStockingTimeConfig.setId(param.getId());
        wncStockingTimeConfig.setCreateTime(param.getCreateTime());
        wncStockingTimeConfig.setUpdateTime(param.getUpdateTime());
        wncStockingTimeConfig.setBusinessNo(param.getBusinessNo());
        wncStockingTimeConfig.setTenantId(param.getTenantId());
        wncStockingTimeConfig.setFulfillmentType(param.getFulfillmentType());
        wncStockingTimeConfig.setDeliveryRules(param.getDeliveryRules());
        wncStockingTimeConfig.setStockingTime(param.getStockingTime());
        return wncStockingTimeConfig;
    }
}
