package net.summerfarm.wnc.infrastructure.converter;

import net.summerfarm.wnc.common.constants.AppConsts;
import net.summerfarm.wnc.domain.fence.entity.AreaEntity;
import net.summerfarm.wnc.infrastructure.model.Area;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-08-15
 **/
@Mapper(componentModel = AppConsts.MapStructConstants.COMPONENT_MODEL_SPRING)
public interface AreaConverter {

	/**
	 * doToEntityList
	 *
	 * @param areaList
	 * @return
	 */
	List<AreaEntity> doToEntityList(List<Area> areaList);


	/**
	 * doToEntity
	 *
	 * @param area
	 * @return
	 */
	AreaEntity doToEntity(Area area);
}
