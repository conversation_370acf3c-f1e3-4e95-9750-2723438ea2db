package net.summerfarm.wnc.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import lombok.RequiredArgsConstructor;
import net.summerfarm.wnc.common.enums.DeliveryAlertEnums;
import net.summerfarm.wnc.domain.alert.DeliveryAlertRuleRepository;
import net.summerfarm.wnc.domain.alert.entity.DeliveryAlertRuleItemVO;
import net.summerfarm.wnc.infrastructure.converter.TmsCompleteDeliveryRuleItemConverter;
import net.summerfarm.wnc.infrastructure.mapper.TmsCompleteDeliveryRuleItemMapper;
import net.summerfarm.wnc.infrastructure.mapper.TmsCompleteDeliveryRuleMapper;
import net.summerfarm.wnc.infrastructure.model.TmsCompleteDeliveryRuleItem;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description:配送提醒仓库实现类
 * date: 2023/3/21 15:13
 *
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class DeliveryAlertRuleRepositoryImpl implements DeliveryAlertRuleRepository {

    private final TmsCompleteDeliveryRuleItemMapper tmsCompleteDeliveryRuleItemMapper;

    @Override
    public DeliveryAlertRuleItemVO queryByUk(Integer storeNo, DeliveryAlertEnums.Channel channel, DeliveryAlertEnums.Type type, String bizNo) {
        TmsCompleteDeliveryRuleItem tmsCompleteDeliveryRuleItem = tmsCompleteDeliveryRuleItemMapper.selectOne(new LambdaQueryWrapper<TmsCompleteDeliveryRuleItem>()
                .eq(TmsCompleteDeliveryRuleItem::getStoreNo, storeNo)
                .eq(TmsCompleteDeliveryRuleItem::getChannel, channel.getValue())
                .eq(TmsCompleteDeliveryRuleItem::getType, type.getValue())
                .eq(TmsCompleteDeliveryRuleItem::getBizNo, bizNo));
        return TmsCompleteDeliveryRuleItemConverter.do2vo(tmsCompleteDeliveryRuleItem);
    }
}
