package net.summerfarm.wnc.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import jodd.util.StringUtil;
import lombok.NonNull;
import net.summerfarm.common.gaode.GaoDeUtil;
import net.summerfarm.wnc.common.enums.ContactConfigEnums;
import net.summerfarm.wnc.common.enums.WncContactDeliveryRuleEnums;
import net.summerfarm.wnc.domain.config.repository.ContactConfigRepository;
import net.summerfarm.wnc.domain.fence.OutLandContactRepository;
import net.summerfarm.wnc.domain.fence.entity.OutLandContactEntity;
import net.summerfarm.wnc.infrastructure.converter.OutLandConverter;
import net.summerfarm.wnc.infrastructure.converter.WncContactDeliveryRuleConverter;
import net.summerfarm.wnc.infrastructure.mapper.OutLandContactMapper;
import net.summerfarm.wnc.infrastructure.mapper.WncContactDeliveryRuleMapper;
import net.summerfarm.wnc.infrastructure.mapper.WncContactStoreCompareErrorMapper;
import net.summerfarm.wnc.infrastructure.model.OutLandContact;
import net.summerfarm.wnc.infrastructure.model.WncContactDeliveryRule;
import net.summerfarm.wnc.infrastructure.model.WncContactStoreCompareError;
import net.summerfarm.wnc.infrastructure.util.MybatisPlusUtil;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023-06-20
 **/
@Service
public class OutLandContactRepositoryImpl implements OutLandContactRepository {
	@Autowired
	private OutLandContactMapper outLandContactMapper;
	@Resource
	private WncContactDeliveryRuleMapper contactDeliveryRuleMapper;
	@Resource
	private WncContactStoreCompareErrorMapper wncContactStoreCompareErrorMapper;
	@Resource
	private ContactConfigRepository contactConfigRepository;

	@Override
	public OutLandContactEntity queryWithRuleByUk(@NonNull Long contactId) {
		OutLandContact outLandContact = outLandContactMapper.selectById(contactId);
		if(outLandContact == null){
			throw new BizException("未查询到客户信息");
		}
		OutLandContactEntity outLandContactEntity = OutLandConverter.do2Entity(outLandContact);
		if(outLandContactEntity != null){
			//查询鲜沐客户配送规则
			WncContactDeliveryRule contactDeliveryRule = contactDeliveryRuleMapper.selectOne(new LambdaQueryWrapper<WncContactDeliveryRule>()
					.eq(WncContactDeliveryRule::getOutBusinessNo, String.valueOf(contactId))
					.eq(WncContactDeliveryRule::getSystemSource, WncContactDeliveryRuleEnums.SystemSource.XM.getValue())
			);
			outLandContactEntity.setContactDeliveryRuleEntity(WncContactDeliveryRuleConverter.do2Entity(contactDeliveryRule));
		}
		//查询指定城配仓信息
		outLandContactEntity.setContactConfigEntity(contactConfigRepository.queryByUk(ContactConfigEnums.Source.XM, contactId));
		return outLandContactEntity;
	}

	@Override
	public List<OutLandContactEntity> queryListByBeginEndNum(Integer beginNum, Integer endNum) {
		if(beginNum == null || endNum == null){
			throw new BizException("beginNum/endNum不能为空");
		}
		List<OutLandContact> outLandContacts = outLandContactMapper.selectList(new LambdaQueryWrapper<OutLandContact>()
				.eq(OutLandContact::getStatus, 1)
				.ge(OutLandContact::getContactId, beginNum)
				.le(OutLandContact::getContactId, endNum)
		);

		return outLandContacts.stream().map(OutLandConverter::do2Entity).collect(Collectors.toList());
	}

	@Override
	public void saveCompareError(Long contactId, Integer storeNo, Integer fenceStoreNo) {
		if(contactId == null || storeNo == null || fenceStoreNo == null){
			throw new BizException("saveCompareError contactId storeNo fenceStoreNo not null");
		}
		WncContactStoreCompareError model = new WncContactStoreCompareError();
		model.setContactId(contactId);
		model.setContactStoreNo(storeNo);
		model.setPoiStoreNo(fenceStoreNo);
		wncContactStoreCompareErrorMapper.insert(model);
	}

	@Override
	public void saveBatchCompareError(List<OutLandContactEntity> saveList) {
		if(CollectionUtils.isEmpty(saveList)){
			return;
		}
		List<WncContactStoreCompareError> models = new ArrayList<>();

		//过滤已经存在的客户信息
		List<Long> reqContactIdList = saveList.stream().map(OutLandContactEntity::getContactId).collect(Collectors.toList());
		List<WncContactStoreCompareError> contactStoreCompareErrors = wncContactStoreCompareErrorMapper.selectList(new LambdaQueryWrapper<WncContactStoreCompareError>()
				.in(WncContactStoreCompareError::getContactId, reqContactIdList)
		);
		List<Long> esContactIdList = contactStoreCompareErrors.stream().map(WncContactStoreCompareError::getContactId).collect(Collectors.toList());

		for (OutLandContactEntity entity : saveList) {
			if(esContactIdList.contains(entity.getContactId())){
				continue;
			}
			WncContactStoreCompareError model = new WncContactStoreCompareError();
			model.setContactId(entity.getContactId());
			model.setContactStoreNo(entity.getStoreNo());
			model.setContactCity(entity.getCity());
			model.setContactArea(entity.getArea());
			model.setPoiStoreNo(entity.getPoiStoreNo());
			model.setPoiCity(entity.getPoiCity());
			model.setPoiArea(entity.getPoiArea());

			models.add(model);
		}

		MybatisPlusUtil.createBatch(models,WncContactStoreCompareError.class);
	}

	@Override
	public Long queryContactTotal() {
		return outLandContactMapper.selectCount(new LambdaQueryWrapper<OutLandContact>().eq(OutLandContact::getStatus, 1));
	}

	@Override
	public void updateNingJiAddressPoi() {
		List<OutLandContact> outLandContacts = outLandContactMapper.queryNingjiList();
		this.updatePoiByAddress(outLandContacts);
	}

	private void updatePoiByAddress(List<OutLandContact> outLandContacts) {
		if(CollectionUtils.isEmpty(outLandContacts)){
			return;
		}
		for (OutLandContact outLandContact : outLandContacts) {
			try {
				Thread.sleep(1000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
			String address = outLandContact.getProvince() + outLandContact.getCity() + outLandContact.getArea() + outLandContact.getAddress();
			String poi = GaoDeUtil.getPoiByAddressV2(address.replace(",", ""));
			if(StringUtil.isBlank(poi)){
				continue;
			}
			outLandContact.setPoiNote(poi);
			outLandContactMapper.updateById(outLandContact);
		}
	}

	@Override
	public void updateAddressPoi(List<Long> contactIds) {
		if(CollectionUtils.isEmpty(contactIds)){
			return;
		}
		List<OutLandContact> outLandContacts = outLandContactMapper.selectList(new LambdaQueryWrapper<OutLandContact>()
				.in(OutLandContact::getContactId, contactIds)
		);
		this.updatePoiByAddress(outLandContacts);
	}

	@Override
	public List<OutLandContactEntity> queryContactRecentOrderDays(LocalDate beginDay, LocalDate endDay) {
		long days = ChronoUnit.DAYS.between(beginDay, endDay);
		if(days > 6){
			throw new BizException("最大查询日期间隔为5天");
		}

		List<OutLandContact> outLandContacts = outLandContactMapper.queryContactOrdersDays(beginDay,endDay);
		return outLandContacts.stream().map(OutLandConverter::do2Entity).collect(Collectors.toList());
	}

	@Override
	public List<OutLandContactEntity> queryByIds(List<Long> contactIds) {
		if(CollectionUtils.isEmpty(contactIds)){
			return Collections.emptyList();
		}
		List<OutLandContact> outLandContacts = outLandContactMapper.selectList(new LambdaQueryWrapper<OutLandContact>()
				.in(OutLandContact::getContactId, contactIds)
		);

		return outLandContacts.stream().map(OutLandConverter::do2Entity).collect(Collectors.toList());
	}

	@Override
	public void contactAppointStoreNo(Long contactId, Integer newStoreNo) {
		if(contactId == null || newStoreNo == null){
			return;
		}

		outLandContactMapper.update(null, new LambdaUpdateWrapper<OutLandContact>()
				.eq(OutLandContact::getContactId, contactId)
				.set(OutLandContact::getStoreNo, newStoreNo));
	}
}
