package net.summerfarm.wnc.infrastructure.converter;

import net.summerfarm.wnc.common.constants.AppConsts;
import net.summerfarm.wnc.domain.fence.entity.AdCodeMsgEntity;
import net.summerfarm.wnc.infrastructure.model.AdCodeMsg;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-08-14
 **/
@Mapper(componentModel = AppConsts.MapStructConstants.COMPONENT_MODEL_SPRING)
public interface AdCodeMsgConvert {


	/**
	 * doToEntity
	 *
	 * @param adCodeMsgList
	 * @return
	 */
	List<AdCodeMsgEntity> doToEntityList(List<AdCodeMsg> adCodeMsgList);

	/**
	 * entity2do
	 *
	 * @param adCodeMsgEntity
	 * @return
	 */
	AdCodeMsg entity2do(AdCodeMsgEntity adCodeMsgEntity);
}
