package net.summerfarm.wnc.infrastructure.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 仓库工作时间关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-241 16:20:06
 */
@Getter
@Setter
@TableName("warehouse_storage_center_business_work")
public class WarehouseStorageCenterBusinessWork implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 仓库业务属性id
     */
    @TableField("warehouse_storage_center_business_id")
    private Long warehouseStorageCenterBusinessId;

    /**
     * 仓库编号
     */
    @TableField("warehouse_no")
    private Integer warehouseNo;

    /**
     * 仓库工作开始时间
     */
    @TableField("work_start_time")
    private LocalTime workStartTime;

    /**
     * 仓库工作结束时间
     */
    @TableField("work_end_time")
    private LocalTime workEndTime;

    /**
     * 是否删除
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


}
