package net.summerfarm.wnc.infrastructure.mapper.config;

import net.summerfarm.wnc.infrastructure.model.config.WncStockingTimeConfig;
import net.summerfarm.wnc.domain.config.param.query.WncStockingTimeConfigQueryParam;
import net.summerfarm.wnc.domain.config.entity.WncStockingTimeConfigEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025-04-15 14:51:44
 * @version 1.0
 *
 */
@Mapper
public interface WncStockingTimeConfigMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(WncStockingTimeConfig record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(WncStockingTimeConfig record);

    /**
     * @Describe: 通过主键删除
     * @param
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    WncStockingTimeConfig selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param param
     * @return
     */
    List<WncStockingTimeConfig> selectByCondition(WncStockingTimeConfigQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param param
     * @return
     */
    List<WncStockingTimeConfigEntity> getPage(WncStockingTimeConfigQueryParam param);
}

