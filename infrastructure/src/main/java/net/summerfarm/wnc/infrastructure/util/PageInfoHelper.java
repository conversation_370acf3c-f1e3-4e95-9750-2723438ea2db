package net.summerfarm.wnc.infrastructure.util;

import com.github.pagehelper.PageInfo;
import org.springframework.util.CollectionUtils;

import java.util.List;

public class PageInfoHelper {
    /**
     * @param data
     * @param <T>
     * @return
     */
    public static <T> PageInfo<T> createPageInfo(List<T> data) {
        return CollectionUtils.isEmpty(data) ? new PageInfo<T>() : new PageInfo<T>(data);
    }
}