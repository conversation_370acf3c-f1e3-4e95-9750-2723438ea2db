package net.summerfarm.wnc.infrastructure.repository.config;


import net.summerfarm.wnc.infrastructure.mapper.config.WncStockingTimeConfigMapper;
import net.summerfarm.wnc.infrastructure.converter.config.WncStockingTimeConfigConverter;
import net.summerfarm.wnc.domain.config.repository.WncStockingTimeConfigQueryRepository;
import net.summerfarm.wnc.domain.config.entity.WncStockingTimeConfigEntity;
import net.summerfarm.wnc.domain.config.param.query.WncStockingTimeConfigQueryParam;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.List;




/**
*
* <AUTHOR>
* @date 2025-04-15 14:51:44
* @version 1.0
*
*/
@Repository
public class WncStockingTimeConfigQueryRepositoryImpl implements WncStockingTimeConfigQueryRepository {

    @Autowired
    private WncStockingTimeConfigMapper wncStockingTimeConfigMapper;


    @Override
    public PageInfo<WncStockingTimeConfigEntity> getPage(WncStockingTimeConfigQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<WncStockingTimeConfigEntity> entities = wncStockingTimeConfigMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public WncStockingTimeConfigEntity selectById(Long id) {
        return WncStockingTimeConfigConverter.toWncStockingTimeConfigEntity(wncStockingTimeConfigMapper.selectById(id));
    }


    @Override
    public List<WncStockingTimeConfigEntity> selectByCondition(WncStockingTimeConfigQueryParam param) {
        return WncStockingTimeConfigConverter.toWncStockingTimeConfigEntityList(wncStockingTimeConfigMapper.selectByCondition(param));
    }

}