package net.summerfarm.wnc.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.common.query.warehouse.WarehouseLogisticsMappingQuery;
import net.summerfarm.wnc.domain.warehouse.WarehouseLogisticsMappingRepository;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseLogisticsMappingEntity;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseStorageEntity;
import net.summerfarm.wnc.infrastructure.converter.WarehouseLogisticsMappingConverter;
import net.summerfarm.wnc.infrastructure.converter.WarehouseStorageCenterConverter;
import net.summerfarm.wnc.infrastructure.mapper.WarehouseLogisticsMappingMapper;
import net.summerfarm.wnc.infrastructure.mapper.WarehouseStorageCenterMapper;
import net.summerfarm.wnc.infrastructure.model.WarehouseLogisticsMapping;
import net.summerfarm.wnc.infrastructure.model.WarehouseStorageCenter;
import net.summerfarm.wnc.infrastructure.util.MybatisPlusUtil;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/4/7 16:11<br/>
 *
 * <AUTHOR> />
 */
@Service
@Slf4j
public class WarehouseLogisticsMappingRepositoryImpl implements WarehouseLogisticsMappingRepository {

    @Resource
    private WarehouseLogisticsMappingMapper warehouseLogisticsMappingMapper;
    @Resource
    private WarehouseStorageCenterMapper warehouseStorageCenterMapper;

    @Override
    public List<WarehouseLogisticsMappingEntity> queryByStoreNoList(List<Integer> storeNoList) {
        if(CollectionUtils.isEmpty(storeNoList)){
            return Collections.emptyList();
        }
        List<WarehouseLogisticsMapping> warehouseLogisticsMappings = warehouseLogisticsMappingMapper.selectList(new LambdaQueryWrapper<WarehouseLogisticsMapping>()
                .in(WarehouseLogisticsMapping::getStoreNo, storeNoList)
        );

        return warehouseLogisticsMappings.stream().map(WarehouseLogisticsMappingConverter::model2Entity).collect(Collectors.toList());
    }

    @Override
    public List<WarehouseLogisticsMappingEntity> queryWithValidWarehouseMapping(List<Integer> storeNoList) {
        if(CollectionUtils.isEmpty(storeNoList)){
            return Collections.emptyList();
        }
        List<WarehouseLogisticsMapping> warehouseLogisticsMappings = warehouseLogisticsMappingMapper.queryValidWarehouseMapping(storeNoList);
        List<WarehouseLogisticsMappingEntity> mappingEntities = warehouseLogisticsMappings.stream().map(WarehouseLogisticsMappingConverter::model2Entity).collect(Collectors.toList());

        //获取库存仓信息
        List<Integer> warehouseNoList = warehouseLogisticsMappings.stream().map(WarehouseLogisticsMapping::getWarehouseNo).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(warehouseNoList)){
            List<WarehouseStorageCenter> warehouseStorageCenters = warehouseStorageCenterMapper.selectList(new LambdaQueryWrapper<WarehouseStorageCenter>()
                    .in(WarehouseStorageCenter::getWarehouseNo, warehouseNoList));
            List<WarehouseStorageEntity> warehouseStorageEntities = warehouseStorageCenters.stream().map(WarehouseStorageCenterConverter::warehouseStorage2Entity).collect(Collectors.toList());

            Map<Integer, WarehouseStorageEntity> warehouseStorageEntityMap = warehouseStorageEntities.stream().collect(Collectors.toMap(WarehouseStorageEntity::getWarehouseNo, Function.identity()));
            for (WarehouseLogisticsMappingEntity mappingEntity : mappingEntities) {
                mappingEntity.setWarehouseStorageEntity(warehouseStorageEntityMap.get(mappingEntity.getWarehouseNo()));
            }
        }
        return mappingEntities;
    }

    @Override
    public List<Integer> queryWarehouseNosByStoreNo(Integer storeNo) {
        if(storeNo == null){
            return Collections.emptyList();
        }
        List<WarehouseLogisticsMapping> warehouseLogisticsMappings = warehouseLogisticsMappingMapper.selectList(new LambdaQueryWrapper<WarehouseLogisticsMapping>()
                .eq(WarehouseLogisticsMapping::getStoreNo, storeNo)
        );

        return warehouseLogisticsMappings.stream().map(WarehouseLogisticsMapping::getWarehouseNo).collect(Collectors.toList());
    }

    @Override
    public List<Integer> queryValidWarehouseNosByStoreNo(Integer storeNo) {
        if (storeNo == null){
            return Collections.emptyList();
        }
        List<WarehouseLogisticsMapping> warehouseLogisticsMappings = warehouseLogisticsMappingMapper.queryValidWarehouseMapping(Collections.singletonList(storeNo));
        if (CollectionUtils.isEmpty(warehouseLogisticsMappings)){
            return Collections.emptyList();
        }
        return warehouseLogisticsMappings.stream().map(WarehouseLogisticsMapping::getWarehouseNo).collect(Collectors.toList());
    }

    @Override
    public void batchSave(List<WarehouseLogisticsMappingEntity> warehouseLogisticsMappingEntities) {
        if(CollectionUtils.isEmpty(warehouseLogisticsMappingEntities)){
            return;
        }
        List<WarehouseLogisticsMapping> warehouseLogisticsMappings = warehouseLogisticsMappingEntities.stream().map(WarehouseLogisticsMappingConverter::entity2Model).collect(Collectors.toList());
        MybatisPlusUtil.createBatch(warehouseLogisticsMappings,WarehouseLogisticsMapping.class);
    }

    @Override
    public void removeMapping(Integer storeNo, Integer warehouseNo) {
        if(storeNo == null || warehouseNo == null){
            throw new BizException("删除映射关系城配仓编号和库存仓编号都不能为空");
        }
        warehouseLogisticsMappingMapper.delete(new LambdaQueryWrapper<WarehouseLogisticsMapping>()
                .eq(WarehouseLogisticsMapping::getWarehouseNo,warehouseNo)
                .eq(WarehouseLogisticsMapping::getStoreNo,storeNo)
        );
    }

    @Override
    public Map<Integer, List<Integer>> queryLogisticsMapping(List<Integer> storeNos) {
        if(CollectionUtils.isEmpty(storeNos)){
            return Collections.emptyMap();
        }
        List<WarehouseLogisticsMappingEntity> mappingEntities = this.queryByStoreNoList(storeNos);
        if(CollectionUtils.isEmpty(mappingEntities)){
            return Collections.emptyMap();
        }

        Map<Integer, List<WarehouseLogisticsMappingEntity>> storeNoMappingEntityMap = mappingEntities.stream()
                .filter(s -> s.getStoreNo() != null)
                .filter(s -> s.getWarehouseNo() != null)
                .collect(Collectors.groupingBy(WarehouseLogisticsMappingEntity::getStoreNo));
        Map<Integer, List<Integer>> storeWarehouseMappings = new HashMap<>();
        for (Integer storeNo : storeNoMappingEntityMap.keySet()) {
            List<WarehouseLogisticsMappingEntity> mappingList = storeNoMappingEntityMap.get(storeNo);
            if(CollectionUtils.isEmpty(mappingList)){
                continue;
            }
            List<Integer> warehouseNoList = mappingList.stream().map(WarehouseLogisticsMappingEntity::getWarehouseNo).collect(Collectors.toList());
            storeWarehouseMappings.put(storeNo,warehouseNoList);
        }

        return storeWarehouseMappings;
    }
}
