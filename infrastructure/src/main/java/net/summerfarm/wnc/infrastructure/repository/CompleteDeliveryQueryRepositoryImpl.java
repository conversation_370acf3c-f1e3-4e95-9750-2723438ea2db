package net.summerfarm.wnc.infrastructure.repository;

import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.common.enums.AdCodeMessageEnums;
import net.summerfarm.wnc.common.enums.CompleteDeliveryEnums;
import net.summerfarm.wnc.common.query.alert.CompleteDeliveryTimeQueryParam;
import net.summerfarm.wnc.domain.alert.CompleteDeliveryQueryRepository;
import net.summerfarm.wnc.infrastructure.mapper.CompleteDeliveryMapper;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: 完成配送提醒查询实现类<br/>
 * date: 2024/3/29 14:52<br/>
 *
 * <AUTHOR> />
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CompleteDeliveryQueryRepositoryImpl implements CompleteDeliveryQueryRepository {

    private final CompleteDeliveryMapper completeDeliveryMapper;

    @Override
    public List<LocalTime> queryListAreaLastTime(CompleteDeliveryTimeQueryParam query) {
        if(query.getStoreNo() == null || StringUtil.isBlank(query.getCity())){
            throw new BizException("查询最晚配送时间条件有误");
        }
        query.setAdCodeStatus(AdCodeMessageEnums.Status.NORMAL.getValue());
        query.setCompleteDeliveryStatus(CompleteDeliveryEnums.Status.NORMAL.getValue());

        List<String> timeStrings = completeDeliveryMapper.queryListAreaLastTime(query);

        if(CollectionUtils.isEmpty(timeStrings)){
            return null;
        }
        List<LocalTime> localTimes = null;
        try {
            localTimes = timeStrings.stream()
                    .map(LocalTime::parse)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("queryListAreaLastTime 解析时间失败",e);
        }

        return localTimes;
    }
}
