package net.summerfarm.wnc.infrastructure.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 租户城配仓SKU黑名单配置
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-239 14:42:07
 */
@Getter
@Setter
@TableName("wnc_tenant_store_sku_black_config")
public class WncTenantStoreSkuBlackConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 城配仓编号
     */
    @TableField("store_no")
    private Integer storeNo;

    /**
     * sku
     */
    @TableField("sku")
    private String sku;


}
