package net.summerfarm.wnc.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.summerfarm.wnc.common.query.warehouse.WarehouseStorageFenceQuery;
import net.summerfarm.wnc.domain.ConfigRepository;
import net.summerfarm.wnc.domain.warehouse.WncWarehouseStorageFenceRepository;
import net.summerfarm.wnc.domain.warehouse.entity.WncWarehouseStorageFenceEntity;
import net.summerfarm.wnc.infrastructure.converter.WncWarehouseStorageFenceConverter;
import net.summerfarm.wnc.infrastructure.mapper.ConfigMapper;
import net.summerfarm.wnc.infrastructure.mapper.WncWarehouseStorageFenceMapper;
import net.summerfarm.wnc.infrastructure.model.WncWarehouseStorageFence;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/3/28 17:26<br/>
 *
 * <AUTHOR> />
 */
@Service
public class WncWarehouseStorageFenceRepositoryImpl implements WncWarehouseStorageFenceRepository {

    @Resource
    private WncWarehouseStorageFenceMapper wncWarehouseStorageFenceMapper;

    @Resource
    private ConfigMapper configMapper;
    @Resource
    private ConfigRepository configRepository;

    @Override
    public List<WncWarehouseStorageFenceEntity> queryList(WarehouseStorageFenceQuery warehouseStorageFenceQuery) {
        List<String> queryNoAreaCityList = new ArrayList<>();
        //过滤没有区域的城市
        List<String> noAreaCityList = configRepository.queryNoAreaCity();
        List<String> citys = warehouseStorageFenceQuery.getCitys();
        List<String> newHaveAreaCitys = new ArrayList<>();

        if (!CollectionUtils.isEmpty(citys)) {
            citys.forEach(city -> {
                if (noAreaCityList.contains(city)) {
                    queryNoAreaCityList.add(city);
                } else {
                    newHaveAreaCitys.add(city);
                }
            });
        }

        List<WncWarehouseStorageFence> wncWarehouseStorageFences = new ArrayList<>();
        //相等说明只查询没有区域的城市
        if (queryNoAreaCityList.size() != 0) {
            //只查询只有城市的
            List<WncWarehouseStorageFence> wncWarehouseStorageFenceList = wncWarehouseStorageFenceMapper.selectList(new LambdaQueryWrapper<WncWarehouseStorageFence>()
                    .in(!CollectionUtils.isEmpty(warehouseStorageFenceQuery.getWarehouseNos()), WncWarehouseStorageFence::getWarehouseNo, warehouseStorageFenceQuery.getWarehouseNos())
                    .in(!CollectionUtils.isEmpty(queryNoAreaCityList), WncWarehouseStorageFence::getCity, queryNoAreaCityList)
                    .eq(warehouseStorageFenceQuery.getTenantId() != null, WncWarehouseStorageFence::getTenantId, warehouseStorageFenceQuery.getTenantId())
                    .eq(warehouseStorageFenceQuery.getWarehouseNo() != null, WncWarehouseStorageFence::getWarehouseNo, warehouseStorageFenceQuery.getWarehouseNo())
            );
            wncWarehouseStorageFences.addAll(wncWarehouseStorageFenceList);
        } else {
            //只查询只有城市和区域的
            List<WncWarehouseStorageFence> wncWarehouseStorageFenceList = wncWarehouseStorageFenceMapper.selectList(new LambdaQueryWrapper<WncWarehouseStorageFence>()
                    .in(!CollectionUtils.isEmpty(warehouseStorageFenceQuery.getWarehouseNos()), WncWarehouseStorageFence::getWarehouseNo, warehouseStorageFenceQuery.getWarehouseNos())
                    .in(!CollectionUtils.isEmpty(newHaveAreaCitys), WncWarehouseStorageFence::getCity, newHaveAreaCitys)
                    .in(!CollectionUtils.isEmpty(warehouseStorageFenceQuery.getAreas()), WncWarehouseStorageFence::getArea, warehouseStorageFenceQuery.getAreas())
                    .eq(warehouseStorageFenceQuery.getTenantId() != null, WncWarehouseStorageFence::getTenantId, warehouseStorageFenceQuery.getTenantId())
                    .eq(warehouseStorageFenceQuery.getWarehouseNo() != null, WncWarehouseStorageFence::getWarehouseNo, warehouseStorageFenceQuery.getWarehouseNo())
            );
            wncWarehouseStorageFences.addAll(wncWarehouseStorageFenceList);
        }

        return wncWarehouseStorageFences.stream().map(WncWarehouseStorageFenceConverter::fence2Entity).collect(Collectors.toList());
    }

    @Override
    public List<WncWarehouseStorageFenceEntity> queryFenceConflict(WarehouseStorageFenceQuery warehouseStorageFenceQuery) {
        List<String> queryNoAreaCityList = new ArrayList<>();
        //过滤没有区域的城市
        List<String> noAreaCityList = configRepository.queryNoAreaCity();
        List<String> citys = warehouseStorageFenceQuery.getCitys();
        List<String> newHaveAreaCitys = new ArrayList<>();

        if(!CollectionUtils.isEmpty(citys)){
            citys.forEach(city ->{
                if(noAreaCityList.contains(city)){
                    queryNoAreaCityList.add(city);
                }else{
                    newHaveAreaCitys.add(city);
                }
            });
        }
        List<WncWarehouseStorageFence> wncWarehouseStorageFences = new ArrayList<>();
        //相等说明只查询没有区域的城市
        if(queryNoAreaCityList.size() != 0){
            //只查询只有城市的
            List<WncWarehouseStorageFence> wncWarehouseStorageFenceList = wncWarehouseStorageFenceMapper.selectList(new LambdaQueryWrapper<WncWarehouseStorageFence>()
                    .in(!CollectionUtils.isEmpty(warehouseStorageFenceQuery.getCitys()),WncWarehouseStorageFence::getCity,warehouseStorageFenceQuery.getCitys())
                    .eq(warehouseStorageFenceQuery.getTenantId() != null,WncWarehouseStorageFence::getTenantId,warehouseStorageFenceQuery.getTenantId())
                    .ne(warehouseStorageFenceQuery.getNoThisWarehouseNo() != null,WncWarehouseStorageFence::getWarehouseNo,warehouseStorageFenceQuery.getNoThisWarehouseNo())
            );
            wncWarehouseStorageFences.addAll(wncWarehouseStorageFenceList);
        }else{
            //只查询只有城市和区域的
            List<WncWarehouseStorageFence> wncWarehouseStorageFenceList = wncWarehouseStorageFenceMapper.selectList(new LambdaQueryWrapper<WncWarehouseStorageFence>()
                    .in(!CollectionUtils.isEmpty(newHaveAreaCitys),WncWarehouseStorageFence::getCity,newHaveAreaCitys)
                    .in(!CollectionUtils.isEmpty(warehouseStorageFenceQuery.getAreas()),WncWarehouseStorageFence::getArea,warehouseStorageFenceQuery.getAreas())
                    .eq(warehouseStorageFenceQuery.getTenantId() != null,WncWarehouseStorageFence::getTenantId,warehouseStorageFenceQuery.getTenantId())
                    .ne(warehouseStorageFenceQuery.getNoThisWarehouseNo() != null,WncWarehouseStorageFence::getWarehouseNo,warehouseStorageFenceQuery.getNoThisWarehouseNo())
            );
            wncWarehouseStorageFences.addAll(wncWarehouseStorageFenceList);
        }

        return wncWarehouseStorageFences.stream().map(WncWarehouseStorageFenceConverter::fence2Entity).collect(Collectors.toList());
    }

}
