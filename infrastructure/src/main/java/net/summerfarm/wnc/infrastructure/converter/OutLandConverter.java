package net.summerfarm.wnc.infrastructure.converter;

import net.summerfarm.wnc.domain.fence.entity.OutLandContactEntity;
import net.summerfarm.wnc.infrastructure.model.OutLandContact;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2023-06-20
 **/
public class OutLandConverter {

	public static OutLandContactEntity do2Entity(OutLandContact outLandContact) {
		if (Objects.isNull(outLandContact)) {
			return null;
		}
		OutLandContactEntity outLandContactEntity = new OutLandContactEntity();
		outLandContactEntity.setContactId(outLandContact.getContactId());
		outLandContactEntity.setMId(outLandContact.getMId());
		outLandContactEntity.setContact(outLandContact.getContact());
		outLandContactEntity.setPosition(outLandContact.getPosition());
		outLandContactEntity.setGender(outLandContact.getGender());
		outLandContactEntity.setPhone(outLandContact.getPhone());
		outLandContactEntity.setEmail(outLandContact.getEmail());
		outLandContactEntity.setWeixincode(outLandContact.getWeixincode());
		outLandContactEntity.setProvince(outLandContact.getProvince());
		outLandContactEntity.setCity(outLandContact.getCity());
		outLandContactEntity.setArea(outLandContact.getArea());
		outLandContactEntity.setAddress(outLandContact.getAddress());
		outLandContactEntity.setDeliveryCar(outLandContact.getDeliveryCar());
		outLandContactEntity.setStatus(outLandContact.getStatus());
		outLandContactEntity.setRemark(outLandContact.getRemark());
		outLandContactEntity.setIsDefault(outLandContact.getIsDefault());
		outLandContactEntity.setPoiNote(outLandContact.getPoiNote());
		outLandContactEntity.setDistance(outLandContact.getDistance());
		outLandContactEntity.setPath(outLandContact.getPath());
		outLandContactEntity.setHouseNumber(outLandContact.getHouseNumber());
		outLandContactEntity.setCreateTime(outLandContact.getCreateTime());
		outLandContactEntity.setUpdateTime(outLandContact.getUpdateTime());
		outLandContactEntity.setStoreNo(outLandContact.getStoreNo());
		outLandContactEntity.setAcmId(outLandContact.getAcmId());
		outLandContactEntity.setBackStoreNo(outLandContact.getBackStoreNo());
		outLandContactEntity.setDeliveryRule(outLandContact.getDeliveryRule());
		outLandContactEntity.setDeliveryFee(outLandContact.getDeliveryFee());
		return outLandContactEntity;
	}
}
