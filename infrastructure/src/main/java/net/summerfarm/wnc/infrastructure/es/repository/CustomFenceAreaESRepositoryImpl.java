package net.summerfarm.wnc.infrastructure.es.repository;

import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.domain.fence.CustomFenceAreaESRepository;
import net.summerfarm.wnc.domain.fence.entity.CustomFenceAreaEntity;
import net.summerfarm.wnc.infrastructure.es.converter.CustomFenceAreaDocConverter;
import net.summerfarm.wnc.infrastructure.es.document.CustomFenceAreaDoc;
import net.summerfarm.wnc.infrastructure.es.mapper.CustomFenceAreaMapper;
import net.xianmu.common.exception.BizException;
import org.elasticsearch.common.geo.ShapeRelation;
import org.elasticsearch.geometry.Point;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Description: 自定义围栏ES<br/>
 * date: 2023/12/6 15:04<br/>
 *
 * <AUTHOR> />
 */
@Service
@Slf4j
public class CustomFenceAreaESRepositoryImpl implements CustomFenceAreaESRepository {

    @Resource
    private CustomFenceAreaMapper customFenceAreaMapper;

    @Override
    public CustomFenceAreaEntity queryByAdCode(String adCode) {
        if(adCode == null){
            throw new BizException("区域编码不能为空");
        }
        CustomFenceAreaDoc customFenceAreaDoc = customFenceAreaMapper.selectOne(new LambdaEsQueryWrapper<CustomFenceAreaDoc>()
                .eq(CustomFenceAreaDoc::getAdCode, adCode));
        return CustomFenceAreaDocConverter.model2Entity(customFenceAreaDoc);
    }

    @Override
    public Set<String> queryAdCodeByAdCodes(Set<String> adCodeSet) {
        if(CollectionUtils.isEmpty(adCodeSet)){
            return Collections.emptySet();
        }
        List<CustomFenceAreaDoc> customFenceAreaDocs = customFenceAreaMapper.selectList(new LambdaEsQueryWrapper<CustomFenceAreaDoc>()
                .in(CustomFenceAreaDoc::getAdCode, adCodeSet)
                .select(CustomFenceAreaDoc::getAdCode));
        if(CollectionUtils.isEmpty(customFenceAreaDocs)){
            return Collections.emptySet();
        }
        return customFenceAreaDocs.stream().map(CustomFenceAreaDoc::getAdCode).collect(java.util.stream.Collectors.toSet());
    }

    @Override
    public void batchSave(List<CustomFenceAreaEntity> needSaveDataList) {
        if(CollectionUtils.isEmpty(needSaveDataList)){
            return;
        }
        List<CustomFenceAreaDoc> customFenceAreaDocs = needSaveDataList.stream().map(CustomFenceAreaDocConverter::entity2Model).collect(Collectors.toList());
        customFenceAreaMapper.insertBatch(customFenceAreaDocs);
    }

    @Override
    public void createCustomFenceAreaIndex() {
        customFenceAreaMapper.createIndex();
    }

    @Override
    public CustomFenceAreaEntity queryPoiFence(String poi) {
        String[] split = poi.split(",");
        List<CustomFenceAreaDoc> customFenceAreaDocs = customFenceAreaMapper.selectList(new LambdaEsQueryWrapper<CustomFenceAreaDoc>()
                .geoShape(CustomFenceAreaDoc::getGeoLocation, new Point(Double.parseDouble(split[0]), Double.parseDouble(split[1])),ShapeRelation.CONTAINS));
        if(CollectionUtils.isEmpty(customFenceAreaDocs)){
            return null;
        }
        return CustomFenceAreaDocConverter.model2Entity(customFenceAreaDocs.get(0));
    }

    @Override
    public void save(CustomFenceAreaEntity entity) {
        if(entity == null){
            return;
        }
        customFenceAreaMapper.insert(CustomFenceAreaDocConverter.entity2Model(entity));
    }
}
