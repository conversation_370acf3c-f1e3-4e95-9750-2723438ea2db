package net.summerfarm.wnc.infrastructure.converter;

import com.google.common.collect.Lists;
import net.summerfarm.wnc.domain.preciseDelivery.aggregate.PreciseDeliveryConfigAggregate;
import net.summerfarm.wnc.domain.preciseDelivery.entity.PreciseDeliveryConfigEntity;
import net.summerfarm.wnc.domain.preciseDelivery.param.PreciseDeliveryConfigAreaCommandParam;
import net.summerfarm.wnc.domain.preciseDelivery.param.PreciseDeliveryConfigCommandParam;
import net.summerfarm.wnc.domain.preciseDelivery.param.PreciseDeliveryConfigTimeCommandParam;
import net.summerfarm.wnc.domain.preciseDelivery.valueObject.PreciseDeliveryConfigAreaValueObject;
import net.summerfarm.wnc.domain.preciseDelivery.valueObject.PreciseDeliveryConfigTimeValueObject;
import net.summerfarm.wnc.infrastructure.model.WncPreciseDeliveryAreaDetail;
import net.summerfarm.wnc.infrastructure.model.WncPreciseDeliveryConfig;
import net.summerfarm.wnc.infrastructure.model.WncPreciseDeliveryTimeDetail;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Description:精准送配置转换器
 * date: 2024/1/22 18:34
 *
 * <AUTHOR>
 */
public class WncPreciseDeliveryConfigConverter {


    public static PreciseDeliveryConfigAggregate convertAggregate(WncPreciseDeliveryConfig wncPreciseDeliveryConfig,
                                                                  List<WncPreciseDeliveryAreaDetail> areaList,
                                                                  List<WncPreciseDeliveryTimeDetail> timeList){
        if(wncPreciseDeliveryConfig == null){
            return null;
        }
        PreciseDeliveryConfigAggregate preciseDeliveryConfigAggregate = new PreciseDeliveryConfigAggregate();

        preciseDeliveryConfigAggregate.setConfigId(wncPreciseDeliveryConfig.getId());
        preciseDeliveryConfigAggregate.setProvince(wncPreciseDeliveryConfig.getProvince());
        preciseDeliveryConfigAggregate.setCity(wncPreciseDeliveryConfig.getCity());
        preciseDeliveryConfigAggregate.setCreator(wncPreciseDeliveryConfig.getCreator());
        preciseDeliveryConfigAggregate.setCreateTime(wncPreciseDeliveryConfig.getCreateTime());
        preciseDeliveryConfigAggregate.setUpdater(wncPreciseDeliveryConfig.getUpdater());
        preciseDeliveryConfigAggregate.setUpdateTime(wncPreciseDeliveryConfig.getUpdateTime());

        preciseDeliveryConfigAggregate.setAreaList(Optional.ofNullable(areaList).orElse(Lists.newArrayList())
                .stream().map(WncPreciseDeliveryAreaDetailConverter::do2Entity).collect(Collectors.toList()));

        preciseDeliveryConfigAggregate.setTimeList(Optional.ofNullable(timeList).orElse(Lists.newArrayList())
                .stream().sorted(Comparator.comparing(WncPreciseDeliveryTimeDetail::getBeginTime))
                .map(WncPreciseDeliveryTimeDetailConverter::do2Entity).collect(Collectors.toList()));
        return preciseDeliveryConfigAggregate;
    }

    public static PreciseDeliveryConfigEntity do2Entity(WncPreciseDeliveryConfig wncPreciseDeliveryConfig){
        if(wncPreciseDeliveryConfig == null){
            return null;
        }
        PreciseDeliveryConfigEntity preciseDeliveryConfigEntity = new PreciseDeliveryConfigEntity();
        preciseDeliveryConfigEntity.setId(wncPreciseDeliveryConfig.getId());
        preciseDeliveryConfigEntity.setProvince(wncPreciseDeliveryConfig.getProvince());
        preciseDeliveryConfigEntity.setCity(wncPreciseDeliveryConfig.getCity());
        preciseDeliveryConfigEntity.setCreator(wncPreciseDeliveryConfig.getCreator());
        preciseDeliveryConfigEntity.setCreateTime(wncPreciseDeliveryConfig.getCreateTime());
        preciseDeliveryConfigEntity.setUpdater(wncPreciseDeliveryConfig.getUpdater());
        preciseDeliveryConfigEntity.setUpdateTime(wncPreciseDeliveryConfig.getUpdateTime());
        return preciseDeliveryConfigEntity;
    }

    public static WncPreciseDeliveryConfig param2Do(PreciseDeliveryConfigCommandParam param){
        if(param == null){
            return null;
        }
        WncPreciseDeliveryConfig wncPreciseDeliveryConfig = new WncPreciseDeliveryConfig();
        wncPreciseDeliveryConfig.setId(param.getConfigId());
        wncPreciseDeliveryConfig.setProvince(param.getProvince());
        wncPreciseDeliveryConfig.setCity(param.getCity());
        wncPreciseDeliveryConfig.setCreator(param.getCreator());
        wncPreciseDeliveryConfig.setCreateTime(param.getCreateTime());
        wncPreciseDeliveryConfig.setUpdater(param.getUpdater());
        wncPreciseDeliveryConfig.setUpdateTime(param.getUpdateTime());
        return wncPreciseDeliveryConfig;
    }
}
