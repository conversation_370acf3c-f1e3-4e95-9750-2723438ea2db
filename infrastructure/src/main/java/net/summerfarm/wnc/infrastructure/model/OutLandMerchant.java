package net.summerfarm.wnc.infrastructure.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 商户
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-66 14:40:19
 */
@Getter
@Setter
@TableName("merchant")
public class OutLandMerchant implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "m_id", type = IdType.AUTO)
    private Long mId;

    /**
     * 商户类型
     */
    @TableField("role_id")
    private Integer roleId;

    /**
     * 商户名称
     */
    @TableField("mname")
    private String mname;

    /**
     * 主联系人
     */
    @TableField("mcontact")
    private String mcontact;

    /**
     * 微信用户id
     */
    @TableField("openid")
    private String openid;

    /**
     * 手机
     */
    @TableField("phone")
    private String phone;

    /**
     * 审核状态：0、审核通过 1、审核中 2、审核未通过 3、账号被拉黑
     */
    @TableField("islock")
    private Integer islock;

    /**
     * 等级
     */
    @TableField("rank_id")
    private Integer rankId;

    /**
     * 注册时间
     */
    @TableField("register_time")
    private LocalDateTime registerTime;

    /**
     * 登录时间
     */
    @TableField("login_time")
    private LocalDateTime loginTime;

    /**
     * 6位邀请码
     */
    @TableField("invitecode")
    private String invitecode;

    /**
     * 用户分享码
     */
    @TableField("channel_code")
    private String channelCode;

    /**
     * 邀请人渠道码
     */
    @TableField("inviter_channel_code")
    private String inviterChannelCode;

    /**
     * 审核时间
     */
    @TableField("audit_time")
    private LocalDateTime auditTime;

    /**
     * 审核人
     */
    @TableField("audit_user")
    private Integer auditUser;

    /**
     * 营业执照路径
     */
    @TableField("business_license")
    private String businessLicense;

    /**
     * 省
     */
    @TableField("province")
    private String province;

    /**
     * 市
     */
    @TableField("city")
    private String city;

    /**
     * 地区
     */
    @TableField("area")
    private String area;

    /**
     * 详细地址
     */
    @TableField("address")
    private String address;

    /**
     * 商家腾讯地图坐标
     */
    @TableField("poi_note")
    private String poiNote;

    /**
     * 审核备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 店铺招牌
     */
    @TableField("shop_sign")
    private String shopSign;

    /**
     * 其他证明照片
     */
    @TableField("other_proof")
    private String otherProof;

    /**
     * 上次下单时间
     */
    @TableField("last_order_time")
    private LocalDateTime lastOrderTime;

    @TableField("area_no")
    private Integer areaNo;

    /**
     * 1大客户\2大连锁3\小连锁\4单点
     */
    @TableField("size")
    private String size;

    /**
     * 客户类型
     */
    @TableField("`type`")
    private String type;

    /**
     * 商圈
     */
    @TableField("trade_area")
    private String tradeArea;

    /**
     * 商圈组
     */
    @TableField("trade_group")
    private String tradeGroup;

    @TableField("unionid")
    private String unionid;

    @TableField("mp_openid")
    private String mpOpenid;

    /**
     * 用户ID
     */
    @TableField("admin_id")
    private Integer adminId;

    /**
     * 1是直营 2是加盟
1 账期 2  现结


     */
    @TableField("direct")
    private Integer direct;

    /**
     * 1服务区内 2服务区外
     */
    @TableField("`server`")
    private Integer server;

    @TableField("pop_view")
    private Integer popView;

    /**
     * 会员当月积分
     */
    @TableField("member_integral")
    private BigDecimal memberIntegral;

    /**
     * 会员等级
     */
    @TableField("grade")
    private Integer grade;

    @TableField("sku_show")
    private Integer skuShow;

    /**
     * 余额
     */
    @TableField("recharge_amount")
    private BigDecimal rechargeAmount;

    /**
     * 可提现金额
     */
    @TableField("cash_amount")
    private BigDecimal cashAmount;

    /**
     * cash_amount更新时间
     */
    @TableField("cash_update_time")
    private LocalDateTime cashUpdateTime;

    /**
     * 配送单是否展示价格
     */
    @TableField("show_price")
    private Boolean showPrice;

    /**
     * 账号合并人
     */
    @TableField("merge_admin")
    private String mergeAdmin;

    /**
     * 账号和并时间
     */
    @TableField("merge_time")
    private LocalDateTime mergeTime;

    /**
     * 首次登录弹窗：0、未弹 1、已弹
     */
    @TableField("first_login_pop")
    private Integer firstLoginPop;

    /**
     * 更换账号绑定弹窗：0、未弹 1、已弹或未更换账号绑定
     */
    @TableField("change_pop")
    private Integer changePop;

    /**
     * 拉黑备注
     */
    @TableField("pull_black_remark")
    private String pullBlackRemark;

    /**
     * 操作人
     */
    @TableField("pull_black_operator")
    private String pullBlackOperator;

    /**
     * 门牌号
     */
    @TableField("house_number")
    private String houseNumber;

    /**
     * 企业品牌
     */
    @TableField("company_brand")
    private String companyBrand;

    /**
     * 是否选择线索池 0 不是 1 是
     */
    @TableField("clue_pool")
    private Integer cluePool;

    /**
     * 大客户类型: ka,批发大客户,普通
     */
    @TableField("merchant_type")
    private String merchantType;

    /**
     * 规模
     */
    @TableField("enterprise_scale")
    private String enterpriseScale;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    @TableField("examine_type")
    private Integer examineType;

    /**
     * 开关状态 0 开（展示） 1 关（不展示）
     */
    @TableField("display_button")
    private Integer displayButton;

    /**
     * 运营状态:正常(0),倒闭(1)
     */
    @TableField("operate_status")
    private Integer operateStatus;

    /**
     * 更新人adminId
     */
    @TableField("updater")
    private Integer updater;


}
