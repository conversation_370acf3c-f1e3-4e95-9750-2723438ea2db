package net.summerfarm.wnc.infrastructure.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 仓库围栏
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-87 15:45:30
 */
@Getter
@Setter
@TableName("wnc_warehouse_storage_fence")
public class WncWarehouseStorageFence implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 省
     */
    @TableField("province")
    private String province;

    /**
     * 市
     */
    @TableField("city")
    private String city;

    /**
     * 区
     */
    @TableField("area")
    private String area;

    /**
     * 库存仓编号
     */
    @TableField("warehouse_no")
    private Integer warehouseNo;

    /**
     * 所属租户
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 最后操作人名称
     */
    @TableField("last_operator_name")
    private String lastOperatorName;


}
