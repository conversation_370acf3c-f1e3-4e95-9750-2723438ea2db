package net.summerfarm.wnc.infrastructure.converter.config;

import net.summerfarm.wnc.infrastructure.model.config.WncFullCategoryWarehouseSkuWhiteConfig;
import net.summerfarm.wnc.domain.config.entity.WncFullCategoryWarehouseSkuWhiteConfigEntity;
import net.summerfarm.wnc.domain.config.param.command.WncFullCategoryWarehouseSkuWhiteConfigCommandParam;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 *
 * <AUTHOR>
 * @date 2025-01-06 15:31:26
 * @version 1.0
 *
 */
public class WncFullCategoryWarehouseSkuWhiteConfigConverter {

    private WncFullCategoryWarehouseSkuWhiteConfigConverter() {
        // 无需实现
    }




    public static List<WncFullCategoryWarehouseSkuWhiteConfigEntity> toWncFullCategoryWarehouseSkuWhiteConfigEntityList(List<WncFullCategoryWarehouseSkuWhiteConfig> wncFullCategoryWarehouseSkuWhiteConfigList) {
        if (wncFullCategoryWarehouseSkuWhiteConfigList == null) {
            return Collections.emptyList();
        }
        List<WncFullCategoryWarehouseSkuWhiteConfigEntity> wncFullCategoryWarehouseSkuWhiteConfigEntityList = new ArrayList<>();
        for (WncFullCategoryWarehouseSkuWhiteConfig wncFullCategoryWarehouseSkuWhiteConfig : wncFullCategoryWarehouseSkuWhiteConfigList) {
            wncFullCategoryWarehouseSkuWhiteConfigEntityList.add(toWncFullCategoryWarehouseSkuWhiteConfigEntity(wncFullCategoryWarehouseSkuWhiteConfig));
        }
        return wncFullCategoryWarehouseSkuWhiteConfigEntityList;
}


    public static WncFullCategoryWarehouseSkuWhiteConfigEntity toWncFullCategoryWarehouseSkuWhiteConfigEntity(WncFullCategoryWarehouseSkuWhiteConfig wncFullCategoryWarehouseSkuWhiteConfig) {
        if (wncFullCategoryWarehouseSkuWhiteConfig == null) {
             return null;
        }
        WncFullCategoryWarehouseSkuWhiteConfigEntity wncFullCategoryWarehouseSkuWhiteConfigEntity = new WncFullCategoryWarehouseSkuWhiteConfigEntity();
        wncFullCategoryWarehouseSkuWhiteConfigEntity.setId(wncFullCategoryWarehouseSkuWhiteConfig.getId());
        wncFullCategoryWarehouseSkuWhiteConfigEntity.setCreateTime(wncFullCategoryWarehouseSkuWhiteConfig.getCreateTime());
        wncFullCategoryWarehouseSkuWhiteConfigEntity.setUpdateTime(wncFullCategoryWarehouseSkuWhiteConfig.getUpdateTime());
        wncFullCategoryWarehouseSkuWhiteConfigEntity.setWarehouseNo(wncFullCategoryWarehouseSkuWhiteConfig.getWarehouseNo());
        wncFullCategoryWarehouseSkuWhiteConfigEntity.setSku(wncFullCategoryWarehouseSkuWhiteConfig.getSku());
        return wncFullCategoryWarehouseSkuWhiteConfigEntity;
    }

    public static List<WncFullCategoryWarehouseSkuWhiteConfig> toWncFullCategoryWarehouseSkuWhiteConfigList(List<WncFullCategoryWarehouseSkuWhiteConfigCommandParam> paramList) {
        if (CollectionUtils.isEmpty(paramList)) {
            return Collections.emptyList();
        }
        return paramList.stream().map(WncFullCategoryWarehouseSkuWhiteConfigConverter::toWncFullCategoryWarehouseSkuWhiteConfig).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static WncFullCategoryWarehouseSkuWhiteConfig toWncFullCategoryWarehouseSkuWhiteConfig(WncFullCategoryWarehouseSkuWhiteConfigCommandParam param) {
        if (param == null) {
            return null;
        }
        WncFullCategoryWarehouseSkuWhiteConfig wncFullCategoryWarehouseSkuWhiteConfig = new WncFullCategoryWarehouseSkuWhiteConfig();
        wncFullCategoryWarehouseSkuWhiteConfig.setId(param.getId());
        wncFullCategoryWarehouseSkuWhiteConfig.setCreateTime(param.getCreateTime());
        wncFullCategoryWarehouseSkuWhiteConfig.setUpdateTime(param.getUpdateTime());
        wncFullCategoryWarehouseSkuWhiteConfig.setWarehouseNo(param.getWarehouseNo());
        wncFullCategoryWarehouseSkuWhiteConfig.setSku(param.getSku());
        return wncFullCategoryWarehouseSkuWhiteConfig;
    }
}
