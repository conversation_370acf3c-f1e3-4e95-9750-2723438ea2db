package net.summerfarm.wnc.infrastructure.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 围栏表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-96 15:48:23
 */
@Getter
@Setter
@TableName("fence")
public class Fence implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 围栏名称
     */
    @TableField("fence_name")
    private String fenceName;

    /**
     * 城配仓
     */
    @TableField("store_no")
    private Integer storeNo;

    /**
     * 运营范围
     */
    @TableField("area_no")
    private Integer areaNo;

    /**
     * 状态 0正常 1失效
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 添加时间
     */
    @TableField("add_time")
    private LocalDateTime addTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 最后操作人ID
     */
    @TableField("admin_id")
    private Integer adminId;

    /**
     * 打包id
     */
    @TableField("pack_id")
    private Integer packId;

    /**
     * 0 新建 1 拆分
     */
    @TableField("`type`")
    private Integer type;

    /**
     * 下单渠道类型 1000鲜沐平台客户 2000鲜沐大客户 3000 Saas客户
     */
    @TableField("order_channel_type")
    private String orderChannelType;

    /**
     * 运营区域名称
     */
    @TableField(exist = false)
    private String areaName;

    /**
     * 城配仓名称
     */
    @TableField(exist = false)
    private String storeName;

    /**
     * 最后操作人名称
     */
    @TableField(exist = false)
    private String adminName;

    /**
     * 行政城市名称
     */
    @TableField(exist = false)
    private String cityName;
}
