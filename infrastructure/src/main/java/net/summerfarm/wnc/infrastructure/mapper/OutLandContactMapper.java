package net.summerfarm.wnc.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.wnc.infrastructure.model.OutLandContact;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 联系人 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-66 14:40:19
 */
public interface OutLandContactMapper extends BaseMapper<OutLandContact> {

    /**
     * 查询柠季地址POI为空的数据
     * @return 结果
     */
    List<OutLandContact> queryNingjiList();

    /**
     * 查询最近几天的订单联系人信息
     * @param beginDay 开始日期
     * @param endDay 结束日期
     * @return 结果
     */
    List<OutLandContact> queryContactOrdersDays(@Param("beginDay") LocalDate beginDay,@Param("endDay") LocalDate endDay);
}
