package net.summerfarm.wnc.infrastructure.es.converter;

import cn.hutool.core.util.StrUtil;
import net.summerfarm.wnc.domain.fence.entity.CustomFenceAreaEntity;
import net.summerfarm.wnc.infrastructure.es.document.CustomFenceAreaDoc;
import org.apache.lucene.spatial3d.geom.GeoPolygon;
import org.elasticsearch.common.geo.GeoPoint;
import org.elasticsearch.common.geo.GeoShapeUtils;
import org.elasticsearch.common.geo.builders.GeometryCollectionBuilder;
import org.elasticsearch.common.geo.builders.MultiPointBuilder;
import org.elasticsearch.common.geo.builders.PointBuilder;
import org.elasticsearch.geometry.LinearRing;
import org.elasticsearch.geometry.MultiPoint;
import org.elasticsearch.geometry.Point;
import org.elasticsearch.geometry.Polygon;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * Description: 转换类<br/>
 * date: 2023/12/6 15:26<br/>
 *
 * <AUTHOR> />
 */
public class CustomFenceAreaDocConverter {

    public static CustomFenceAreaEntity model2Entity(CustomFenceAreaDoc customFenceAreaDoc) {
        if (customFenceAreaDoc == null) {
            return null;
        }
        CustomFenceAreaEntity customFenceAreaEntity = new CustomFenceAreaEntity();
        customFenceAreaEntity.setAdCode(customFenceAreaDoc.getAdCode());
        customFenceAreaEntity.setProvince(customFenceAreaDoc.getProvince());
        customFenceAreaEntity.setCity(customFenceAreaDoc.getCity());
        customFenceAreaEntity.setArea(customFenceAreaDoc.getArea());
        customFenceAreaEntity.setCreateTime(customFenceAreaDoc.getCreateTime());
        customFenceAreaEntity.setUpdateTime(customFenceAreaDoc.getUpdateTime());
        customFenceAreaEntity.setGeoLocation(customFenceAreaDoc.getGeoLocation());

        return customFenceAreaEntity;
    }


    public static CustomFenceAreaDoc entity2Model(CustomFenceAreaEntity customFenceAreaEntity) {
        if (customFenceAreaEntity == null) {
            return null;
        }
        CustomFenceAreaDoc customFenceAreaDoc = new CustomFenceAreaDoc();
        customFenceAreaDoc.setAdCode(customFenceAreaEntity.getAdCode());
        customFenceAreaDoc.setProvince(customFenceAreaEntity.getProvince());
        customFenceAreaDoc.setCity(customFenceAreaEntity.getCity());
        customFenceAreaDoc.setArea(customFenceAreaEntity.getArea());
        customFenceAreaDoc.setCreateTime(customFenceAreaEntity.getCreateTime());
        customFenceAreaDoc.setUpdateTime(customFenceAreaEntity.getUpdateTime());
        //批量处理 点位信息

        if(StrUtil.isNotBlank(customFenceAreaEntity.getGeoLocation())){
            customFenceAreaDoc.setGeoLocation("POLYGON(("+customFenceAreaEntity.getGeoLocation()
                    .replace(",", " ")
                    .replace(";", ",")
                    +"))");
        }

        return customFenceAreaDoc;
    }
}

