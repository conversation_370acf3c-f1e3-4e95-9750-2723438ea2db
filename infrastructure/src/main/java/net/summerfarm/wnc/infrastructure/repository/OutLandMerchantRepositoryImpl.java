package net.summerfarm.wnc.infrastructure.repository;

import net.summerfarm.wnc.domain.fence.OutLandMerchantRepository;
import net.summerfarm.wnc.domain.fence.entity.OutLandMerchantEntity;
import net.summerfarm.wnc.infrastructure.converter.OutLandMerchantConverter;
import net.summerfarm.wnc.infrastructure.mapper.OutLandMerchantMapper;
import net.summerfarm.wnc.infrastructure.model.OutLandMerchant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2023-06-20
 **/
@Service
public class OutLandMerchantRepositoryImpl implements OutLandMerchantRepository {
	@Autowired
	OutLandMerchantMapper outLandMerchantMapper;

	@Override
	public OutLandMerchantEntity queryByUk(Long merchantId) {
		OutLandMerchant outLandMerchant = outLandMerchantMapper.selectById(merchantId);
		return OutLandMerchantConverter.do2Entity(outLandMerchant);
	}
}
