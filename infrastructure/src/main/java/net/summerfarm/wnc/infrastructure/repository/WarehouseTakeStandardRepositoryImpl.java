package net.summerfarm.wnc.infrastructure.repository;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.summerfarm.wnc.common.query.warehouse.WarehouseTakeStandardQuery;
import net.summerfarm.wnc.domain.warehouse.WarehouseTakeStandardRepository;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseTakeStandardEntity;
import net.summerfarm.wnc.infrastructure.converter.WarehouseTakeStandardConverter;
import net.summerfarm.wnc.infrastructure.mapper.WarehouseTakeStandardMapper;
import net.summerfarm.wnc.infrastructure.model.WarehouseTakeStandard;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/4/4 18:57<br/>
 *
 * <AUTHOR> />
 */
@Service
public class WarehouseTakeStandardRepositoryImpl implements WarehouseTakeStandardRepository {
    @Resource
    private WarehouseTakeStandardMapper warehouseTakeStandardMapper;

    @Override
    public List<WarehouseTakeStandardEntity> queryList(WarehouseTakeStandardQuery warehouseTakeStandardQuery) {
        List<WarehouseTakeStandard> warehouseTakeStandards = warehouseTakeStandardMapper.selectList(new LambdaQueryWrapper<WarehouseTakeStandard>()
                .in(!CollectionUtils.isEmpty(warehouseTakeStandardQuery.getWarehouseNos()), WarehouseTakeStandard::getWarehouseNo, warehouseTakeStandardQuery.getWarehouseNos())
        );

        return warehouseTakeStandards.stream().map(WarehouseTakeStandardConverter::model2Entity).collect(Collectors.toList());
    }
}
