package net.summerfarm.wnc.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.wnc.common.query.fence.AdCodeMsgByStoreNoAddrStatusQuery;
import net.summerfarm.wnc.common.query.fence.AdCodeMsgQuery;
import net.summerfarm.wnc.domain.fence.dataObject.AdCodeMsgFenceStoreDO;
import net.summerfarm.wnc.infrastructure.model.AdCodeMsg;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 围栏对应的区域信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-96 15:47:56
 */
public interface AdCodeMsgMapper extends BaseMapper<AdCodeMsg> {


	/**
	 * 根据城市查询有效区域
	 *
	 * @param cityList
	 * @return
	 */
	List<AdCodeMsg> queryValidList(@Param("cityList") List<String> cityList);

	/**
	 * 根据城市和非围栏ID查询区域信息
	 * @param city 城市
	 * @param fenceId 围栏ID
	 * @return 区域信息
	 */
    List<AdCodeMsg> selectByCityAndNotFenceId(@Param("city") String city, @Param("fenceId") Integer fenceId);

	/**
	 * 根据城配仓号、状态、围栏状态、仓配中心状态、城市查询区域信息
	 * @param query 查询条件
	 * @return 结果
	 */
	List<AdCodeMsg> queryAdCodeMsgByStoreNoAddrStatus(AdCodeMsgByStoreNoAddrStatusQuery query);

	/**
	 * 根据查询条件查询围栏对应的区域信息
	 * @param adCodeMsgQuery 查询
	 * @return 结果
	 */
    List<AdCodeMsgFenceStoreDO> queryAdCodeMsgFenceStore(AdCodeMsgQuery adCodeMsgQuery);
}
