package net.summerfarm.wnc.infrastructure.repository.fence;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.summerfarm.wnc.infrastructure.model.fence.FenceChannelBusinessWhiteConfig;
import net.summerfarm.wnc.infrastructure.mapper.fence.FenceChannelBusinessWhiteConfigMapper;
import net.summerfarm.wnc.infrastructure.converter.fence.FenceChannelBusinessWhiteConfigConverter;
import net.summerfarm.wnc.domain.fence.repository.FenceChannelBusinessWhiteConfigCommandRepository;
import net.summerfarm.wnc.domain.fence.entity.FenceChannelBusinessWhiteConfigEntity;
import net.summerfarm.wnc.domain.fence.param.command.FenceChannelBusinessWhiteConfigCommandParam;
import net.summerfarm.wnc.infrastructure.util.MybatisPlusUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
*
* <AUTHOR>
* @date 2024-10-12 16:29:33
* @version 1.0
*
*/
@Repository
public class FenceChannelBusinessWhiteConfigCommandRepositoryImpl implements FenceChannelBusinessWhiteConfigCommandRepository {

    @Autowired
    private FenceChannelBusinessWhiteConfigMapper fenceChannelBusinessWhiteConfigMapper;
    @Override
    public FenceChannelBusinessWhiteConfigEntity insertSelective(FenceChannelBusinessWhiteConfigCommandParam param) {
        FenceChannelBusinessWhiteConfig fenceChannelBusinessWhiteConfig = FenceChannelBusinessWhiteConfigConverter.toFenceChannelBusinessWhiteConfig(param);
        fenceChannelBusinessWhiteConfigMapper.insertSelective(fenceChannelBusinessWhiteConfig);
        return FenceChannelBusinessWhiteConfigConverter.toFenceChannelBusinessWhiteConfigEntity(fenceChannelBusinessWhiteConfig);
    }

    @Override
    public int updateSelectiveById(FenceChannelBusinessWhiteConfigCommandParam param){
        return fenceChannelBusinessWhiteConfigMapper.updateSelectiveById(FenceChannelBusinessWhiteConfigConverter.toFenceChannelBusinessWhiteConfig(param));
    }


    @Override
    public int remove(Long id) {
        return fenceChannelBusinessWhiteConfigMapper.remove(id);
    }

    @Override
    public void batchInsert(List<FenceChannelBusinessWhiteConfigEntity> businessWhiteConfigEntities) {
        if(CollectionUtils.isEmpty(businessWhiteConfigEntities)){
            return;
        }
        businessWhiteConfigEntities = businessWhiteConfigEntities.stream().distinct().collect(Collectors.toList());
        List<FenceChannelBusinessWhiteConfigCommandParam> saveBusinessChannelList = new ArrayList<>();
        businessWhiteConfigEntities.forEach(e ->{
            FenceChannelBusinessWhiteConfigCommandParam param = FenceChannelBusinessWhiteConfigCommandParam.builder()
                    .fenceId(e.getFenceId())
                    .orderChannelType(e.getOrderChannelType())
                    .tenantId(e.getTenantId())
                    .scopeChannelBusinessId(e.getScopeChannelBusinessId())
                    .scopeChannelBusinessName(e.getScopeChannelBusinessName())
                    .build();
            saveBusinessChannelList.add(param);
        });
        List<FenceChannelBusinessWhiteConfig> fenceChannelBusinessWhiteConfigList = saveBusinessChannelList.stream().map(FenceChannelBusinessWhiteConfigConverter::toFenceChannelBusinessWhiteConfig).collect(Collectors.toList());
        MybatisPlusUtil.createBatch(fenceChannelBusinessWhiteConfigList, FenceChannelBusinessWhiteConfig.class);
    }

    @Override
    public void removeByFenceId(Integer fenceId) {
        if(fenceId == null){
            return;
        }
        fenceChannelBusinessWhiteConfigMapper.delete(new LambdaQueryWrapper<FenceChannelBusinessWhiteConfig>()
                .eq(FenceChannelBusinessWhiteConfig::getFenceId, fenceId));
    }
}