package net.summerfarm.wnc.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.summerfarm.wnc.common.query.warehouse.SkuWarehouseMappingQuery;
import net.summerfarm.wnc.common.query.warehouse.WarehouseInventoryMappingQuery;
import net.summerfarm.wnc.domain.warehouse.WarehouseInventoryMappingRepository;
import net.summerfarm.wnc.domain.warehouse.WarehouseLogisticsMappingRepository;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseInventoryMappingEntity;
import net.summerfarm.wnc.infrastructure.converter.WarehouseInventoryMappingConverter;
import net.summerfarm.wnc.infrastructure.mapper.WarehouseInventoryMappingMapper;
import net.summerfarm.wnc.infrastructure.model.WarehouseInventoryMapping;
import net.summerfarm.wnc.infrastructure.util.MybatisPlusUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/4/7 18:33<br/>
 *
 * <AUTHOR> />
 */
@Service
public class WarehouseInventoryMappingRepositoryImpl implements WarehouseInventoryMappingRepository {

    @Resource
    private WarehouseInventoryMappingMapper warehouseInventoryMappingMapper;
    @Resource
    private WarehouseLogisticsMappingRepository warehouseLogisticsMappingRepository;

    @Override
    public List<WarehouseInventoryMappingEntity> queryList(WarehouseInventoryMappingQuery warehouseInventoryMappingQuery) {
        List<WarehouseInventoryMapping> warehouseInventoryMappings = warehouseInventoryMappingMapper.selectList(new LambdaQueryWrapper<WarehouseInventoryMapping>()
                .eq(warehouseInventoryMappingQuery.getStoreNo() != null, WarehouseInventoryMapping::getStoreNo, warehouseInventoryMappingQuery.getStoreNo())
                .eq(StringUtils.isNotBlank(warehouseInventoryMappingQuery.getSku()), WarehouseInventoryMapping::getSku, warehouseInventoryMappingQuery.getSku())
                .in(!CollectionUtils.isEmpty(warehouseInventoryMappingQuery.getStoreNos()), WarehouseInventoryMapping::getStoreNo, warehouseInventoryMappingQuery.getStoreNos())
                .in(!CollectionUtils.isEmpty(warehouseInventoryMappingQuery.getSkus()), WarehouseInventoryMapping::getSku, warehouseInventoryMappingQuery.getSkus())
                .in(!CollectionUtils.isEmpty(warehouseInventoryMappingQuery.getWarehouseNos()), WarehouseInventoryMapping::getWarehouseNo, warehouseInventoryMappingQuery.getWarehouseNos())
        );

        return warehouseInventoryMappings.stream().map(WarehouseInventoryMappingConverter::model2Entity).collect(Collectors.toList());
    }

    @Override
    public void batchSave(List<WarehouseInventoryMappingEntity> needCreateMapping) {
        if(CollectionUtils.isEmpty(needCreateMapping)){
            return;
        }
        needCreateMapping = this.handleValideStoreNoWarehouseNos(needCreateMapping);
        List<WarehouseInventoryMapping> warehouseInventoryMappings = needCreateMapping.stream().map(WarehouseInventoryMappingConverter::entity2model).collect(Collectors.toList());
        MybatisPlusUtil.createBatch(warehouseInventoryMappings, WarehouseInventoryMapping.class);
    }

    @Override
    public List<WarehouseInventoryMappingEntity> queryListBySkuStoreNo(List<SkuWarehouseMappingQuery> skuWarehouseMappingQueries) {
        if (CollectionUtils.isEmpty(skuWarehouseMappingQueries)){
            return Collections.emptyList();
        }
        List<WarehouseInventoryMapping> warehouseInventoryMappings = warehouseInventoryMappingMapper.selectListBySkuStoreNo(skuWarehouseMappingQueries);
        warehouseInventoryMappings = Optional.ofNullable(warehouseInventoryMappings).orElse(new ArrayList<>());
        List<WarehouseInventoryMappingEntity> mappingEntities = warehouseInventoryMappings.stream().map(WarehouseInventoryMappingConverter::model2Entity).collect(Collectors.toList());

        List<WarehouseInventoryMappingEntity> invalidMappingEntities = new ArrayList<>();
        //映射关系不存在返回结果处理
        if (CollectionUtils.isEmpty(warehouseInventoryMappings) || warehouseInventoryMappings.size() != skuWarehouseMappingQueries.size()){
            //已存在的SKU库存映射关系集合
            Set<String> existedMappings = warehouseInventoryMappings.stream().map(e -> e.getStoreNo() + "#" + e.getSku()).collect(Collectors.toSet());
            //不存在的无效SKU库存映射关系集合
            List<SkuWarehouseMappingQuery> invalidMappingQueries = skuWarehouseMappingQueries.stream().filter(e -> {
                String uk = e.getStoreNo() + "#" + e.getSku();
                return !existedMappings.contains(uk);
            }).collect(Collectors.toList());
            //处理无效映射关系
            invalidMappingEntities = invalidMappingQueries.stream().map(query -> {
                WarehouseInventoryMappingEntity warehouseInventoryMappingEntity = new WarehouseInventoryMappingEntity();
                warehouseInventoryMappingEntity.setStoreNo(query.getStoreNo());
                warehouseInventoryMappingEntity.setSku(query.getSku());
                return warehouseInventoryMappingEntity;
            }).collect(Collectors.toList());
        }
        mappingEntities.addAll(invalidMappingEntities);

        return mappingEntities;
    }

    @Override
    public long queryCountByStoreWarehouseNo(Integer storeNoReq, Integer warehouseNo) {
        return warehouseInventoryMappingMapper.selectCount(new LambdaQueryWrapper<WarehouseInventoryMapping>()
                .eq(WarehouseInventoryMapping::getWarehouseNo,warehouseNo)
                .eq(WarehouseInventoryMapping::getStoreNo,storeNoReq)
        );
    }

    @Override
    public List<WarehouseInventoryMappingEntity> queryMappingByUkList(List<SkuWarehouseMappingQuery> skuWarehouseMappingQueries) {
        if (CollectionUtils.isEmpty(skuWarehouseMappingQueries)){
            return Collections.emptyList();
        }
        List<WarehouseInventoryMapping> warehouseInventoryMappings = warehouseInventoryMappingMapper.selectListBySkuStoreNo(skuWarehouseMappingQueries);
        warehouseInventoryMappings = Optional.ofNullable(warehouseInventoryMappings).orElse(new ArrayList<>());
        return warehouseInventoryMappings.stream().map(WarehouseInventoryMappingConverter::model2Entity).collect(Collectors.toList());
    }

    @Override
    public void batchUpdate(List<WarehouseInventoryMappingEntity> needUpdateList) {
        if(CollectionUtils.isEmpty(needUpdateList)){
            return;
        }
        List<WarehouseInventoryMapping> updateList = needUpdateList.stream().map(WarehouseInventoryMappingConverter::entity2model).collect(Collectors.toList());
        updateList.sort(Comparator.comparing(WarehouseInventoryMapping::getId));
        //批量更新
        MybatisPlusUtil.updateBatch(updateList,WarehouseInventoryMapping.class);
    }

    @Override
    public Long queryCountByStoreNo(Integer storeNo) {
        if (storeNo == null){
            return 0L;
        }
        return warehouseInventoryMappingMapper.selectCount(new LambdaQueryWrapper<WarehouseInventoryMapping>()
                .eq(WarehouseInventoryMapping::getStoreNo, storeNo)
        );

    }

    @Override
    public List<WarehouseInventoryMappingEntity> queryValideStoreWarehouseNoMappingBySkuWarehouseNos(List<String> skuList, List<Integer> warehouseNos) {
        if(CollectionUtils.isEmpty(skuList) || CollectionUtils.isEmpty(warehouseNos)){
            return Collections.emptyList();
        }
        //查询信息
        List<WarehouseInventoryMappingEntity> skuMappings = this.queryList(WarehouseInventoryMappingQuery.builder().skus(skuList).warehouseNos(warehouseNos).build());
        //处理城配仓库存仓关系校验
        skuMappings = this.handleValideStoreNoWarehouseNos(skuMappings);

        return skuMappings;
    }

    @Override
    public List<WarehouseInventoryMappingEntity> queryValideStoreWarehouseNoMappingBySkuStoreNos(List<String> skuList, List<Integer> storeNosReq) {
        if(CollectionUtils.isEmpty(skuList) || CollectionUtils.isEmpty(storeNosReq)){
            return Collections.emptyList();
        }
        //查询信息
        List<WarehouseInventoryMappingEntity> skuMappings = this.queryList(WarehouseInventoryMappingQuery.builder().skus(skuList).storeNos(storeNosReq).build());
        //处理城配仓库存仓关系校验
        skuMappings = this.handleValideStoreNoWarehouseNos(skuMappings);

        return skuMappings;
    }

    /**
     * 城配仓库存仓映射关系校验
     * @param skuMappings 映射
     * @return 校验后映射关系
     */
    public List<WarehouseInventoryMappingEntity> handleValideStoreNoWarehouseNos(List<WarehouseInventoryMappingEntity> skuMappings) {
        if(CollectionUtils.isEmpty(skuMappings)){
            return Collections.emptyList();
        }
        List<Integer> storeNos = skuMappings.stream().map(WarehouseInventoryMappingEntity::getStoreNo).collect(Collectors.toList());
        //校验城配仓库存仓位是否有效
        Map<Integer, List<Integer>> storeNoWarehouseMap = warehouseLogisticsMappingRepository.queryLogisticsMapping(storeNos);

        return skuMappings.stream().filter(mapping -> {
            List<Integer> warehouseNoValues = storeNoWarehouseMap.get(mapping.getStoreNo());
            if (CollectionUtils.isEmpty(warehouseNoValues)) {
                return false;
            } else {
                return warehouseNoValues.contains(mapping.getWarehouseNo());
            }
        }).collect(Collectors.toList());
    }

}
