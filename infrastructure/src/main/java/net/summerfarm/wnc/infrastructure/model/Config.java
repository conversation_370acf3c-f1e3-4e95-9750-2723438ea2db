package net.summerfarm.wnc.infrastructure.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 商城配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-96 15:59:07
 */
@Getter
@Setter
@TableName("config")
public class Config implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * '配置键值'
     */
    @TableField("`key`")
    private String key;

    /**
     * '配置名称'
     */
    @TableField("`value`")
    private String value;

    /**
     * '备注'
     */
    @TableField("remark")
    private String remark;


}
