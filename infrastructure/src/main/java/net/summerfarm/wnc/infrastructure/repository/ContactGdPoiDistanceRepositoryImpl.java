package net.summerfarm.wnc.infrastructure.repository;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.gaode.GaoDeUtil;
import net.summerfarm.dto.LocationGeoDTO;
import net.summerfarm.wnc.common.util.DistanceUtil;
import net.summerfarm.wnc.domain.fence.ContactGdPoiDistanceRepository;
import net.summerfarm.wnc.domain.fence.entity.OutLandContactEntity;
import net.summerfarm.wnc.infrastructure.mapper.WncContactGdPoiDistanceMapper;
import net.summerfarm.wnc.infrastructure.model.WncContactGdPoiDistance;
import net.summerfarm.wnc.infrastructure.util.MybatisPlusUtil;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Description: 客户POI地址和高德POI距离操作类<br/>
 * date: 2023/12/15 13:50<br/>
 *
 * <AUTHOR> />
 */
@Repository
@Slf4j
public class ContactGdPoiDistanceRepositoryImpl implements ContactGdPoiDistanceRepository {

    @Resource
    private WncContactGdPoiDistanceMapper wncContactGdPoiDistanceMapper;
    @Override
    public void contactPoiDistanceSave(List<OutLandContactEntity> outLandContactEntities) {
        if(CollectionUtils.isEmpty(outLandContactEntities)){
            return;
        }

        List<WncContactGdPoiDistance> wncContactGdPoiDistances = wncContactGdPoiDistanceMapper.selectList(new LambdaQueryWrapper<WncContactGdPoiDistance>()
                .select(WncContactGdPoiDistance::getContactId)
        );
        Set<Long> contactIdSet = wncContactGdPoiDistances.stream().map(WncContactGdPoiDistance::getContactId).collect(Collectors.toSet());

        for (OutLandContactEntity outLandContactEntity : outLandContactEntities) {
            if(contactIdSet.contains(outLandContactEntity.getContactId())){
                continue;
            }
            //排除柠季地址的逗号
            String completeAddress = outLandContactEntity.getCompleteAddress();
            LocationGeoDTO locationGeo = null;
            try {
                String city = outLandContactEntity.getCity();
                if(StrUtil.isNotBlank(outLandContactEntity.getCity())){
                    city = city.replace(",", "");
                }
                locationGeo = GaoDeUtil.getLocationGeoByAddrCity(completeAddress,city);
            } catch (Exception e) {
                log.info("请求高德异常，客户信息:{}", JSON.toJSONString(outLandContactEntity), e);
                continue;
            }
            double poiDistance = 0;
            try {
                if(locationGeo == null || StrUtil.isBlank(locationGeo.getLocation())){
                    continue;
                }
                poiDistance = DistanceUtil.getPoiDistance(outLandContactEntity.getPoiNote(), locationGeo.getLocation());
            } catch (Exception e) {
                log.info("计算POI之间的距离异常,客户信息:{},高德POI:{}", JSON.toJSONString(outLandContactEntity),locationGeo.getLocation(), e);
                continue;
            }

            WncContactGdPoiDistance model = new WncContactGdPoiDistance();

            model.setContactId(outLandContactEntity.getContactId());
            model.setContactPoi(outLandContactEntity.getPoiNote());
            model.setGdPoi(locationGeo.getLocation());
            model.setDistance(new BigDecimal(poiDistance));
            model.setGdProvince(locationGeo.getProvince());
            model.setGdCity(locationGeo.getCity());
            model.setGdArea(locationGeo.getArea());
            model.setGdAddress(locationGeo.getAddress());

            wncContactGdPoiDistanceMapper.insert(model);
        }
    }

    @Override
    public void deleteContactPoiDistance() {
        wncContactGdPoiDistanceMapper.delete(new LambdaQueryWrapper<WncContactGdPoiDistance>());
    }
}
