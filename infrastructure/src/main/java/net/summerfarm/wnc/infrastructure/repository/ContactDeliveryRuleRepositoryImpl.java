package net.summerfarm.wnc.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jodd.util.StringUtil;
import net.summerfarm.wnc.common.query.deliveryRule.ContactDeliveryRuleQuery;
import net.summerfarm.wnc.domain.config.monit.PopMerchantMonit;
import net.summerfarm.wnc.domain.deliveryRule.ContactDeliveryRuleRepository;
import net.summerfarm.wnc.domain.deliveryRule.entity.ContactDeliveryRuleEntity;
import net.summerfarm.wnc.infrastructure.converter.WncContactDeliveryRuleConverter;
import net.summerfarm.wnc.infrastructure.mapper.WncContactDeliveryRuleMapper;
import net.summerfarm.wnc.infrastructure.model.WncContactDeliveryRule;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/11/13 11:41<br/>
 *
 * <AUTHOR> />
 */
@Repository
public class ContactDeliveryRuleRepositoryImpl implements ContactDeliveryRuleRepository {

    @Resource
    private WncContactDeliveryRuleMapper contactDeliveryRuleMapper;

    @Override
    public ContactDeliveryRuleEntity queryByUk(String outBusinessNo, Integer systemSource) {
        if(StringUtil.isBlank(outBusinessNo) || systemSource == null){
            throw new BizException("外部编号或者系统来源不能为空");
        }
        WncContactDeliveryRule wncContactDeliveryRule = contactDeliveryRuleMapper.selectOne(new LambdaQueryWrapper<WncContactDeliveryRule>()
                .eq(WncContactDeliveryRule::getOutBusinessNo, outBusinessNo)
                .eq(WncContactDeliveryRule::getSystemSource, systemSource));

        return WncContactDeliveryRuleConverter.do2Entity(wncContactDeliveryRule);
    }

    @Override
    public void saveOrUpdate(ContactDeliveryRuleEntity entity) {
        //先根据系统来源和业务编号查询地址配送规则信息
        ContactDeliveryRuleEntity contactDeliveryRuleEntity = this.queryByUk(entity.getOutBusinessNo(), entity.getSystemSource());

        WncContactDeliveryRule rule = new WncContactDeliveryRule();
        rule.setOutBusinessNo(entity.getOutBusinessNo());
        rule.setSystemSource(entity.getSystemSource());
        rule.setFrequentMethod(entity.getFrequentMethod());
        rule.setWeekDeliveryFrequent(entity.getWeekDeliveryFrequent());
        rule.setDeliveryFrequentInterval(entity.getDeliveryFrequentInterval());
        rule.setBeginCalculateDate(entity.getBeginCalculateDate());
        rule.setTenantId(entity.getTenantId());

        if(contactDeliveryRuleEntity == null){
            //新增
            contactDeliveryRuleMapper.insert(rule);
        }else{
            //更新操作
            rule.setId(contactDeliveryRuleEntity.getId());
            contactDeliveryRuleMapper.updateById(rule);
        }
    }

    @Override
    public void delteByUk(String outBusinessNo, Integer systemSource) {
        ContactDeliveryRuleEntity contactDeliveryRuleEntity = this.queryByUk(outBusinessNo, systemSource);
        if(contactDeliveryRuleEntity == null){
            return;
        }
        contactDeliveryRuleMapper.deleteById(contactDeliveryRuleEntity.getId());
    }

    @Override
    public List<ContactDeliveryRuleEntity> queryList(ContactDeliveryRuleQuery query) {
        List<WncContactDeliveryRule> wncContactDeliveryRules = contactDeliveryRuleMapper.selectList(new LambdaQueryWrapper<WncContactDeliveryRule>()
                .eq(query.getSystemSource() != null, WncContactDeliveryRule::getSystemSource, query.getSystemSource()));
        if (CollectionUtils.isEmpty(wncContactDeliveryRules)){
            Collections.emptyList();
        }
        return wncContactDeliveryRules.stream().map(WncContactDeliveryRuleConverter::do2Entity).collect(java.util.stream.Collectors.toList());
    }

    @Override
    public List<PopMerchantMonit> queryPopNoDeliveryRuleMonit(Integer adminId) {
        if(adminId == null){
            return Collections.emptyList();
        }
        return contactDeliveryRuleMapper.queryPopNoDeliveryRuleMonit(adminId);
    }
}
