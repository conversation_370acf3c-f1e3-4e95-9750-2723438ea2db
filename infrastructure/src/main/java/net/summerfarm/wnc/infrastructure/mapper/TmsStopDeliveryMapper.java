package net.summerfarm.wnc.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.wnc.infrastructure.model.TmsStopDelivery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 停运时间 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-66 14:40:19
 */
public interface TmsStopDeliveryMapper extends BaseMapper<TmsStopDelivery> {

    List<TmsStopDelivery> queryRecentlyStopByStoreNoList(@Param("storeNoList") List<Integer> storeNoList);
}
