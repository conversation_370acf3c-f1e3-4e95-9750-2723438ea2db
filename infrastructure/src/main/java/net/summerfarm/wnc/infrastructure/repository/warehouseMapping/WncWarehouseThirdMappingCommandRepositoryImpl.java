package net.summerfarm.wnc.infrastructure.repository.warehouseMapping;

import net.summerfarm.wnc.infrastructure.model.warehouseMapping.WncWarehouseThirdMapping;
import net.summerfarm.wnc.infrastructure.mapper.warehouseMapping.WncWarehouseThirdMappingMapper;
import net.summerfarm.wnc.infrastructure.converter.warehouseMapping.WncWarehouseThirdMappingConverter;
import net.summerfarm.wnc.domain.warehouseMapping.repository.WncWarehouseThirdMappingCommandRepository;
import net.summerfarm.wnc.domain.warehouseMapping.entity.WncWarehouseThirdMappingEntity;
import net.summerfarm.wnc.domain.warehouseMapping.param.command.WncWarehouseThirdMappingCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2025-06-11 15:08:31
* @version 1.0
*
*/
@Repository
public class WncWarehouseThirdMappingCommandRepositoryImpl implements WncWarehouseThirdMappingCommandRepository {

    @Autowired
    private WncWarehouseThirdMappingMapper wncWarehouseThirdMappingMapper;
    @Override
    public WncWarehouseThirdMappingEntity insertSelective(WncWarehouseThirdMappingCommandParam param) {
        WncWarehouseThirdMapping wncWarehouseThirdMapping = WncWarehouseThirdMappingConverter.toWncWarehouseThirdMapping(param);
        wncWarehouseThirdMappingMapper.insertSelective(wncWarehouseThirdMapping);
        return WncWarehouseThirdMappingConverter.toWncWarehouseThirdMappingEntity(wncWarehouseThirdMapping);
    }

    @Override
    public int updateSelectiveById(WncWarehouseThirdMappingCommandParam param){
        return wncWarehouseThirdMappingMapper.updateSelectiveById(WncWarehouseThirdMappingConverter.toWncWarehouseThirdMapping(param));
    }


    @Override
    public int remove(Long id) {
        return wncWarehouseThirdMappingMapper.remove(id);
    }
}