package net.summerfarm.wnc.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import net.summerfarm.wnc.domain.preciseDelivery.PreciseDeliveryConfigCommandRepository;
import net.summerfarm.wnc.domain.preciseDelivery.param.PreciseDeliveryConfigCommandParam;
import net.summerfarm.wnc.infrastructure.converter.WncPreciseDeliveryAreaDetailConverter;
import net.summerfarm.wnc.infrastructure.converter.WncPreciseDeliveryConfigConverter;
import net.summerfarm.wnc.infrastructure.converter.WncPreciseDeliveryTimeDetailConverter;
import net.summerfarm.wnc.infrastructure.mapper.WncPreciseDeliveryAreaDetailMapper;
import net.summerfarm.wnc.infrastructure.mapper.WncPreciseDeliveryConfigMapper;
import net.summerfarm.wnc.infrastructure.mapper.WncPreciseDeliveryTimeDetailMapper;
import net.summerfarm.wnc.infrastructure.model.WncPreciseDeliveryAreaDetail;
import net.summerfarm.wnc.infrastructure.model.WncPreciseDeliveryConfig;
import net.summerfarm.wnc.infrastructure.model.WncPreciseDeliveryTimeDetail;
import net.summerfarm.wnc.infrastructure.util.MybatisPlusUtil;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Description:精准送配置仓库操作接口实现
 * date: 2024/1/22 16:36
 *
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class PreciseDeliveryConfigCommandRepositoryImpl implements PreciseDeliveryConfigCommandRepository {

    private final WncPreciseDeliveryConfigMapper wncPreciseDeliveryConfigMapper;
    private final WncPreciseDeliveryAreaDetailMapper wncPreciseDeliveryAreaDetailMapper;
    private final WncPreciseDeliveryTimeDetailMapper wncPreciseDeliveryTimeDetailMapper;

    @Override
    public void save(PreciseDeliveryConfigCommandParam commandParam) {
        if (commandParam == null){
            return;
        }
        WncPreciseDeliveryConfig wncPreciseDeliveryConfig = WncPreciseDeliveryConfigConverter.param2Do(commandParam);
        wncPreciseDeliveryConfigMapper.insert(wncPreciseDeliveryConfig);
        this.insertDetail(commandParam, wncPreciseDeliveryConfig.getId());
    }

    private void insertDetail(PreciseDeliveryConfigCommandParam commandParam, Long configId) {
        List<WncPreciseDeliveryAreaDetail> areaDetails = commandParam.getAreaList().stream().map(WncPreciseDeliveryAreaDetailConverter::param2Do)
                .peek(e -> e.setConfigId(configId)).collect(Collectors.toList());
        List<WncPreciseDeliveryTimeDetail> timeDetails = commandParam.getTimeList().stream().map(WncPreciseDeliveryTimeDetailConverter::param2Do)
                .peek(e -> e.setConfigId(configId)).collect(Collectors.toList());
        MybatisPlusUtil.createBatch(areaDetails, WncPreciseDeliveryAreaDetail.class);
        MybatisPlusUtil.createBatch(timeDetails, WncPreciseDeliveryTimeDetail.class);
    }

    @Override
    public void remove(Long configId) {
        if (configId == null){
            return;
        }
        wncPreciseDeliveryConfigMapper.deleteById(configId);
        this.removeDetail(configId);

    }

    private void removeDetail(Long configId) {
        wncPreciseDeliveryAreaDetailMapper.delete(new LambdaQueryWrapper<WncPreciseDeliveryAreaDetail>().eq(WncPreciseDeliveryAreaDetail::getConfigId, configId));
        wncPreciseDeliveryTimeDetailMapper.delete(new LambdaQueryWrapper<WncPreciseDeliveryTimeDetail>().eq(WncPreciseDeliveryTimeDetail::getConfigId, configId));
    }

    @Override
    public void update(PreciseDeliveryConfigCommandParam commandParam) {
        this.removeDetail(commandParam.getConfigId());
        this.insertDetail(commandParam, commandParam.getConfigId());
        WncPreciseDeliveryConfig wncPreciseDeliveryConfig = WncPreciseDeliveryConfigConverter.param2Do(commandParam);
        wncPreciseDeliveryConfigMapper.updateById(wncPreciseDeliveryConfig);
    }
}
