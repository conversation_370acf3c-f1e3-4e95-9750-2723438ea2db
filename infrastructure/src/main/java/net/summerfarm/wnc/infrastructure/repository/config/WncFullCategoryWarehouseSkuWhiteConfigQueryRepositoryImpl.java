package net.summerfarm.wnc.infrastructure.repository.config;


import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.infrastructure.model.config.WncFullCategoryWarehouseSkuWhiteConfig;
import net.summerfarm.wnc.infrastructure.mapper.config.WncFullCategoryWarehouseSkuWhiteConfigMapper;
import net.summerfarm.wnc.infrastructure.converter.config.WncFullCategoryWarehouseSkuWhiteConfigConverter;
import net.summerfarm.wnc.domain.config.repository.WncFullCategoryWarehouseSkuWhiteConfigQueryRepository;
import net.summerfarm.wnc.domain.config.entity.WncFullCategoryWarehouseSkuWhiteConfigEntity;
import net.summerfarm.wnc.domain.config.param.query.WncFullCategoryWarehouseSkuWhiteConfigQueryParam;
import net.summerfarm.wnc.common.converter.PageInfoConverter;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
*
* <AUTHOR>
* @date 2025-01-06 15:31:26
* @version 1.0
*
*/
@Repository
@Slf4j
public class WncFullCategoryWarehouseSkuWhiteConfigQueryRepositoryImpl implements WncFullCategoryWarehouseSkuWhiteConfigQueryRepository {

    @Autowired
    private WncFullCategoryWarehouseSkuWhiteConfigMapper wncFullCategoryWarehouseSkuWhiteConfigMapper;


    @Override
    public PageInfo<WncFullCategoryWarehouseSkuWhiteConfigEntity> getPage(WncFullCategoryWarehouseSkuWhiteConfigQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<WncFullCategoryWarehouseSkuWhiteConfigEntity> entities = wncFullCategoryWarehouseSkuWhiteConfigMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public WncFullCategoryWarehouseSkuWhiteConfigEntity selectById(Long id) {
        return WncFullCategoryWarehouseSkuWhiteConfigConverter.toWncFullCategoryWarehouseSkuWhiteConfigEntity(wncFullCategoryWarehouseSkuWhiteConfigMapper.selectById(id));
    }


    @Override
    public List<WncFullCategoryWarehouseSkuWhiteConfigEntity> selectByCondition(WncFullCategoryWarehouseSkuWhiteConfigQueryParam param) {
        return WncFullCategoryWarehouseSkuWhiteConfigConverter.toWncFullCategoryWarehouseSkuWhiteConfigEntityList(wncFullCategoryWarehouseSkuWhiteConfigMapper.selectByCondition(param));
    }

    @Override
    public Map<String, List<Integer>> selectWhiteConfigMapBySkusWarehouseNos(List<String> skus, List<Integer> warehouseNos) {
        if(CollectionUtils.isEmpty(skus) || CollectionUtils.isEmpty(warehouseNos)){
            return Collections.emptyMap();
        }
        List<WncFullCategoryWarehouseSkuWhiteConfigEntity> fullCategoryWarehouseSkuWhiteConfigList = this.selectByCondition(WncFullCategoryWarehouseSkuWhiteConfigQueryParam.builder()
                .warehouseNos(warehouseNos)
                .skus(skus)
                .build());
        log.info("代销不入仓T+2配送仓库白名单配置:{}", JSON.toJSONString(fullCategoryWarehouseSkuWhiteConfigList));
        if(CollectionUtils.isEmpty(fullCategoryWarehouseSkuWhiteConfigList)){
            return Collections.emptyMap();
        }

        Map<String, List<Integer>> skuWarehouseMap = fullCategoryWarehouseSkuWhiteConfigList.stream()
                .collect(Collectors.groupingBy(
                        WncFullCategoryWarehouseSkuWhiteConfigEntity::getSku,
                        Collectors.mapping(WncFullCategoryWarehouseSkuWhiteConfigEntity::getWarehouseNo, Collectors.toList())
                ));
        return skuWarehouseMap;
    }

}