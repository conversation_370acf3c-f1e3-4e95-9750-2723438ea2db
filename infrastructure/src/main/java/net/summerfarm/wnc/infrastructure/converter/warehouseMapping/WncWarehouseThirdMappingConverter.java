package net.summerfarm.wnc.infrastructure.converter.warehouseMapping;

import net.summerfarm.wnc.infrastructure.model.warehouseMapping.WncWarehouseThirdMapping;
import net.summerfarm.wnc.domain.warehouseMapping.entity.WncWarehouseThirdMappingEntity;
import net.summerfarm.wnc.domain.warehouseMapping.param.command.WncWarehouseThirdMappingCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2025-06-11 15:08:31
 * @version 1.0
 *
 */
public class WncWarehouseThirdMappingConverter {

    private WncWarehouseThirdMappingConverter() {
        // 无需实现
    }




    public static List<WncWarehouseThirdMappingEntity> toWncWarehouseThirdMappingEntityList(List<WncWarehouseThirdMapping> wncWarehouseThirdMappingList) {
        if (wncWarehouseThirdMappingList == null) {
            return Collections.emptyList();
        }
        List<WncWarehouseThirdMappingEntity> wncWarehouseThirdMappingEntityList = new ArrayList<>();
        for (WncWarehouseThirdMapping wncWarehouseThirdMapping : wncWarehouseThirdMappingList) {
            wncWarehouseThirdMappingEntityList.add(toWncWarehouseThirdMappingEntity(wncWarehouseThirdMapping));
        }
        return wncWarehouseThirdMappingEntityList;
}


    public static WncWarehouseThirdMappingEntity toWncWarehouseThirdMappingEntity(WncWarehouseThirdMapping wncWarehouseThirdMapping) {
        if (wncWarehouseThirdMapping == null) {
             return null;
        }
        WncWarehouseThirdMappingEntity wncWarehouseThirdMappingEntity = new WncWarehouseThirdMappingEntity();
        wncWarehouseThirdMappingEntity.setId(wncWarehouseThirdMapping.getId());
        wncWarehouseThirdMappingEntity.setCreateTime(wncWarehouseThirdMapping.getCreateTime());
        wncWarehouseThirdMappingEntity.setUpdateTime(wncWarehouseThirdMapping.getUpdateTime());
        wncWarehouseThirdMappingEntity.setWarehouseNo(wncWarehouseThirdMapping.getWarehouseNo());
        wncWarehouseThirdMappingEntity.setThirdSourceName(wncWarehouseThirdMapping.getThirdSourceName());
        wncWarehouseThirdMappingEntity.setThirdWarehouseNo(wncWarehouseThirdMapping.getThirdWarehouseNo());
        wncWarehouseThirdMappingEntity.setTenantId(wncWarehouseThirdMapping.getTenantId());
        wncWarehouseThirdMappingEntity.setOpenPlatformAppKey(wncWarehouseThirdMapping.getOpenPlatformAppKey());
        return wncWarehouseThirdMappingEntity;
    }








    public static WncWarehouseThirdMapping toWncWarehouseThirdMapping(WncWarehouseThirdMappingCommandParam param) {
        if (param == null) {
            return null;
        }
        WncWarehouseThirdMapping wncWarehouseThirdMapping = new WncWarehouseThirdMapping();
        wncWarehouseThirdMapping.setId(param.getId());
        wncWarehouseThirdMapping.setCreateTime(param.getCreateTime());
        wncWarehouseThirdMapping.setUpdateTime(param.getUpdateTime());
        wncWarehouseThirdMapping.setWarehouseNo(param.getWarehouseNo());
        wncWarehouseThirdMapping.setThirdSourceName(param.getThirdSourceName());
        wncWarehouseThirdMapping.setThirdWarehouseNo(param.getThirdWarehouseNo());
        wncWarehouseThirdMapping.setTenantId(param.getTenantId());
        wncWarehouseThirdMapping.setOpenPlatformAppKey(param.getOpenPlatformAppKey());
        return wncWarehouseThirdMapping;
    }
}
