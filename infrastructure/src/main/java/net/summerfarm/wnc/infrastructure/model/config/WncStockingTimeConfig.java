package net.summerfarm.wnc.infrastructure.model.config;

import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2025-04-15 14:51:44
 * @version 1.0
 *
 */
@Data
public class WncStockingTimeConfig {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 业务编号
	 */
	private Integer businessNo;

	/**
	 * 租户ID
	 */
	private Long tenantId;

	/**
	 * 履约方式 0：城配履约，1：快递履约
	 */
	private Integer fulfillmentType;

	/**
	 * 配送规则0日配，1非日配
	 */
	private Integer deliveryRules;

	/**
	 * 备货时长天数
	 */
	private Integer stockingTime;



}