package net.summerfarm.wnc.infrastructure.converter;

import net.summerfarm.wnc.domain.warehouse.entity.ConfigEntity;
import net.summerfarm.wnc.infrastructure.model.Config;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2023-06-20
 **/
public class ConfigConverter {
	/**
	 * do2Entity
	 *
	 * @param config
	 * @return
	 */
	public static ConfigEntity do2Entity(Config config) {
		if (Objects.isNull(config)) {
			return null;
		}
		ConfigEntity configEntity = new ConfigEntity();
		configEntity.setId(config.getId());
		configEntity.setKey(config.getKey());
		configEntity.setValue(config.getValue());
		configEntity.setRemark(config.getRemark());
		return configEntity;
	}
}
