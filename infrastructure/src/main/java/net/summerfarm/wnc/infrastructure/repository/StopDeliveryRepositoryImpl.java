package net.summerfarm.wnc.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.common.enums.StopDeliveryEnums;
import net.summerfarm.wnc.domain.fence.entity.StopDeliveryEntity;
import net.summerfarm.wnc.domain.warehouse.StopDeliveryRepository;
import net.summerfarm.wnc.infrastructure.converter.TmsStopDeliveryConverter;
import net.summerfarm.wnc.infrastructure.mapper.TmsStopDeliveryMapper;
import net.summerfarm.wnc.infrastructure.model.TmsStopDelivery;
import net.summerfarm.wnc.infrastructure.util.MybatisPlusUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.sql.Array;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: 停配信息
 * date: 2023/9/21 17:58<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Service
public class StopDeliveryRepositoryImpl implements StopDeliveryRepository {

    @Resource
    private TmsStopDeliveryMapper tmsStopDeliveryMapper;


    @Override
    public List<StopDeliveryEntity> queryRecentlyStopByStoreNoList(List<Integer> storeNoList) {
        if(CollectionUtils.isEmpty(storeNoList)){
            return Collections.emptyList();
        }
        List<TmsStopDelivery> tmsStopDeliveries = tmsStopDeliveryMapper.queryRecentlyStopByStoreNoList(storeNoList);

        return tmsStopDeliveries.stream().map(TmsStopDeliveryConverter::do2Entity).collect(Collectors.toList());
    }

    @Override
    public Map<Integer, StopDeliveryEntity> queryRecentlyStopMapByStoreNos(List<Integer> storeNoList) {
        List<StopDeliveryEntity> stopDeliveryEntities = queryRecentlyStopByStoreNoList(storeNoList);
        return stopDeliveryEntities.stream().collect(Collectors.toMap(StopDeliveryEntity::getStoreNo, Function.identity(), (oldData, newData) -> newData));
    }

    @Override
    public void updateDeleteStatus(List<Integer> storeNoList) {
        if(CollectionUtils.isEmpty(storeNoList)){
            return;
        }

        tmsStopDeliveryMapper.update(null,new LambdaUpdateWrapper<TmsStopDelivery>()
                .in(TmsStopDelivery::getStoreNo,storeNoList)
                .eq(TmsStopDelivery::getDeleteFlag,StopDeliveryEnums.DeleteFlag.Effective.getValue())
                .set(TmsStopDelivery::getDeleteFlag, StopDeliveryEnums.DeleteFlag.Delete.getValue()));
    }

    @Override
    public void batchSave(List<StopDeliveryEntity> stopDeliveryEntities) {
        if(CollectionUtils.isEmpty(stopDeliveryEntities)){
            return;
        }
        List<TmsStopDelivery> stopDeliveries = new ArrayList<>();

        for (StopDeliveryEntity stopDeliveryEntity : stopDeliveryEntities) {
            TmsStopDelivery tmsStopDelivery = new TmsStopDelivery();

            tmsStopDelivery.setStoreNo(stopDeliveryEntity.getStoreNo());
            tmsStopDelivery.setShutdownStartTime(stopDeliveryEntity.getShutdownStartTime());
            tmsStopDelivery.setShutdownEndTime(stopDeliveryEntity.getShutdownEndTime());

            stopDeliveries.add(tmsStopDelivery);
        }

        MybatisPlusUtil.createBatch(stopDeliveries, TmsStopDelivery.class);
    }
}
