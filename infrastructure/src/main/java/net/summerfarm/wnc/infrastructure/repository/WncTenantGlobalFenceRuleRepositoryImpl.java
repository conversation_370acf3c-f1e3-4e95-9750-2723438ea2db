package net.summerfarm.wnc.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.summerfarm.wnc.domain.fence.WncTenantGlobalFenceRuleRepository;
import net.summerfarm.wnc.domain.warehouse.entity.WncTenantGlobalFenceRuleEntity;
import net.summerfarm.wnc.infrastructure.converter.WncTenantGlobalFenceRuleConverter;
import net.summerfarm.wnc.infrastructure.mapper.WncTenantGlobalFenceRuleMapper;
import net.summerfarm.wnc.infrastructure.model.WncTenantGlobalFenceRule;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Description: <br/>
 * date: 2023/4/7 13:51<br/>
 *
 * <AUTHOR> />
 */
@Service
public class WncTenantGlobalFenceRuleRepositoryImpl implements WncTenantGlobalFenceRuleRepository {

    @Resource
    private WncTenantGlobalFenceRuleMapper wncTenantGlobalFenceRuleMapper;

    @Override
    public WncTenantGlobalFenceRuleEntity queryUk(Long tenantId) {
        WncTenantGlobalFenceRule wncTenantGlobalFenceRule = wncTenantGlobalFenceRuleMapper.selectOne(new LambdaQueryWrapper<WncTenantGlobalFenceRule>()
                .eq(WncTenantGlobalFenceRule::getTenantId, tenantId)
        );

        return WncTenantGlobalFenceRuleConverter.model2Entity(wncTenantGlobalFenceRule);
    }

    @Override
    public void save(WncTenantGlobalFenceRuleEntity wncTenantGlobalFenceRuleEntity) {
        WncTenantGlobalFenceRule wncTenantGlobalFenceRule = new WncTenantGlobalFenceRule();
        wncTenantGlobalFenceRule.setTenantId(wncTenantGlobalFenceRuleEntity.getTenantId());
        wncTenantGlobalFenceRule.setGlobalDeliveryRule(wncTenantGlobalFenceRuleEntity.getGlobalDeliveryRule());

        wncTenantGlobalFenceRuleMapper.insert(wncTenantGlobalFenceRule);
        wncTenantGlobalFenceRuleEntity.setId(wncTenantGlobalFenceRule.getId());
    }

    @Override
    public void updateGlobalFenceRule(WncTenantGlobalFenceRuleEntity wncTenantGlobalFenceRuleEntity) {
        wncTenantGlobalFenceRuleMapper.updateById(WncTenantGlobalFenceRuleConverter.entity2Model(wncTenantGlobalFenceRuleEntity));
    }
}
