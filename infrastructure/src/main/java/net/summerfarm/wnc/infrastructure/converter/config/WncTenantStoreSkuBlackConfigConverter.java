package net.summerfarm.wnc.infrastructure.converter.config;

import net.summerfarm.wnc.domain.config.entity.TenantStoreSkuBlackConfigEntity;
import net.summerfarm.wnc.infrastructure.model.WncTenantStoreSkuBlackConfig;
import net.summerfarm.wnc.domain.config.param.command.WncTenantStoreSkuBlackConfigCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-09-12 14:00:16
 * @version 1.0
 *
 */
public class WncTenantStoreSkuBlackConfigConverter {

    private WncTenantStoreSkuBlackConfigConverter() {
        // 无需实现
    }




    public static List<TenantStoreSkuBlackConfigEntity> toWncTenantStoreSkuBlackConfigEntityList(List<WncTenantStoreSkuBlackConfig> wncTenantStoreSkuBlackConfigList) {
        if (wncTenantStoreSkuBlackConfigList == null) {
            return Collections.emptyList();
        }
        List<TenantStoreSkuBlackConfigEntity> wncTenantStoreSkuBlackConfigEntityList = new ArrayList<>();
        for (WncTenantStoreSkuBlackConfig wncTenantStoreSkuBlackConfig : wncTenantStoreSkuBlackConfigList) {
            wncTenantStoreSkuBlackConfigEntityList.add(toWncTenantStoreSkuBlackConfigEntity(wncTenantStoreSkuBlackConfig));
        }
        return wncTenantStoreSkuBlackConfigEntityList;
}


    public static TenantStoreSkuBlackConfigEntity toWncTenantStoreSkuBlackConfigEntity(WncTenantStoreSkuBlackConfig wncTenantStoreSkuBlackConfig) {
        if (wncTenantStoreSkuBlackConfig == null) {
             return null;
        }
        TenantStoreSkuBlackConfigEntity wncTenantStoreSkuBlackConfigEntity = new TenantStoreSkuBlackConfigEntity();
        wncTenantStoreSkuBlackConfigEntity.setId(wncTenantStoreSkuBlackConfig.getId());
        wncTenantStoreSkuBlackConfigEntity.setCreateTime(wncTenantStoreSkuBlackConfig.getCreateTime());
        wncTenantStoreSkuBlackConfigEntity.setUpdateTime(wncTenantStoreSkuBlackConfig.getUpdateTime());
        wncTenantStoreSkuBlackConfigEntity.setTenantId(wncTenantStoreSkuBlackConfig.getTenantId());
        wncTenantStoreSkuBlackConfigEntity.setStoreNo(wncTenantStoreSkuBlackConfig.getStoreNo());
        wncTenantStoreSkuBlackConfigEntity.setSku(wncTenantStoreSkuBlackConfig.getSku());
        return wncTenantStoreSkuBlackConfigEntity;
    }








    public static WncTenantStoreSkuBlackConfig toWncTenantStoreSkuBlackConfig(WncTenantStoreSkuBlackConfigCommandParam param) {
        if (param == null) {
            return null;
        }
        WncTenantStoreSkuBlackConfig wncTenantStoreSkuBlackConfig = new WncTenantStoreSkuBlackConfig();
        wncTenantStoreSkuBlackConfig.setId(param.getId());
        wncTenantStoreSkuBlackConfig.setCreateTime(param.getCreateTime());
        wncTenantStoreSkuBlackConfig.setUpdateTime(param.getUpdateTime());
        wncTenantStoreSkuBlackConfig.setTenantId(param.getTenantId());
        wncTenantStoreSkuBlackConfig.setStoreNo(param.getStoreNo());
        wncTenantStoreSkuBlackConfig.setSku(param.getSku());
        return wncTenantStoreSkuBlackConfig;
    }
}
