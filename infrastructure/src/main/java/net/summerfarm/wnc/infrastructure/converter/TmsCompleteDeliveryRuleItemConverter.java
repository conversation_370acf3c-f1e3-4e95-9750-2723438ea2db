package net.summerfarm.wnc.infrastructure.converter;


import net.summerfarm.wnc.common.enums.DeliveryAlertEnums;
import net.summerfarm.wnc.domain.alert.entity.DeliveryAlertRuleItemVO;
import net.summerfarm.wnc.infrastructure.model.TmsCompleteDeliveryRuleItem;

/**
 * Description:配送提醒规则项转换器
 * date: 2023/3/21 19:14
 *
 * <AUTHOR>
 */
public class TmsCompleteDeliveryRuleItemConverter {

    public static TmsCompleteDeliveryRuleItem vo2do(DeliveryAlertRuleItemVO deliveryAlertRuleItemVO){
        if (deliveryAlertRuleItemVO == null){
            return null;
        }
        TmsCompleteDeliveryRuleItem tmsCompleteDeliveryRuleItem = new TmsCompleteDeliveryRuleItem();
        tmsCompleteDeliveryRuleItem.setId(deliveryAlertRuleItemVO.getId());
        tmsCompleteDeliveryRuleItem.setRuleId(deliveryAlertRuleItemVO.getRuleId());
        tmsCompleteDeliveryRuleItem.setStoreNo(deliveryAlertRuleItemVO.getStoreNo());
        tmsCompleteDeliveryRuleItem.setChannel(deliveryAlertRuleItemVO.getChannel().getValue());
        tmsCompleteDeliveryRuleItem.setType(deliveryAlertRuleItemVO.getType().getValue());
        tmsCompleteDeliveryRuleItem.setBizNo(deliveryAlertRuleItemVO.getBizNo());
        tmsCompleteDeliveryRuleItem.setBizName(deliveryAlertRuleItemVO.getBizName());
        tmsCompleteDeliveryRuleItem.setBeginTime(deliveryAlertRuleItemVO.getBeginTime());
        tmsCompleteDeliveryRuleItem.setEndTime(deliveryAlertRuleItemVO.getEndTime());
        tmsCompleteDeliveryRuleItem.setCreateTime(deliveryAlertRuleItemVO.getCreateTime());
        tmsCompleteDeliveryRuleItem.setUpdateTime(deliveryAlertRuleItemVO.getUpdateTime());

        return tmsCompleteDeliveryRuleItem;
    }

    public static DeliveryAlertRuleItemVO do2vo(TmsCompleteDeliveryRuleItem tmsCompleteDeliveryRuleItem){
        if (tmsCompleteDeliveryRuleItem == null){
            return null;
        }
        DeliveryAlertRuleItemVO deliveryAlertRuleItemVO = new DeliveryAlertRuleItemVO();
        deliveryAlertRuleItemVO.setId(tmsCompleteDeliveryRuleItem.getId());
        deliveryAlertRuleItemVO.setRuleId(tmsCompleteDeliveryRuleItem.getRuleId());
        deliveryAlertRuleItemVO.setStoreNo(tmsCompleteDeliveryRuleItem.getStoreNo());
        DeliveryAlertEnums.Channel channel = DeliveryAlertEnums.Channel.getChannelByValue(tmsCompleteDeliveryRuleItem.getChannel());
        deliveryAlertRuleItemVO.setChannel(channel);
        DeliveryAlertEnums.Type type = DeliveryAlertEnums.Type.getTypeByValue(tmsCompleteDeliveryRuleItem.getType());
        deliveryAlertRuleItemVO.setType(type);
        deliveryAlertRuleItemVO.setBizNo(tmsCompleteDeliveryRuleItem.getBizNo());
        deliveryAlertRuleItemVO.setBizName(tmsCompleteDeliveryRuleItem.getBizName());
        deliveryAlertRuleItemVO.setBeginTime(tmsCompleteDeliveryRuleItem.getBeginTime());
        deliveryAlertRuleItemVO.setEndTime(tmsCompleteDeliveryRuleItem.getEndTime());
        deliveryAlertRuleItemVO.setCreateTime(tmsCompleteDeliveryRuleItem.getCreateTime());
        deliveryAlertRuleItemVO.setUpdateTime(tmsCompleteDeliveryRuleItem.getUpdateTime());

        return deliveryAlertRuleItemVO;
    }
}
