package net.summerfarm.wnc.infrastructure.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 仓库收货标准表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-87 16:31:18
 */
@Getter
@Setter
@TableName("warehouse_take_standard")
public class WarehouseTakeStandard implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 0国产鲜果 1国产非鲜果 2进口鲜果 3进口非鲜果
     */
    @TableField("standard_type")
    private Integer standardType;

    /**
     * 仓储区域
     */
    @TableField("storage_location")
    private Integer storageLocation;

    /**
     * 库存仓编号
     */
    @TableField("warehouse_no")
    private Integer warehouseNo;

    /**
     * 证明类型：0质检报告 1农残检测报告 2消毒记录 3核酸检测 4报关证明 5监管仓证明
     */
    @TableField("prove_type")
    private Integer proveType;

    /**
     * 创建人id
     */
    @TableField("create_admin_id")
    private Integer createAdminId;


}
