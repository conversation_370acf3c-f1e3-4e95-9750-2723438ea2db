package net.summerfarm.wnc.infrastructure.converter.fence;

import net.summerfarm.wnc.infrastructure.model.fence.FenceChannelBusinessWhiteConfig;
import net.summerfarm.wnc.domain.fence.entity.FenceChannelBusinessWhiteConfigEntity;
import net.summerfarm.wnc.domain.fence.param.command.FenceChannelBusinessWhiteConfigCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-10-12 16:29:33
 * @version 1.0
 *
 */
public class FenceChannelBusinessWhiteConfigConverter {

    private FenceChannelBusinessWhiteConfigConverter() {
        // 无需实现
    }




    public static List<FenceChannelBusinessWhiteConfigEntity> toFenceChannelBusinessWhiteConfigEntityList(List<FenceChannelBusinessWhiteConfig> fenceChannelBusinessWhiteConfigList) {
        if (fenceChannelBusinessWhiteConfigList == null) {
            return Collections.emptyList();
        }
        List<FenceChannelBusinessWhiteConfigEntity> fenceChannelBusinessWhiteConfigEntityList = new ArrayList<>();
        for (FenceChannelBusinessWhiteConfig fenceChannelBusinessWhiteConfig : fenceChannelBusinessWhiteConfigList) {
            fenceChannelBusinessWhiteConfigEntityList.add(toFenceChannelBusinessWhiteConfigEntity(fenceChannelBusinessWhiteConfig));
        }
        return fenceChannelBusinessWhiteConfigEntityList;
}


    public static FenceChannelBusinessWhiteConfigEntity toFenceChannelBusinessWhiteConfigEntity(FenceChannelBusinessWhiteConfig fenceChannelBusinessWhiteConfig) {
        if (fenceChannelBusinessWhiteConfig == null) {
             return null;
        }
        FenceChannelBusinessWhiteConfigEntity fenceChannelBusinessWhiteConfigEntity = new FenceChannelBusinessWhiteConfigEntity();
        fenceChannelBusinessWhiteConfigEntity.setId(fenceChannelBusinessWhiteConfig.getId());
        fenceChannelBusinessWhiteConfigEntity.setCreateTime(fenceChannelBusinessWhiteConfig.getCreateTime());
        fenceChannelBusinessWhiteConfigEntity.setUpdateTime(fenceChannelBusinessWhiteConfig.getUpdateTime());
        fenceChannelBusinessWhiteConfigEntity.setFenceId(fenceChannelBusinessWhiteConfig.getFenceId());
        fenceChannelBusinessWhiteConfigEntity.setOrderChannelType(fenceChannelBusinessWhiteConfig.getOrderChannelType());
        fenceChannelBusinessWhiteConfigEntity.setTenantId(fenceChannelBusinessWhiteConfig.getTenantId());
        fenceChannelBusinessWhiteConfigEntity.setScopeChannelBusinessId(fenceChannelBusinessWhiteConfig.getScopeChannelBusinessId());
        fenceChannelBusinessWhiteConfigEntity.setScopeChannelBusinessName(fenceChannelBusinessWhiteConfig.getScopeChannelBusinessName());
        return fenceChannelBusinessWhiteConfigEntity;
    }








    public static FenceChannelBusinessWhiteConfig toFenceChannelBusinessWhiteConfig(FenceChannelBusinessWhiteConfigCommandParam param) {
        if (param == null) {
            return null;
        }
        FenceChannelBusinessWhiteConfig fenceChannelBusinessWhiteConfig = new FenceChannelBusinessWhiteConfig();
        fenceChannelBusinessWhiteConfig.setId(param.getId());
        fenceChannelBusinessWhiteConfig.setCreateTime(param.getCreateTime());
        fenceChannelBusinessWhiteConfig.setUpdateTime(param.getUpdateTime());
        fenceChannelBusinessWhiteConfig.setFenceId(param.getFenceId());
        fenceChannelBusinessWhiteConfig.setOrderChannelType(param.getOrderChannelType());
        fenceChannelBusinessWhiteConfig.setTenantId(param.getTenantId());
        fenceChannelBusinessWhiteConfig.setScopeChannelBusinessId(param.getScopeChannelBusinessId());
        fenceChannelBusinessWhiteConfig.setScopeChannelBusinessName(param.getScopeChannelBusinessName());
        return fenceChannelBusinessWhiteConfig;
    }
}
