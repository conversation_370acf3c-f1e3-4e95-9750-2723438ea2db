package net.summerfarm.wnc.infrastructure.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 区域精准送配置-时效详情
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-18 17:24:11
 */
@Getter
@Setter
@TableName("wnc_precise_delivery_time_detail")
public class WncPreciseDeliveryTimeDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("id")
    private Long id;

    /**
     * 配置ID
     */
    @TableField("config_id")
    private Long configId;

    /**
     * 开始时间
     */
    @TableField("begin_time")
    private LocalTime beginTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    private LocalTime endTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;


}
