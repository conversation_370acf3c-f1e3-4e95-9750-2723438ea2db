package net.summerfarm.wnc.infrastructure.repository;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.common.base.WncAssert;
import net.summerfarm.wnc.common.query.fence.WncWarehouseStorageFenceRuleQuery;
import net.summerfarm.wnc.domain.fence.WncWarehouseStorageFenceRuleRepository;
import net.summerfarm.wnc.domain.warehouse.entity.WncWarehouseStorageFenceRuleEntity;
import net.summerfarm.wnc.infrastructure.converter.WncWarehouseStorageFenceRuleConverter;
import net.summerfarm.wnc.infrastructure.mapper.WncWarehouseStorageFenceRuleMapper;
import net.summerfarm.wnc.infrastructure.model.WncWarehouseStorageFenceRule;
import net.summerfarm.wnc.infrastructure.util.PageInfoHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/4/6 17:18<br/>
 *
 * <AUTHOR> />
 */
@Service
public class WncWarehouseStorageFenceRuleRepositoryImpl implements WncWarehouseStorageFenceRuleRepository {

    @Resource
    private WncWarehouseStorageFenceRuleMapper wncWarehouseStorageFenceRuleMapper;

    @Override
    public WncWarehouseStorageFenceRuleEntity queryByUk(WncWarehouseStorageFenceRuleQuery wncWarehouseStorageFenceRuleQuery) {
        WncAssert.notNull(wncWarehouseStorageFenceRuleQuery.getTenantId(),"tenantId不能为空");
        WncAssert.hasText(wncWarehouseStorageFenceRuleQuery.getCity(),"city不能为空");

        WncWarehouseStorageFenceRule wncWarehouseStorageFenceRule = wncWarehouseStorageFenceRuleMapper.selectOne(new LambdaQueryWrapper<WncWarehouseStorageFenceRule>()
                .eq(WncWarehouseStorageFenceRule::getTenantId,wncWarehouseStorageFenceRuleQuery.getTenantId())
                .eq(WncWarehouseStorageFenceRule::getCity,wncWarehouseStorageFenceRuleQuery.getCity())
                .eq(StringUtils.isNoneBlank(wncWarehouseStorageFenceRuleQuery.getArea()),WncWarehouseStorageFenceRule::getArea,wncWarehouseStorageFenceRuleQuery.getArea())
                .last("limit 1")
        );

        return WncWarehouseStorageFenceRuleConverter.model2Entity(wncWarehouseStorageFenceRule);
    }

    @Override
    public List<WncWarehouseStorageFenceRuleEntity> queryList(WncWarehouseStorageFenceRuleQuery wncWarehouseStorageFenceRuleQuery) {
        List<WncWarehouseStorageFenceRule> wncWarehouseStorageFenceRules = wncWarehouseStorageFenceRuleMapper.selectList(new LambdaQueryWrapper<WncWarehouseStorageFenceRule>()
                .eq(wncWarehouseStorageFenceRuleQuery.getTenantId() != null,WncWarehouseStorageFenceRule::getTenantId, wncWarehouseStorageFenceRuleQuery.getTenantId())
                .in(CollectionUtils.isNotEmpty(wncWarehouseStorageFenceRuleQuery.getCitys()),WncWarehouseStorageFenceRule::getCity, wncWarehouseStorageFenceRuleQuery.getCitys())
                .in(CollectionUtils.isNotEmpty(wncWarehouseStorageFenceRuleQuery.getAreas()),WncWarehouseStorageFenceRule::getArea, wncWarehouseStorageFenceRuleQuery.getAreas())
        );

        return wncWarehouseStorageFenceRules.stream().map(WncWarehouseStorageFenceRuleConverter::model2Entity).collect(Collectors.toList());
    }

    @Override
    public void update(WncWarehouseStorageFenceRuleEntity wncWarehouseStorageFenceRuleEntity) {
        wncWarehouseStorageFenceRuleMapper.updateById(WncWarehouseStorageFenceRuleConverter.entity2Model(wncWarehouseStorageFenceRuleEntity));
    }

    @Override
    public void save(WncWarehouseStorageFenceRuleEntity wncWarehouseStorageFenceRuleEntitySave) {
        wncWarehouseStorageFenceRuleMapper.insert(WncWarehouseStorageFenceRuleConverter.entity2Model(wncWarehouseStorageFenceRuleEntitySave));
    }

    @Override
    public PageInfo<WncWarehouseStorageFenceRuleEntity> queryPage(WncWarehouseStorageFenceRuleQuery wncWarehouseStorageFenceRuleQuery) {
        PageHelper.startPage(wncWarehouseStorageFenceRuleQuery.getPageIndex(), wncWarehouseStorageFenceRuleQuery.getPageSize());
        List<WncWarehouseStorageFenceRule> wncWarehouseStorageFenceRules = wncWarehouseStorageFenceRuleMapper.selectList(new LambdaQueryWrapper<WncWarehouseStorageFenceRule>()
                .eq(wncWarehouseStorageFenceRuleQuery.getTenantId() != null, WncWarehouseStorageFenceRule::getTenantId, wncWarehouseStorageFenceRuleQuery.getTenantId())
                .in(!CollectionUtils.isEmpty(wncWarehouseStorageFenceRuleQuery.getProvinces()), WncWarehouseStorageFenceRule::getProvince, wncWarehouseStorageFenceRuleQuery.getProvinces())
                .in(!CollectionUtils.isEmpty(wncWarehouseStorageFenceRuleQuery.getCitys()), WncWarehouseStorageFenceRule::getCity, wncWarehouseStorageFenceRuleQuery.getCitys())
                .in(!CollectionUtils.isEmpty(wncWarehouseStorageFenceRuleQuery.getAreas()), WncWarehouseStorageFenceRule::getArea, wncWarehouseStorageFenceRuleQuery.getAreas())
                .like(StringUtils.isNotBlank(wncWarehouseStorageFenceRuleQuery.getWarehouseName()),WncWarehouseStorageFenceRule::getWarehouseNameList,wncWarehouseStorageFenceRuleQuery.getWarehouseName())
                .orderByDesc(WncWarehouseStorageFenceRule::getUpdateTime)
        );

        PageInfo<WncWarehouseStorageFenceRule> pageInfo = PageInfoHelper.createPageInfo(wncWarehouseStorageFenceRules);
        PageInfo<WncWarehouseStorageFenceRuleEntity> resultPageInfo = new PageInfo();
        BeanUtil.copyProperties(pageInfo,resultPageInfo);

        if(CollectionUtils.isEmpty(pageInfo.getList())){
            return resultPageInfo;
        }
        resultPageInfo.setList(pageInfo.getList().stream().map(WncWarehouseStorageFenceRuleConverter::model2Entity).collect(Collectors.toList()));
        return resultPageInfo;
    }

    @Override
    public WncWarehouseStorageFenceRuleEntity queryById(Long id) {
        return WncWarehouseStorageFenceRuleConverter.model2Entity(wncWarehouseStorageFenceRuleMapper.selectById(id));
    }

    @Override
    public void removeByIdList(List<Long> needDeleteIdList) {
        if(CollectionUtils.isEmpty(needDeleteIdList)){
            return;
        }
        wncWarehouseStorageFenceRuleMapper.deleteBatchIds(needDeleteIdList);
    }

    @Override
    public void updateList(List<WncWarehouseStorageFenceRuleEntity> needUpdateWarehouseStorageFenceRuleList) {
        List<WncWarehouseStorageFenceRule> wncWarehouseStorageFenceRules = needUpdateWarehouseStorageFenceRuleList.stream().map(WncWarehouseStorageFenceRuleConverter::entity2Model).collect(Collectors.toList());
        for (WncWarehouseStorageFenceRule wncWarehouseStorageFenceRule : wncWarehouseStorageFenceRules) {
            wncWarehouseStorageFenceRule.setWarehouseNameList(wncWarehouseStorageFenceRule.getConflictWarehouseJson());
            wncWarehouseStorageFenceRuleMapper.updateById(wncWarehouseStorageFenceRule);
        }
    }
}
