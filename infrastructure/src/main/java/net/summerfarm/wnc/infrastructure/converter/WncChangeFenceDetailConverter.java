package net.summerfarm.wnc.infrastructure.converter;

import net.summerfarm.wnc.common.enums.FenceChangeTaskDetailEnums;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskOrderEntity;
import net.summerfarm.wnc.infrastructure.model.WncFenceChangeTaskDetail;


/**
 * Description:围栏切仓任务明细转换器
 * date: 2023/8/25 16:15
 *
 * <AUTHOR>
 */
public class WncChangeFenceDetailConverter {

    public static WncFenceChangeTaskDetail entity2Do(FenceChangeTaskOrderEntity fenceChangeTaskOrderEntity){
        if(fenceChangeTaskOrderEntity == null){
            return null;
        }
        WncFenceChangeTaskDetail wncFenceChangeTaskDetail = new WncFenceChangeTaskDetail();
        wncFenceChangeTaskDetail.setId(fenceChangeTaskOrderEntity.getId());
        wncFenceChangeTaskDetail.setTaskId(fenceChangeTaskOrderEntity.getTaskId());
        wncFenceChangeTaskDetail.setOuterOrderId(fenceChangeTaskOrderEntity.getOuterOrderId());
        if (fenceChangeTaskOrderEntity.getSource() != null){
            wncFenceChangeTaskDetail.setSource(fenceChangeTaskOrderEntity.getSource().getValue());
        }
        wncFenceChangeTaskDetail.setOuterContactId(fenceChangeTaskOrderEntity.getOuterContactId());
        wncFenceChangeTaskDetail.setDeliveryTime(fenceChangeTaskOrderEntity.getDeliveryTime());
        wncFenceChangeTaskDetail.setOuterClientId(fenceChangeTaskOrderEntity.getOuterClientId());
        wncFenceChangeTaskDetail.setOuterClientName(fenceChangeTaskOrderEntity.getOuterClientName());
        if (fenceChangeTaskOrderEntity.getStatus() != null){
            wncFenceChangeTaskDetail.setStatus(fenceChangeTaskOrderEntity.getStatus().getValue());
        }
        wncFenceChangeTaskDetail.setRemark(fenceChangeTaskOrderEntity.getRemark());
        wncFenceChangeTaskDetail.setFulfillConfirmTime(fenceChangeTaskOrderEntity.getFulfillConfirmTime());
        wncFenceChangeTaskDetail.setCreateTime(fenceChangeTaskOrderEntity.getCreateTime());
        wncFenceChangeTaskDetail.setUpdateTime(fenceChangeTaskOrderEntity.getUpdateTime());
        return wncFenceChangeTaskDetail;
    }

    public static FenceChangeTaskOrderEntity do2Entity(WncFenceChangeTaskDetail wncFenceChangeTaskDetail){
        if(wncFenceChangeTaskDetail == null){
            return null;
        }
        FenceChangeTaskOrderEntity fenceChangeTaskOrderEntity = new FenceChangeTaskOrderEntity();
        fenceChangeTaskOrderEntity.setId(wncFenceChangeTaskDetail.getId());
        fenceChangeTaskOrderEntity.setTaskId(wncFenceChangeTaskDetail.getTaskId());
        fenceChangeTaskOrderEntity.setOuterOrderId(wncFenceChangeTaskDetail.getOuterOrderId());
        fenceChangeTaskOrderEntity.setSource(FenceChangeTaskDetailEnums.Source.getSourceByValue(wncFenceChangeTaskDetail.getSource()));
        fenceChangeTaskOrderEntity.setOuterContactId(wncFenceChangeTaskDetail.getOuterContactId());
        fenceChangeTaskOrderEntity.setDeliveryTime(wncFenceChangeTaskDetail.getDeliveryTime());
        fenceChangeTaskOrderEntity.setOuterClientId(wncFenceChangeTaskDetail.getOuterClientId());
        fenceChangeTaskOrderEntity.setOuterClientName(wncFenceChangeTaskDetail.getOuterClientName());
        fenceChangeTaskOrderEntity.setStatus(FenceChangeTaskDetailEnums.Status.getStatusByValue(wncFenceChangeTaskDetail.getStatus()));
        fenceChangeTaskOrderEntity.setRemark(wncFenceChangeTaskDetail.getRemark());
        fenceChangeTaskOrderEntity.setFulfillConfirmTime(wncFenceChangeTaskDetail.getFulfillConfirmTime());
        fenceChangeTaskOrderEntity.setCreateTime(wncFenceChangeTaskDetail.getCreateTime());
        fenceChangeTaskOrderEntity.setUpdateTime(wncFenceChangeTaskDetail.getUpdateTime());
        return fenceChangeTaskOrderEntity;
    }
}
