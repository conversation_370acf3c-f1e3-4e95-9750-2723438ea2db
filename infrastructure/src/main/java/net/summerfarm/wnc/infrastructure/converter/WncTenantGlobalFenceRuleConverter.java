package net.summerfarm.wnc.infrastructure.converter;

import net.summerfarm.wnc.domain.warehouse.entity.WncTenantGlobalFenceRuleEntity;
import net.summerfarm.wnc.infrastructure.model.WncTenantGlobalFenceRule;

/**
 * Description: <br/>
 * date: 2023/4/7 13:52<br/>
 *
 * <AUTHOR> />
 */
public class WncTenantGlobalFenceRuleConverter {

    public static WncTenantGlobalFenceRuleEntity model2Entity(WncTenantGlobalFenceRule wncTenantGlobalFenceRule){
        if(wncTenantGlobalFenceRule == null){
            return null;
        }

        WncTenantGlobalFenceRuleEntity wncTenantGlobalFenceRuleEntity = new WncTenantGlobalFenceRuleEntity();
        wncTenantGlobalFenceRuleEntity.setId(wncTenantGlobalFenceRule.getId());
        wncTenantGlobalFenceRuleEntity.setCreateTime(wncTenantGlobalFenceRule.getCreateTime());
        wncTenantGlobalFenceRuleEntity.setUpdateTime(wncTenantGlobalFenceRule.getUpdateTime());
        wncTenantGlobalFenceRuleEntity.setGlobalDeliveryRule(wncTenantGlobalFenceRule.getGlobalDeliveryRule());
        wncTenantGlobalFenceRuleEntity.setTenantId(wncTenantGlobalFenceRule.getTenantId());

        return wncTenantGlobalFenceRuleEntity;
    }

    public static WncTenantGlobalFenceRule entity2Model(WncTenantGlobalFenceRuleEntity wncTenantGlobalFenceRuleEntity){
        if(wncTenantGlobalFenceRuleEntity == null){
            return null;
        }

        WncTenantGlobalFenceRule wncTenantGlobalFenceRule = new WncTenantGlobalFenceRule();
        wncTenantGlobalFenceRule.setId(wncTenantGlobalFenceRuleEntity.getId());
        wncTenantGlobalFenceRule.setCreateTime(wncTenantGlobalFenceRuleEntity.getCreateTime());
        wncTenantGlobalFenceRule.setUpdateTime(wncTenantGlobalFenceRuleEntity.getUpdateTime());
        wncTenantGlobalFenceRule.setGlobalDeliveryRule(wncTenantGlobalFenceRuleEntity.getGlobalDeliveryRule());
        wncTenantGlobalFenceRule.setTenantId(wncTenantGlobalFenceRuleEntity.getTenantId());

        return wncTenantGlobalFenceRule;
    }
}
