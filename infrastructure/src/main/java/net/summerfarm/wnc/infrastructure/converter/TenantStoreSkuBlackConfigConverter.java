package net.summerfarm.wnc.infrastructure.converter;

import net.summerfarm.wnc.domain.config.entity.TenantStoreSkuBlackConfigEntity;
import net.summerfarm.wnc.infrastructure.model.WncTenantStoreSkuBlackConfig;

/**
 * Description: 转换类<br/>
 * date: 2024/8/26 16:01<br/>
 *
 * <AUTHOR> />
 */
public class TenantStoreSkuBlackConfigConverter {
    
    public static TenantStoreSkuBlackConfigEntity convert(WncTenantStoreSkuBlackConfig model) {
        if(model == null){
            return null;            
        }

        TenantStoreSkuBlackConfigEntity entity = new TenantStoreSkuBlackConfigEntity();

        entity.setId(model.getId());
        entity.setCreateTime(model.getCreateTime());
        entity.setUpdateTime(model.getUpdateTime());
        entity.setTenantId(model.getTenantId());
        entity.setStoreNo(model.getStoreNo());
        entity.setSku(model.getSku());

        return entity;
    }

    public static WncTenantStoreSkuBlackConfig convert(TenantStoreSkuBlackConfigEntity entity) {
        if(entity == null){
            return null;
        }

        WncTenantStoreSkuBlackConfig model = new WncTenantStoreSkuBlackConfig();

        model.setId(entity.getId());
        model.setCreateTime(entity.getCreateTime());
        model.setUpdateTime(entity.getUpdateTime());
        model.setTenantId(entity.getTenantId());
        model.setStoreNo(entity.getStoreNo());
        model.setSku(entity.getSku());

        return model;
    }
}
