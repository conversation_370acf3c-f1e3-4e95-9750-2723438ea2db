package net.summerfarm.wnc.infrastructure.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 客户和高德POI距离表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-349 11:49:14
 */
@Getter
@Setter
@TableName("wnc_contact_gd_poi_distance")
public class WncContactGdPoiDistance implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 客户ID
     */
    @TableField("contact_id")
    private Long contactId;

    /**
     * 客户POI
     */
    @TableField("contact_poi")
    private String contactPoi;

    /**
     * 高德POI
     */
    @TableField("gd_poi")
    private String gdPoi;

    /**
     * 客户和高德POI距离差
     */
    @TableField("distance")
    private BigDecimal distance;

    /**
     * 高德省
     */
    @TableField("gd_province")
    private String gdProvince;

    /**
     * 高德城市
     */
    @TableField("gd_city")
    private String gdCity;

    /**
     * 高德区域
     */
    @TableField("gd_area")
    private String gdArea;

    /**
     * 高德详细地址
     */
    @TableField("gd_address")
    private String gdAddress;
}
