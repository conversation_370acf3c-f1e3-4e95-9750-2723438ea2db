package net.summerfarm.wnc.infrastructure.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 物流信息配置(截止2022年06月08号,只有调拨配置使用,没有其他类型)
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-335 15:19:01
 */
@Getter
@Setter
@TableName("warehouse_logistics_config")
public class WarehouseLogisticsConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键、自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 类型：0、调拨-仓到仓 1、干线车-仓到配送中心 2、配送车-配送中心到城市
     */
    @TableField("`type`")
    private Integer type;

    /**
     * 状态：0、失效 1、有效
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 仓库编号
     */
    @TableField("warehouse_no")
    private Integer warehouseNo;

    /**
     * 物流中心编号(配送仓编号)
     */
    @TableField("store_no")
    private Integer storeNo;

    /**
     * 城市编号
     */
    @TableField("area_no")
    private Integer areaNo;

    /**
     * 调拨到仓
     */
    @TableField("allocation_warehouse_no")
    private Integer allocationWarehouseNo;

    /**
     * 周期类型：0、天 1、周 2、月
     */
    @TableField("cycle_type")
    private Integer cycleType;

    /**
     * 配送时间：0、每天 1、每周 2、每月
     */
    @TableField("logistics_time")
    private Integer logisticsTime;

    /**
     * 出库即售/是否次日达：0、开启/是 1、关闭/否
     */
    @TableField("next_day_arrive")
    private Integer nextDayArrive;

    /**
     * 更新人
     */
    @TableField("updater")
    private Integer updater;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField("creator")
    private Integer creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 销售参与：0.不参与1.参与
     */
    @TableField("sale_partake")
    private Integer salePartake;

    /**
     * 上次系统发起调拨时间
     */
    @TableField("last_implement_time")
    private LocalDate lastImplementTime;

    /**
     * 采购参与：0.不参与1.参与
     */
    @TableField("purchase_partake")
    private Integer purchasePartake;

    /**
     * 1:冷冻 2:冷藏 4:常温  逗号分割组合 例如3,4表示冷冻+冷藏,常温组合
     */
    @TableField("temperature_split_rule")
    private String temperatureSplitRule;


}
