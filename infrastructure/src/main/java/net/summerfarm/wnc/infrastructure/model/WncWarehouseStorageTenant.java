package net.summerfarm.wnc.infrastructure.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 仓库租户关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-88 15:16:08
 */
@Getter
@Setter
@TableName("wnc_warehouse_storage_tenant")
public class WncWarehouseStorageTenant implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 仓库编号
     */
    @TableField("warehouse_no")
    private Long warehouseNo;

    /**
     * 租户Id
     */
    @TableField("tenant_id")
    private Long tenantId;


}
