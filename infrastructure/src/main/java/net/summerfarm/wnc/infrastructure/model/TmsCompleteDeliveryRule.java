package net.summerfarm.wnc.infrastructure.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 完成配送提醒规则组
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-99 15:50:29
 */
@Getter
@Setter
@TableName("tms_complete_delivery_rule")
public class TmsCompleteDeliveryRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 配送仓编号
     */
    @TableField("store_no")
    private Integer storeNo;

    /**
     * 规则名称
     */
    @TableField("rule_name")
    private String ruleName;

    /**
     * 品牌规则文件oss标识
     */
    @TableField("brand_rule_object_oss_key")
    private String brandRuleObjectOssKey;

    /**
     * 门店规则文件oss标识
     */
    @TableField("merchant_rule_object_oss_key")
    private String merchantRuleObjectOssKey;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


}
