package net.summerfarm.wnc.infrastructure.repository;

import net.summerfarm.wnc.domain.config.repository.ContactConfigCommandRepository;
import net.summerfarm.wnc.domain.config.entity.ContactConfigEntity;
import net.summerfarm.wnc.domain.config.param.command.ContactConfigCommandParam;
import net.summerfarm.wnc.infrastructure.converter.WncContactConfigConverter;
import net.summerfarm.wnc.infrastructure.mapper.WncContactConfigMapper;
import net.summerfarm.wnc.infrastructure.model.WncContactConfig;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * Description:联系人配置仓库接口实现
 * date: 2023/9/22 18:29
 *
 * <AUTHOR>
 */
@Repository
public class ContactConfigCommandRepositoryImpl implements ContactConfigCommandRepository {

    @Resource
    private WncContactConfigMapper wncContactConfigMapper;

    @Override
    public int save(ContactConfigEntity contactConfigEntity) {
        WncContactConfig wncContactConfig = WncContactConfigConverter.entity2Do(contactConfigEntity);
        return wncContactConfigMapper.insert(wncContactConfig);
    }

    @Override
    public int save(ContactConfigCommandParam contactConfigCommandParam) {
        if (contactConfigCommandParam == null){
            return 0;
        }
        WncContactConfig wncContactConfig = WncContactConfigConverter.param2Do(contactConfigCommandParam);
        return wncContactConfigMapper.insert(wncContactConfig);
    }

    @Override
    public int remove(Long configId) {
        if (configId == null){
            return 0;
        }
        return wncContactConfigMapper.deleteById(configId);
    }

    @Override
    public int update(ContactConfigEntity contactConfigEntity) {
        if (contactConfigEntity == null){
            return 0;
        }
        WncContactConfig wncContactConfig = WncContactConfigConverter.entity2Do(contactConfigEntity);
        return wncContactConfigMapper.updateById(wncContactConfig);
    }
}
