package net.summerfarm.wnc.infrastructure.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 围栏切仓任务明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-250 15:27:47
 */
@Getter
@Setter
@TableName("wnc_fence_change_task_detail")
public class WncFenceChangeTaskDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 切仓任务ID
     */
    @TableField("task_id")
    private Long taskId;

    /**
     * 外部单号
     */
    @TableField("outer_order_id")
    private String outerOrderId;

    /**
     * 来源，200：鲜沐-订单，201：鲜沐-售后，202：鲜沐-样品，203：鲜沐-省心送，210：saas-订单，211：saas-售后
     */
    @TableField("`source`")
    private Integer source;

    /**
     * 外部联系人ID
     */
    @TableField("outer_contact_id")
    private String outerContactId;

    /**
     * 配送时间
     */
    @TableField("delivery_time")
    private LocalDate deliveryTime;

    /**
     * 外部客户号
     */
    @TableField("outer_client_id")
    private String outerClientId;

    /**
     * 外部客户名
     */
    @TableField("outer_client_name")
    private String outerClientName;

    /**
     * 状态，10：待处理，20：处理成功，30：处理失败
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 失败原因
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 履约确认时间
     */
    @TableField("fulfill_confirm_time")
    private LocalDateTime fulfillConfirmTime;


}
