package net.summerfarm.wnc.infrastructure.mapper;

import net.summerfarm.wnc.domain.fence.dataObject.AddrDO;
import net.summerfarm.wnc.common.query.fence.FencePageQuery;
import net.summerfarm.wnc.domain.fence.dataObject.AreaSkuManyWarehouseDO;
import net.summerfarm.wnc.infrastructure.model.Fence;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 围栏表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-96 15:48:23
 */
public interface FenceMapper extends BaseMapper<Fence> {

    /**
     * 分页查询围栏列表
     * @param fencePageQuery 查询
     * @return 结果
     */
    List<Fence> queryPage(FencePageQuery fencePageQuery);

    /**
     * 获取最大打包ID
     * @return 结果
     */
    Integer selectMaxPackId();
    /**
     * 获取围栏详情
     * @param fenceId 围栏ID
     * @return 结果
     */
    Fence queryDetail(@Param("fenceId") Integer fenceId);
    /**
     * 查询围栏信息
     * @param storeNoList 城配仓
     * @return 结果
     */
    List<AddrDO> queryAddrDOByStoreNoList(@Param("storeNoList") List<Integer> storeNoList);

    /**
     * 查询区域SKU多仓库
     * @param areaNo 区域
     * @return 结果
     */
    List<AreaSkuManyWarehouseDO> queryAreaSkuManyWarehouse(@Param("areaNo") Integer areaNo);
}
