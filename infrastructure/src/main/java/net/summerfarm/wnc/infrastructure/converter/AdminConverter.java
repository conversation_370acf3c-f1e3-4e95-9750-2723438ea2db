package net.summerfarm.wnc.infrastructure.converter;

import net.summerfarm.wnc.domain.warehouse.entity.AdminEntity;
import net.summerfarm.wnc.infrastructure.model.Admin;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2023-06-20
 **/
public class AdminConverter {

	/**
	 * @param admin
	 * @return
	 */
	public static AdminEntity do2Entity(Admin admin) {
		if (Objects.isNull(admin)) {
			return null;
		}
		AdminEntity adminEntity = AdminEntity.builder().build();
//		adminEntity.setManageAdminName(admin.getSalerName());
		adminEntity.setAdminId(admin.getAdminId());
		adminEntity.setCreateTime(admin.getCreateTime());
		adminEntity.setLoginFailTimes(admin.getLoginFailTimes());
		adminEntity.setIsDisabled(admin.getIsDisabled());
		adminEntity.setUsername(admin.getUsername());
		adminEntity.setPassword(admin.getPassword());
		adminEntity.setLoginTime(admin.getLoginTime());
		adminEntity.setRealname(admin.getRealname());
		adminEntity.setGender(admin.getGender());
		adminEntity.setDepartment(admin.getDepartment());
		adminEntity.setPhone(admin.getPhone());
		adminEntity.setKp(admin.getKp());
		adminEntity.setSalerId(admin.getSalerId());
		adminEntity.setSalerName(admin.getSalerName());
		adminEntity.setContract(admin.getContract());
		adminEntity.setContractMethod(admin.getContractMethod());
		adminEntity.setNameRemakes(admin.getNameRemakes());
		adminEntity.setOperateId(admin.getOperateId());
		adminEntity.setMajorCycle(admin.getMajorCycle());
		adminEntity.setCloseOrderType(admin.getCloseOrderType());
		adminEntity.setCooperationStage(admin.getCooperationStage());
		adminEntity.setUpdateTime(admin.getUpdateTime());
		adminEntity.setCloseOrderTime(admin.getCloseOrderTime());
		adminEntity.setUpdateCloseOrderTime(admin.getUpdateCloseOrderTime());
		adminEntity.setLowPriceRemainder(admin.getLowPriceRemainder());
		adminEntity.setNotIncludedArea(admin.getNotIncludedArea());
		adminEntity.setSkuSorting(admin.getSkuSorting());
		adminEntity.setAdminType(admin.getAdminType());
		adminEntity.setAdminChain(admin.getAdminChain());
		adminEntity.setAdminGrade(admin.getAdminGrade());
		adminEntity.setAdminSwitch(admin.getAdminSwitch());
		adminEntity.setCreditCode(admin.getCreditCode());
		adminEntity.setBusinessLicenseAddress(admin.getBusinessLicenseAddress());
		adminEntity.setBillToPay(admin.getBillToPay());
		adminEntity.setBaseUserId(admin.getBaseUserId());
		return adminEntity;
	}
}
