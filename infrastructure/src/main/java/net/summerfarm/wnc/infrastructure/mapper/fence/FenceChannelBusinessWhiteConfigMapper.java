package net.summerfarm.wnc.infrastructure.mapper.fence;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.wnc.infrastructure.model.fence.FenceChannelBusinessWhiteConfig;
import net.summerfarm.wnc.domain.fence.param.query.FenceChannelBusinessWhiteConfigQueryParam;
import net.summerfarm.wnc.domain.fence.entity.FenceChannelBusinessWhiteConfigEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-10-12 16:29:33
 * @version 1.0
 *
 */
@Mapper
public interface FenceChannelBusinessWhiteConfigMapper extends BaseMapper<FenceChannelBusinessWhiteConfig> {
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(FenceChannelBusinessWhiteConfig record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(FenceChannelBusinessWhiteConfig record);

    /**
     * @Describe: 通过主键删除
     * @param
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    FenceChannelBusinessWhiteConfig selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param param
     * @return
     */
    List<FenceChannelBusinessWhiteConfig> selectByCondition(FenceChannelBusinessWhiteConfigQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param param
     * @return
     */
    List<FenceChannelBusinessWhiteConfigEntity> getPage(FenceChannelBusinessWhiteConfigQueryParam param);
}

