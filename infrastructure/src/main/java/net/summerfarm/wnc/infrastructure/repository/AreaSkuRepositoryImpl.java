package net.summerfarm.wnc.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.domain.fence.AreaSkuRepository;
import net.summerfarm.wnc.domain.fence.entity.SkuManyWarehouseEntity;
import net.summerfarm.wnc.infrastructure.mapper.AreaSkuMapper;
import net.summerfarm.wnc.infrastructure.model.AreaSku;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/8/2 16:38<br/>
 *
 * <AUTHOR> />
 */
@Service
@Slf4j
public class AreaSkuRepositoryImpl implements AreaSkuRepository {

    @Resource
    private AreaSkuMapper areaSkuMapper;

    @Override
    public List<String> queryAreaOnSaleSku(Integer areaNo) {
        if(areaNo == null){
            return Collections.emptyList();
        }
        List<AreaSku> areaSkus = areaSkuMapper.selectList(new LambdaQueryWrapper<AreaSku>()
                .eq(AreaSku::getAreaNo, areaNo)
                .eq(AreaSku::getOnSale,1)
        );

        return areaSkus.stream().map(AreaSku::getSku).distinct().collect(Collectors.toList());
    }

    @Override
    public List<String> queryAreaNoSaleSku(Integer areaNo) {
        if(areaNo == null){
            return Collections.emptyList();
        }
        List<AreaSku> areaSkus = areaSkuMapper.selectList(new LambdaQueryWrapper<AreaSku>()
                .eq(AreaSku::getAreaNo, areaNo)
                .eq(AreaSku::getOnSale,0)
        );

        return areaSkus.stream().map(AreaSku::getSku).distinct().collect(Collectors.toList());
    }

    @Override
    public Map<Integer, List<String>> querySkuByAreaNo(Integer areaNo) {
        if(areaNo == null){
            return Collections.emptyMap();
        }
        List<AreaSku> areaSkus = areaSkuMapper.selectList(new LambdaQueryWrapper<AreaSku>().eq(AreaSku::getAreaNo, areaNo));

        return areaSkus.stream()
                .filter(areaSku -> areaSku.getOnSale() != null)
                .collect(Collectors.groupingBy(AreaSku::getOnSale, Collectors.mapping(AreaSku::getSku, Collectors.toList())));
    }

    @Override
    public List<Integer> queryWareNoByAreaNoStoreNo(Integer areaNo,List<Integer> storeNos) {
        if(areaNo == null || CollectionUtils.isEmpty(storeNos)){
            return Collections.emptyList();
        }

        return areaSkuMapper.queryWareNoByAreaNoStoreNo(areaNo,storeNos);
    }

    @Override
    public List<SkuManyWarehouseEntity> querySkuManyWarehouse(Integer areaNo, List<Integer> warehouseNos) {
        if(areaNo == null || CollectionUtils.isEmpty(warehouseNos)){
            return Collections.emptyList();
        }
        return areaSkuMapper.querySkuManyWarehouse(areaNo,warehouseNos);
    }
}
