package net.summerfarm.wnc.infrastructure.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 仓库围栏配送规则
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-96 15:33:31
 */
@Getter
@Setter
@TableName("wnc_warehouse_storage_fence_rule")
public class WncWarehouseStorageFenceRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 省
     */
    @TableField("province")
    private String province;

    /**
     * 市
     */
    @TableField("city")
    private String city;

    /**
     * 区
     */
    @TableField("area")
    private String area;

    /**
     * 配送规则 0 距离最短 1手动设置优先级
     */
    @TableField("delivery_rule")
    private Integer deliveryRule;

    /**
     * 仓库优先级JSON规则
     */
    @TableField("conflict_warehouse_json")
    private String conflictWarehouseJson;

    /**
     * 仓库名称合集
     */
    @TableField("warehouse_name_list")
    private String warehouseNameList;

    /**
     * 最后操作人名称
     */
    @TableField("last_operator_name")
    private String lastOperatorName;

    /**
     * 最后操作人id
     */
    @TableField("last_operator_id")
    private Long lastOperatorId;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;


}
