package net.summerfarm.wnc.infrastructure.mapper;

import net.summerfarm.wnc.common.query.changeTask.FenceChangeTaskPageQuery;
import net.summerfarm.wnc.infrastructure.model.ChangeFence;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-23 10:41:06
 */
public interface ChangeFenceMapper extends BaseMapper<ChangeFence> {

    /**
     * 分页查询切仓任务列表
     * @param fenceChangeTaskPageQuery 查询
     * @return 结果
     */
    List<ChangeFence> queryPage(FenceChangeTaskPageQuery fenceChangeTaskPageQuery);
}
