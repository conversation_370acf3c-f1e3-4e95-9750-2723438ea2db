package net.summerfarm.wnc.infrastructure.model.fence;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2024-10-12 16:29:33
 * @version 1.0
 *
 */
@Data
public class FenceChannelBusinessWhiteConfig {
	/**
	 * primary key
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * create time
	 */
	@TableField("create_time")
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	@TableField("update_time")
	private LocalDateTime updateTime;

	/**
	 * 围栏ID
	 */
	@TableField("fence_id")
	private Integer fenceId;

	/**
	 * 下单渠道类型 1000鲜沐平台客户 2000鲜沐大客户 3000 Saas客户
	 */
	@TableField("order_channel_type")
	private String orderChannelType;

	/**
	 * 租户ID
	 */
	@TableField("tenant_id")
	private Long tenantId;

	/**
	 * 作用域下的业务ID 0是全部
	 */
	@TableField("scope_channel_business_id")
	private String scopeChannelBusinessId;

	/**
	 * 业务方名称
	 */
	@TableField("scope_channel_business_name")
	private String scopeChannelBusinessName;

}