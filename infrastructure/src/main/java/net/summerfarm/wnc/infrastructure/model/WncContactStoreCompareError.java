package net.summerfarm.wnc.infrastructure.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 客户poi 城配仓比较不对应表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-346 11:28:56
 */
@Getter
@Setter
@TableName("wnc_contact_store_compare_error")
public class WncContactStoreCompareError implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 客户id
     */
    @TableField("contact_id")
    private Long contactId;

    /**
     * 客户城市
     */
    @TableField("contact_city")
    private String contactCity;

    /**
     *客户区域
     */
    @TableField("contact_area")
    private String contactArea;

    /**
     * 城配仓编号
     */
    @TableField("contact_store_no")
    private Integer contactStoreNo;

    /**
     * poi匹配的城配仓编号
     */
    @TableField("poi_store_no")
    private Integer poiStoreNo;

    /**
     * poi匹配城市
     */
    @TableField("poi_city")
    private String poiCity;

    /**
     * poi匹配区域
     */
    @TableField("poi_area")
    private String poiArea;

}
