package net.summerfarm.wnc.infrastructure.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 仓库业务表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-87 16:31:18
 */
@Getter
@Setter
@TableName("warehouse_storage_center_business")
public class WarehouseStorageCenterBusiness implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 仓库编号
     */
    @TableField("warehouse_no")
    private Integer warehouseNo;

    /**
     * 产能
     */
    @TableField("capacity")
    private Long capacity;

    /**
     * 预约提前期
     */
    @TableField("advance_day")
    private Integer advanceDay;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    @TableField("is_delete")
    private Integer isDelete;


}
