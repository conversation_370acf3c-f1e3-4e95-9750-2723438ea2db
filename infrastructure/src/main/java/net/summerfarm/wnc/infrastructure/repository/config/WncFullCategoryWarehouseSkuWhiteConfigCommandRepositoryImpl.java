package net.summerfarm.wnc.infrastructure.repository.config;

import net.summerfarm.wnc.infrastructure.model.config.WncFullCategoryWarehouseSkuWhiteConfig;
import net.summerfarm.wnc.infrastructure.mapper.config.WncFullCategoryWarehouseSkuWhiteConfigMapper;
import net.summerfarm.wnc.infrastructure.converter.config.WncFullCategoryWarehouseSkuWhiteConfigConverter;
import net.summerfarm.wnc.domain.config.repository.WncFullCategoryWarehouseSkuWhiteConfigCommandRepository;
import net.summerfarm.wnc.domain.config.entity.WncFullCategoryWarehouseSkuWhiteConfigEntity;
import net.summerfarm.wnc.domain.config.param.command.WncFullCategoryWarehouseSkuWhiteConfigCommandParam;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2025-01-06 15:31:26
* @version 1.0
*
*/
@Repository
public class WncFullCategoryWarehouseSkuWhiteConfigCommandRepositoryImpl implements WncFullCategoryWarehouseSkuWhiteConfigCommandRepository {

    @Autowired
    private WncFullCategoryWarehouseSkuWhiteConfigMapper wncFullCategoryWarehouseSkuWhiteConfigMapper;

    @Override
    public void batchInsert(List<WncFullCategoryWarehouseSkuWhiteConfigCommandParam> paramList) {
        if (CollectionUtils.isEmpty(paramList)) {
            return;
        }
        List<WncFullCategoryWarehouseSkuWhiteConfig> wncFullCategoryWarehouseSkuWhiteConfigList = WncFullCategoryWarehouseSkuWhiteConfigConverter.toWncFullCategoryWarehouseSkuWhiteConfigList(paramList);
        wncFullCategoryWarehouseSkuWhiteConfigMapper.batchInsert(wncFullCategoryWarehouseSkuWhiteConfigList);
    }

    @Override
    public WncFullCategoryWarehouseSkuWhiteConfigEntity insertSelective(WncFullCategoryWarehouseSkuWhiteConfigCommandParam param) {
        WncFullCategoryWarehouseSkuWhiteConfig wncFullCategoryWarehouseSkuWhiteConfig = WncFullCategoryWarehouseSkuWhiteConfigConverter.toWncFullCategoryWarehouseSkuWhiteConfig(param);
        wncFullCategoryWarehouseSkuWhiteConfigMapper.insertSelective(wncFullCategoryWarehouseSkuWhiteConfig);
        return WncFullCategoryWarehouseSkuWhiteConfigConverter.toWncFullCategoryWarehouseSkuWhiteConfigEntity(wncFullCategoryWarehouseSkuWhiteConfig);
    }

    @Override
    public int updateSelectiveById(WncFullCategoryWarehouseSkuWhiteConfigCommandParam param){
        return wncFullCategoryWarehouseSkuWhiteConfigMapper.updateSelectiveById(WncFullCategoryWarehouseSkuWhiteConfigConverter.toWncFullCategoryWarehouseSkuWhiteConfig(param));
    }


    @Override
    public int remove(Long id) {
        return wncFullCategoryWarehouseSkuWhiteConfigMapper.remove(id);
    }
}