package net.summerfarm.wnc.infrastructure.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 全局围栏规则
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-96 15:34:03
 */
@Getter
@Setter
@TableName("wnc_tenant_global_fence_rule")
public class WncTenantGlobalFenceRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 全局配送规则 0自营仓优先 1三方仓优先
     */
    @TableField("global_delivery_rule")
    private Integer globalDeliveryRule;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;


}
