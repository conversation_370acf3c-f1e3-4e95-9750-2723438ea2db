package net.summerfarm.wnc.infrastructure.converter;

import net.summerfarm.wnc.domain.path.entity.SkuPathMappingEntity;
import net.summerfarm.wnc.infrastructure.model.WncSkuPathMapping;

/**
 * Description: <br/>
 * date: 2023/11/30 11:36<br/>
 *
 * <AUTHOR> />
 */
public class WncSkuPathMappingConverter {

    public static SkuPathMappingEntity model2Entity(WncSkuPathMapping wncSkuPathMapping) {
        if(wncSkuPathMapping == null){
            return null;
        }
        SkuPathMappingEntity skuPathMappingEntity = new SkuPathMappingEntity();
        skuPathMappingEntity.setId(wncSkuPathMapping.getId());
        skuPathMappingEntity.setSku(wncSkuPathMapping.getSku());
        skuPathMappingEntity.setPathId(wncSkuPathMapping.getPathId());
        skuPathMappingEntity.setCreateTime(wncSkuPathMapping.getCreateTime());
        skuPathMappingEntity.setUpdateTime(wncSkuPathMapping.getUpdateTime());
        skuPathMappingEntity.setWarehouseNo(wncSkuPathMapping.getWarehouseNo());

        return skuPathMappingEntity;
    }


    public static WncSkuPathMapping entity2Model(SkuPathMappingEntity entity) {
        if(entity == null){
            return null;
        }
        WncSkuPathMapping model = new WncSkuPathMapping();
        model.setId(entity.getId());
        model.setSku(entity.getSku());
        model.setPathId(entity.getPathId());
        model.setWarehouseNo(entity.getWarehouseNo());
        model.setCreator(entity.getCreator());
        model.setUpdater(entity.getUpdater());
        return model;
    }
}
