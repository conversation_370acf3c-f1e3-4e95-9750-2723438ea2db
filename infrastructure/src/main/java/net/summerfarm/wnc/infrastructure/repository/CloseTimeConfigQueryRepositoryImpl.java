package net.summerfarm.wnc.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import net.summerfarm.wnc.domain.closeTime.CloseTimeConfigQueryRepository;
import net.summerfarm.wnc.domain.closeTime.entity.CloseTimeAreaConfigEntity;
import net.summerfarm.wnc.domain.closeTime.param.CloseTimeAreaConfigQueryParam;
import net.summerfarm.wnc.infrastructure.converter.WncCloseTimeAreaConfigConverter;
import net.summerfarm.wnc.infrastructure.mapper.WncCloseTimeAreaConfigMapper;
import net.summerfarm.wnc.infrastructure.model.WncCloseTimeAreaConfig;
import net.summerfarm.wnc.infrastructure.util.PageInfoHelper;
import net.xianmu.common.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Description:
 * date: 2024/3/20 14:02
 *
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class CloseTimeConfigQueryRepositoryImpl implements CloseTimeConfigQueryRepository {

    private final WncCloseTimeAreaConfigMapper wncCloseTimeAreaConfigMapper;

    @Override
    public PageInfo<CloseTimeAreaConfigEntity> queryPage(CloseTimeAreaConfigQueryParam queryParam) {
        if(queryParam.getPageIndex() == null || queryParam.getPageSize() == null){
            throw new BizException("分页信息不能为空");
        }
        PageHelper.startPage(queryParam.getPageIndex(), queryParam.getPageSize());
        List<WncCloseTimeAreaConfig> wncCloseTimeAreaConfigs = wncCloseTimeAreaConfigMapper.selectList(new LambdaQueryWrapper<WncCloseTimeAreaConfig>()
                .eq(WncCloseTimeAreaConfig::getTenantId, queryParam.getTenantId())
                .eq(WncCloseTimeAreaConfig::getBrandId, queryParam.getBrandId()));

        PageInfo pageInfo = PageInfoHelper.createPageInfo(wncCloseTimeAreaConfigs);
        List<CloseTimeAreaConfigEntity> entityList = Optional.ofNullable(wncCloseTimeAreaConfigs).orElse(Lists.newArrayList()).stream()
                .map(WncCloseTimeAreaConfigConverter::do2Entity).collect(Collectors.toList());
        pageInfo.setList(entityList);
        return pageInfo;
    }

    @Override
    public List<CloseTimeAreaConfigEntity> queryList(CloseTimeAreaConfigQueryParam queryParam) {
        List<WncCloseTimeAreaConfig> wncCloseTimeAreaConfigs = wncCloseTimeAreaConfigMapper.selectList(new LambdaQueryWrapper<WncCloseTimeAreaConfig>()
                .eq(queryParam.getTenantId() != null, WncCloseTimeAreaConfig::getTenantId, queryParam.getTenantId())
                .eq(queryParam.getBrandId() != null, WncCloseTimeAreaConfig::getBrandId, queryParam.getBrandId())
                .eq(queryParam.getUpdateFlag() != null, WncCloseTimeAreaConfig::getUpdateFlag, queryParam.getUpdateFlag()));

        return Optional.ofNullable(wncCloseTimeAreaConfigs).orElse(Lists.newArrayList()).stream()
                .map(WncCloseTimeAreaConfigConverter::do2Entity).collect(Collectors.toList());
    }

    @Override
    public CloseTimeAreaConfigEntity queryByUk(Long tenantId, String city, String area) {
        if(tenantId == null || city == null){
            throw new BizException("租户ID和城市不能为空");
        }
        WncCloseTimeAreaConfig wncCloseTimeAreaConfig = wncCloseTimeAreaConfigMapper.selectOne(new LambdaQueryWrapper<WncCloseTimeAreaConfig>()
                .eq(WncCloseTimeAreaConfig::getTenantId, tenantId)
                .eq(WncCloseTimeAreaConfig::getCity, city)
                .eq(StringUtils.isNotBlank(area), WncCloseTimeAreaConfig::getArea, area));
        return WncCloseTimeAreaConfigConverter.do2Entity(wncCloseTimeAreaConfig);
    }
}
