package net.summerfarm.wnc.infrastructure.repository;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.common.enums.PathConfigEnums;
import net.summerfarm.wnc.common.query.path.PathQuery;
import net.summerfarm.wnc.domain.path.PathConfigRepository;
import net.summerfarm.wnc.domain.path.entity.PathConfigEntity;
import net.summerfarm.wnc.domain.path.entity.SkuPathMappingEntity;
import net.summerfarm.wnc.domain.warehouse.entity.WncWarehouseStorageFenceRuleEntity;
import net.summerfarm.wnc.infrastructure.converter.WncPathConfigConverter;
import net.summerfarm.wnc.infrastructure.converter.WncSkuPathMappingConverter;
import net.summerfarm.wnc.infrastructure.mapper.WarehouseLogisticsConfigMapper;
import net.summerfarm.wnc.infrastructure.mapper.WncPathConfigMapper;
import net.summerfarm.wnc.infrastructure.mapper.WncSkuPathMappingMapper;
import net.summerfarm.wnc.infrastructure.model.WarehouseLogisticsConfig;
import net.summerfarm.wnc.infrastructure.model.WncFenceChangeTaskDetail;
import net.summerfarm.wnc.infrastructure.model.WncPathConfig;
import net.summerfarm.wnc.infrastructure.model.WncSkuPathMapping;
import net.summerfarm.wnc.infrastructure.util.MybatisPlusUtil;
import net.summerfarm.wnc.infrastructure.util.PageInfoHelper;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: 路线配置<br/>
 * date: 2023/11/29 10:38<br/>
 *
 * <AUTHOR> />
 */
@Service
public class PathConfigRepositoryImpl implements PathConfigRepository {

    @Resource
    private WncPathConfigMapper wncPathConfigMapper;
    @Resource
    private WncSkuPathMappingMapper wncSkuPathMappingMapper;
    @Resource
    private WarehouseLogisticsConfigMapper warehouseLogisticsConfigMapper;

    @Override
    public List<PathConfigEntity> queryPathConfigList(PathQuery pathQuery) {
        List<WncPathConfig> wncPathConfigs = wncPathConfigMapper.selectList(new LambdaQueryWrapper<WncPathConfig>()
                .eq(pathQuery.getBeginOutNo() != null, WncPathConfig::getBeginOutNo, pathQuery.getBeginOutNo())
                .eq(pathQuery.getEndOutNo() != null, WncPathConfig::getEndOutNo, pathQuery.getEndOutNo())
                .eq(pathQuery.getBusinseeType() != null, WncPathConfig::getBusinseeType, pathQuery.getBusinseeType())
                .eq(pathQuery.getFrequentMethod() != null, WncPathConfig::getFrequentMethod, pathQuery.getFrequentMethod())
                .orderByDesc(WncPathConfig::getId)
        );

        return wncPathConfigs.stream().map(WncPathConfigConverter::model2Entity).collect(Collectors.toList());
    }

    @Override
    public PathConfigEntity queryByUk(Integer beginOutNo, Integer endOutNo, Integer businseeType) {
        if(beginOutNo == null || endOutNo == null || businseeType == null){
            throw new BizException("查询uk条件不能为空");
        }
        WncPathConfig wncPathConfig = wncPathConfigMapper.selectOne(new LambdaQueryWrapper<WncPathConfig>()
                .eq(WncPathConfig::getBeginOutNo, beginOutNo)
                .eq(WncPathConfig::getEndOutNo, endOutNo)
                .eq(WncPathConfig::getBusinseeType, businseeType)
        );

        return WncPathConfigConverter.model2Entity(wncPathConfig);
    }

    @Override
    public void save(PathConfigEntity reqEntity) {
        if(reqEntity == null){
            return;
        }
        wncPathConfigMapper.insert(WncPathConfigConverter.entity2model(reqEntity));
    }

    @Override
    public void updateById(PathConfigEntity reqEntity) {
        if(reqEntity == null){
            return;
        }
        WncPathConfig wncPathConfig = WncPathConfigConverter.entity2model(reqEntity);
        wncPathConfigMapper.updateById(wncPathConfig);
    }

    @Override
    public List<PathConfigEntity> queryByBeginOutNosWithType(List<Integer> beginOutNoList, Integer businseeType) {
        if(CollectionUtils.isEmpty(beginOutNoList) || businseeType == null){
            throw new BizException("开始编号和类型不能为空");
        }
        List<WncPathConfig> wncPathConfigs = wncPathConfigMapper.selectList(new LambdaQueryWrapper<WncPathConfig>()
               .in(WncPathConfig::getBeginOutNo, beginOutNoList)
               .eq(WncPathConfig::getBusinseeType, businseeType)
        );
        return wncPathConfigs.stream().map(WncPathConfigConverter::model2Entity).collect(Collectors.toList());
    }

    @Override
    public List<PathConfigEntity> queryPathConfigWithSkuPathMappingList(List<String> skuList, List<Integer> warehouseList) {
        if(CollectionUtils.isEmpty(skuList) || CollectionUtils.isEmpty(warehouseList)){
            throw new BizException("sku集合和仓库列表不能为空");
        }

        List<WncSkuPathMapping> wncSkuPathMappings = wncSkuPathMappingMapper.selectList(new LambdaQueryWrapper<WncSkuPathMapping>()
                .in(WncSkuPathMapping::getSku, skuList)
                .in(WncSkuPathMapping::getWarehouseNo, warehouseList)
        );

        if(CollectionUtils.isEmpty(wncSkuPathMappings)){
            return Collections.emptyList();
        }

        Set<Long> pathIdSet = wncSkuPathMappings.stream().map(WncSkuPathMapping::getPathId).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(pathIdSet)){
            return Collections.emptyList();
        }
        List<WncPathConfig> wncPathConfigs = wncPathConfigMapper.selectList(new LambdaQueryWrapper<WncPathConfig>()
                .in(WncPathConfig::getId, pathIdSet)
        );
        if(CollectionUtils.isEmpty(wncPathConfigs)){
            return Collections.emptyList();
        }
        Map<Long, List<WncSkuPathMapping>> pathId2SkuPathMapping = wncSkuPathMappings.stream().collect(Collectors.groupingBy(WncSkuPathMapping::getPathId));
        List<PathConfigEntity> pathConfigEntities = wncPathConfigs.stream().map(WncPathConfigConverter::model2Entity).collect(Collectors.toList());
        for (PathConfigEntity pathConfigEntity : pathConfigEntities) {
            List<WncSkuPathMapping> skuPathMappings = pathId2SkuPathMapping.get(pathConfigEntity.getId());
            if(CollectionUtils.isEmpty(skuPathMappings)){
                continue;
            }
            pathConfigEntity.setSkuPathMappingEntities(skuPathMappings.stream().map(WncSkuPathMappingConverter::model2Entity).collect(Collectors.toList()));
        }


        return pathConfigEntities;
    }

    @Override
    public List<PathConfigEntity> queryAll() {
        List<WncPathConfig> wncPathConfigs = wncPathConfigMapper.selectList(null);
        return wncPathConfigs.stream().map(WncPathConfigConverter::model2Entity).collect(Collectors.toList());
    }

    @Override
    public void batchUpdate(List<PathConfigEntity> pathConfigEntities) {
        if(CollectionUtils.isEmpty(pathConfigEntities)){
            return;
        }
        List<WncPathConfig> wncPathConfigs = pathConfigEntities.stream().map(WncPathConfigConverter::entity2model).collect(Collectors.toList());
        MybatisPlusUtil.updateBatch(wncPathConfigs,WncPathConfig.class);
    }

    @Override
    public void initData() {
        //查询历史数据
        List<WarehouseLogisticsConfig> warehouseLogisticsConfigs = warehouseLogisticsConfigMapper.selectList(new LambdaQueryWrapper<WarehouseLogisticsConfig>()
                .eq(WarehouseLogisticsConfig::getStatus, 1)
                .eq(WarehouseLogisticsConfig::getType, 0)
        );
        if(CollectionUtils.isEmpty(warehouseLogisticsConfigs)){
            return;
        }
        Map<String, List<WarehouseLogisticsConfig>> beginEndNo2ConfigList = warehouseLogisticsConfigs.stream()
                .collect(Collectors.groupingBy(config -> config.getWarehouseNo() + "#" + config.getAllocationWarehouseNo()));

        List<WncPathConfig> wncPathConfigs = new ArrayList<>();
        for (String beginEndNo : beginEndNo2ConfigList.keySet()) {
            List<WarehouseLogisticsConfig> configs = beginEndNo2ConfigList.get(beginEndNo);
            if(CollectionUtils.isEmpty(configs)){
                continue;
            }

            WarehouseLogisticsConfig minLastImlementTime = configs.stream()
                    .filter(config -> config.getLastImplementTime() != null)
                    .min(Comparator.comparing(WarehouseLogisticsConfig::getLastImplementTime))
                    .orElse(null);

            if(minLastImlementTime == null){
                continue;
            }
            String frequent = configs.stream()
                    .map(WarehouseLogisticsConfig::getLogisticsTime)
                    .filter(Objects::nonNull)
                    .map(String::valueOf).collect(Collectors.joining(","));

            WncPathConfig wncPathConfig = new WncPathConfig();
            wncPathConfig.setBusinseeType(PathConfigEnums.BusinseeType.REALLOCATE.getValue());
            wncPathConfig.setLastTime(minLastImlementTime.getLastImplementTime().plusWeeks(minLastImlementTime.getCycleType()).atStartOfDay());
            wncPathConfig.setBeginOutNo(minLastImlementTime.getWarehouseNo());
            wncPathConfig.setEndOutNo(minLastImlementTime.getAllocationWarehouseNo());
            wncPathConfig.setFrequentMethod(minLastImlementTime.getCycleType());
            wncPathConfig.setFrequent(frequent);

            wncPathConfigs.add(wncPathConfig);
        }
        //初始化数据
        MybatisPlusUtil.createBatch(wncPathConfigs,WncPathConfig.class);
    }

    @Override
    public PathConfigEntity queryById(Long id) {
        if(id == null){
            throw new BizException("路线ID不能为空");
        }
        return WncPathConfigConverter.model2Entity(wncPathConfigMapper.selectById(id));
    }

    @Override
    public void deleteById(Long id) {
        if(id == null){
            throw new BizException("路线ID不能为空");
        }
        wncPathConfigMapper.deleteById(id);
    }

    @Override
    public PageInfo<PathConfigEntity> queryPagePathConfig(PathQuery pathQuery) {
        PageHelper.startPage(pathQuery.getPageIndex(), pathQuery.getPageSize());
        List<WncPathConfig> wncPathConfigs = wncPathConfigMapper.selectList(new LambdaQueryWrapper<WncPathConfig>()
                .eq(pathQuery.getBeginOutNo() != null, WncPathConfig::getBeginOutNo, pathQuery.getBeginOutNo())
                .eq(pathQuery.getEndOutNo() != null, WncPathConfig::getEndOutNo, pathQuery.getEndOutNo())
                .eq(pathQuery.getBusinseeType() != null, WncPathConfig::getBusinseeType, pathQuery.getBusinseeType())
                .eq(pathQuery.getFrequentMethod() != null, WncPathConfig::getFrequentMethod, pathQuery.getFrequentMethod())
                .orderByDesc(WncPathConfig::getId)
        );
        PageInfo<WncPathConfig> pageInfo = PageInfoHelper.createPageInfo(wncPathConfigs);
        PageInfo<PathConfigEntity> resultPageInfo = new PageInfo();

        BeanUtil.copyProperties(pageInfo, resultPageInfo);
        if(CollectionUtils.isEmpty(pageInfo.getList())){
            return resultPageInfo;
        }
        List<PathConfigEntity> pathConfigEntities = pageInfo.getList().stream().map(WncPathConfigConverter::model2Entity).collect(Collectors.toList());
        resultPageInfo.setList(pathConfigEntities);
        return resultPageInfo;
    }
}
