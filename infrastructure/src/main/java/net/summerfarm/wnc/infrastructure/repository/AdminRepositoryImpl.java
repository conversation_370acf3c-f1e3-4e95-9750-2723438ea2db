package net.summerfarm.wnc.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.domain.warehouse.AdminRepository;
import net.summerfarm.wnc.domain.warehouse.entity.AdminEntity;
import net.summerfarm.wnc.infrastructure.converter.AdminConverter;
import net.summerfarm.wnc.infrastructure.mapper.AdminMapper;
import net.summerfarm.wnc.infrastructure.model.Admin;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/4/26 15:53<br/>
 *
 * <AUTHOR> />
 */
@Service
@Slf4j
public class AdminRepositoryImpl implements AdminRepository {

    @Resource
    private AdminMapper adminMapper;

    @Override
    public Map<Integer, String> queryByIds(List<Integer> manageAdminIds) {
        if(CollectionUtils.isEmpty(manageAdminIds)){
            return new HashMap<>();
        }
        List<Admin> admins = adminMapper.selectList(new LambdaQueryWrapper<Admin>().in(Admin::getAdminId, manageAdminIds));
        if(CollectionUtils.isEmpty(admins)){
            return new HashMap<>();
        }
        return admins.stream().collect(Collectors.toMap(Admin::getAdminId,Admin::getRealname));
    }

    @Override
    public String queryNameById(Integer manageAdminId) {
        if (manageAdminId == null) {
            return null;
        }
        Admin admin = adminMapper.selectById(manageAdminId);
        return admin != null ? admin.getRealname() : null;
    }

    @Override
    public AdminEntity queryById(Integer adminId) {
        return AdminConverter.do2Entity(adminMapper.selectById(adminId));
    }
}
