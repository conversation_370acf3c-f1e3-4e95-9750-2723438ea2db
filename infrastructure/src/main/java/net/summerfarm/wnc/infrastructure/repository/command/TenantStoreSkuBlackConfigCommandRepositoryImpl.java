package net.summerfarm.wnc.infrastructure.repository.command;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.domain.config.entity.TenantStoreSkuBlackConfigEntity;
import net.summerfarm.wnc.domain.config.param.command.WncTenantStoreSkuBlackConfigCommandParam;
import net.summerfarm.wnc.domain.config.repository.TenantStoreSkuBlackConfigCommandRepository;
import net.summerfarm.wnc.infrastructure.converter.config.WncTenantStoreSkuBlackConfigConverter;
import net.summerfarm.wnc.infrastructure.mapper.WncTenantStoreSkuBlackConfigMapper;
import net.summerfarm.wnc.infrastructure.model.WncTenantStoreSkuBlackConfig;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Description: 租户城配仓sku黑名单配置查询<br/>
 * date: 2024/8/26 15:13<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Service
public class TenantStoreSkuBlackConfigCommandRepositoryImpl implements TenantStoreSkuBlackConfigCommandRepository {

    @Resource
    private WncTenantStoreSkuBlackConfigMapper wncTenantStoreSkuBlackConfigMapper;

    @Override
    public TenantStoreSkuBlackConfigEntity insertSelective(WncTenantStoreSkuBlackConfigCommandParam param) {
        WncTenantStoreSkuBlackConfig wncTenantStoreSkuBlackConfig = WncTenantStoreSkuBlackConfigConverter.toWncTenantStoreSkuBlackConfig(param);
        wncTenantStoreSkuBlackConfigMapper.insertSelective(wncTenantStoreSkuBlackConfig);
        return WncTenantStoreSkuBlackConfigConverter.toWncTenantStoreSkuBlackConfigEntity(wncTenantStoreSkuBlackConfig);
    }

    @Override
    public int updateSelectiveById(WncTenantStoreSkuBlackConfigCommandParam param){
        return wncTenantStoreSkuBlackConfigMapper.updateSelectiveById(WncTenantStoreSkuBlackConfigConverter.toWncTenantStoreSkuBlackConfig(param));
    }


    @Override
    public int remove(Long id) {
        return wncTenantStoreSkuBlackConfigMapper.remove(id);
    }
}
