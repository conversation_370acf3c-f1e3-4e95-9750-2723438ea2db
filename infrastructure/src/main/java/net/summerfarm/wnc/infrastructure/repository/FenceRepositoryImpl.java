package net.summerfarm.wnc.infrastructure.repository;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.common.enums.AdCodeMsgEnums;
import net.summerfarm.wnc.common.enums.FenceDeliveryEnums;
import net.summerfarm.wnc.common.enums.FenceEnums;
import net.summerfarm.wnc.common.query.fence.FencePageQuery;
import net.summerfarm.wnc.common.query.fence.FenceQuery;
import net.summerfarm.wnc.domain.fence.FenceRepository;
import net.summerfarm.wnc.domain.fence.dataObject.AreaSkuManyWarehouseDO;
import net.summerfarm.wnc.domain.fence.entity.AdCodeMsgEntity;
import net.summerfarm.wnc.domain.fence.dataObject.AddrDO;
import net.summerfarm.wnc.domain.fence.entity.FenceDeliveryEntity;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.summerfarm.wnc.infrastructure.converter.AdCodeMsgConverter;
import net.summerfarm.wnc.infrastructure.converter.FenceConverter;
import net.summerfarm.wnc.infrastructure.mapper.AdCodeMsgMapper;
import net.summerfarm.wnc.infrastructure.mapper.AdCodeMsgMapper;
import net.summerfarm.wnc.infrastructure.mapper.FenceDeliveryLogMapper;
import net.summerfarm.wnc.infrastructure.mapper.FenceDeliveryMapper;
import net.summerfarm.wnc.infrastructure.mapper.FenceMapper;
import net.summerfarm.wnc.infrastructure.model.AdCodeMsg;
import net.summerfarm.wnc.infrastructure.model.Fence;
import net.summerfarm.wnc.infrastructure.model.FenceDelivery;
import net.xianmu.common.exception.BizException;
import net.summerfarm.wnc.infrastructure.model.*;
import net.summerfarm.wnc.infrastructure.util.MybatisPlusUtil;
import net.summerfarm.wnc.infrastructure.util.PageInfoHelper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/8/2 13:50<br/>
 *
 * <AUTHOR> />
 */
@Service
@Slf4j
public class FenceRepositoryImpl implements FenceRepository {

    @Resource
    private FenceMapper fenceMapper;
    @Resource
    private FenceDeliveryMapper fenceDeliveryMapper;
    @Resource
    private AdCodeMsgMapper adCodeMsgMapper;
    @Resource
    private FenceDeliveryLogMapper fenceDeliveryLogMapper;

    @Override
    public PageInfo<FenceEntity> queryPage(FencePageQuery fencePageQuery) {
        PageHelper.startPage(fencePageQuery.getPageIndex(), fencePageQuery.getPageSize());
        List<Fence> fences = fenceMapper.queryPage(fencePageQuery);
        PageInfo pageInfo = PageInfoHelper.createPageInfo(fences);
        List<FenceEntity> fenceEntities = fences.stream().map(FenceConverter::fence2Entity).collect(Collectors.toList());
        pageInfo.setList(fenceEntities);
        return pageInfo;
    }

    @Override
    public List<FenceEntity> queryValidFenceByAreaNo(Integer areaNo) {
        if(areaNo == null){
            return Collections.emptyList();
        }
        List<Fence> fences = fenceMapper.selectList(new LambdaQueryWrapper<Fence>()
                .eq(Fence::getStatus, 0)
                .eq(Fence::getAreaNo, areaNo)
        );

        return fences.stream().map(FenceConverter::fence2Entity).collect(Collectors.toList());
    }

    @Override
    public List<Integer> queryValidStoreNoByPackId(Integer packId) {
        if (packId == null) {
            return Collections.emptyList();
        }
        List<Fence> fences = fenceMapper.selectList(new LambdaQueryWrapper<Fence>()
                .eq(Fence::getStatus, 0)
                .eq(Fence::getPackId, packId)
        );

        return fences.stream().map(Fence::getStoreNo).distinct().collect(Collectors.toList());
    }

    @Override
    public FenceEntity queryById(Integer fenceId) {
        Fence fence = fenceMapper.selectById(fenceId);
        return FenceConverter.fence2Entity(fence);
    }

    @Override
    public FenceDeliveryEntity queryDeliveryById(Integer fenceId) {
        FenceDelivery fenceDelivery = fenceDeliveryMapper.selectOne(new LambdaQueryWrapper<FenceDelivery>().eq(FenceDelivery::getFenceId, fenceId).last("limit 1"));
        return FenceConverter.fenceDelivery2Entity(fenceDelivery);
    }

    @Override
    public List<FenceEntity> queryList(FenceQuery fenceQuery) {
        List<Fence> fences = fenceMapper.selectList(new LambdaQueryWrapper<Fence>()
                .eq(fenceQuery.getFenceId() != null, Fence::getId, fenceQuery.getFenceId())
                .eq(fenceQuery.getStoreNo() != null, Fence::getStoreNo, fenceQuery.getStoreNo())
                .eq(fenceQuery.getAreaNo() != null, Fence::getAreaNo, fenceQuery.getAreaNo())
                .eq(fenceQuery.getFenceName() != null, Fence::getFenceName, fenceQuery.getFenceName())
                .eq(fenceQuery.getStatus() != null,Fence::getStatus,fenceQuery.getStatus())
                .in(!CollectionUtils.isEmpty(fenceQuery.getStatusList()),Fence::getStatus,fenceQuery.getStatusList())
                .in(!CollectionUtils.isEmpty(fenceQuery.getFenceIds()),Fence::getId,fenceQuery.getFenceIds())
                .in(!CollectionUtils.isEmpty(fenceQuery.getAreaNos()),Fence::getAreaNo,fenceQuery.getAreaNos())
                .in(!CollectionUtils.isEmpty(fenceQuery.getStoreNos()),Fence::getStoreNo,fenceQuery.getStoreNos())
                .ge(fenceQuery.getBeginId() != null,Fence::getId,fenceQuery.getBeginId())
                .le(fenceQuery.getEndId() != null,Fence::getId,fenceQuery.getEndId())
                .notIn(!CollectionUtils.isEmpty(fenceQuery.getNotInFenceIds()),Fence::getId,fenceQuery.getNotInFenceIds())
        );
        return fences.stream().map(FenceConverter::fence2Entity).collect(Collectors.toList());
    }

    @Override
    public int update(FenceEntity fenceEntity) {
        if (fenceEntity == null){
            return 0;
        }
        Fence fence = FenceConverter.entity2Fence(fenceEntity);
        return fenceMapper.updateById(fence);
    }

    @Override
    public List<Integer> queryPackAllByStoreNo(Integer storeNo) {
        if(storeNo == null){
            return Collections.emptyList();
        }
        //根据城配仓查询围栏信息
        Fence fence = fenceMapper.selectOne(new LambdaQueryWrapper<Fence>()
                .eq(Fence::getStatus, FenceEnums.Status.VALID.getValue())
                .eq(Fence::getStoreNo, storeNo)
                .last("limit 1")
        );
        if(fence == null){
            return Collections.emptyList();
        }
        List<Fence> fences = fenceMapper.selectList(new LambdaQueryWrapper<Fence>()
                .eq(Fence::getStatus, FenceEnums.Status.VALID.getValue())
                .eq(Fence::getPackId, fence.getPackId())
        );

        return fences.stream().map(Fence::getStoreNo).filter(Objects::nonNull).distinct().collect(Collectors.toList());
    }

    @Override
    public FenceEntity queryDetail(Integer fenceId) {
        Fence fence = fenceMapper.queryDetail(fenceId);
        if (fence == null){
            return null;
        }
        List<AdCodeMsg> adCodeMsgList = adCodeMsgMapper.selectList(new LambdaQueryWrapper<AdCodeMsg>()
                .eq(AdCodeMsg::getFenceId, fenceId)
                .eq(AdCodeMsg::getStatus, fence.getStatus()));
        FenceDelivery fenceDelivery = fenceDeliveryMapper.selectOne(new LambdaQueryWrapper<FenceDelivery>()
                .eq(FenceDelivery::getFenceId, fenceId).eq(FenceDelivery::getDeleteFlag, FenceDeliveryEnums.DeleteFlag.NO.getValue())
                .last("limit 1"));
        FenceEntity fenceEntity = FenceConverter.fence2Entity(fence);
        fenceEntity.setFenceDeliveryEntity(FenceConverter.fenceDelivery2Entity(fenceDelivery));
        fenceEntity.setAdCodeMsgEntities(adCodeMsgList.stream().map(AdCodeMsgConverter::adCodeMsg2Entity).collect(Collectors.toList()));
        return fenceEntity;
    }

    @Override
    public Integer queryMaxPackId() {
        return fenceMapper.selectMaxPackId();
    }

    @Override
    public void saveOrUpdate(FenceEntity fenceEntity) {
        if (fenceEntity == null){
            return;
        }
        Fence fence = FenceConverter.entity2Fence(fenceEntity);
        if (fence.getId() == null){
            fenceMapper.insert(fence);
            fenceEntity.setId(fence.getId());
        }else {
            fenceMapper.updateById(fence);
        }

        List<AdCodeMsgEntity> adCodeMsgEntities = fenceEntity.getAdCodeMsgEntities();
        if (!CollectionUtils.isEmpty(adCodeMsgEntities)){
            List<AdCodeMsg> adCodeMsgList = adCodeMsgEntities.stream().peek(e -> e.setFenceId(fence.getId())).map(AdCodeMsgConverter::entity2po).collect(Collectors.toList());
            MybatisPlusUtil.createBatch(adCodeMsgList, AdCodeMsg.class);
        }
        FenceDeliveryEntity fenceDeliveryEntity = fenceEntity.getFenceDeliveryEntity();
        if (fenceDeliveryEntity != null){
            fenceDeliveryEntity.setFenceId(fence.getId());
            FenceDelivery fenceDelivery = FenceConverter.entity2FenceDelivery(fenceDeliveryEntity);
            if (fenceDelivery.getId() == null){
                fenceDeliveryMapper.insert(fenceDelivery);
            }else {
                fenceDeliveryMapper.updateById(fenceDelivery);
            }
            fenceDeliveryLogMapper.insert(FenceConverter.fenceDelivery2Log(fenceDelivery));
        }

    }

    @Override
    public void changeStatus(FenceEntity fenceEntity) {
        if (fenceEntity == null){
            return;
        }
        Fence fence = FenceConverter.entity2Fence(fenceEntity);
        fenceMapper.updateById(fence);
        List<AdCodeMsgEntity> adCodeMsgEntities = fenceEntity.getAdCodeMsgEntities();
        if (!CollectionUtils.isEmpty(adCodeMsgEntities)){
            List<AdCodeMsg> adCodeMsgList = adCodeMsgEntities.stream().map(AdCodeMsgConverter::entity2po).collect(Collectors.toList());
            adCodeMsgList.stream().sorted(Comparator.comparing(AdCodeMsg::getId)).forEach(e -> adCodeMsgMapper.updateById(e));
        }
    }

    @Override
    public List<AdCodeMsgEntity> queryAllFenceAreas(List<Integer> status) {
        if (CollectionUtils.isEmpty(status)){
            return Collections.emptyList();
        }
        List<AdCodeMsg> adCodeMsgList = adCodeMsgMapper.selectList(new LambdaQueryWrapper<AdCodeMsg>().in(AdCodeMsg::getStatus, status));
        return adCodeMsgList.stream().map(AdCodeMsgConverter::adCodeMsg2Entity).collect(Collectors.toList());
    }

    @Override
    public List<FenceEntity> queryByStoreNoList(List<Integer> storeNoList) {
        if(CollectionUtils.isEmpty(storeNoList)){
            return Collections.emptyList();
        }
        List<Fence> fences = fenceMapper.selectList(new LambdaQueryWrapper<Fence>().in(Fence::getStoreNo, storeNoList));
        return fences.stream().map(FenceConverter::fence2Entity).collect(Collectors.toList());
    }

    @Override
    public List<AddrDO> queryAddrDOByStoreNoList(List<Integer> storeNoList) {
        if(CollectionUtils.isEmpty(storeNoList)){
            return Collections.emptyList();
        }

        return fenceMapper.queryAddrDOByStoreNoList(storeNoList);
    }

    @Override
    public List<AddrDO> queryAddrDOAll() {
        return fenceMapper.queryAddrDOByStoreNoList(null);
    }

    @Override
    public FenceEntity queryFenceByAddr(String city, String area) {
        if(StrUtil.isBlank(city)){
            throw new BizException("queryFenceByAddr 城市不能为空");
        }
        AdCodeMsg adCodeMsg = adCodeMsgMapper.selectOne(new LambdaQueryWrapper<AdCodeMsg>()
                .eq(AdCodeMsg::getCity, city)
                .eq(StrUtil.isNotBlank(area), AdCodeMsg::getArea, area)
                .eq(AdCodeMsg::getStatus, FenceEnums.Status.VALID.getValue())
                .last("limit 1")
        );
        if(adCodeMsg == null || adCodeMsg.getFenceId() == null){
            return null;
        }
        Fence fence = fenceMapper.selectById(adCodeMsg.getFenceId());
        return FenceConverter.fence2Entity(fence);
    }

    @Override
    public List<AreaSkuManyWarehouseDO> queryAreaSkuManyWarehouse(Integer areaNo) {
        if(areaNo == null){
            return Collections.emptyList();
        }

        return fenceMapper.queryAreaSkuManyWarehouse(areaNo);
    }

    @Override
    public List<FenceEntity> queryListWithArea(FenceQuery fenceQuery) {
        if (fenceQuery == null){
            return Collections.emptyList();
        }
        List<FenceEntity> fenceEntities = this.queryList(fenceQuery);
        if (CollectionUtils.isEmpty(fenceEntities)){
            return Collections.emptyList();
        }
        List<Integer> fenceIds = fenceEntities.stream().map(FenceEntity::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fenceIds)){
            return Collections.emptyList();
        }
        List<AdCodeMsg> allFenceAreas = adCodeMsgMapper.selectList(new LambdaQueryWrapper<AdCodeMsg>()
                .in(AdCodeMsg::getFenceId, fenceIds)
                .eq(fenceQuery.getStatus() != null, AdCodeMsg::getStatus, fenceQuery.getStatus()));
        Map<Integer/*fenceId*/, List<AdCodeMsg>> fenceAreaMap = Optional.ofNullable(allFenceAreas).orElse(Lists.newArrayList()).stream().collect(Collectors.groupingBy(AdCodeMsg::getFenceId));
        for (FenceEntity fenceEntity : fenceEntities) {
            List<AdCodeMsg> fenceArea = fenceAreaMap.get(fenceEntity.getId());
            List<AdCodeMsgEntity> adCodeMsgEntities = Optional.ofNullable(fenceArea).orElse(Lists.newArrayList()).stream().map(AdCodeMsgConverter::adCodeMsg2Entity).collect(Collectors.toList());
            fenceEntity.setAdCodeMsgEntities(adCodeMsgEntities);
        }
        return fenceEntities;
    }
}
