package net.summerfarm.wnc.infrastructure.mapper;

import net.summerfarm.wnc.domain.config.monit.PopMerchantMonit;
import net.summerfarm.wnc.infrastructure.model.WncContactConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 地址映射配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-265 17:00:37
 */
public interface WncContactConfigMapper extends BaseMapper<WncContactConfig> {

    /**
     * POP查询没有指定城配仓的门店信息
     * @param adminId 大客户ID
     * @return 结果
     */
    List<PopMerchantMonit> queryPopNoHaveStoreNoMonit(@Param("adminId") Integer adminId);
}
