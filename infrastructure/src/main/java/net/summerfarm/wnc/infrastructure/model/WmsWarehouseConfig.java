package net.summerfarm.wnc.infrastructure.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 仓库配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-240 11:11:01
 */
@Getter
@Setter
@TableName("wms_warehouse_config")
public class WmsWarehouseConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 仓库编号
     */
    @TableField("warehouse_no")
    private Integer warehouseNo;

    /**
     * 配置key
     */
    @TableField("config_key")
    private String configKey;

    /**
     * 配置value
     */
    @TableField("config_value")
    private String configValue;

    /**
     * 创建人
     */
    @TableField("create_operator")
    private String createOperator;

    /**
     * 更新人
     */
    @TableField("update_operator")
    private String updateOperator;


}
