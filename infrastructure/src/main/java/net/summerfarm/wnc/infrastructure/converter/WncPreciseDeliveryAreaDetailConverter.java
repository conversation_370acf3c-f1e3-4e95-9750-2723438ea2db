package net.summerfarm.wnc.infrastructure.converter;

import com.google.common.collect.Lists;
import net.summerfarm.wnc.domain.preciseDelivery.aggregate.PreciseDeliveryConfigAggregate;
import net.summerfarm.wnc.domain.preciseDelivery.entity.PreciseDeliveryConfigEntity;
import net.summerfarm.wnc.domain.preciseDelivery.param.PreciseDeliveryConfigAreaCommandParam;
import net.summerfarm.wnc.domain.preciseDelivery.param.PreciseDeliveryConfigCommandParam;
import net.summerfarm.wnc.domain.preciseDelivery.param.PreciseDeliveryConfigTimeCommandParam;
import net.summerfarm.wnc.domain.preciseDelivery.valueObject.PreciseDeliveryConfigAreaValueObject;
import net.summerfarm.wnc.domain.preciseDelivery.valueObject.PreciseDeliveryConfigTimeValueObject;
import net.summerfarm.wnc.infrastructure.model.WncPreciseDeliveryAreaDetail;
import net.summerfarm.wnc.infrastructure.model.WncPreciseDeliveryConfig;
import net.summerfarm.wnc.infrastructure.model.WncPreciseDeliveryTimeDetail;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Description:精准送配置区域转换器
 * date: 2024/1/22 18:34
 *
 * <AUTHOR>
 */
public class WncPreciseDeliveryAreaDetailConverter {

    public static PreciseDeliveryConfigAreaValueObject do2Entity(WncPreciseDeliveryAreaDetail wncPreciseDeliveryAreaDetail){
        if(wncPreciseDeliveryAreaDetail == null){
            return null;
        }
        PreciseDeliveryConfigAreaValueObject preciseDeliveryConfigAreaValueObject = new PreciseDeliveryConfigAreaValueObject();
        preciseDeliveryConfigAreaValueObject.setId(wncPreciseDeliveryAreaDetail.getId());
        preciseDeliveryConfigAreaValueObject.setConfigId(wncPreciseDeliveryAreaDetail.getConfigId());
        preciseDeliveryConfigAreaValueObject.setAdCode(wncPreciseDeliveryAreaDetail.getAdCode());
        preciseDeliveryConfigAreaValueObject.setCity(wncPreciseDeliveryAreaDetail.getCity());
        preciseDeliveryConfigAreaValueObject.setArea(wncPreciseDeliveryAreaDetail.getArea());
        preciseDeliveryConfigAreaValueObject.setCreateTime(wncPreciseDeliveryAreaDetail.getCreateTime());
        return preciseDeliveryConfigAreaValueObject;
    }

    public static WncPreciseDeliveryAreaDetail param2Do(PreciseDeliveryConfigAreaCommandParam param){
        if(param == null){
            return null;
        }
        WncPreciseDeliveryAreaDetail wncPreciseDeliveryAreaDetail = new WncPreciseDeliveryAreaDetail();
        wncPreciseDeliveryAreaDetail.setAdCode(param.getAdCode());
        wncPreciseDeliveryAreaDetail.setCity(param.getCity());
        wncPreciseDeliveryAreaDetail.setArea(param.getArea());
        wncPreciseDeliveryAreaDetail.setCreateTime(param.getCreateTime());
        return wncPreciseDeliveryAreaDetail;
    }
}
