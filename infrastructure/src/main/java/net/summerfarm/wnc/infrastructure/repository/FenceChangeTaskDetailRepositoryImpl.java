package net.summerfarm.wnc.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import net.summerfarm.wnc.common.query.changeTask.FenceChangeTaskDetailQuery;
import net.summerfarm.wnc.common.query.changeTask.FenceChangeTaskOrderPageQuery;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskDetailRepository;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskOrderEntity;
import net.summerfarm.wnc.infrastructure.converter.WncChangeFenceDetailConverter;
import net.summerfarm.wnc.infrastructure.mapper.*;
import net.summerfarm.wnc.infrastructure.model.WncFenceChangeTaskDetail;
import net.summerfarm.wnc.infrastructure.util.PageInfoHelper;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description:围栏切仓任务仓库接口实现
 * date: 2023/8/24 16:46
 *
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class FenceChangeTaskDetailRepositoryImpl implements FenceChangeTaskDetailRepository {

    private final WncFenceChangeTaskDetailMapper wncFenceChangeTaskDetailMapper;

    @Override
    public PageInfo<FenceChangeTaskOrderEntity> queryTaskOrderPage(FenceChangeTaskOrderPageQuery fenceChangeTaskOrderPageQuery) {
        PageHelper.startPage(fenceChangeTaskOrderPageQuery.getPageIndex(), fenceChangeTaskOrderPageQuery.getPageSize());
        List<WncFenceChangeTaskDetail> wncFenceChangeTaskDetails = wncFenceChangeTaskDetailMapper.queryPage(fenceChangeTaskOrderPageQuery);
        PageInfo pageInfo = PageInfoHelper.createPageInfo(wncFenceChangeTaskDetails);

        List<FenceChangeTaskOrderEntity> fenceChangeTaskOrderEntities = wncFenceChangeTaskDetails.stream().map(WncChangeFenceDetailConverter::do2Entity).collect(Collectors.toList());
        pageInfo.setList(fenceChangeTaskOrderEntities);
        return pageInfo;
    }

    @Override
    public void saveBatch(Collection<FenceChangeTaskOrderEntity> fenceChangeTaskOrderEntityList) {
        if (CollectionUtils.isEmpty(fenceChangeTaskOrderEntityList)) {
            return;
        }
        for (FenceChangeTaskOrderEntity fenceChangeTaskOrderEntity : fenceChangeTaskOrderEntityList) {
            WncFenceChangeTaskDetail wncFenceChangeTaskDetail = WncChangeFenceDetailConverter.entity2Do(fenceChangeTaskOrderEntity);
            wncFenceChangeTaskDetailMapper.insert(wncFenceChangeTaskDetail);
            fenceChangeTaskOrderEntity.setId(wncFenceChangeTaskDetail.getId());
        }
    }

    @Override
    public int update(FenceChangeTaskOrderEntity fenceChangeTaskOrderEntity) {
        if (fenceChangeTaskOrderEntity == null){
            return 0;
        }
        WncFenceChangeTaskDetail wncFenceChangeTaskDetail = WncChangeFenceDetailConverter.entity2Do(fenceChangeTaskOrderEntity);
        return wncFenceChangeTaskDetailMapper.updateById(wncFenceChangeTaskDetail);
    }

    @Override
    public FenceChangeTaskOrderEntity queryById(Long changeTaskDetailId) {
        if (changeTaskDetailId == null){
            return null;
        }
        WncFenceChangeTaskDetail wncFenceChangeTaskDetail = wncFenceChangeTaskDetailMapper.selectById(changeTaskDetailId);
        return WncChangeFenceDetailConverter.do2Entity(wncFenceChangeTaskDetail);
    }

    @Override
    public List<FenceChangeTaskOrderEntity> queryRetryableTaskDetails() {
        List<WncFenceChangeTaskDetail> wncFenceChangeTaskDetails = wncFenceChangeTaskDetailMapper.selectRetryableTaskDetails();
        if (CollectionUtils.isEmpty(wncFenceChangeTaskDetails)){
            return Collections.emptyList();
        }
        return wncFenceChangeTaskDetails.stream().map(WncChangeFenceDetailConverter::do2Entity).collect(Collectors.toList());
    }

    @Override
    public List<FenceChangeTaskOrderEntity> queryList(FenceChangeTaskDetailQuery fenceChangeTaskDetailQuery) {
        List<WncFenceChangeTaskDetail> wncFenceChangeTaskDetails = wncFenceChangeTaskDetailMapper.selectList(new LambdaQueryWrapper<WncFenceChangeTaskDetail>()
                .eq(WncFenceChangeTaskDetail::getTaskId, fenceChangeTaskDetailQuery.getTaskId())
                .eq(fenceChangeTaskDetailQuery.getStatus() != null, WncFenceChangeTaskDetail::getStatus,
                        fenceChangeTaskDetailQuery.getStatus()));
        return wncFenceChangeTaskDetails.stream().map(WncChangeFenceDetailConverter::do2Entity).collect(Collectors.toList());
    }
}
