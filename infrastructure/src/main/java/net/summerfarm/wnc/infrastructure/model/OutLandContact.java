package net.summerfarm.wnc.infrastructure.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 联系人
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-66 14:40:19
 */
@Getter
@Setter
@TableName("contact")
public class OutLandContact implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "contact_id", type = IdType.AUTO)
    private Long contactId;

    /**
     *  商户id
     */
    @TableField("m_id")
    private Long mId;

    /**
     * 联系人
     */
    @TableField("contact")
    private String contact;

    /**
     * 职位
     */
    @TableField("position")
    private String position;

    /**
     * 性别
     */
    @TableField("gender")
    private Boolean gender;

    @TableField("phone")
    private String phone;

    @TableField("email")
    private String email;

    /**
     * 微信号
     */
    @TableField("weixincode")
    private String weixincode;

    @TableField("province")
    private String province;

    @TableField("city")
    private String city;

    @TableField("area")
    private String area;

    @TableField("address")
    private String address;

    /**
     * 配送车
     */
    @TableField("delivery_car")
    private String deliveryCar;

    /**
     * 状态(1正常或审核通过、2删除、3待审核、4审核不通过)
     */
    @TableField("`status`")
    private Integer status;

    @TableField("remark")
    private String remark;

    /**
     * 1默认地址 与merchat中一致
     */
    @TableField("is_default")
    private Integer isDefault;

    /**
     * 高德地图poi坐标
     */
    @TableField("poi_note")
    private String poiNote;

    /**
     * 与仓库的距离
     */
    @TableField("distance")
    private BigDecimal distance;

    /**
     * 路线
     */
    @TableField("path")
    private String path;

    /**
     * 门牌号
     */
    @TableField("house_number")
    private String houseNumber;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 配送仓编号
     */
    @TableField("store_no")
    private Integer storeNo;

    /**
     * 归属区域id adCodeMsg表
     */
    @TableField("acm_id")
    private Integer acmId;

    /**
     * 配送仓编号 备注
     */
    @TableField("back_store_no")
    private Integer backStoreNo;

    /**
     * 配送周期
     */
    @TableField("delivery_frequent")
    private String deliveryFrequent;

    /**
     * 运费规则
     */
    @TableField("delivery_rule")
    private String deliveryRule;

    /**
     * 运费
     */
    @TableField("delivery_fee")
    private BigDecimal deliveryFee;


}
