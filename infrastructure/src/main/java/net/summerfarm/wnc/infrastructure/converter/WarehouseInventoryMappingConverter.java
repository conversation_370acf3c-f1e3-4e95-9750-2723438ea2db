package net.summerfarm.wnc.infrastructure.converter;

import net.summerfarm.wnc.domain.warehouse.entity.WarehouseInventoryMappingEntity;
import net.summerfarm.wnc.infrastructure.model.WarehouseInventoryMapping;

/**
 * Description: <br/>
 * date: 2023/4/7 18:44<br/>
 *
 * <AUTHOR> />
 */
public class WarehouseInventoryMappingConverter {

    public static WarehouseInventoryMappingEntity model2Entity(WarehouseInventoryMapping warehouseInventoryMapping){
        if(warehouseInventoryMapping == null){
            return null;
        }
        WarehouseInventoryMappingEntity warehouseInventoryMappingEntity = new WarehouseInventoryMappingEntity();

        warehouseInventoryMappingEntity.setId(warehouseInventoryMapping.getId());
        warehouseInventoryMappingEntity.setWarehouseNo(warehouseInventoryMapping.getWarehouseNo());
        warehouseInventoryMappingEntity.setStoreNo(warehouseInventoryMapping.getStoreNo());
        warehouseInventoryMappingEntity.setSku(warehouseInventoryMapping.getSku());
        warehouseInventoryMappingEntity.setSaleLockQuantity(warehouseInventoryMapping.getSaleLockQuantity());
        warehouseInventoryMappingEntity.setReserveQuantity(warehouseInventoryMapping.getReserveQuantity());
        warehouseInventoryMappingEntity.setSupportReserved(warehouseInventoryMapping.getSupportReserved());
        warehouseInventoryMappingEntity.setUpdater(warehouseInventoryMapping.getUpdater());
        warehouseInventoryMappingEntity.setUpdateTime(warehouseInventoryMapping.getUpdateTime());
        warehouseInventoryMappingEntity.setCreator(warehouseInventoryMapping.getCreator());
        warehouseInventoryMappingEntity.setCreateTime(warehouseInventoryMapping.getCreateTime());

        return warehouseInventoryMappingEntity;
    }

    public static WarehouseInventoryMapping entity2model(WarehouseInventoryMappingEntity warehouseInventoryMappingEntity){
        if(warehouseInventoryMappingEntity == null){
            return null;
        }
        WarehouseInventoryMapping warehouseInventoryMapping = new WarehouseInventoryMapping();

        warehouseInventoryMapping.setId(warehouseInventoryMappingEntity.getId());
        warehouseInventoryMapping.setWarehouseNo(warehouseInventoryMappingEntity.getWarehouseNo());
        warehouseInventoryMapping.setStoreNo(warehouseInventoryMappingEntity.getStoreNo());
        warehouseInventoryMapping.setSku(warehouseInventoryMappingEntity.getSku());
        warehouseInventoryMapping.setSaleLockQuantity(warehouseInventoryMappingEntity.getSaleLockQuantity());
        warehouseInventoryMapping.setReserveQuantity(warehouseInventoryMappingEntity.getReserveQuantity());
        warehouseInventoryMapping.setSupportReserved(warehouseInventoryMappingEntity.getSupportReserved());
        warehouseInventoryMapping.setUpdater(warehouseInventoryMappingEntity.getUpdater());
        warehouseInventoryMapping.setUpdateTime(warehouseInventoryMappingEntity.getUpdateTime());
        warehouseInventoryMapping.setCreator(warehouseInventoryMappingEntity.getCreator());
        warehouseInventoryMapping.setCreateTime(warehouseInventoryMappingEntity.getCreateTime());

        return warehouseInventoryMapping;
    }
}
