package net.summerfarm.wnc.infrastructure.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 地址映射配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-265 17:00:37
 */
@Getter
@Setter
@TableName("wnc_contact_config")
public class WncContactConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 来源，0：鲜沐，1：saas
     */
    @TableField("`source`")
    private Integer source;

    /**
     * 租户ID
     */
    @TableField("tenant_Id")
    private Long tenantId;

    /**
     * 外部联系人ID
     */
    @TableField("outer_contact_id")
    private Long outerContactId;

    /**
     * 城配仓编号
     */
    @TableField("store_no")
    private Integer storeNo;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;


}
