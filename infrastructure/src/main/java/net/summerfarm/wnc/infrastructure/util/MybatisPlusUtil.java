/**
 * 版权所有(C)，上海勾芒信息科技，2019，所有权利保留。
 * <p>
 * 项目名：	newretail-server
 * 文件名：	MybatisPlusUtil.java
 * 模块说明：
 * 修改历史：
 * 2019年9月1日 - Debenson - 创建。
 */
package net.summerfarm.wnc.infrastructure.util;

import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.ReflectionKit;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import lombok.NonNull;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ibatis.binding.MapperMethod;
import org.apache.ibatis.session.SqlSession;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 0.1
 */
public abstract class MybatisPlusUtil {

  public static final int MAX_BATCH_SIZE = 5000;

  public static <T> boolean saveOrUpdateBatch(Collection<T> entityList, Class<T> modelClass) {
    return saveOrUpdateBatch(entityList, modelClass, MAX_BATCH_SIZE);
  }

  public static <T> boolean saveOrUpdateBatch(Collection<T> entityList, Class<T> modelClass, int batchSize) {
    if (CollectionUtils.isEmpty(entityList)) {
      return false;
    }
    TableInfo tableInfo = TableInfoHelper.getTableInfo(modelClass);
    Assert.notNull(tableInfo,
        "error: can not execute. because can not find cache of TableInfo for entity!");
    String keyProperty = tableInfo.getKeyProperty();
    Assert.notEmpty(keyProperty,
        "error: can not execute. because can not find column for id from entity!");
    try (SqlSession batchSqlSession = sqlSessionBatch(modelClass)) {
      int i = 0;
      for (T entity : entityList) {
        Object idVal = ReflectionKit.getFieldValue(entity, keyProperty);
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.checkValNull(idVal)) {
          batchSqlSession.insert(sqlStatement(SqlMethod.INSERT_ONE, modelClass), entity);
        } else {
          MapperMethod.ParamMap<T> param = new MapperMethod.ParamMap<>();
          param.put(Constants.ENTITY, entity);
          batchSqlSession.update(sqlStatement(SqlMethod.UPDATE_BY_ID, modelClass), param);
        }
        // 不知道以后会不会有人说更新失败了还要执行插入 😂😂😂
        if (i >= 1 && i % batchSize == 0) {
          batchSqlSession.flushStatements();
        }
        i++;
      }
      batchSqlSession.flushStatements();
    }
    return true;
  }

  /**
   * 批量插入
   *
   * @param entityList   实体列表
   * @param exBaseMapper 实体对应的mapper
   * @param <T>
   * @return
   */
  public static <T> boolean createBatch(List<T> entityList, @NonNull ExBaseMapper<T> exBaseMapper) {
    return createBatch(entityList, exBaseMapper, MAX_BATCH_SIZE);
  }

  public static <T> boolean createBatch(Collection<T> entityList, Class<T> modelClass) {
    return createBatch(entityList, modelClass, MAX_BATCH_SIZE);
  }

  public static <T> boolean createBatch(Collection<T> entityList, Class<T> modelClass, int batchSize) {
    if (CollectionUtils.isEmpty(entityList)) {
      return false;
    }
    TableInfo tableInfo = TableInfoHelper.getTableInfo(modelClass);
    Assert.notNull(tableInfo,
        "error: can not execute. because can not find cache of TableInfo for entity " + modelClass.getName());
    String keyProperty = tableInfo.getKeyProperty();
    Assert.notEmpty(keyProperty,
        "error: can not execute. because can not find column for id from entity " + modelClass.getName());
    try (SqlSession batchSqlSession = sqlSessionBatch(modelClass)) {
      int i = 0;
      for (T entity : entityList) {
        batchSqlSession.insert(sqlStatement(SqlMethod.INSERT_ONE, modelClass), entity);

        // 不知道以后会不会有人说更新失败了还要执行插入 😂😂😂
        if (i >= 1 && i % batchSize == 0) {
          batchSqlSession.flushStatements();
        }
        i++;
      }
      batchSqlSession.flushStatements();
    }
    return true;
  }

  /**
   * 批量插入
   *
   * @param entityList   实体列表
   * @param exBaseMapper 实体对应的mapper
   * @param batchSize    批量大小
   * @param <T>
   * @return
   */
  public static <T> boolean createBatch(List<T> entityList, @NonNull ExBaseMapper<T> exBaseMapper, int batchSize) {
    if (CollectionUtils.isEmpty(entityList)) {
      return false;
    }

    int from = 0, to = 0;
    boolean finished = false;
    while (!finished) {
      if (from >= entityList.size()) {
        break;
      }
      to = from + batchSize;
      if (to > entityList.size()) {
        to = entityList.size();
        finished = true;
      }
      final List<T> sub = entityList.subList(from, to);
      exBaseMapper.insertBatchSomeColumn(sub);
      from = to;
    }
    return true;
  }

  public static <T> boolean updateBatch(Collection<T> entityList, Class<T> modelClass) {
    return updateBatch(entityList, modelClass, MAX_BATCH_SIZE);
  }

  public static <T> boolean updateBatch(Collection<T> entityList, Class<T> modelClass,
                                        int batchSize) {
    if (CollectionUtils.isEmpty(entityList)) {
      return false;
    }
    TableInfo tableInfo = TableInfoHelper.getTableInfo(modelClass);
    Assert.notNull(tableInfo,
        "error: can not execute. because can not find cache of TableInfo for entity!");
    String keyProperty = tableInfo.getKeyProperty();
    Assert.notEmpty(keyProperty,
        "error: can not execute. because can not find column for id from entity!");
    try (SqlSession batchSqlSession = sqlSessionBatch(modelClass)) {
      int i = 0;
      for (T entity : entityList) {
        MapperMethod.ParamMap<T> param = new MapperMethod.ParamMap<>();
        param.put(Constants.ENTITY, entity);
        batchSqlSession.update(sqlStatement(SqlMethod.UPDATE_BY_ID, modelClass), param);

        // 不知道以后会不会有人说更新失败了还要执行插入 😂😂😂
        if (i >= 1 && i % batchSize == 0) {
          batchSqlSession.flushStatements();
        }
        i++;
      }
      batchSqlSession.flushStatements();
    }
    return true;
  }

  /**
   * 批量操作 SqlSession
   */
  private static <T> SqlSession sqlSessionBatch(Class<T> modelClass) {
    return SqlHelper.sqlSessionBatch(modelClass);
  }

  /**
   * 获取 SqlStatement
   *
   * @param sqlMethod ignore
   * @return ignore
   */
  private static <T> String sqlStatement(SqlMethod sqlMethod, Class<T> modelClass) {
    return SqlHelper.table(modelClass).getSqlStatement(sqlMethod.getMethod());
  }

  private static List<List<String>> splitList(List<String> list) {
    List<List<String>> listGroup = new ArrayList<List<String>>();
    int listSize = list.size();
    //子集合的长度
    int toIndex = 2;
    for (int i = 0; i < list.size(); i += 2) {
      if (i + 2 > listSize) {
        toIndex = listSize - i;
      }
      List<String> newList = list.subList(i, i + toIndex);
      listGroup.add(newList);
    }
    return listGroup;
  }

}
