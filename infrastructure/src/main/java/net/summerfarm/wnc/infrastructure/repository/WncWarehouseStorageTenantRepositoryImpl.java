package net.summerfarm.wnc.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.summerfarm.wnc.common.base.WncAssert;
import net.summerfarm.wnc.common.query.warehouse.WncWarehouseStorageTenantQuery;
import net.summerfarm.wnc.domain.warehouse.WncWarehouseStorageTenantRepository;
import net.summerfarm.wnc.domain.warehouse.entity.WncWarehouseStorageTenantEntity;
import net.summerfarm.wnc.infrastructure.converter.WncWarehouseStorageTenantConverter;
import net.summerfarm.wnc.infrastructure.mapper.WncWarehouseStorageTenantMapper;
import net.summerfarm.wnc.infrastructure.model.WncWarehouseStorageTenant;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/4/6 11:07<br/>
 *
 * <AUTHOR> />
 */
@Service
public class WncWarehouseStorageTenantRepositoryImpl implements WncWarehouseStorageTenantRepository {

    @Resource
    private WncWarehouseStorageTenantMapper wncWarehouseStorageTenantMapper;

    @Override
    public WncWarehouseStorageTenantEntity queryUk(Long warehouseNo, Long tenantId) {
        WncAssert.notNull(warehouseNo,"warehouseNo不能为空");
        WncAssert.notNull(tenantId,"tenantId不能为空");
        WncWarehouseStorageTenant wncWarehouseStorageTenant = wncWarehouseStorageTenantMapper.selectOne(new LambdaQueryWrapper<WncWarehouseStorageTenant>()
                .eq(WncWarehouseStorageTenant::getWarehouseNo, warehouseNo)
                .eq(WncWarehouseStorageTenant::getTenantId, tenantId)
                .last("limit 1")
        );

        return WncWarehouseStorageTenantConverter.model2Entity(wncWarehouseStorageTenant);
    }

    @Override
    public void save(WncWarehouseStorageTenantEntity rsWarehouseStorageTenantEntity) {
        wncWarehouseStorageTenantMapper.insert(WncWarehouseStorageTenantConverter.entity2Model(rsWarehouseStorageTenantEntity));
    }

    @Override
    public void remove(WncWarehouseStorageTenantEntity rsWarehouseStorageTenantEntity) {
        WncAssert.notNull(rsWarehouseStorageTenantEntity.getWarehouseNo(),"warehouseNo不能为空");
        WncAssert.notNull(rsWarehouseStorageTenantEntity.getTenantId(),"tenantId不能为空");

        WncWarehouseStorageTenant wncWarehouseStorageTenant = wncWarehouseStorageTenantMapper.selectOne(new LambdaQueryWrapper<WncWarehouseStorageTenant>()
                .eq(WncWarehouseStorageTenant::getWarehouseNo, rsWarehouseStorageTenantEntity.getWarehouseNo())
                .eq(WncWarehouseStorageTenant::getTenantId, rsWarehouseStorageTenantEntity.getTenantId())
                .last("limit 1")
        );

        wncWarehouseStorageTenantMapper.deleteById(wncWarehouseStorageTenant);
    }

    @Override
    public List<WncWarehouseStorageTenantEntity> queryList(WncWarehouseStorageTenantQuery rsWarehouseStorageTenantQuery) {
        if(rsWarehouseStorageTenantQuery.getTenantId() == null){
            return Collections.emptyList();
        }
        List<WncWarehouseStorageTenant> wncWarehouseStorageTenants = wncWarehouseStorageTenantMapper.selectList(new LambdaQueryWrapper<WncWarehouseStorageTenant>()
                .eq(rsWarehouseStorageTenantQuery.getTenantId() != null, WncWarehouseStorageTenant::getTenantId, rsWarehouseStorageTenantQuery.getTenantId())
        );

        return wncWarehouseStorageTenants.stream().map(WncWarehouseStorageTenantConverter::model2Entity).collect(Collectors.toList());
    }
}
