package net.summerfarm.wnc.infrastructure.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 服务城市表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-214 11:54:34
 */
@Getter
@Setter
@TableName("area")
public class Area implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 城市编号
     */
    @TableField("area_no")
    private Integer areaNo;

    /**
     * 城市名称
     */
    @TableField("area_name")
    private String areaName;

    /**
     * 城市负责人
     */
    @TableField("admin_id")
    private Integer adminId;

    @TableField("parent_no")
    private Integer parentNo;

    /**
     * 配送频率
     */
    @TableField("delivery_frequent")
    private String deliveryFrequent;

    /**
     * 是否开放，0不开放，1开放
     */
    @TableField("`status`")
    private Boolean status;

    @TableField("delivery_fee")
    private BigDecimal deliveryFee;

    @TableField("info")
    private String info;

    @TableField("address")
    private String address;

    /**
     * 快递费
     */
    @TableField("express_fee")
    private BigDecimal expressFee;

    /**
     * 运费规则
     */
    @TableField("delivery_rule")
    private String deliveryRule;

    /**
     * 会员规则
     */
    @TableField("member_rule")
    private String memberRule;

    /**
     * 收款账号（默认5：杭州）
     */
    @TableField("company_account_id")
    private Integer companyAccountId;

    /**
     * 高德地图poi坐标
     */
    @TableField("poi_note")
    private String poiNote;

    /**
     * 仓库类型:0本部仓、1外部仓、2合伙人仓
     */
    @TableField("`type`")
    private Integer type;

    /**
     * 免配送费日期（结构同delivery_frequent）
     */
    @TableField("free_day")
    private String freeDay;

    /**
     * 预约入库邮件接收人
     */
    @TableField("mail_to_address")
    private String mailToAddress;

    /**
     * 截单映射区域（形如 浙江/杭州市/西湖区，多个区域用“,”隔开，其他区域表示市级映射）
     */
    @TableField("map_section")
    private String mapSection;

    /**
     * 同步城市
     */
    @TableField("origin_area_no")
    private Integer originAreaNo;

    /**
     * 开始/下次配送时间
     */
    @TableField("next_delivery_date")
    private LocalDate nextDeliveryDate;

    /**
     * 支付通道，0微信 1中银 2招行
     */
    @TableField("pay_channel")
    private Integer payChannel;

    /**
     * 仓库切换标识：f、未预约切换 t、切换中
     */
    @TableField("change_flag")
    private Boolean changeFlag;

    /**
     * 切换的城市编号（处理完后清空）
     */
    @TableField("change_store_no")
    private Integer changeStoreNo;

    /**
     * 切换状态：0、默认 1、预约中 2、大客户停服 3、城市停服
     */
    @TableField("change_status")
    private Integer changeStatus;

    /**
     * 根据国家行政区域划分，精确到市
     */
    @TableField("administrative_area")
    private String administrativeArea;

    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 是否支持加单 0 支持加单 1 不支持加单
     */
    @TableField("support_add_order")
    private Integer supportAddOrder;

    /**
     * 更新是否支持加单
     */
    @TableField("update_support_add_order")
    private Integer updateSupportAddOrder;

    /**
     * 运营大区编号
     */
    @TableField("large_area_no")
    private Integer largeAreaNo;

    /**
     * 区域等级:S.A.B.C.D
     */
    @TableField("grade")
    private String grade;

    /**
     * 微信小程序是否使用招行收款：0、不使用（默认）1、使用
     */
    @TableField("wxlite_pay_channel")
    private Integer wxlitePayChannel;


}
