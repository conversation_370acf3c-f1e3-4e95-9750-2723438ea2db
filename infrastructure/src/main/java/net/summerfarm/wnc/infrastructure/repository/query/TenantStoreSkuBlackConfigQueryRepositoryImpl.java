package net.summerfarm.wnc.infrastructure.repository.query;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.domain.config.entity.TenantStoreSkuBlackConfigEntity;
import net.summerfarm.wnc.domain.config.param.query.WncTenantStoreSkuBlackConfigQueryParam;
import net.summerfarm.wnc.domain.config.repository.TenantStoreSkuBlackConfigQueryRepository;
import net.summerfarm.wnc.infrastructure.converter.TenantStoreSkuBlackConfigConverter;
import net.summerfarm.wnc.infrastructure.converter.config.WncTenantStoreSkuBlackConfigConverter;
import net.summerfarm.wnc.infrastructure.mapper.WncTenantStoreSkuBlackConfigMapper;
import net.summerfarm.wnc.infrastructure.model.WncTenantStoreSkuBlackConfig;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: 租户城配仓sku黑名单配置查询<br/>
 * date: 2024/8/26 15:13<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Service
public class TenantStoreSkuBlackConfigQueryRepositoryImpl implements TenantStoreSkuBlackConfigQueryRepository {

    @Resource
    private WncTenantStoreSkuBlackConfigMapper wncTenantStoreSkuBlackConfigMapper;

    @Override
    public List<TenantStoreSkuBlackConfigEntity> queryByTenantIdAndSkuList(Long tenantId, List<String> skuList) {
        if(tenantId == null){
            return Collections.emptyList();
        }

        if(CollectionUtils.isEmpty(skuList)){
            return Collections.emptyList();
        }

        List<WncTenantStoreSkuBlackConfig> wncTenantStoreSkuBlackConfigs = wncTenantStoreSkuBlackConfigMapper.selectList(new LambdaQueryWrapper<WncTenantStoreSkuBlackConfig>()
                .eq(WncTenantStoreSkuBlackConfig::getTenantId, tenantId)
                .in(WncTenantStoreSkuBlackConfig::getSku, skuList)
        );

        return wncTenantStoreSkuBlackConfigs.stream().map(TenantStoreSkuBlackConfigConverter::convert).collect(Collectors.toList());
    }

    @Override
    public PageInfo<TenantStoreSkuBlackConfigEntity> getPage(WncTenantStoreSkuBlackConfigQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<TenantStoreSkuBlackConfigEntity> entities = wncTenantStoreSkuBlackConfigMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public TenantStoreSkuBlackConfigEntity selectById(Long id) {
        return WncTenantStoreSkuBlackConfigConverter.toWncTenantStoreSkuBlackConfigEntity(wncTenantStoreSkuBlackConfigMapper.selectById(id));
    }


    @Override
    public List<TenantStoreSkuBlackConfigEntity> selectByCondition(WncTenantStoreSkuBlackConfigQueryParam param) {
        return WncTenantStoreSkuBlackConfigConverter.toWncTenantStoreSkuBlackConfigEntityList(wncTenantStoreSkuBlackConfigMapper.selectByCondition(param));
    }
}
