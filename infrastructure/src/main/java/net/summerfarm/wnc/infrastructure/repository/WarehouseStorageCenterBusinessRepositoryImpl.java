package net.summerfarm.wnc.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.summerfarm.wnc.common.query.warehouse.WarehouseBusinessQuery;
import net.summerfarm.wnc.domain.warehouse.WarehouseStorageCenterBusinessRepository;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseStorageCenterBusEntity;
import net.summerfarm.wnc.infrastructure.converter.WarehouseStorageCenterBusinessConverter;
import net.summerfarm.wnc.infrastructure.mapper.WarehouseStorageCenterBusinessMapper;
import net.summerfarm.wnc.infrastructure.model.WarehouseStorageCenterBusiness;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/4/4 18:58<br/>
 *
 * <AUTHOR> />
 */
@Service
public class WarehouseStorageCenterBusinessRepositoryImpl implements WarehouseStorageCenterBusinessRepository {

    @Resource
    private WarehouseStorageCenterBusinessMapper warehouseStorageCenterBusinessMapper;

    @Override
    public List<WarehouseStorageCenterBusEntity> queryList(WarehouseBusinessQuery warehouseBusinessQuery) {
        List<WarehouseStorageCenterBusiness> warehouseStorageCenterBusinesses = warehouseStorageCenterBusinessMapper.selectList(new LambdaQueryWrapper<WarehouseStorageCenterBusiness>()
                .in(!CollectionUtils.isEmpty(warehouseBusinessQuery.getWarehouseNos()), WarehouseStorageCenterBusiness::getWarehouseNo, warehouseBusinessQuery.getWarehouseNos())
        );
        return warehouseStorageCenterBusinesses.stream().map(WarehouseStorageCenterBusinessConverter::warehouseStorageCenterBus2Entity).collect(Collectors.toList());
    }
}
