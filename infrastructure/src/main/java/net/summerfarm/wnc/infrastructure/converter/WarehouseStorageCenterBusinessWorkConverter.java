package net.summerfarm.wnc.infrastructure.converter;

import net.summerfarm.wnc.domain.warehouse.entity.WarehouseStorageCenterWorkEntity;
import net.summerfarm.wnc.infrastructure.model.WarehouseStorageCenterBusinessWork;

/**
 * Description: <br/>
 * date: 2023/8/29 16:34<br/>
 *
 * <AUTHOR> />
 */
public class WarehouseStorageCenterBusinessWorkConverter {

    public static WarehouseStorageCenterWorkEntity model2Entity(WarehouseStorageCenterBusinessWork warehouseStorageCenterBusinessWork){
        if(warehouseStorageCenterBusinessWork == null){
            return null;
        }
        WarehouseStorageCenterWorkEntity warehouseStorageCenterWorkEntity = new WarehouseStorageCenterWorkEntity();

        warehouseStorageCenterWorkEntity.setId(warehouseStorageCenterBusinessWork.getId());
        warehouseStorageCenterWorkEntity.setWarehouseStorageCenterBusinessId(warehouseStorageCenterBusinessWork.getWarehouseStorageCenterBusinessId());
        warehouseStorageCenterWorkEntity.setWarehouseNo(warehouseStorageCenterBusinessWork.getWarehouseNo());
        warehouseStorageCenterWorkEntity.setWorkStartTime(warehouseStorageCenterBusinessWork.getWorkStartTime());
        warehouseStorageCenterWorkEntity.setWorkEndTime(warehouseStorageCenterBusinessWork.getWorkEndTime());
        warehouseStorageCenterWorkEntity.setIsDelete(warehouseStorageCenterBusinessWork.getIsDelete());
        warehouseStorageCenterWorkEntity.setCreateTime(warehouseStorageCenterBusinessWork.getCreateTime());
        warehouseStorageCenterWorkEntity.setUpdateTime(warehouseStorageCenterBusinessWork.getUpdateTime());

        return warehouseStorageCenterWorkEntity;
    }

    public static WarehouseStorageCenterBusinessWork entity2Model(WarehouseStorageCenterWorkEntity warehouseStorageCenterWorkEntity){
        if(warehouseStorageCenterWorkEntity == null){
            return null;
        }
        WarehouseStorageCenterBusinessWork warehouseStorageCenterBusinessWork = new WarehouseStorageCenterBusinessWork();

        warehouseStorageCenterBusinessWork.setId(warehouseStorageCenterWorkEntity.getId());
        warehouseStorageCenterBusinessWork.setWarehouseStorageCenterBusinessId(warehouseStorageCenterWorkEntity.getWarehouseStorageCenterBusinessId());
        warehouseStorageCenterBusinessWork.setWarehouseNo(warehouseStorageCenterWorkEntity.getWarehouseNo());
        warehouseStorageCenterBusinessWork.setWorkStartTime(warehouseStorageCenterWorkEntity.getWorkStartTime());
        warehouseStorageCenterBusinessWork.setWorkEndTime(warehouseStorageCenterWorkEntity.getWorkEndTime());
        warehouseStorageCenterBusinessWork.setIsDelete(warehouseStorageCenterWorkEntity.getIsDelete());
        warehouseStorageCenterBusinessWork.setCreateTime(warehouseStorageCenterWorkEntity.getCreateTime());
        warehouseStorageCenterBusinessWork.setUpdateTime(warehouseStorageCenterWorkEntity.getUpdateTime());

        return warehouseStorageCenterBusinessWork;
    }
}
