package net.summerfarm.wnc.infrastructure.converter;

import com.google.common.collect.Lists;
import net.summerfarm.wnc.domain.preciseDelivery.aggregate.PreciseDeliveryConfigAggregate;
import net.summerfarm.wnc.domain.preciseDelivery.entity.PreciseDeliveryConfigEntity;
import net.summerfarm.wnc.domain.preciseDelivery.param.PreciseDeliveryConfigAreaCommandParam;
import net.summerfarm.wnc.domain.preciseDelivery.param.PreciseDeliveryConfigCommandParam;
import net.summerfarm.wnc.domain.preciseDelivery.param.PreciseDeliveryConfigTimeCommandParam;
import net.summerfarm.wnc.domain.preciseDelivery.valueObject.PreciseDeliveryConfigAreaValueObject;
import net.summerfarm.wnc.domain.preciseDelivery.valueObject.PreciseDeliveryConfigTimeValueObject;
import net.summerfarm.wnc.infrastructure.model.WncPreciseDeliveryAreaDetail;
import net.summerfarm.wnc.infrastructure.model.WncPreciseDeliveryConfig;
import net.summerfarm.wnc.infrastructure.model.WncPreciseDeliveryTimeDetail;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Description:精准送配置时效转换器
 * date: 2024/1/22 18:34
 *
 * <AUTHOR>
 */
public class WncPreciseDeliveryTimeDetailConverter {

    public static PreciseDeliveryConfigTimeValueObject do2Entity(WncPreciseDeliveryTimeDetail wncPreciseDeliveryTimeDetail){
        if(wncPreciseDeliveryTimeDetail == null){
            return null;
        }
        PreciseDeliveryConfigTimeValueObject preciseDeliveryConfigTimeValueObject = new PreciseDeliveryConfigTimeValueObject();
        preciseDeliveryConfigTimeValueObject.setId(wncPreciseDeliveryTimeDetail.getId());
        preciseDeliveryConfigTimeValueObject.setConfigId(wncPreciseDeliveryTimeDetail.getConfigId());
        preciseDeliveryConfigTimeValueObject.setBeginTime(wncPreciseDeliveryTimeDetail.getBeginTime());
        preciseDeliveryConfigTimeValueObject.setEndTime(wncPreciseDeliveryTimeDetail.getEndTime());
        preciseDeliveryConfigTimeValueObject.setCreateTime(wncPreciseDeliveryTimeDetail.getCreateTime());
        return preciseDeliveryConfigTimeValueObject;
    }

    public static WncPreciseDeliveryTimeDetail param2Do(PreciseDeliveryConfigTimeCommandParam param){
        if(param == null){
            return null;
        }
        WncPreciseDeliveryTimeDetail wncPreciseDeliveryTimeDetail = new WncPreciseDeliveryTimeDetail();
        wncPreciseDeliveryTimeDetail.setBeginTime(param.getBeginTime());
        wncPreciseDeliveryTimeDetail.setEndTime(param.getEndTime());
        wncPreciseDeliveryTimeDetail.setCreateTime(param.getCreateTime());
        return wncPreciseDeliveryTimeDetail;
    }

}
