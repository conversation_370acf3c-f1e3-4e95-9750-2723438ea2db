package net.summerfarm.wnc.infrastructure.mapper.warehouseMapping;

import net.summerfarm.wnc.infrastructure.model.warehouseMapping.WncWarehouseThirdMapping;
import net.summerfarm.wnc.domain.warehouseMapping.param.query.WncWarehouseThirdMappingQueryParam;
import net.summerfarm.wnc.domain.warehouseMapping.entity.WncWarehouseThirdMappingEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025-06-11 15:08:31
 * @version 1.0
 *
 */
@Mapper
public interface WncWarehouseThirdMappingMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(WncWarehouseThirdMapping record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(WncWarehouseThirdMapping record);

    /**
     * @Describe: 通过主键删除
     * @param
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    WncWarehouseThirdMapping selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param param
     * @return
     */
    List<WncWarehouseThirdMapping> selectByCondition(WncWarehouseThirdMappingQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param param
     * @return
     */
    List<WncWarehouseThirdMappingEntity> getPage(WncWarehouseThirdMappingQueryParam param);
}

