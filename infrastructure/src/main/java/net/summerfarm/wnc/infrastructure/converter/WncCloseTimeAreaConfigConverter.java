package net.summerfarm.wnc.infrastructure.converter;

import net.summerfarm.wnc.common.enums.CloseTimeConfigEnums;
import net.summerfarm.wnc.domain.closeTime.entity.CloseTimeAreaConfigEntity;
import net.summerfarm.wnc.domain.closeTime.param.CloseTimeAreaConfigCommandParam;
import net.summerfarm.wnc.infrastructure.model.WncCloseTimeAreaConfig;

/**
 * Description:截单时间区域配置转换器
 * date: 2024/3/20 15:17
 *
 * <AUTHOR>
 */
public class WncCloseTimeAreaConfigConverter {

    public static CloseTimeAreaConfigEntity do2Entity(WncCloseTimeAreaConfig wncCloseTimeAreaConfig){
        if(wncCloseTimeAreaConfig == null){
            return null;
        }
        CloseTimeAreaConfigEntity closeTimeAreaConfigEntity = new CloseTimeAreaConfigEntity();
        closeTimeAreaConfigEntity.setId(wncCloseTimeAreaConfig.getId());
        closeTimeAreaConfigEntity.setTenantId(wncCloseTimeAreaConfig.getTenantId());
        closeTimeAreaConfigEntity.setBrandId(wncCloseTimeAreaConfig.getBrandId());
        closeTimeAreaConfigEntity.setProvince(wncCloseTimeAreaConfig.getProvince());
        closeTimeAreaConfigEntity.setCity(wncCloseTimeAreaConfig.getCity());
        closeTimeAreaConfigEntity.setArea(wncCloseTimeAreaConfig.getArea());
        closeTimeAreaConfigEntity.setCloseTime(wncCloseTimeAreaConfig.getCloseTime());
        closeTimeAreaConfigEntity.setUpdateCloseTime(wncCloseTimeAreaConfig.getUpdateCloseTime());
        closeTimeAreaConfigEntity.setUpdateFlag(CloseTimeConfigEnums.UpdateFlag.getFlagByValue(wncCloseTimeAreaConfig.getUpdateFlag()));
        closeTimeAreaConfigEntity.setCreator(wncCloseTimeAreaConfig.getCreator());
        closeTimeAreaConfigEntity.setCreateTime(wncCloseTimeAreaConfig.getCreateTime());
        closeTimeAreaConfigEntity.setUpdater(wncCloseTimeAreaConfig.getUpdater());
        closeTimeAreaConfigEntity.setUpdateTime(wncCloseTimeAreaConfig.getUpdateTime());
        return closeTimeAreaConfigEntity;
    }

    public static WncCloseTimeAreaConfig param2Do(CloseTimeAreaConfigCommandParam param){
        if(param == null){
            return null;
        }
        WncCloseTimeAreaConfig wncCloseTimeAreaConfig = new WncCloseTimeAreaConfig();
        wncCloseTimeAreaConfig.setId(param.getId());
        wncCloseTimeAreaConfig.setTenantId(param.getTenantId());
        wncCloseTimeAreaConfig.setBrandId(param.getBrandId());
        wncCloseTimeAreaConfig.setProvince(param.getProvince());
        wncCloseTimeAreaConfig.setCity(param.getCity());
        wncCloseTimeAreaConfig.setArea(param.getArea());
        wncCloseTimeAreaConfig.setCloseTime(param.getCloseTime());
        wncCloseTimeAreaConfig.setUpdateCloseTime(param.getUpdateCloseTime());
        wncCloseTimeAreaConfig.setUpdateFlag(param.getUpdateFlag());
        wncCloseTimeAreaConfig.setCreator(param.getCreator());
        wncCloseTimeAreaConfig.setCreateTime(param.getCreateTime());
        wncCloseTimeAreaConfig.setUpdater(param.getUpdater());
        wncCloseTimeAreaConfig.setUpdateTime(param.getUpdateTime());
        return wncCloseTimeAreaConfig;
    }
}
