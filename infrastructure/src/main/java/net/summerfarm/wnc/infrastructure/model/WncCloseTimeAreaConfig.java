package net.summerfarm.wnc.infrastructure.model;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 截单时间配置-行政区域维度
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-79 17:12:17
 */
@Getter
@Setter
@TableName("wnc_close_time_area_config")
public class WncCloseTimeAreaConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 品牌ID
     */
    @TableField("brand_id")
    private Long brandId;

    /**
     * 省
     */
    @TableField("province")
    private String province;

    /**
     * 市
     */
    @TableField("city")
    private String city;

    /**
     * 区
     */
    @TableField("area")
    private String area;

    /**
     * 截单时间
     */
    @TableField(value = "close_time")
    private LocalTime closeTime;

    /**
     * 更新截单时间
     */
    @TableField(value ="update_close_time",updateStrategy = FieldStrategy.IGNORED)
    private LocalTime updateCloseTime;

    /**
     * 有无更新标识，0：无，1：有
     */
    @TableField("update_flag")
    private Integer updateFlag;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField("updater")
    private String updater;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

}
