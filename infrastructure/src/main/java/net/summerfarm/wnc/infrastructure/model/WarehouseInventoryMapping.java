package net.summerfarm.wnc.infrastructure.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * sku配送中心仓库映射表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-96 15:43:38
 */
@Getter
@Setter
@TableName("warehouse_inventory_mapping")
public class WarehouseInventoryMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键、自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 仓库编号
     */
    @TableField("warehouse_no")
    private Integer warehouseNo;

    /**
     * 物流中心编号
     */
    @TableField("store_no")
    private Integer storeNo;

    /**
     * sku
     */
    @TableField("sku")
    private String sku;

    /**
     * 物流中心销售冻结
     */
    @TableField("sale_lock_quantity")
    private Integer saleLockQuantity;

    /**
     * 预留库存使用数量
     */
    @TableField("reserve_quantity")
    private Integer reserveQuantity;

    /**
     * 是否支持预留库存
     */
    @TableField("support_reserved")
    private Integer supportReserved;

    /**
     * 更新人
     */
    @TableField("updater")
    private Integer updater;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField("creator")
    private Integer creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;


}
