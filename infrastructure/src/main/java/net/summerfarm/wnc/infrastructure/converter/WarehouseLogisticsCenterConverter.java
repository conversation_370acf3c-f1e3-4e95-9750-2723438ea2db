package net.summerfarm.wnc.infrastructure.converter;

import net.summerfarm.wnc.common.enums.WarehouseLogisticsCenterEnums;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseLogisticsCenterEntity;
import net.summerfarm.wnc.infrastructure.model.WarehouseLogisticsCenter;

/**
 * Description:配送中心转换器
 * date: 2023/8/28 18:09
 *
 * <AUTHOR>
 */
public class WarehouseLogisticsCenterConverter {

    public static WarehouseLogisticsCenter entity2Do(WarehouseLogisticsCenterEntity warehouseLogisticsCenterEntity){
        if(warehouseLogisticsCenterEntity == null){
            return null;
        }
        WarehouseLogisticsCenter warehouseLogisticsCenter = new WarehouseLogisticsCenter();
        warehouseLogisticsCenter.setId(warehouseLogisticsCenterEntity.getId());
        warehouseLogisticsCenter.setStoreNo(warehouseLogisticsCenterEntity.getStoreNo());
        warehouseLogisticsCenter.setStoreName(warehouseLogisticsCenterEntity.getStoreName());
        warehouseLogisticsCenter.setStatus(warehouseLogisticsCenterEntity.getStatus());
        warehouseLogisticsCenter.setManageAdminId(warehouseLogisticsCenterEntity.getManageAdminId());
        warehouseLogisticsCenter.setPoiNote(warehouseLogisticsCenterEntity.getPoiNote());
        warehouseLogisticsCenter.setAddress(warehouseLogisticsCenterEntity.getAddress());
        warehouseLogisticsCenter.setCloseOrderType(warehouseLogisticsCenterEntity.getCloseOrderType());
        warehouseLogisticsCenter.setOriginStoreNo(warehouseLogisticsCenterEntity.getOriginStoreNo());
        warehouseLogisticsCenter.setSotFinishTime(warehouseLogisticsCenterEntity.getSotFinishTime());
        warehouseLogisticsCenter.setCreator(warehouseLogisticsCenterEntity.getCreator());
        warehouseLogisticsCenter.setUpdater(warehouseLogisticsCenterEntity.getUpdater());
        warehouseLogisticsCenter.setUpdateTime(warehouseLogisticsCenterEntity.getUpdateTime());
        warehouseLogisticsCenter.setCreateTime(warehouseLogisticsCenterEntity.getCreateTime());
        warehouseLogisticsCenter.setCloseTime(warehouseLogisticsCenterEntity.getCloseTime());
        warehouseLogisticsCenter.setUpdateCloseTime(warehouseLogisticsCenterEntity.getUpdateCloseTime());
        warehouseLogisticsCenter.setPersonContact(warehouseLogisticsCenterEntity.getPersonContact());
        warehouseLogisticsCenter.setPhone(warehouseLogisticsCenterEntity.getPhone());
        warehouseLogisticsCenter.setRegion(warehouseLogisticsCenterEntity.getRegion());
        warehouseLogisticsCenter.setStorePic(warehouseLogisticsCenterEntity.getStorePic());
        warehouseLogisticsCenter.setFulfillmentType(warehouseLogisticsCenterEntity.getFulfillmentType().getValue());
        return warehouseLogisticsCenter;
    }

    public static WarehouseLogisticsCenterEntity do2Entity(WarehouseLogisticsCenter warehouseLogisticsCenter){
        if(warehouseLogisticsCenter == null){
            return null;
        }
        WarehouseLogisticsCenterEntity warehouseLogisticsCenterEntity = new WarehouseLogisticsCenterEntity();
        warehouseLogisticsCenterEntity.setId(warehouseLogisticsCenter.getId());
        warehouseLogisticsCenterEntity.setStoreNo(warehouseLogisticsCenter.getStoreNo());
        warehouseLogisticsCenterEntity.setStoreName(warehouseLogisticsCenter.getStoreName());
        warehouseLogisticsCenterEntity.setStatus(warehouseLogisticsCenter.getStatus());
        warehouseLogisticsCenterEntity.setManageAdminId(warehouseLogisticsCenter.getManageAdminId());
        warehouseLogisticsCenterEntity.setPoiNote(warehouseLogisticsCenter.getPoiNote());
        warehouseLogisticsCenterEntity.setAddress(warehouseLogisticsCenter.getAddress());
        warehouseLogisticsCenterEntity.setCloseOrderType(warehouseLogisticsCenter.getCloseOrderType());
        warehouseLogisticsCenterEntity.setOriginStoreNo(warehouseLogisticsCenter.getOriginStoreNo());
        warehouseLogisticsCenterEntity.setSotFinishTime(warehouseLogisticsCenter.getSotFinishTime());
        warehouseLogisticsCenterEntity.setCreator(warehouseLogisticsCenter.getCreator());
        warehouseLogisticsCenterEntity.setUpdater(warehouseLogisticsCenter.getUpdater());
        warehouseLogisticsCenterEntity.setUpdateTime(warehouseLogisticsCenter.getUpdateTime());
        warehouseLogisticsCenterEntity.setCreateTime(warehouseLogisticsCenter.getCreateTime());
        warehouseLogisticsCenterEntity.setCloseTime(warehouseLogisticsCenter.getCloseTime());
        warehouseLogisticsCenterEntity.setUpdateCloseTime(warehouseLogisticsCenter.getUpdateCloseTime());
        warehouseLogisticsCenterEntity.setPersonContact(warehouseLogisticsCenter.getPersonContact());
        warehouseLogisticsCenterEntity.setPhone(warehouseLogisticsCenter.getPhone());
        warehouseLogisticsCenterEntity.setRegion(warehouseLogisticsCenter.getRegion());
        warehouseLogisticsCenterEntity.setStorePic(warehouseLogisticsCenter.getStorePic());

        WarehouseLogisticsCenterEnums.FulfillmentType fulfillmentType = WarehouseLogisticsCenterEnums.FulfillmentType.getTypeByValue(warehouseLogisticsCenter.getFulfillmentType());
        warehouseLogisticsCenterEntity.setFulfillmentType(fulfillmentType);
        return warehouseLogisticsCenterEntity;
    }
}
