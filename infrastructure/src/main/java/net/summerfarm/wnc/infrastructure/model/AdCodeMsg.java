package net.summerfarm.wnc.infrastructure.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 围栏对应的区域信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-96 15:47:56
 */
@Getter
@Setter
@TableName("ad_code_msg")
public class AdCodeMsg implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 区域编码
     */
    @TableField("ad_code")
    private String adCode;

    /**
     * 省
     */
    @TableField("province")
    private String province;

    /**
     * 市
     */
    @TableField("city")
    private String city;

    /**
     * 区
     */
    @TableField("area")
    private String area;

    /**
     * 等级
     */
    @TableField("`level`")
    private String level;

    /**
     * 高德id
     */
    @TableField("gd_id")
    private String gdId;

    @TableField("add_time")
    private LocalDateTime addTime;

    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 状态 0 正常 1 失效 3停用
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 围栏id
     */
    @TableField("fence_id")
    private Integer fenceId;


}
