package net.summerfarm.wnc.infrastructure.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 区域精准送配置-区域详情
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-18 17:24:10
 */
@Getter
@Setter
@TableName("wnc_precise_delivery_area_detail")
public class WncPreciseDeliveryAreaDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("id")
    private Long id;

    /**
     * 配置ID
     */
    @TableField("config_id")
    private Long configId;

    /**
     * 区域编码
     */
    @TableField("ad_code")
    private String adCode;

    /**
     * 市
     */
    @TableField("city")
    private String city;

    /**
     * 区
     */
    @TableField("area")
    private String area;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;


}
