package net.summerfarm.wnc.infrastructure.converter;

import net.summerfarm.wnc.domain.warehouse.entity.WarehouseLogisticsMappingEntity;
import net.summerfarm.wnc.infrastructure.model.WarehouseLogisticsMapping;

/**
 * Description: <br/>
 * date: 2023/4/7 16:40<br/>
 *
 * <AUTHOR> />
 */
public class WarehouseLogisticsMappingConverter {

    public static WarehouseLogisticsMappingEntity model2Entity(WarehouseLogisticsMapping warehouseLogisticsMapping){
        if(warehouseLogisticsMapping == null){
            return null;
        }
        WarehouseLogisticsMappingEntity warehouseLogisticsMappingEntity = new WarehouseLogisticsMappingEntity();
        warehouseLogisticsMappingEntity.setId(warehouseLogisticsMapping.getId());
        warehouseLogisticsMappingEntity.setStoreNo(warehouseLogisticsMapping.getStoreNo());
        warehouseLogisticsMappingEntity.setWarehouseNo(warehouseLogisticsMapping.getWarehouseNo());
        warehouseLogisticsMappingEntity.setCreator(warehouseLogisticsMapping.getCreator());
        warehouseLogisticsMappingEntity.setCreateTime(warehouseLogisticsMapping.getCreateTime());

        return warehouseLogisticsMappingEntity;
    }

    public static WarehouseLogisticsMapping entity2Model(WarehouseLogisticsMappingEntity entity){
        if(entity == null){
            return null;
        }
        WarehouseLogisticsMapping model = new WarehouseLogisticsMapping();

        model.setId(entity.getId());
        model.setStoreNo(entity.getStoreNo());
        model.setWarehouseNo(entity.getWarehouseNo());
        model.setCreator(entity.getCreator());
        model.setCreateTime(entity.getCreateTime());

        return model;
    }
}
