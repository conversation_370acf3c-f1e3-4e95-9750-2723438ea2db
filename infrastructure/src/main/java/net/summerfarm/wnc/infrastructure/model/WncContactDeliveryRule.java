package net.summerfarm.wnc.infrastructure.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 地址配送规则
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-317 11:35:16
 */
@Getter
@Setter
@TableName("wnc_contact_delivery_rule")
public class WncContactDeliveryRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 外部业务编号
     */
    @TableField("out_business_no")
    private String outBusinessNo;

    /**
     * 系统来源 0鲜沐商城 1Saas
     */
    @TableField("system_source")
    private Integer systemSource;

    /**
     * 周期方案 1周计算 2间隔计算
     */
    @TableField("frequent_method")
    private Integer frequentMethod;

    /**
     * 周的配送周期 0每天 1周一依次
     */
    @TableField("week_delivery_frequent")
    private String weekDeliveryFrequent;

    /**
     * 配送间隔周期
     */
    @TableField("delivery_frequent_interval")
    private Integer deliveryFrequentInterval;

    /**
     * 开始计算日期
     */
    @TableField("begin_calculate_date")
    private LocalDate beginCalculateDate;


    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;
}
