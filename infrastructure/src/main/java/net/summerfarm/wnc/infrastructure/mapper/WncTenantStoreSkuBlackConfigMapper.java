package net.summerfarm.wnc.infrastructure.mapper;

import net.summerfarm.wnc.domain.config.entity.TenantStoreSkuBlackConfigEntity;
import net.summerfarm.wnc.domain.config.param.query.WncTenantStoreSkuBlackConfigQueryParam;
import net.summerfarm.wnc.infrastructure.model.WncTenantStoreSkuBlackConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 租户城配仓SKU黑名单配置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-239 14:42:07
 */
public interface WncTenantStoreSkuBlackConfigMapper extends BaseMapper<WncTenantStoreSkuBlackConfig> {
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(WncTenantStoreSkuBlackConfig record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(WncTenantStoreSkuBlackConfig record);

    /**
     * @Describe: 通过主键删除
     * @param
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    WncTenantStoreSkuBlackConfig selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param param
     * @return
     */
    List<WncTenantStoreSkuBlackConfig> selectByCondition(WncTenantStoreSkuBlackConfigQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param param
     * @return
     */
    List<TenantStoreSkuBlackConfigEntity> getPage(WncTenantStoreSkuBlackConfigQueryParam param);
}
