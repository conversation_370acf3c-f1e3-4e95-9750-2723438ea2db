package net.summerfarm.wnc.infrastructure.converter;

import net.summerfarm.wnc.domain.warehouse.entity.WarehouseTakeStandardEntity;
import net.summerfarm.wnc.infrastructure.model.WarehouseTakeStandard;

/**
 * Description: <br/>
 * date: 2023/3/28 17:01<br/>
 *
 * <AUTHOR> />
 */
public class WarehouseTakeStandardConverter {

    public static WarehouseTakeStandardEntity model2Entity(WarehouseTakeStandard warehouseTakeStandard){
        if(warehouseTakeStandard == null){
            return null;
        }
        WarehouseTakeStandardEntity warehouseTakeStandardEntity = new WarehouseTakeStandardEntity();

        warehouseTakeStandardEntity.setId(warehouseTakeStandard.getId());
        warehouseTakeStandardEntity.setCreateTime(warehouseTakeStandard.getCreateTime());
        warehouseTakeStandardEntity.setUpdateTime(warehouseTakeStandard.getUpdateTime());
        warehouseTakeStandardEntity.setStandardType(warehouseTakeStandard.getStandardType());
        warehouseTakeStandardEntity.setStorageLocation(warehouseTakeStandard.getStorageLocation());
        warehouseTakeStandardEntity.setWarehouseNo(warehouseTakeStandard.getWarehouseNo());
        warehouseTakeStandardEntity.setProveType(warehouseTakeStandard.getProveType());
        warehouseTakeStandardEntity.setCreateAdminId(warehouseTakeStandard.getCreateAdminId());

        return warehouseTakeStandardEntity;
    }

    public static WarehouseTakeStandard entity2model(WarehouseTakeStandardEntity warehouseTakeStandardEntity){
        if(warehouseTakeStandardEntity == null){
            return null;
        }
        WarehouseTakeStandard warehouseTakeStandard = new WarehouseTakeStandard();

        warehouseTakeStandard.setId(warehouseTakeStandardEntity.getId());
        warehouseTakeStandard.setCreateTime(warehouseTakeStandardEntity.getCreateTime());
        warehouseTakeStandard.setUpdateTime(warehouseTakeStandardEntity.getUpdateTime());
        warehouseTakeStandard.setStandardType(warehouseTakeStandardEntity.getStandardType());
        warehouseTakeStandard.setStorageLocation(warehouseTakeStandardEntity.getStorageLocation());
        warehouseTakeStandard.setWarehouseNo(warehouseTakeStandardEntity.getWarehouseNo());
        warehouseTakeStandard.setProveType(warehouseTakeStandardEntity.getProveType());
        warehouseTakeStandard.setCreateAdminId(warehouseTakeStandardEntity.getCreateAdminId());

        return warehouseTakeStandard;
    }
}
