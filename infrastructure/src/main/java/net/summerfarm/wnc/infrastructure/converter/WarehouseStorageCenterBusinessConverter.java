package net.summerfarm.wnc.infrastructure.converter;


import net.summerfarm.wnc.domain.warehouse.entity.WarehouseStorageCenterBusEntity;
import net.summerfarm.wnc.infrastructure.model.WarehouseStorageCenterBusiness;

/**
 * Description: <br/>
 * date: 2023/3/28 17:00<br/>
 *
 * <AUTHOR> />
 */
public class WarehouseStorageCenterBusinessConverter {

    public static WarehouseStorageCenterBusEntity warehouseStorageCenterBus2Entity(WarehouseStorageCenterBusiness warehouseStorageCenterBusiness){
        if(warehouseStorageCenterBusiness == null){
            return null;
        }

        WarehouseStorageCenterBusEntity warehouseStorageCenterBusEntity = new WarehouseStorageCenterBusEntity();

        warehouseStorageCenterBusEntity.setId(warehouseStorageCenterBusiness.getId());
        warehouseStorageCenterBusEntity.setWarehouseNo(warehouseStorageCenterBusiness.getWarehouseNo());
        warehouseStorageCenterBusEntity.setCapacity(warehouseStorageCenterBusiness.getCapacity());
        warehouseStorageCenterBusEntity.setAdvanceDay(warehouseStorageCenterBusiness.getAdvanceDay());
        warehouseStorageCenterBusEntity.setCreateTime(warehouseStorageCenterBusiness.getCreateTime());
        warehouseStorageCenterBusEntity.setUpdateTime(warehouseStorageCenterBusiness.getUpdateTime());
        warehouseStorageCenterBusEntity.setIsDelete(warehouseStorageCenterBusiness.getIsDelete());

        return warehouseStorageCenterBusEntity;
    }

    public static WarehouseStorageCenterBusiness entity2Model(WarehouseStorageCenterBusEntity warehouseStorageCenterBusEntity){
        if(warehouseStorageCenterBusEntity == null){
            return null;
        }

        WarehouseStorageCenterBusiness warehouseStorageCenterBusiness = new WarehouseStorageCenterBusiness();

        warehouseStorageCenterBusiness.setId(warehouseStorageCenterBusEntity.getId());
        warehouseStorageCenterBusiness.setWarehouseNo(warehouseStorageCenterBusEntity.getWarehouseNo());
        warehouseStorageCenterBusiness.setCapacity(warehouseStorageCenterBusEntity.getCapacity());
        warehouseStorageCenterBusiness.setAdvanceDay(warehouseStorageCenterBusEntity.getAdvanceDay());
        warehouseStorageCenterBusiness.setCreateTime(warehouseStorageCenterBusEntity.getCreateTime());
        warehouseStorageCenterBusiness.setUpdateTime(warehouseStorageCenterBusEntity.getUpdateTime());
        warehouseStorageCenterBusiness.setIsDelete(warehouseStorageCenterBusEntity.getIsDelete());

        return warehouseStorageCenterBusiness;
    }
}
