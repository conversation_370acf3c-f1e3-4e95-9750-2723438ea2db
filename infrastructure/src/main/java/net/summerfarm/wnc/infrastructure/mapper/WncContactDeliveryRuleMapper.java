package net.summerfarm.wnc.infrastructure.mapper;

import net.summerfarm.wnc.domain.config.monit.PopMerchantMonit;
import net.summerfarm.wnc.infrastructure.model.WncContactDeliveryRule;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 地址配送规则 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-317 11:35:16
 */
public interface WncContactDeliveryRuleMapper extends BaseMapper<WncContactDeliveryRule> {

    /**
     * 查询POP没有指定城配仓的数据
     * @param adminId 大客户ID
     * @return 结果
     */
    List<PopMerchantMonit> queryPopNoDeliveryRuleMonit(@Param("adminId") Integer adminId);
}
