package net.summerfarm.wnc.infrastructure.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 仓储物流中心对应
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-97 16:09:57
 */
@Getter
@Setter
@TableName("warehouse_logistics_mapping")
public class WarehouseLogisticsMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键、自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 物流中心编号（配送仓编号）
     */
    @TableField("store_no")
    private Integer storeNo;

    /**
     * 仓库编号（库存仓编号）
     */
    @TableField("warehouse_no")
    private Integer warehouseNo;

    /**
     * 创建人adminId
     */
    @TableField("creator")
    private Integer creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;


}
