package net.summerfarm.wnc.infrastructure.mapper;

import net.summerfarm.wnc.common.query.changeTask.FenceChangeTaskPageQuery;
import net.summerfarm.wnc.infrastructure.model.WncFenceChangeTask;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <p>
 * 围栏切仓任务 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-250 15:25:57
 */
public interface WncFenceChangeTaskMapper extends BaseMapper<WncFenceChangeTask> {

    /**
     * 分页查询切仓任务列表
     * @param fenceChangeTaskPageQuery 查询
     * @return 结果
     */
    List<WncFenceChangeTask> queryPage(FenceChangeTaskPageQuery fenceChangeTaskPageQuery);

}
