package net.summerfarm.wnc.infrastructure.mapper;

import net.summerfarm.wnc.common.query.warehouse.WarehouseStorageQuery;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseStorageEntity;
import net.summerfarm.wnc.infrastructure.model.WarehouseStorageCenter;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <p>
 * 仓储中心 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-87 15:45:30
 */
public interface WarehouseStorageCenterMapper extends BaseMapper<WarehouseStorageCenter> {

    /**
     * 查询自营仓库信息
     * @param warehouseStorageQuery
     * @return
     */
    List<WarehouseStorageEntity> querySelfWarehouseStorage(WarehouseStorageQuery warehouseStorageQuery);

    /**
     * 查询代仓仓库信息
     * @param warehouseStorageQuery 查询
     * @return 结果
     */
    List<WarehouseStorageEntity> queryProxyWarehouseStoragePage(WarehouseStorageQuery warehouseStorageQuery);
}
