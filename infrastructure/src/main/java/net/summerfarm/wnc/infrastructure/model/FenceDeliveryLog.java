package net.summerfarm.wnc.infrastructure.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 配送调整记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-334 13:49:30
 */
@Getter
@Setter
@TableName("fence_delivery_log")
public class FenceDeliveryLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 围栏id
     */
    @TableField("fence_id")
    private Integer fenceId;

    /**
     * 配送周期
     */
    @TableField("delivery_frequent")
    private String deliveryFrequent;

    /**
     * 首配日
     */
    @TableField("next_delivery_date")
    private LocalDate nextDeliveryDate;

    /**
     * 创建人
     */
    @TableField("creator")
    private Integer creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField("updater")
    private Integer updater;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 周期方案 1周计算 2间隔计算
     */
    @TableField("frequent_method")
    private Integer frequentMethod;

    /**
     * 配送间隔周期
     */
    @TableField("delivery_frequent_interval")
    private Integer deliveryFrequentInterval;

    /**
     * 开始计算日期
     */
    @TableField("begin_calculate_date")
    private LocalDate beginCalculateDate;


}
