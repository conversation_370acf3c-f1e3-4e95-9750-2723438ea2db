package net.summerfarm.wnc.infrastructure.converter;

import net.summerfarm.wnc.domain.warehouse.entity.WmsWarehouseConfigEntity;
import net.summerfarm.wnc.infrastructure.model.WmsWarehouseConfig;

/**
 * Description: <br/>
 * date: 2023/8/28 14:10<br/>
 *
 * <AUTHOR> />
 */
public class WmsWarehouseConfigConverter {

    public static WmsWarehouseConfigEntity model2Entity(WmsWarehouseConfig wmsWarehouseConfig){
        if(wmsWarehouseConfig == null){
            return null;
        }
        WmsWarehouseConfigEntity wmsWarehouseConfigEntity = new WmsWarehouseConfigEntity();

        wmsWarehouseConfigEntity.setId(wmsWarehouseConfig.getId());
        wmsWarehouseConfigEntity.setCreateTime(wmsWarehouseConfig.getCreateTime());
        wmsWarehouseConfigEntity.setUpdateTime(wmsWarehouseConfig.getUpdateTime());
        wmsWarehouseConfigEntity.setWarehouseNo(wmsWarehouseConfig.getWarehouseNo());
        wmsWarehouseConfigEntity.setConfigKey(wmsWarehouseConfig.getConfigKey());
        wmsWarehouseConfigEntity.setConfigValue(wmsWarehouseConfig.getConfigValue());
        wmsWarehouseConfigEntity.setCreateOperator(wmsWarehouseConfig.getCreateOperator());
        wmsWarehouseConfigEntity.setUpdateOperator(wmsWarehouseConfig.getUpdateOperator());

        return wmsWarehouseConfigEntity;
    }
}
