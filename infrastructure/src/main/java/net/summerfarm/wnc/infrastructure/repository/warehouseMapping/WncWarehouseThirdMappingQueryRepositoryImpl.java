package net.summerfarm.wnc.infrastructure.repository.warehouseMapping;


import net.summerfarm.wnc.infrastructure.model.warehouseMapping.WncWarehouseThirdMapping;
import net.summerfarm.wnc.infrastructure.mapper.warehouseMapping.WncWarehouseThirdMappingMapper;
import net.summerfarm.wnc.infrastructure.converter.warehouseMapping.WncWarehouseThirdMappingConverter;
import net.summerfarm.wnc.domain.warehouseMapping.repository.WncWarehouseThirdMappingQueryRepository;
import net.summerfarm.wnc.domain.warehouseMapping.entity.WncWarehouseThirdMappingEntity;
import net.summerfarm.wnc.domain.warehouseMapping.param.query.WncWarehouseThirdMappingQueryParam;
import net.summerfarm.wnc.common.converter.PageInfoConverter;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.List;




/**
*
* <AUTHOR>
* @date 2025-06-11 15:08:31
* @version 1.0
*
*/
@Repository
public class WncWarehouseThirdMappingQueryRepositoryImpl implements WncWarehouseThirdMappingQueryRepository {

    @Autowired
    private WncWarehouseThirdMappingMapper wncWarehouseThirdMappingMapper;


    @Override
    public PageInfo<WncWarehouseThirdMappingEntity> getPage(WncWarehouseThirdMappingQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<WncWarehouseThirdMappingEntity> entities = wncWarehouseThirdMappingMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public WncWarehouseThirdMappingEntity selectById(Long id) {
        return WncWarehouseThirdMappingConverter.toWncWarehouseThirdMappingEntity(wncWarehouseThirdMappingMapper.selectById(id));
    }


    @Override
    public List<WncWarehouseThirdMappingEntity> selectByCondition(WncWarehouseThirdMappingQueryParam param) {
        return WncWarehouseThirdMappingConverter.toWncWarehouseThirdMappingEntityList(wncWarehouseThirdMappingMapper.selectByCondition(param));
    }

}