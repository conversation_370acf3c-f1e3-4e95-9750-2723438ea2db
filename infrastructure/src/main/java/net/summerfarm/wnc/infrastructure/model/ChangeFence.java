package net.summerfarm.wnc.infrastructure.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-23 10:41:06
 */
@Getter
@Setter
@TableName("change_fence")
public class ChangeFence implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 类型，0：切仓，1：切围栏
     */
    @TableField("`type`")
    private Integer type;

    /**
     * 状态，0：待处理，1：已取消，2：已完成，10：区域切换中，15：订单切换中，20：处理失败
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 操作围栏归属运营服务区编号
     */
    @TableField("area_no")
    private Integer areaNo;

    /**
     * 操作围栏归属城配仓编号
     */
    @TableField("store_no")
    private Integer storeNo;

    /**
     * 操作围栏ID
     */
    @TableField("fence_id")
    private Integer fenceId;

    /**
     * 目标围栏ID
     */
    @TableField("change_to_fence_id")
    private Integer changeToFenceId;

    /**
     * 目标城配仓编号
     */
    @TableField("change_to_store_no")
    private Integer changeToStoreNo;

    /**
     * 区域ID，多个区域 ','分割
     */
    @TableField("change_acm_id")
    private String changeAcmId;

    /**
     * 执行时间
     */
    @TableField("exe_time")
    private LocalDateTime exeTime;

    /**
     * 创建时间
     */
    @TableField("add_time")
    private LocalDateTime addTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 操作人adminId
     */
    @TableField("operator")
    private Integer operator;

    /**
     * 围栏名称
     */
    @TableField("fence_name")
    private String fenceName;

    /**
     * 切仓说明，json格式存储网点变更信息
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 更新人
     */
    @TableField("updater")
    private String updater;

}
