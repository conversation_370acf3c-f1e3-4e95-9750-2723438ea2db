package net.summerfarm.wnc.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.summerfarm.wnc.domain.warehouse.WmsWarehouseConfigRepository;
import net.summerfarm.wnc.domain.warehouse.entity.WmsWarehouseConfigEntity;
import net.summerfarm.wnc.infrastructure.converter.WmsWarehouseConfigConverter;
import net.summerfarm.wnc.infrastructure.mapper.WmsWarehouseConfigMapper;
import net.summerfarm.wnc.infrastructure.model.WmsWarehouseConfig;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/8/28 11:17<br/>
 *
 * <AUTHOR> />
 */
@Service
public class WmsWarehouseConfigRepositoryImpl implements WmsWarehouseConfigRepository {

    @Resource
    private WmsWarehouseConfigMapper wmsWarehouseConfigMapper;

    @Override
    public List<WmsWarehouseConfigEntity> queryCabinetByWarehouseNos(List<Integer> warehouseNos) {
        if(CollectionUtils.isEmpty(warehouseNos)){
            return Collections.emptyList();
        }
        List<WmsWarehouseConfig> wmsWarehouseConfigs = wmsWarehouseConfigMapper.selectList(new LambdaQueryWrapper<WmsWarehouseConfig>()
                .in(WmsWarehouseConfig::getWarehouseNo, warehouseNos)
                .eq(WmsWarehouseConfig::getConfigKey, "cabinetStatus")
        );

        return wmsWarehouseConfigs.stream().map(WmsWarehouseConfigConverter::model2Entity).collect(Collectors.toList());
    }
}
