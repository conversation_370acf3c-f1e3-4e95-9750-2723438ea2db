package net.summerfarm.wnc.infrastructure.converter;

import net.summerfarm.wnc.common.enums.PathConfigEnums;
import net.summerfarm.wnc.domain.path.entity.PathConfigEntity;
import net.summerfarm.wnc.infrastructure.model.WncPathConfig;

/**
 * Description: 转换类<br/>
 * date: 2023/11/29 10:50<br/>
 *
 * <AUTHOR> />
 */
public class WncPathConfigConverter {

    public static PathConfigEntity model2Entity(WncPathConfig model){
        if(model == null){
            return null;
        }
        PathConfigEntity entity = new PathConfigEntity();
        entity.setId(model.getId());
        entity.setCreateTime(model.getCreateTime());
        entity.setUpdateTime(model.getUpdateTime());
        entity.setBeginOutNo(model.getBeginOutNo());
        entity.setEndOutNo(model.getEndOutNo());
        entity.setBusinseeType(PathConfigEnums.BusinseeType.getBusinseeTypeByValue(model.getBusinseeType()));
        entity.setFrequentMethod(PathConfigEnums.FrequentMethod.getFrequentMethodByValue(model.getFrequentMethod()));
        entity.setFrequent(model.getFrequent());
        entity.setTravelDuratio(model.getTravelDuratio());
        entity.setLastTime(model.getLastTime());
        entity.setCreator(model.getCreator());
        entity.setUpdater(model.getUpdater());

        return entity;
    }

    public static WncPathConfig entity2model(PathConfigEntity entity){
        if(entity == null){
            return null;
        }
        WncPathConfig model = new WncPathConfig();
        model.setId(entity.getId());
        model.setCreateTime(entity.getCreateTime());
        model.setUpdateTime(entity.getUpdateTime());
        model.setBeginOutNo(entity.getBeginOutNo());
        model.setEndOutNo(entity.getEndOutNo());
        model.setBusinseeType(entity.getBusinseeType() != null ? entity.getBusinseeType().getValue() : null);
        model.setFrequentMethod(entity.getFrequentMethod() != null ? entity.getFrequentMethod().getValue() : null);
        model.setFrequent(entity.getFrequent());
        model.setTravelDuratio(entity.getTravelDuratio());
        model.setLastTime(entity.getLastTime());
        model.setCreator(entity.getCreator());
        model.setUpdater(entity.getUpdater());

        return model;
    }
}
