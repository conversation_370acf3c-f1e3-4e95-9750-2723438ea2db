package net.summerfarm.wnc.infrastructure.converter;

import net.summerfarm.wnc.common.enums.ContactConfigEnums;
import net.summerfarm.wnc.domain.config.entity.ContactConfigEntity;
import net.summerfarm.wnc.domain.config.param.command.ContactConfigCommandParam;
import net.summerfarm.wnc.infrastructure.model.WncContactConfig;

/**
 * Description:联系人配置do转换器
 * date: 2023/9/22 18:34
 *
 * <AUTHOR>
 */
public class WncContactConfigConverter {

    public static ContactConfigEntity do2Entity(WncContactConfig wncContactConfig){
        if (wncContactConfig == null){
            return null;
        }
        ContactConfigEntity contactConfigEntity = new ContactConfigEntity();
        contactConfigEntity.setId(wncContactConfig.getId());
        contactConfigEntity.setSource(ContactConfigEnums.Source.getSourceByValue(wncContactConfig.getSource()));
        contactConfigEntity.setTenantId(wncContactConfig.getTenantId());
        contactConfigEntity.setOuterContactId(wncContactConfig.getOuterContactId());
        contactConfigEntity.setStoreNo(wncContactConfig.getStoreNo());
        contactConfigEntity.setCreator(wncContactConfig.getCreator());
        contactConfigEntity.setCreateTime(wncContactConfig.getCreateTime());
        return contactConfigEntity;
    }

    public static WncContactConfig entity2Do(ContactConfigEntity contactConfigEntity){
        if (contactConfigEntity == null){
            return null;
        }
        WncContactConfig wncContactConfig = new WncContactConfig();
        wncContactConfig.setId(contactConfigEntity.getId());
        ContactConfigEnums.Source source = contactConfigEntity.getSource();
        if (source != null){
            wncContactConfig.setSource(source.getValue());
        }
        wncContactConfig.setTenantId(contactConfigEntity.getTenantId());
        wncContactConfig.setOuterContactId(contactConfigEntity.getOuterContactId());
        wncContactConfig.setStoreNo(contactConfigEntity.getStoreNo());
        wncContactConfig.setCreator(contactConfigEntity.getCreator());
        wncContactConfig.setCreateTime(contactConfigEntity.getCreateTime());
        return wncContactConfig;
    }

    public static WncContactConfig param2Do(ContactConfigCommandParam contactConfigCommandParam){
        if (contactConfigCommandParam == null){
            return null;
        }
        WncContactConfig wncContactConfig = new WncContactConfig();
        wncContactConfig.setSource(contactConfigCommandParam.getSource().getValue());
        wncContactConfig.setTenantId(contactConfigCommandParam.getTenantId());
        wncContactConfig.setOuterContactId(contactConfigCommandParam.getOuterContactId());
        wncContactConfig.setStoreNo(contactConfigCommandParam.getStoreNo());
        wncContactConfig.setCreator(contactConfigCommandParam.getCreator());
        wncContactConfig.setCreateTime(contactConfigCommandParam.getCreateTime());
        return wncContactConfig;
    }
}
