package net.summerfarm.wnc.infrastructure.mapper;

import net.summerfarm.wnc.common.query.changeTask.FenceChangeTaskOrderPageQuery;
import net.summerfarm.wnc.infrastructure.model.WncFenceChangeTaskDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <p>
 * 围栏切仓任务明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-250 15:27:47
 */
public interface WncFenceChangeTaskDetailMapper extends BaseMapper<WncFenceChangeTaskDetail> {

    /**
     * 分页查询切仓任务订单列表
     * @param fenceChangeTaskOrderPageQuery 查询
     * @return 结果
     */
    List<WncFenceChangeTaskDetail> queryPage(FenceChangeTaskOrderPageQuery fenceChangeTaskOrderPageQuery);

    /**
     * 查询可重试切仓任务订单明细
     * @return 切仓任务订单明细集合
     */
    List<WncFenceChangeTaskDetail> selectRetryableTaskDetails();

}
