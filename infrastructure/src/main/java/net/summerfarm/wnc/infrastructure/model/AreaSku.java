package net.summerfarm.wnc.infrastructure.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-214 16:36:45
 */
@Getter
@Setter
@TableName("area_sku")
public class AreaSku implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("sku")
    private String sku;

    /**
     * 城市编号
     */
    @TableField("area_no")
    private Integer areaNo;

    /**
     * 可售库存
     */
    @TableField("quantity")
    private Integer quantity;

    /**
     * 1使用虚拟库存0不
     */
    @TableField("`share`")
    private Boolean share;

    /**
     * 原价
     */
    @TableField("original_price")
    private BigDecimal originalPrice;

    /**
     * 销售价
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 0下架 1上架
     */
    @TableField("on_sale")
    private Integer onSale;

    /**
     * 添加时间
     */
    @TableField("add_time")
    private LocalDateTime addTime;

    /**
     * 越小排序越靠前
     */
    @TableField("priority")
    private Integer priority;

    @TableField("pd_priority")
    private Integer pdPriority;

    @TableField("ladder_price")
    private String ladderPrice;

    @TableField("limited_quantity")
    private Integer limitedQuantity;

    @TableField("sales_mode")
    private Integer salesMode;

    @TableField("`show`")
    private Integer show;

    @TableField("info")
    private String info;

    /**
     * 是否是大客户0 不是 1是
     */
    @TableField("m_type")
    private Integer mType;

    /**
     * 是否展示预告:0否 1是
     */
    @TableField("show_advance")
    private Boolean showAdvance;

    /**
     * 预告信息
     */
    @TableField("advance")
    private String advance;

    /**
     * 角标状态 0、关闭 1、开启
     */
    @TableField("corner_status")
    private Integer cornerStatus;

    @TableField("corner_open_time")
    private LocalDateTime cornerOpenTime;

    /**
     * 上架操作: 0上架、1有库存时上架、2定时上架 3有库存时上架(永久生效)
     */
    @TableField("open_sale")
    private Integer openSale;

    /**
     * 定时上架时间
     */
    @TableField("open_sale_time")
    private LocalDateTime openSaleTime;

    /**
     * 下架操作: 0下架、1售罄下架、2定时下架
     */
    @TableField("close_sale")
    private Integer closeSale;

    /**
     * 定时下架时间
     */
    @TableField("close_sale_time")
    private LocalDateTime closeSaleTime;

    /**
     * 固定排序标识 0、关闭 1、开启
     */
    @TableField("fix_flag")
    private Integer fixFlag;

    /**
     * 固定排序值
     */
    @TableField("fix_num")
    private Integer fixNum;

    /**
     * 上下架更新人
     */
    @TableField("updater")
    private String updater;


}
