package net.summerfarm.wnc.infrastructure.converter;

import com.alibaba.fastjson.JSON;
import net.summerfarm.wnc.domain.warehouse.entity.ConflictWarehouseJsonEntity;
import net.summerfarm.wnc.domain.warehouse.entity.WncWarehouseStorageFenceRuleEntity;
import net.summerfarm.wnc.infrastructure.model.WncWarehouseStorageFenceRule;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/4/7 15:34<br/>
 *
 * <AUTHOR> />
 */
public class WncWarehouseStorageFenceRuleConverter {

    public static WncWarehouseStorageFenceRuleEntity model2Entity(WncWarehouseStorageFenceRule wncWarehouseStorageFenceRule){
        if(wncWarehouseStorageFenceRule == null){
            return null;
        }
        WncWarehouseStorageFenceRuleEntity wncWarehouseStorageFenceRuleEntity = new WncWarehouseStorageFenceRuleEntity();
        wncWarehouseStorageFenceRuleEntity.setId(wncWarehouseStorageFenceRule.getId());
        wncWarehouseStorageFenceRuleEntity.setCreateTime(wncWarehouseStorageFenceRule.getCreateTime());
        wncWarehouseStorageFenceRuleEntity.setUpdateTime(wncWarehouseStorageFenceRule.getUpdateTime());
        wncWarehouseStorageFenceRuleEntity.setProvince(wncWarehouseStorageFenceRule.getProvince());
        wncWarehouseStorageFenceRuleEntity.setCity(wncWarehouseStorageFenceRule.getCity());
        wncWarehouseStorageFenceRuleEntity.setArea(wncWarehouseStorageFenceRule.getArea());
        wncWarehouseStorageFenceRuleEntity.setDeliveryRule(wncWarehouseStorageFenceRule.getDeliveryRule());
        if(StringUtils.isNotBlank(wncWarehouseStorageFenceRule.getConflictWarehouseJson())){
            wncWarehouseStorageFenceRuleEntity.setConflictWarehouseJsonEntityList(
                    WncWarehouseStorageFenceRuleConverter.conflictWarehouseJson2Entity(wncWarehouseStorageFenceRule.getConflictWarehouseJson()));
        }
        wncWarehouseStorageFenceRuleEntity.setWarehouseNameList(wncWarehouseStorageFenceRule.getWarehouseNameList());
        wncWarehouseStorageFenceRuleEntity.setLastOperatorName(wncWarehouseStorageFenceRule.getLastOperatorName());
        wncWarehouseStorageFenceRuleEntity.setLastOperatorId(wncWarehouseStorageFenceRule.getLastOperatorId());
        wncWarehouseStorageFenceRuleEntity.setTenantId(wncWarehouseStorageFenceRule.getTenantId());

        return wncWarehouseStorageFenceRuleEntity;
    }

    public static WncWarehouseStorageFenceRule entity2Model(WncWarehouseStorageFenceRuleEntity wncWarehouseStorageFenceRuleEntity){
        if(wncWarehouseStorageFenceRuleEntity == null){
            return null;
        }
        WncWarehouseStorageFenceRule wncWarehouseStorageFenceRule = new WncWarehouseStorageFenceRule();
        wncWarehouseStorageFenceRule.setId(wncWarehouseStorageFenceRuleEntity.getId());
        wncWarehouseStorageFenceRule.setProvince(wncWarehouseStorageFenceRuleEntity.getProvince());
        wncWarehouseStorageFenceRule.setCity(wncWarehouseStorageFenceRuleEntity.getCity());
        wncWarehouseStorageFenceRule.setArea(wncWarehouseStorageFenceRuleEntity.getArea());
        wncWarehouseStorageFenceRule.setDeliveryRule(wncWarehouseStorageFenceRuleEntity.getDeliveryRule());
        if(!CollectionUtils.isEmpty(wncWarehouseStorageFenceRuleEntity.getConflictWarehouseJsonEntityList())){
            wncWarehouseStorageFenceRule.setConflictWarehouseJson(JSON.toJSONString(wncWarehouseStorageFenceRuleEntity.getConflictWarehouseJsonEntityList()));
            String warehouseNameList = wncWarehouseStorageFenceRuleEntity.getConflictWarehouseJsonEntityList().stream().map(ConflictWarehouseJsonEntity::getWarehouseName).collect(Collectors.joining(","));
            wncWarehouseStorageFenceRule.setWarehouseNameList(warehouseNameList);
        }
        wncWarehouseStorageFenceRule.setLastOperatorName(wncWarehouseStorageFenceRuleEntity.getLastOperatorName());
        wncWarehouseStorageFenceRule.setLastOperatorId(wncWarehouseStorageFenceRuleEntity.getLastOperatorId());
        wncWarehouseStorageFenceRule.setTenantId(wncWarehouseStorageFenceRuleEntity.getTenantId());

        return wncWarehouseStorageFenceRule;
    }

    public static List<ConflictWarehouseJsonEntity> conflictWarehouseJson2Entity(String conflictWarehouseJson){
        if(StringUtils.isBlank(conflictWarehouseJson)){
            return null;
        }
        return JSON.parseArray(conflictWarehouseJson, ConflictWarehouseJsonEntity.class);
    }
}
