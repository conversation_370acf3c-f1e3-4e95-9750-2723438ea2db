package net.summerfarm.wnc.infrastructure.repository;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import net.summerfarm.wnc.domain.ConfigRepository;
import net.summerfarm.wnc.domain.preciseDelivery.PreciseDeliveryConfigQueryRepository;
import net.summerfarm.wnc.domain.preciseDelivery.aggregate.PreciseDeliveryConfigAggregate;
import net.summerfarm.wnc.domain.preciseDelivery.entity.PreciseDeliveryConfigEntity;
import net.summerfarm.wnc.domain.preciseDelivery.param.PreciseDeliveryConfigQueryParam;
import net.summerfarm.wnc.domain.preciseDelivery.valueObject.PreciseDeliveryConfigAreaValueObject;
import net.summerfarm.wnc.domain.preciseDelivery.valueObject.PreciseDeliveryConfigTimeValueObject;
import net.summerfarm.wnc.infrastructure.converter.WncPreciseDeliveryAreaDetailConverter;
import net.summerfarm.wnc.infrastructure.converter.WncPreciseDeliveryConfigConverter;
import net.summerfarm.wnc.infrastructure.converter.WncPreciseDeliveryTimeDetailConverter;
import net.summerfarm.wnc.infrastructure.mapper.WncPreciseDeliveryAreaDetailMapper;
import net.summerfarm.wnc.infrastructure.mapper.WncPreciseDeliveryConfigMapper;
import net.summerfarm.wnc.infrastructure.mapper.WncPreciseDeliveryTimeDetailMapper;
import net.summerfarm.wnc.infrastructure.model.WncPreciseDeliveryAreaDetail;
import net.summerfarm.wnc.infrastructure.model.WncPreciseDeliveryConfig;
import net.summerfarm.wnc.infrastructure.model.WncPreciseDeliveryTimeDetail;
import net.summerfarm.wnc.infrastructure.util.PageInfoHelper;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:精准送配置仓库查询接口实现
 * date: 2024/1/22 16:35
 *
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class PreciseDeliveryConfigQueryRepositoryImpl implements PreciseDeliveryConfigQueryRepository {

    private final WncPreciseDeliveryConfigMapper wncPreciseDeliveryConfigMapper;
    private final WncPreciseDeliveryAreaDetailMapper wncPreciseDeliveryAreaDetailMapper;
    private final WncPreciseDeliveryTimeDetailMapper wncPreciseDeliveryTimeDetailMapper;
    private final ConfigRepository configRepository;

    @Override
    public PreciseDeliveryConfigAggregate queryDetail(Long configId) {
        if (configId == null){
            return null;
        }
        WncPreciseDeliveryConfig wncPreciseDeliveryConfig = wncPreciseDeliveryConfigMapper.selectById(configId);
        if (wncPreciseDeliveryConfig == null){
            return null;
        }
        List<WncPreciseDeliveryAreaDetail> areaDetails = wncPreciseDeliveryAreaDetailMapper.selectList(new LambdaQueryWrapper<WncPreciseDeliveryAreaDetail>()
                .eq(WncPreciseDeliveryAreaDetail::getConfigId, wncPreciseDeliveryConfig.getId()));
        List<WncPreciseDeliveryTimeDetail> timeDetails = wncPreciseDeliveryTimeDetailMapper.selectList(new LambdaQueryWrapper<WncPreciseDeliveryTimeDetail>()
                .eq(WncPreciseDeliveryTimeDetail::getConfigId, wncPreciseDeliveryConfig.getId()));
        return WncPreciseDeliveryConfigConverter.convertAggregate(wncPreciseDeliveryConfig, areaDetails, timeDetails);
    }

    @Override
    public PreciseDeliveryConfigEntity query(Long configId) {
        WncPreciseDeliveryConfig wncPreciseDeliveryConfig = wncPreciseDeliveryConfigMapper.selectById(configId);
        return WncPreciseDeliveryConfigConverter.do2Entity(wncPreciseDeliveryConfig);
    }

    @Override
    public PageInfo<PreciseDeliveryConfigAggregate> queryPage(PreciseDeliveryConfigQueryParam queryParam) {
        if (queryParam == null){
            return null;
        }

        Set<Long> configIds = Sets.newHashSet();
        if (StrUtil.isNotBlank(queryParam.getArea())){
            List<WncPreciseDeliveryAreaDetail> wncPreciseDeliveryAreaDetails = wncPreciseDeliveryAreaDetailMapper.selectList(new LambdaQueryWrapper<WncPreciseDeliveryAreaDetail>()
                    .eq(WncPreciseDeliveryAreaDetail::getArea, queryParam.getArea()));
            configIds = Optional.ofNullable(wncPreciseDeliveryAreaDetails).orElse(Lists.newArrayList()).stream().map(WncPreciseDeliveryAreaDetail::getConfigId).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(configIds)){
                configIds = Sets.newHashSet(-1L);
            }
        }
        PageHelper.startPage(queryParam.getPageIndex(), queryParam.getPageSize());
        List<WncPreciseDeliveryConfig> wncPreciseDeliveryConfigs = wncPreciseDeliveryConfigMapper.selectList(new LambdaQueryWrapper<WncPreciseDeliveryConfig>()
                .eq(StrUtil.isNotBlank(queryParam.getProvince()), WncPreciseDeliveryConfig::getProvince, queryParam.getProvince())
                .eq(StrUtil.isNotBlank(queryParam.getCity()), WncPreciseDeliveryConfig::getCity, queryParam.getCity())
                .in(!CollectionUtils.isEmpty(configIds), WncPreciseDeliveryConfig::getId, configIds)
                .orderByDesc(WncPreciseDeliveryConfig::getId));

        PageInfo pageInfo = PageInfoHelper.createPageInfo(wncPreciseDeliveryConfigs);
        List<PreciseDeliveryConfigAggregate> configAggregates = this.getConfigWithAreaWithTime(wncPreciseDeliveryConfigs);
        pageInfo.setList(configAggregates);
        return pageInfo;
    }

    @Override
    public List<PreciseDeliveryConfigAreaValueObject> queryAreaList(PreciseDeliveryConfigQueryParam queryParam) {
        List<WncPreciseDeliveryAreaDetail> wncPreciseDeliveryConfigs = wncPreciseDeliveryAreaDetailMapper.selectList(new LambdaQueryWrapper<WncPreciseDeliveryAreaDetail>()
                .eq(StrUtil.isNotBlank(queryParam.getCity()), WncPreciseDeliveryAreaDetail::getCity, queryParam.getCity())
                .eq(StrUtil.isNotBlank(queryParam.getArea()), WncPreciseDeliveryAreaDetail::getArea, queryParam.getArea()));
        if (CollectionUtils.isEmpty(wncPreciseDeliveryConfigs)){
            return Collections.emptyList();
        }
        return wncPreciseDeliveryConfigs.stream().map(WncPreciseDeliveryAreaDetailConverter::do2Entity).collect(Collectors.toList());
    }

    @Override
    public List<PreciseDeliveryConfigTimeValueObject> queryTimeListByCityAndArea(String city, String area) {
        //过滤没有区域的城市
        List<String> noAreaCityList = configRepository.queryNoAreaCity();
        if(noAreaCityList.contains(city)) {
            area = null;
        }
        //根据 行政市+行政区查询
        WncPreciseDeliveryAreaDetail areaDetail = wncPreciseDeliveryAreaDetailMapper.selectOne(new LambdaQueryWrapper<WncPreciseDeliveryAreaDetail>()
                .eq(WncPreciseDeliveryAreaDetail::getCity, city)
                .eq(StrUtil.isNotBlank(area), WncPreciseDeliveryAreaDetail::getArea, area)
                .last("limit 1"));
        if (areaDetail == null){
            return Collections.emptyList();
        }
        List<WncPreciseDeliveryTimeDetail> timeDetails = wncPreciseDeliveryTimeDetailMapper.selectList(new LambdaQueryWrapper<WncPreciseDeliveryTimeDetail>()
                .eq(WncPreciseDeliveryTimeDetail::getConfigId, areaDetail.getConfigId()));
        if (CollectionUtils.isEmpty(timeDetails)){
            return Collections.emptyList();
        }
        return timeDetails.stream().map(WncPreciseDeliveryTimeDetailConverter::do2Entity).collect(Collectors.toList());
    }

    private List<PreciseDeliveryConfigAggregate> getConfigWithAreaWithTime(List<WncPreciseDeliveryConfig> wncPreciseDeliveryConfigs) {
        if (CollectionUtils.isEmpty(wncPreciseDeliveryConfigs)){
            return Collections.emptyList();
        }
        Set<Long> configIds = wncPreciseDeliveryConfigs.stream().map(WncPreciseDeliveryConfig::getId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(configIds)){
            return Collections.emptyList();
        }
        Map<Long/*configId*/, List<WncPreciseDeliveryAreaDetail>> areaMap = wncPreciseDeliveryAreaDetailMapper.selectList(new LambdaQueryWrapper<WncPreciseDeliveryAreaDetail>()
                .in(WncPreciseDeliveryAreaDetail::getConfigId, configIds)).stream().filter(e -> e.getConfigId() != null).collect(Collectors.groupingBy(WncPreciseDeliveryAreaDetail::getConfigId));
        Map<Long/*configId*/, List<WncPreciseDeliveryTimeDetail>> timeMap = wncPreciseDeliveryTimeDetailMapper.selectList(new LambdaQueryWrapper<WncPreciseDeliveryTimeDetail>()
                .in(WncPreciseDeliveryTimeDetail::getConfigId, configIds)).stream().filter(e -> e.getConfigId() != null).collect(Collectors.groupingBy(WncPreciseDeliveryTimeDetail::getConfigId));
        List<PreciseDeliveryConfigAggregate> configAggregateList = wncPreciseDeliveryConfigs.stream().map(e -> {
            List<WncPreciseDeliveryAreaDetail> areaList = areaMap.get(e.getId());
            List<WncPreciseDeliveryTimeDetail> timeList = timeMap.get(e.getId());
            return WncPreciseDeliveryConfigConverter.convertAggregate(e, areaList, timeList);
        }).collect(Collectors.toList());
        return configAggregateList;
    }
}
