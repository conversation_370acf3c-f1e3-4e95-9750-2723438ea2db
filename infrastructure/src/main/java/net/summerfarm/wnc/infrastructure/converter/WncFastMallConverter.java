package net.summerfarm.wnc.infrastructure.converter;

import net.summerfarm.wnc.domain.fastmall.entity.WncFastMallEntity;
import net.summerfarm.wnc.infrastructure.model.WncFastMall;

/**
 * Description: <br/>
 * date: 2023/4/11 16:39<br/>
 *
 * <AUTHOR> />
 */
public class WncFastMallConverter {

    public static WncFastMallEntity model2Entity(WncFastMall wncFastMall){
        if(wncFastMall == null){
            return null;
        }
        WncFastMallEntity wncFastMallEntity = new WncFastMallEntity();
        wncFastMallEntity.setId(wncFastMall.getId());
        wncFastMallEntity.setCreateTime(wncFastMall.getCreateTime());
        wncFastMallEntity.setUpdateTime(wncFastMall.getUpdateTime());
        wncFastMallEntity.setName(wncFastMall.getName());
        return wncFastMallEntity;
    }
}
