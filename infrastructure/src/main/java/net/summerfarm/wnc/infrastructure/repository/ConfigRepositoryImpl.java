package net.summerfarm.wnc.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.summerfarm.wnc.common.constants.AppConsts;
import net.summerfarm.wnc.domain.ConfigRepository;
import net.summerfarm.wnc.domain.warehouse.entity.ConfigEntity;
import net.summerfarm.wnc.infrastructure.converter.ConfigConverter;
import net.summerfarm.wnc.infrastructure.mapper.ConfigMapper;
import net.summerfarm.wnc.infrastructure.model.Config;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-06-20
 **/
@Service
public class ConfigRepositoryImpl implements ConfigRepository {
	@Autowired
	private ConfigMapper configMapper;

	@Override
	public ConfigEntity queryByKey(String key) {
		Config config = configMapper.selectOne(new LambdaQueryWrapper<Config>()
				.eq(Config::getKey, key)
				.last("limit 1"));
		return ConfigConverter.do2Entity(config);
	}

	@Override
	public List<String> queryNoAreaCity() {
		ConfigEntity config = this.queryByKey(AppConsts.ConfigKey.NO_AREA_CITY);
		if (config != null && StringUtils.isNotBlank(config.getValue())) {
			return Arrays.asList(config.getValue().split(","));
		}
		return Collections.emptyList();
	}
}
