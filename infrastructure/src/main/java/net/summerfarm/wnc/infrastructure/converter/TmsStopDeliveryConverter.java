package net.summerfarm.wnc.infrastructure.converter;


import net.summerfarm.wnc.domain.fence.entity.StopDeliveryEntity;
import net.summerfarm.wnc.infrastructure.model.TmsStopDelivery;

/**
 * Description: <br/>
 * date: 2023/3/8 15:14<br/>
 *
 * <AUTHOR> />
 */
public class TmsStopDeliveryConverter {

    public static StopDeliveryEntity do2Entity(TmsStopDelivery tmsStopDelivery){
        if(tmsStopDelivery == null){
            return null;
        }

        StopDeliveryEntity stopDeliveryEntity = new StopDeliveryEntity();

        stopDeliveryEntity.setId(tmsStopDelivery.getId());
        stopDeliveryEntity.setCreateTime(tmsStopDelivery.getCreateTime());
        stopDeliveryEntity.setUpdateTime(tmsStopDelivery.getUpdateTime());
        stopDeliveryEntity.setStoreNo(tmsStopDelivery.getStoreNo());
        stopDeliveryEntity.setShutdownStartTime(tmsStopDelivery.getShutdownStartTime());
        stopDeliveryEntity.setShutdownEndTime(tmsStopDelivery.getShutdownEndTime());
        stopDeliveryEntity.setDeleteFlag(tmsStopDelivery.getDeleteFlag());

        return stopDeliveryEntity;
    }
}
