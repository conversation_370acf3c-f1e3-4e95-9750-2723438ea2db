package net.summerfarm.wnc.infrastructure.converter;

import lombok.Data;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseStorageEntity;
import net.summerfarm.wnc.infrastructure.model.WarehouseStorageCenter;

/**
 * Description: <br/>
 * date: 2023/3/28 16:50<br/>
 *
 * <AUTHOR> />
 */
@Data
public class WarehouseStorageCenterConverter {

    public static WarehouseStorageEntity warehouseStorage2Entity(WarehouseStorageCenter warehouseStorageCenter){
        if(warehouseStorageCenter == null){
            return null;
        }
        WarehouseStorageEntity warehouseStorageEntity = new WarehouseStorageEntity();

        warehouseStorageEntity.setId(warehouseStorageCenter.getId());
        warehouseStorageEntity.setWarehouseNo(warehouseStorageCenter.getWarehouseNo());
        warehouseStorageEntity.setWarehouseName(warehouseStorageCenter.getWarehouseName());
        warehouseStorageEntity.setManageAdminId(warehouseStorageCenter.getManageAdminId());
        warehouseStorageEntity.setType(warehouseStorageCenter.getType());
        warehouseStorageEntity.setAreaManageId(warehouseStorageCenter.getAreaManageId());
        warehouseStorageEntity.setStatus(warehouseStorageCenter.getStatus());
        warehouseStorageEntity.setAddress(warehouseStorageCenter.getAddress());
        warehouseStorageEntity.setPoiNote(warehouseStorageCenter.getPoiNote());
        warehouseStorageEntity.setMailToAddress(warehouseStorageCenter.getMailToAddress());
        warehouseStorageEntity.setUpdater(warehouseStorageCenter.getUpdater());
        warehouseStorageEntity.setUpdateTime(warehouseStorageCenter.getUpdateTime());
        warehouseStorageEntity.setCreator(warehouseStorageCenter.getCreator());
        warehouseStorageEntity.setCreateTime(warehouseStorageCenter.getCreateTime());
        warehouseStorageEntity.setPersonContact(warehouseStorageCenter.getPersonContact());
        warehouseStorageEntity.setPhone(warehouseStorageCenter.getPhone());
        warehouseStorageEntity.setTenantId(warehouseStorageCenter.getTenantId());
        warehouseStorageEntity.setWarehousePic(warehouseStorageCenter.getWarehousePic());

        return warehouseStorageEntity;
    }

    public static WarehouseStorageCenter entity2WarehouseStorage(WarehouseStorageEntity warehouseStorageEntity){
        if(warehouseStorageEntity == null){
            return null;
        }
        WarehouseStorageCenter warehouseStorageCenter = new WarehouseStorageCenter();

        warehouseStorageCenter.setId(warehouseStorageEntity.getId());
        warehouseStorageCenter.setWarehouseNo(warehouseStorageEntity.getWarehouseNo());
        warehouseStorageCenter.setWarehouseName(warehouseStorageEntity.getWarehouseName());
        warehouseStorageCenter.setManageAdminId(warehouseStorageEntity.getManageAdminId());
        warehouseStorageCenter.setType(warehouseStorageEntity.getType());
        warehouseStorageCenter.setAreaManageId(warehouseStorageEntity.getAreaManageId());
        warehouseStorageCenter.setStatus(warehouseStorageEntity.getStatus());
        warehouseStorageCenter.setAddress(warehouseStorageEntity.getAddress());
        warehouseStorageCenter.setPoiNote(warehouseStorageEntity.getPoiNote());
        warehouseStorageCenter.setMailToAddress(warehouseStorageEntity.getMailToAddress());
        warehouseStorageCenter.setUpdater(warehouseStorageEntity.getUpdater());
        warehouseStorageCenter.setCreator(warehouseStorageEntity.getCreator());
        warehouseStorageCenter.setPersonContact(warehouseStorageEntity.getPersonContact());
        warehouseStorageCenter.setPhone(warehouseStorageEntity.getPhone());
        warehouseStorageCenter.setTenantId(warehouseStorageEntity.getTenantId());
        warehouseStorageCenter.setWarehousePic(warehouseStorageEntity.getWarehousePic());

        return warehouseStorageCenter;
    }
}
