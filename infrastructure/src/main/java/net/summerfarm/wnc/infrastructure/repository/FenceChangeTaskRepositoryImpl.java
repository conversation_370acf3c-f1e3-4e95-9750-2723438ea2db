package net.summerfarm.wnc.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import net.summerfarm.wnc.common.enums.FenceChangeTaskEnums;
import net.summerfarm.wnc.common.query.changeTask.FenceChangeTaskPageQuery;
import net.summerfarm.wnc.common.query.changeTask.FenceChangeTaskQuery;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskRepository;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskEntity;
import net.summerfarm.wnc.domain.fence.entity.AdCodeMsgEntity;
import net.summerfarm.wnc.infrastructure.converter.AdCodeMsgConverter;
import net.summerfarm.wnc.infrastructure.converter.FenceChangeConverter;
import net.summerfarm.wnc.infrastructure.mapper.*;
import net.summerfarm.wnc.infrastructure.model.*;
import net.summerfarm.wnc.infrastructure.util.PageInfoHelper;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:围栏切仓任务仓库接口实现
 * date: 2023/8/24 16:46
 *
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class FenceChangeTaskRepositoryImpl implements FenceChangeTaskRepository {

    private final ChangeFenceMapper changeFenceMapper;
    private final WncFenceChangeTaskMapper wncFenceChangeTaskMapper;
    private final WncFenceChangeTaskDetailMapper wncFenceChangeTaskDetailMapper;
    private final AdCodeMsgMapper adCodeMsgMapper;

    private final WncChangeFenceDetailMapper wncChangeFenceDetailMapper;

    @Override
    public PageInfo<FenceChangeTaskEntity> queryTaskPage(FenceChangeTaskPageQuery fenceChangeTaskPageQuery) {
        PageHelper.startPage(fenceChangeTaskPageQuery.getPageIndex(), fenceChangeTaskPageQuery.getPageSize());
        if (fenceChangeTaskPageQuery.getExeDateStart() != null) {
            LocalDateTime exeDateStart = LocalDateTime.of(fenceChangeTaskPageQuery.getExeDateStart().toLocalDate(), LocalTime.MIN);
            fenceChangeTaskPageQuery.setExeDateStart(exeDateStart);
        }
        if (fenceChangeTaskPageQuery.getExeDateEnd() != null) {
            LocalDateTime exeDateEnd = LocalDateTime.of(fenceChangeTaskPageQuery.getExeDateEnd().toLocalDate(), LocalTime.MAX);
            fenceChangeTaskPageQuery.setExeDateEnd(exeDateEnd);
        }
        List<WncFenceChangeTask> wncFenceChangeTasks = wncFenceChangeTaskMapper.queryPage(fenceChangeTaskPageQuery);
        PageInfo pageInfo = PageInfoHelper.createPageInfo(wncFenceChangeTasks);
        List<FenceChangeTaskEntity> fenceChangeTaskEntities = this.getFenceChangeTaskWithFenceArea(wncFenceChangeTasks);
        pageInfo.setList(fenceChangeTaskEntities);
        return pageInfo;
    }

    private List<FenceChangeTaskEntity> getFenceChangeTaskWithFenceArea(List<WncFenceChangeTask> wncFenceChangeTasks) {
        if (CollectionUtils.isEmpty(wncFenceChangeTasks)){
            return Collections.emptyList();
        }
        List<FenceChangeTaskEntity> fenceChangeTaskEntities = wncFenceChangeTasks.stream().map(FenceChangeConverter::do2Entity).collect(Collectors.toList());
        return this.getTaskWithFenceArea(fenceChangeTaskEntities);
    }

    private List<FenceChangeTaskEntity> getTaskWithFenceArea(List<FenceChangeTaskEntity> fenceChangeTaskEntities) {
        if (CollectionUtils.isEmpty(fenceChangeTaskEntities)){
            return Collections.emptyList();
        }
        for (FenceChangeTaskEntity fenceChangeTaskEntity : fenceChangeTaskEntities) {
            List<AdCodeMsgEntity> adCodeMsgEntities = fenceChangeTaskEntity.getAdCodeMsgEntities();
            if (CollectionUtils.isEmpty(adCodeMsgEntities)){
                continue;
            }
            List<Integer> fenceAreaIds = adCodeMsgEntities.stream().map(AdCodeMsgEntity::getId).collect(Collectors.toList());
            List<AdCodeMsg> fenceAreas = this.getFenceAreas(fenceAreaIds);
            fenceChangeTaskEntity.setAdCodeMsgEntities(fenceAreas.stream().map(AdCodeMsgConverter::adCodeMsg2Entity).collect(Collectors.toList()));
        }
        return fenceChangeTaskEntities;
    }

    private List<AdCodeMsg> getFenceAreas(List<Integer> fenceAreaIds) {
        if (CollectionUtils.isEmpty(fenceAreaIds)){
            return Collections.emptyList();
        }
        return adCodeMsgMapper.selectList(new LambdaQueryWrapper<AdCodeMsg>().in(AdCodeMsg::getId, fenceAreaIds));
    }

    @Override
    public FenceChangeTaskEntity queryById(Long changeTaskId) {
        if (changeTaskId == null){
            return null;
        }
        WncFenceChangeTask wncFenceChangeTask = wncFenceChangeTaskMapper.selectById(changeTaskId);
        if (wncFenceChangeTask == null){
            return null;
        }
        FenceChangeTaskEntity fenceChangeTaskEntity = FenceChangeConverter.do2Entity(wncFenceChangeTask);
        List<Integer> fenceAreaIds = fenceChangeTaskEntity.getAdCodeMsgEntities().stream().map(AdCodeMsgEntity::getId).collect(Collectors.toList());
        List<AdCodeMsg> fenceAreas = this.getFenceAreas(fenceAreaIds);
        fenceChangeTaskEntity.setAdCodeMsgEntities(fenceAreas.stream().map(AdCodeMsgConverter::adCodeMsg2Entity).collect(Collectors.toList()));
        return fenceChangeTaskEntity;
    }

    @Override
    public List<FenceChangeTaskEntity> queryList(FenceChangeTaskQuery fenceChangeTaskQuery) {
        List<WncFenceChangeTask> wncFenceChangeTasks = wncFenceChangeTaskMapper.selectList(new LambdaQueryWrapper<WncFenceChangeTask>()
                .eq(fenceChangeTaskQuery.getFenceId() != null, WncFenceChangeTask::getFenceId, fenceChangeTaskQuery.getFenceId())
                .eq(fenceChangeTaskQuery.getType() != null, WncFenceChangeTask::getType, fenceChangeTaskQuery.getType())
                .in(!CollectionUtils.isEmpty(fenceChangeTaskQuery.getStatus()), WncFenceChangeTask::getStatus, fenceChangeTaskQuery.getStatus()));
        return wncFenceChangeTasks.stream().map(FenceChangeConverter::do2Entity).collect(Collectors.toList());
    }

    @Override
    public List<FenceChangeTaskEntity> queryListWithArea(FenceChangeTaskQuery fenceChangeTaskQuery) {
        List<FenceChangeTaskEntity> fenceChangeTaskEntities = this.queryList(fenceChangeTaskQuery);
        return this.getTaskWithFenceArea(fenceChangeTaskEntities);
    }

    @Override
    public int save(FenceChangeTaskEntity fenceChangeTaskEntity) {
        if (fenceChangeTaskEntity == null){
            return 0;
        }
        WncFenceChangeTask wncFenceChangeTask = FenceChangeConverter.entity2Do(fenceChangeTaskEntity);
        int insert = wncFenceChangeTaskMapper.insert(wncFenceChangeTask);
        fenceChangeTaskEntity.setId(wncFenceChangeTask.getId());
        return insert;
    }

    @Override
    public int update(FenceChangeTaskEntity fenceChangeTaskEntity) {
        if (fenceChangeTaskEntity == null){
            return 0;
        }
        WncFenceChangeTask wncFenceChangeTask = FenceChangeConverter.entity2Do(fenceChangeTaskEntity);
        return wncFenceChangeTaskMapper.updateById(wncFenceChangeTask);
    }

    @Override
    public List<FenceChangeTaskEntity> queryExecutableTask(FenceChangeTaskEnums.Status status) {
        if (status == null){
            return Collections.emptyList();
        }
        LocalDateTime nowPlusOneMinute = LocalDateTime.now().plusMinutes(1);
        List<WncFenceChangeTask> wncFenceChangeTasks = wncFenceChangeTaskMapper.selectList(new LambdaQueryWrapper<WncFenceChangeTask>()
                .le(WncFenceChangeTask::getExeTime, nowPlusOneMinute)
                .eq(WncFenceChangeTask::getStatus, status.getValue())
                .orderByAsc(WncFenceChangeTask::getId));
        return this.getFenceChangeTaskWithFenceArea(wncFenceChangeTasks);
    }

    @Override
    public long queryHandlingTasksByStoreNo(Integer storeNo) {
        return wncFenceChangeTaskMapper.selectCount(new LambdaQueryWrapper<WncFenceChangeTask>()
                .eq(WncFenceChangeTask::getStoreNo,storeNo)
                .in(WncFenceChangeTask::getStatus,Arrays.asList(
                        FenceChangeTaskEnums.Status.WAIT.getValue(),
                        FenceChangeTaskEnums.Status.AREA_CHANGE_ING.getValue(),
                        FenceChangeTaskEnums.Status.ORDER_CHANGE_ING.getValue()))
        );
    }
}
