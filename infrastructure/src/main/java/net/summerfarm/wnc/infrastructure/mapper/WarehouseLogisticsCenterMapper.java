package net.summerfarm.wnc.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.wnc.common.query.warehouse.WarehouseLogisticsQuery;
import net.summerfarm.wnc.infrastructure.model.WarehouseLogisticsCenter;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 仓储物流中心 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-96 16:09:50
 */
public interface WarehouseLogisticsCenterMapper extends BaseMapper<WarehouseLogisticsCenter> {

	WarehouseLogisticsCenter selectByStoreNo(@Param("storeNo") Integer storeNo);

    /**
     * 分页查询
     * @param warehouseLogisticsQuery 查询条件
     * @return 结果
     */
    List<WarehouseLogisticsCenter> queryPageList(WarehouseLogisticsQuery warehouseLogisticsQuery);

    /**
     * 城配仓截单时间的取消
     * @param storeNo 城配仓
     */
    void cancelUpdateCloseTime(@Param("storeNo") Integer storeNo);
}
