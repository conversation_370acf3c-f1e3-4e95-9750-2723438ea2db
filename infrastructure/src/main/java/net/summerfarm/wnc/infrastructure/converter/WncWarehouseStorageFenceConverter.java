package net.summerfarm.wnc.infrastructure.converter;

import net.summerfarm.wnc.domain.warehouse.entity.WncWarehouseStorageFenceEntity;
import net.summerfarm.wnc.infrastructure.model.WncWarehouseStorageFence;

/**
 * Description: <br/>
 * date: 2023/3/28 16:38<br/>
 *
 * <AUTHOR> />
 */
public class WncWarehouseStorageFenceConverter {

    public static WncWarehouseStorageFenceEntity fence2Entity(WncWarehouseStorageFence wncWarehouseStorageFence){
        if(wncWarehouseStorageFence == null){
            return null;
        }

        WncWarehouseStorageFenceEntity rcWarehouseStorageFenceEntity = new WncWarehouseStorageFenceEntity();

        rcWarehouseStorageFenceEntity.setId(wncWarehouseStorageFence.getId());
        rcWarehouseStorageFenceEntity.setCreateTime(wncWarehouseStorageFence.getCreateTime());
        rcWarehouseStorageFenceEntity.setUpdateTime(wncWarehouseStorageFence.getUpdateTime());
        rcWarehouseStorageFenceEntity.setProvince(wncWarehouseStorageFence.getProvince());
        rcWarehouseStorageFenceEntity.setCity(wncWarehouseStorageFence.getCity());
        rcWarehouseStorageFenceEntity.setArea(wncWarehouseStorageFence.getArea());
        rcWarehouseStorageFenceEntity.setWarehouseNo(wncWarehouseStorageFence.getWarehouseNo());
        rcWarehouseStorageFenceEntity.setTenantId(wncWarehouseStorageFence.getTenantId());
        rcWarehouseStorageFenceEntity.setLastOperatorName(wncWarehouseStorageFence.getLastOperatorName());

        return rcWarehouseStorageFenceEntity;
    }

    public static WncWarehouseStorageFence entity2RcWarehouseStorageFence(WncWarehouseStorageFenceEntity rcWarehouseStorageFenceEntity){
        if(rcWarehouseStorageFenceEntity == null){
            return null;
        }

        WncWarehouseStorageFence wncWarehouseStorageFence = new WncWarehouseStorageFence();

        wncWarehouseStorageFence.setId(rcWarehouseStorageFenceEntity.getId());
        wncWarehouseStorageFence.setCreateTime(rcWarehouseStorageFenceEntity.getCreateTime());
        wncWarehouseStorageFence.setUpdateTime(rcWarehouseStorageFenceEntity.getUpdateTime());
        wncWarehouseStorageFence.setProvince(rcWarehouseStorageFenceEntity.getProvince());
        wncWarehouseStorageFence.setCity(rcWarehouseStorageFenceEntity.getCity());
        wncWarehouseStorageFence.setArea(rcWarehouseStorageFenceEntity.getArea());
        wncWarehouseStorageFence.setWarehouseNo(rcWarehouseStorageFenceEntity.getWarehouseNo());
        wncWarehouseStorageFence.setTenantId(rcWarehouseStorageFenceEntity.getTenantId());
        wncWarehouseStorageFence.setLastOperatorName(rcWarehouseStorageFenceEntity.getLastOperatorName());

        return wncWarehouseStorageFence;
    }

}
