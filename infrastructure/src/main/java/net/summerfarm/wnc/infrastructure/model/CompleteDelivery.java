package net.summerfarm.wnc.infrastructure.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * complete_delivery完成配送提醒表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-99 15:30:32
 */
@Getter
@Setter
@TableName("complete_delivery")
public class CompleteDelivery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键、自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 物流中心编号（配送仓编号）
     */
    @TableField("store_no")
    private Integer storeNo;

    /**
     * 城市编号
     */
    @TableField("area_no")
    private Integer areaNo;

    /**
     * 配送完成时间
     */
    @TableField("complete_delivery_time")
    private String completeDeliveryTime;

    /**
     * 状态 0 正常 1 暂停
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 更新人
     */
    @TableField("updater")
    private String updater;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 区域
     */
    @TableField("region")
    private String region;

    /**
     * 城市名称
     */
    @TableField("city")
    private String city;


}
