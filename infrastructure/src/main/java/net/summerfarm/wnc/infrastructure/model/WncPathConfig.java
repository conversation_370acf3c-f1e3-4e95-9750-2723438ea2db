package net.summerfarm.wnc.infrastructure.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 路线配置
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-332 14:24:16
 */
@Getter
@Setter
@TableName("wnc_path_config")
public class WncPathConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 开始编号
     */
    @TableField("begin_out_no")
    private Integer beginOutNo;

    /**
     * 结束编号
     */
    @TableField("end_out_no")
    private Integer endOutNo;

    /**
     * 业务类型 0调拨 仓到仓
     */
    @TableField("businsee_type")
    private Integer businseeType;

    /**
     * 周期方案 1每周 2两周
     */
    @TableField("frequent_method")
    private Integer frequentMethod;

    /**
     * 周期 1是周一类推
     */
    @TableField("frequent")
    private String frequent;

    /**
     * 路途时长
     */
    @TableField("travel_duratio")
    private Integer travelDuratio;

    /**
     * 下一次路线时间
     */
    @TableField("last_time")
    private LocalDateTime lastTime;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 更新人
     */
    @TableField("updater")
    private String updater;


}
