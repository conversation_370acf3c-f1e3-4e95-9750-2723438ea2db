package net.summerfarm.wnc.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.summerfarm.wnc.common.enums.ContactConfigEnums;
import net.summerfarm.wnc.common.query.config.ContactConfigQuery;
import net.summerfarm.wnc.domain.config.repository.ContactConfigRepository;
import net.summerfarm.wnc.domain.config.entity.ContactConfigEntity;
import net.summerfarm.wnc.infrastructure.converter.WncContactConfigConverter;
import net.summerfarm.wnc.infrastructure.mapper.WncContactConfigMapper;
import net.summerfarm.wnc.infrastructure.model.WncContactConfig;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description:联系人配置仓库接口实现
 * date: 2023/9/22 18:29
 *
 * <AUTHOR>
 */
@Repository
public class ContactConfigRepositoryImpl implements ContactConfigRepository {

    @Resource
    private WncContactConfigMapper wncContactConfigMapper;

    @Override
    public ContactConfigEntity queryByUk(ContactConfigEnums.Source source, Long outerContactId) {
        if (source == null || outerContactId == null){
            return null;
        }
        WncContactConfig wncContactConfig = wncContactConfigMapper.selectOne(new LambdaQueryWrapper<WncContactConfig>()
                .eq(WncContactConfig::getSource, source.getValue())
                .eq(WncContactConfig::getOuterContactId, outerContactId));
        return WncContactConfigConverter.do2Entity(wncContactConfig);
    }

    @Override
    public int save(ContactConfigEntity contactConfigEntity) {
        WncContactConfig wncContactConfig = WncContactConfigConverter.entity2Do(contactConfigEntity);
        return wncContactConfigMapper.insert(wncContactConfig);
    }

    @Override
    public int remove(Long configId) {
        if (configId == null){
            return 0;
        }
        return wncContactConfigMapper.deleteById(configId);
    }

    @Override
    public List<ContactConfigEntity> queryList(ContactConfigQuery contactConfigQuery) {
        List<WncContactConfig> wncContactConfigs = wncContactConfigMapper.selectList(new LambdaQueryWrapper<WncContactConfig>()
                .in(!CollectionUtils.isEmpty(contactConfigQuery.getOuterContactIds()), WncContactConfig::getOuterContactId, contactConfigQuery.getOuterContactIds()));
        if (CollectionUtils.isEmpty(wncContactConfigs)){
            return Collections.emptyList();
        }
        return wncContactConfigs.stream().map(WncContactConfigConverter::do2Entity).collect(Collectors.toList());
    }

    @Override
    public int update(ContactConfigEntity contactConfigEntity) {
        if (contactConfigEntity == null){
            return 0;
        }
        WncContactConfig wncContactConfig = WncContactConfigConverter.entity2Do(contactConfigEntity);
        return wncContactConfigMapper.updateById(wncContactConfig);
    }
}
