<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wnc.infrastructure.mapper.fence.FenceChannelBusinessWhiteConfigMapper">
    <!-- 结果集映射 -->
    <resultMap id="fenceChannelBusinessWhiteConfigResultMap" type="net.summerfarm.wnc.infrastructure.model.fence.FenceChannelBusinessWhiteConfig">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="fence_id" property="fenceId" jdbcType="INTEGER"/>
		<result column="order_channel_type" property="orderChannelType" jdbcType="VARCHAR"/>
		<result column="tenant_id" property="tenantId" jdbcType="NUMERIC"/>
		<result column="scope_channel_business_id" property="scopeChannelBusinessId" jdbcType="VARCHAR"/>
        <result column="scope_channel_business_name" property="scopeChannelBusinessName" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="fenceChannelBusinessWhiteConfigColumns">
          t.id,
          t.create_time,
          t.update_time,
          t.fence_id,
          t.order_channel_type,
          t.tenant_id,
          t.scope_channel_business_id,
          t.scope_channel_business_name
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="fenceId != null">
                AND t.fence_id = #{fenceId}
            </if>
			<if test="orderChannelType != null and orderChannelType !=''">
                AND t.order_channel_type = #{orderChannelType}
            </if>
			<if test="tenantId != null">
                AND t.tenant_id = #{tenantId}
            </if>
			<if test="scopeChannelBusinessId != null and scopeChannelBusinessId !=''">
                AND t.scope_channel_business_id = #{scopeChannelBusinessId}
            </if>
            <if test="scopeChannelBusinessName != null and scopeChannelBusinessName !=''">
                AND t.scope_channel_business_name = #{scopeChannelBusinessName}
            </if>
            <if test="scopeChannelBusinessIds != null">
                AND t.scope_channel_business_id IN
                <foreach collection="scopeChannelBusinessIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="fenceId != null">
                    t.fence_id = #{fenceId},
                </if>
                <if test="orderChannelType != null">
                    t.order_channel_type = #{orderChannelType},
                </if>
                <if test="tenantId != null">
                    t.tenant_id = #{tenantId},
                </if>
                <if test="scopeChannelBusinessId != null">
                    t.scope_channel_business_id = #{scopeChannelBusinessId},
                </if>
                <if test="scopeChannelBusinessName != null">
                    t.scope_channel_business_name = #{scopeChannelBusinessName},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="fenceChannelBusinessWhiteConfigResultMap" >
        SELECT <include refid="fenceChannelBusinessWhiteConfigColumns" />
        FROM fence_channel_business_white_config t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.wnc.domain.fence.param.query.FenceChannelBusinessWhiteConfigQueryParam"  resultType="net.summerfarm.wnc.domain.fence.entity.FenceChannelBusinessWhiteConfigEntity" >
        SELECT
            t.id id,
            t.create_time createTime,
            t.update_time updateTime,
            t.fence_id fenceId,
            t.order_channel_type orderChannelType,
            t.tenant_id tenantId,
            t.scope_channel_business_id scopeChannelBusinessId,
            t.scope_channel_business_name scopeChannelBusinessName
        FROM fence_channel_business_white_config t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.wnc.domain.fence.param.query.FenceChannelBusinessWhiteConfigQueryParam" resultMap="fenceChannelBusinessWhiteConfigResultMap" >
        SELECT <include refid="fenceChannelBusinessWhiteConfigColumns" />
        FROM fence_channel_business_white_config t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.wnc.infrastructure.model.fence.FenceChannelBusinessWhiteConfig" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO fence_channel_business_white_config
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="fenceId != null">
				  fence_id,
              </if>
              <if test="orderChannelType != null">
				  order_channel_type,
              </if>
              <if test="tenantId != null">
				  tenant_id,
              </if>
              <if test="scopeChannelBusinessId != null">
				  scope_channel_business_id,
              </if>
              <if test="scopeChannelBusinessName != null">
                  scope_channel_business_name,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="fenceId != null">
				#{fenceId,jdbcType=INTEGER},
              </if>
              <if test="orderChannelType != null">
				#{orderChannelType,jdbcType=VARCHAR},
              </if>
              <if test="tenantId != null">
				#{tenantId,jdbcType=NUMERIC},
              </if>
              <if test="scopeChannelBusinessId != null">
				#{scopeChannelBusinessId,jdbcType=VARCHAR},
              </if>
             <if test="scopeChannelBusinessName != null">
                #{scopeChannelBusinessName,jdbcType=VARCHAR},
             </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.wnc.infrastructure.model.fence.FenceChannelBusinessWhiteConfig" >
        UPDATE fence_channel_business_white_config t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.wnc.infrastructure.model.fence.FenceChannelBusinessWhiteConfig" >
        DELETE FROM fence_channel_business_white_config
		WHERE id = #{id,jdbcType=NUMERIC}
    </delete>



</mapper>