package net.summerfarm.wnc.application.deliveryRule;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.deliveryRule.dto.ContactDeliveryRuleDTO;
import net.summerfarm.wnc.api.deliveryRule.input.ContactDeliveryRuleDelCommand;
import net.summerfarm.wnc.api.deliveryRule.input.ContactDeliveryRuleSaveUpdateCommand;
import net.summerfarm.wnc.api.deliveryRule.service.ContactDeliveryRuleService;
import net.summerfarm.wnc.application.deliveryRule.converter.ContactDeliveryRuleDTOConverter;
import net.summerfarm.wnc.common.enums.WncContactDeliveryRuleEnums;
import net.summerfarm.wnc.common.query.deliveryRule.ContactDeliveryRuleQuery;
import net.summerfarm.wnc.domain.deliveryRule.ContactDeliveryRuleRepository;
import net.summerfarm.wnc.domain.deliveryRule.entity.ContactDeliveryRuleEntity;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.Objects;

/**
 * Description: 客户地址配送规则<br/>
 * date: 2023/11/13 15:05<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ContactDeliveryRuleServiceImpl implements ContactDeliveryRuleService {

    private final ContactDeliveryRuleRepository contactDeliveryRuleRepository;

    @Override
    public ContactDeliveryRuleDTO queryContactDeliveryRule(ContactDeliveryRuleQuery query) {
        return ContactDeliveryRuleDTOConverter.entity2DTO(contactDeliveryRuleRepository.queryByUk(query.getOutBusinessNo(),query.getSystemSource()));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveOrUpdate(ContactDeliveryRuleSaveUpdateCommand command) {
        //参数校验
        if(StrUtil.isBlank(command.getOutBusinessNo())){
            throw new BizException("业务编号不能为空");
        }
        if(command.getFrequentMethod() == null){
            throw new BizException("周期方案不能为空");
        }
        if(command.getSystemSource() == null){
            throw new BizException("系统来源不能为空");
        }
        //周计算 weekDeliveryFrequent不能为空
        if(Objects.equals(command.getFrequentMethod(), WncContactDeliveryRuleEnums.FrequentMethod.WEEK_CALC.getValue())){
            if(StrUtil.isBlank(command.getWeekDeliveryFrequent())){
                throw new BizException("周的配送周期不能为空");
            }
            String[] days = command.getWeekDeliveryFrequent().split(",");
            for (String day : days) {
                try {
                    int weekDay = Integer.parseInt(day);
                    if(weekDay > 7 || weekDay < 0){
                        throw new BizException("周的配送周期只能大于等于0小于等于7");
                    }
                } catch (NumberFormatException e) {
                    throw new BizException("周的配送周期只能为整数");
                }
            }
        }else{
            //间隔计算
            if(command.getDeliveryFrequentInterval() == null){
                throw new BizException("配送间隔周期不能为空");
            }
            if(command.getDeliveryFrequentInterval() < 1){
                throw new BizException("配送间隔周期不能小于1");
            }
            if(command.getBeginCalculateDate() == null){
                throw new BizException("开始计算日期不能为空");
            }
            if(command.getBeginCalculateDate().compareTo(LocalDate.now()) > 0){
                throw new BizException("开始计算日期不能大于当天");
            }
        }
        contactDeliveryRuleRepository.saveOrUpdate(ContactDeliveryRuleEntity.builder()
                .systemSource(command.getSystemSource())
                .outBusinessNo(command.getOutBusinessNo())
                .frequentMethod(command.getFrequentMethod())
                .weekDeliveryFrequent(command.getWeekDeliveryFrequent())
                .beginCalculateDate(command.getBeginCalculateDate())
                .tenantId(command.getTenantId())
                .deliveryFrequentInterval(command.getDeliveryFrequentInterval())
                .build());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delteDeliveryRule(ContactDeliveryRuleDelCommand command) {
        if(StrUtil.isBlank(command.getOutBusinessNo())){
            throw new BizException("业务编号不能为空");
        }
        if(command.getSystemSource() == null){
            throw new BizException("系统来源不能为空");
        }
        contactDeliveryRuleRepository.delteByUk(command.getOutBusinessNo(),command.getSystemSource());
    }
}
