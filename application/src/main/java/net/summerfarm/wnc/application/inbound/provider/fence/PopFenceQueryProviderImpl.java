package net.summerfarm.wnc.application.inbound.provider.fence;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.client.provider.fence.PopFenceQueryProvider;
import net.summerfarm.wnc.client.req.fence.PopAddressAreaQueryReq;
import net.summerfarm.wnc.client.resp.fence.PopAreaResp;
import net.summerfarm.wnc.common.config.WncConfig;
import net.summerfarm.wnc.common.config.obj.PopCityAreaOperatingAreaMappingObj;
import net.summerfarm.wnc.common.util.PopConfigUtil;
import net.summerfarm.wnc.domain.ConfigRepository;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * Description: pop围栏查询服务<br/>
 * date: 2024/11/7 11:16<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@DubboService
public class PopFenceQueryProviderImpl implements PopFenceQueryProvider {

    @Resource
    private WncConfig wncConfig;
    @Resource
    private ConfigRepository configRepository;
    @Autowired
    private PopConfigUtil popConfigUtil;

    @Override
    public DubboResponse<PopAreaResp> queryPopAreaByAddress(@Valid PopAddressAreaQueryReq req) {
        // 过滤没有区域的城市
        List<String> noAreaCityList = configRepository.queryNoAreaCity();
        if (noAreaCityList.contains(req.getCity())) {
            req.setArea(null);
        } else {
            if (StringUtils.isEmpty(req.getArea())) {
                return DubboResponse.getDefaultError("区域不能为空");
            }
        }

        PopCityAreaOperatingAreaMappingObj matchedPopConfig = popConfigUtil.queryMatchedPopConfig(req.getCity(), req.getArea());
        if (matchedPopConfig == null) {
            return DubboResponse.getOK(null);
        }

        PopAreaResp popAreaResp = new PopAreaResp();
        popAreaResp.setAreaNo(matchedPopConfig.getAreaNo());
        return DubboResponse.getOK(popAreaResp);
    }
}
