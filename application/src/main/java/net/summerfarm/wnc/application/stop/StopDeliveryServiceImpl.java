package net.summerfarm.wnc.application.stop;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.stop.input.StopDeliveryAddInput;
import net.summerfarm.wnc.api.stop.input.StopDeliveryCancelInput;
import net.summerfarm.wnc.api.stop.service.StopDeliveryService;
import net.summerfarm.wnc.application.stop.converter.StopDeliveryEntityConverter;
import net.summerfarm.wnc.domain.stop.StopDeliveryDomainService;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;

/**
 * Description: 停配服务
 * date: 2023/10/9 16:33<br/>
 *
 * <AUTHOR> />
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class StopDeliveryServiceImpl implements StopDeliveryService {

    private final StopDeliveryDomainService stopDeliveryDomainService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void stopDeliveryAdd(StopDeliveryAddInput input) {
        if(input.getShutdownEndTime().isBefore(input.getShutdownStartTime())){
            throw new BizException("开始时间必须大于等于结束时间");
        }
        if (input.getShutdownStartTime().compareTo(LocalDate.now()) <= 0 || LocalDate.now().equals(input.getShutdownEndTime())) {
            throw new BizException("开始时间必须大于当前");
        }
        //判断配置的停运时间跨度是否超过 31天
        long day = ChronoUnit.DAYS.between(input.getShutdownStartTime(), input.getShutdownEndTime());
        if (day > 31) {
            throw new BizException("最长停配时间不可超过31天，请确认");
        }
        stopDeliveryDomainService.stopDeliveryAdd(StopDeliveryEntityConverter.addInput2Entity(input));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void stopDeliveryCancel(StopDeliveryCancelInput input) {
        stopDeliveryDomainService.stopDeliveryCancel(input.getStoreNo());
    }
}
