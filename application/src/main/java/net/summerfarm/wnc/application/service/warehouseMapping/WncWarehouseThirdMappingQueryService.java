package net.summerfarm.wnc.application.service.warehouseMapping;


import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.domain.warehouseMapping.entity.WncWarehouseThirdMappingEntity;
import net.summerfarm.wnc.domain.warehouseMapping.param.query.WncWarehouseThirdMappingQueryParam;
import net.summerfarm.wnc.application.inbound.controller.warehouseMapping.input.query.WncWarehouseThirdMappingQueryInput;

/**
 *
 * @date 2025-06-11 15:08:31
 * @version 1.0
 *
 */
public interface WncWarehouseThirdMappingQueryService {

    /**
     * @description: 新增
     * @return WncWarehouseThirdMappingEntity
     **/
    PageInfo<WncWarehouseThirdMappingEntity> getPage(WncWarehouseThirdMappingQueryInput input);

    /**
     * @description: 更新
     * @return: java.lang.Boolean
     **/
    WncWarehouseThirdMappingEntity getDetail(Long id);

}