package net.summerfarm.wnc.application.inbound.controller.config.input.query;

import lombok.Data;
import java.io.Serializable;
import net.xianmu.common.input.BasePageInput;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2025-01-06 15:31:26
 * @version 1.0
 *
 */
@Data
public class WncFullCategoryWarehouseSkuWhiteConfigQueryInput extends BasePageInput implements Serializable{
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 库存仓编号
	 */
	private Integer warehouseNo;

	/**
	 * sku
	 */
	private String sku;



}