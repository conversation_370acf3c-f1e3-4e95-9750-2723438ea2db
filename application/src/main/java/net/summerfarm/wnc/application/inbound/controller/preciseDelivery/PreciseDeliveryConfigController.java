package net.summerfarm.wnc.application.inbound.controller.preciseDelivery;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.application.inbound.controller.preciseDelivery.input.*;
import net.summerfarm.wnc.application.inbound.controller.preciseDelivery.vo.PreciseDeliveryConfigVO;
import net.summerfarm.wnc.application.inbound.converter.preciseDelivery.PreciseDeliveryConfigVoConverter;
import net.summerfarm.wnc.application.service.preciseDelivery.PreciseDeliveryConfigCommandService;
import net.summerfarm.wnc.application.service.preciseDelivery.PreciseDeliveryConfigQueryService;
import net.summerfarm.wnc.common.constants.RedisKeys;
import net.summerfarm.wnc.common.enums.WncErrorCode;
import net.summerfarm.wnc.common.util.RedisUtil;
import net.summerfarm.wnc.domain.preciseDelivery.aggregate.PreciseDeliveryConfigAggregate;
import net.xianmu.common.result.CommonResult;
import net.xianmu.redis.support.lock.annotation.XmLock;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * Description:精准送配置相关接口
 * date: 2024/1/18 17:30
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/precise-delivery-config")
public class PreciseDeliveryConfigController {

    @Resource
    private PreciseDeliveryConfigCommandService preciseDeliveryConfigCommandService;

    @Resource
    private PreciseDeliveryConfigQueryService preparedDeliveryConfigQueryService;

    @Resource
    private RedisUtil redisUtil;

    /**
     * 分页查询精准送配置列表
     * @param configPageQuery 查询
     * @return 结果
     */
    @PostMapping("/query/page")
    public CommonResult<PageInfo<PreciseDeliveryConfigVO>> queryConfigPage(@RequestBody PreciseDeliveryConfigPageQueryInput configPageQuery) {
        PageInfo<PreciseDeliveryConfigAggregate> pageInfo = preparedDeliveryConfigQueryService.queryPage(configPageQuery);
        return CommonResult.ok(PreciseDeliveryConfigVoConverter.aggregatePage2VoPage(pageInfo));
    }

    /**
     * 查询精准送配置详情
     * @param configIdQuery 查询
     * @return 结果
     */
    @PostMapping("/query/detail")
    public CommonResult<PreciseDeliveryConfigVO> queryConfigDetail(@RequestBody @Validated PreciseDeliveryConfigIdQueryInput configIdQuery) {
        return CommonResult.ok(PreciseDeliveryConfigVoConverter.aggregate2Vo(preparedDeliveryConfigQueryService.queryDetail(configIdQuery)));
    }

    /**
     * 新增精准送配置
     * @param commandInput 命令
     * @return 结果
     */
    @PostMapping("/upsert/save")
    @XmLock(prefixKey = RedisKeys.GLOBAL_PRECISE_DELIVERY_CONFIG_UPSERT, key = "{commandInput.city}", message = "操作频繁，请稍后重试")
    public CommonResult<Void> saveConfig(@RequestBody @Validated PreciseDeliveryConfigCreateCommandInput commandInput) throws InterruptedException {
        preciseDeliveryConfigCommandService.saveConfig(commandInput);
        return CommonResult.ok();
    }

    /**
     * 编辑精准送配置
     * @param commandInput 命令
     * @return 结果
     */
    @PostMapping("/upsert/edit")
    @XmLock(prefixKey = RedisKeys.GLOBAL_PRECISE_DELIVERY_CONFIG_UPSERT, key = "{commandInput.city}", message = "操作频繁，请稍后重试")
    public CommonResult<Void> editConfig(@RequestBody @Validated PreciseDeliveryConfigUpdateCommandInput commandInput) {
         preciseDeliveryConfigCommandService.editConfig(commandInput);
        return CommonResult.ok();
    }

    /**
     * 删除精准送配置
     * @param commandInput 命令
     * @return 结果
     */
    @PostMapping("/upsert/remove")
    public CommonResult<Void> removeConfig(@RequestBody @Validated PreciseDeliveryConfigRemoveCommandInput commandInput) {
        preciseDeliveryConfigCommandService.removeConfig(commandInput);
        return CommonResult.ok();
    }

    /**
     * 精准送配置初始化
     * @return 结果
     */
    @PostMapping("/upsert/data-init")
    public CommonResult<Void> dataInit() {
        preciseDeliveryConfigCommandService.dataInit();
        return CommonResult.ok();
    }

    /**
     * 精准送配置初始化 by运营服务区域
     * @return 结果
     */
    @PostMapping("/upsert/init-config-by-areaNo")
    public CommonResult<Void> initConfigByAreaNos(@RequestBody @Validated PreciseDeliveryConfigInitCommandInput commandInput) {
        preciseDeliveryConfigCommandService.initConfigByAreaNos(commandInput);
        return CommonResult.ok();
    }
}
