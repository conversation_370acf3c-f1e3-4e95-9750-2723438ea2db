package net.summerfarm.wnc.application.inbound.controller.fence;

import net.summerfarm.wnc.api.fence.service.WncWarehouseStorageFenceService;
import net.summerfarm.wnc.api.warehouse.dto.WncWarehouseStorageFenceDTO;
import net.summerfarm.wnc.api.base.BaseController;
import net.summerfarm.wnc.common.base.WncAssert;
import net.summerfarm.wnc.common.query.warehouse.WarehouseStorageFenceQuery;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 自营仓围栏规则
 * <AUTHOR> />
 */
@RestController
@RequestMapping("/warehouse-fence")
public class WarehouseStorageFenceController extends BaseController {

    @Resource
    private WncWarehouseStorageFenceService wncWarehouseStorageFenceService;

    /**
     * 判断租户是否存在围栏冲突 ture冲突 false不冲突
     */
    @PostMapping("/query/fence-conflict")
    public CommonResult<List<WncWarehouseStorageFenceDTO>> queryFenceConflict(@RequestBody WarehouseStorageFenceQuery warehouseStorageFenceQuery) {
        WncAssert.notNull(warehouseStorageFenceQuery.getWarehouseNo(),"warehouseNo不能为空");
        WncAssert.notEmpty(warehouseStorageFenceQuery.getCitys(), "citys不能为空");
        warehouseStorageFenceQuery.setTenantId(getTenantId());

        return CommonResult.ok(wncWarehouseStorageFenceService.queryFenceConflict(warehouseStorageFenceQuery));
    }
}
