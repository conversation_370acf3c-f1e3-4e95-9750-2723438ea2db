package net.summerfarm.wnc.application.warehouse.converter;

import net.summerfarm.wnc.api.warehouse.dto.*;
import net.summerfarm.wnc.domain.warehouse.entity.*;
import net.summerfarm.wnc.api.warehouse.input.*;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/3/31 14:54<br/>
 *
 * <AUTHOR> />
 */
public class WarehouseStorageCenterEntityConverter {

    public static WarehouseStorageEntity warehouseStorageSave2Entity(WarehouseStorageSaveInput warehouseStorageSaveInput) {
        if (warehouseStorageSaveInput == null) {
            return null;
        }
        WarehouseStorageEntity warehouseStorageEntity = new WarehouseStorageEntity();
        warehouseStorageEntity.setWarehouseName(warehouseStorageSaveInput.getWarehouseName());
        warehouseStorageEntity.setStatus(warehouseStorageSaveInput.getStatus());
        warehouseStorageEntity.setAddress(warehouseStorageSaveInput.getAddress());
        warehouseStorageEntity.setPoiNote(warehouseStorageSaveInput.getPoiNote());
        warehouseStorageEntity.setPersonContact(warehouseStorageSaveInput.getPersonContact());
        warehouseStorageEntity.setPhone(warehouseStorageSaveInput.getPhone());
        warehouseStorageEntity.setTenantId(warehouseStorageSaveInput.getTenantId());
        warehouseStorageEntity.setCreator(warehouseStorageSaveInput.getCreateAdminId());
        warehouseStorageEntity.setOperatorName(warehouseStorageSaveInput.getOperatorName());
        if (warehouseStorageSaveInput.getCapacity() != null && warehouseStorageSaveInput.getAdvanceDay() != null) {
            WarehouseStorageCenterBusEntity warehouseStorageCenterBusEntity = new WarehouseStorageCenterBusEntity();
            warehouseStorageCenterBusEntity.setCapacity(warehouseStorageSaveInput.getCapacity());
            warehouseStorageCenterBusEntity.setAdvanceDay(warehouseStorageSaveInput.getAdvanceDay());
            warehouseStorageEntity.setWarehouseStorageCenterBusEntity(warehouseStorageCenterBusEntity);
        }
        if (!CollectionUtils.isEmpty(warehouseStorageSaveInput.getWarehouseTakeStandardSaveInputs())) {
            List<WarehouseTakeStandardEntity> warehouseTakeStandardEntities = warehouseStorageSaveInput.getWarehouseTakeStandardSaveInputs().stream()
                    .map(WarehouseStorageCenterEntityConverter::warehouseTakeStandardSaveInput2Entity)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());

            warehouseStorageEntity.setWarehouseTakeStandardEntities(warehouseTakeStandardEntities);
        }
        if (!CollectionUtils.isEmpty(warehouseStorageSaveInput.getSendFenceList())) {
            List<WarehouseStorageFenceSaveInput> sendFenceList = warehouseStorageSaveInput.getSendFenceList();

            warehouseStorageEntity.setWarehouseStorageFenceEntities(sendFenceList.stream()
                    .map(WarehouseStorageCenterEntityConverter::warehouseStorageFenceSaveInput2Entity).collect(Collectors.toList())
            );
        }

        return warehouseStorageEntity;
    }

    public static WarehouseStorageEntity warehouseStorageUpdate2Entity(WarehouseStorageUpdateInput warehouseStorageUpdateInput) {
        if (warehouseStorageUpdateInput == null) {
            return null;
        }
        WarehouseStorageEntity warehouseStorageEntity = new WarehouseStorageEntity();
        warehouseStorageEntity.setId(warehouseStorageUpdateInput.getId());
        warehouseStorageEntity.setStatus(warehouseStorageUpdateInput.getStatus());
        warehouseStorageEntity.setAddress(warehouseStorageUpdateInput.getAddress());
        warehouseStorageEntity.setWarehouseName(warehouseStorageUpdateInput.getWarehouseName());
        warehouseStorageEntity.setPoiNote(warehouseStorageUpdateInput.getPoiNote());
        warehouseStorageEntity.setPersonContact(warehouseStorageUpdateInput.getPersonContact());
        warehouseStorageEntity.setPhone(warehouseStorageUpdateInput.getPhone());
        warehouseStorageEntity.setTenantId(warehouseStorageUpdateInput.getTenantId());
        warehouseStorageEntity.setCreator(warehouseStorageUpdateInput.getCreateAdminId().intValue());
        if (warehouseStorageUpdateInput.getCapacity() != null && warehouseStorageUpdateInput.getAdvanceDay() != null) {
            WarehouseStorageCenterBusEntity warehouseStorageCenterBusEntity = new WarehouseStorageCenterBusEntity();
            warehouseStorageCenterBusEntity.setCapacity(warehouseStorageUpdateInput.getCapacity());
            warehouseStorageCenterBusEntity.setAdvanceDay(warehouseStorageUpdateInput.getAdvanceDay());
            warehouseStorageEntity.setWarehouseStorageCenterBusEntity(warehouseStorageCenterBusEntity);
        }
        if (!CollectionUtils.isEmpty(warehouseStorageUpdateInput.getWarehouseTakeStandardSaveInputs())) {
            List<WarehouseTakeStandardEntity> warehouseTakeStandardEntities = warehouseStorageUpdateInput.getWarehouseTakeStandardSaveInputs().stream()
                    .map(WarehouseStorageCenterEntityConverter::warehouseTakeStandardSaveInput2Entity)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());

            warehouseStorageEntity.setWarehouseTakeStandardEntities(warehouseTakeStandardEntities);
        }
        if (!CollectionUtils.isEmpty(warehouseStorageUpdateInput.getSendFenceList())) {
            List<WarehouseStorageFenceSaveInput> sendFenceList = warehouseStorageUpdateInput.getSendFenceList();

            warehouseStorageEntity.setWarehouseStorageFenceEntities(sendFenceList.stream()
                    .map(WarehouseStorageCenterEntityConverter::warehouseStorageFenceSaveInput2Entity).collect(Collectors.toList())
            );
        }

        return warehouseStorageEntity;
    }

    public static List<WarehouseTakeStandardEntity> warehouseTakeStandardSaveInput2Entity(WarehouseTakeStandardSaveInput warehouseTakeStandardSaveInput) {
        if(warehouseTakeStandardSaveInput == null){
            return null;
        }
        List<WarehouseTakeStandardEntity> warehouseTakeStandardEntities = new ArrayList<>();

        for (StorageProveCommandInput storageProveCommandInput : warehouseTakeStandardSaveInput.getStorageProve()) {
            for (Integer proveType : storageProveCommandInput.getProveType()) {
                WarehouseTakeStandardEntity warehouseTakeStandardEntity = new WarehouseTakeStandardEntity();

                warehouseTakeStandardEntity.setProveType(proveType);
                warehouseTakeStandardEntity.setStorageLocation(storageProveCommandInput.getStorageLocation());
                warehouseTakeStandardEntity.setStandardType(warehouseTakeStandardSaveInput.getStandardType());

                warehouseTakeStandardEntities.add(warehouseTakeStandardEntity);
            }
        }

        return warehouseTakeStandardEntities;
    }

    public static WncWarehouseStorageFenceEntity warehouseStorageFenceSaveInput2Entity(WarehouseStorageFenceSaveInput warehouseStorageFenceSaveInput) {
        if(warehouseStorageFenceSaveInput == null){
            return null;
        }

        WncWarehouseStorageFenceEntity rcWarehouseStorageFenceEntity = new WncWarehouseStorageFenceEntity();
        rcWarehouseStorageFenceEntity.setProvince(warehouseStorageFenceSaveInput.getProvince());
        rcWarehouseStorageFenceEntity.setCity(warehouseStorageFenceSaveInput.getCity());
        rcWarehouseStorageFenceEntity.setArea(warehouseStorageFenceSaveInput.getArea());

        return rcWarehouseStorageFenceEntity;
    }


    public static WarehouseStorageDTO entity2DTO(WarehouseStorageEntity warehouseStorageEntity) {
        if(warehouseStorageEntity == null){
            return null;
        }

        WarehouseStorageDTO warehouseStorageDTO = new WarehouseStorageDTO();
        warehouseStorageDTO.setId(warehouseStorageEntity.getId());
        warehouseStorageDTO.setWarehouseNo(warehouseStorageEntity.getWarehouseNo());
        warehouseStorageDTO.setWarehouseName(warehouseStorageEntity.getWarehouseName());
        warehouseStorageDTO.setManageAdminId(warehouseStorageEntity.getManageAdminId());
        warehouseStorageDTO.setType(warehouseStorageEntity.getType());
        warehouseStorageDTO.setAreaManageId(warehouseStorageEntity.getAreaManageId());
        warehouseStorageDTO.setStatus(warehouseStorageEntity.getStatus());
        warehouseStorageDTO.setAddress(warehouseStorageEntity.getAddress());
        warehouseStorageDTO.setPoiNote(warehouseStorageEntity.getPoiNote());
        warehouseStorageDTO.setMailToAddress(warehouseStorageEntity.getMailToAddress());
        warehouseStorageDTO.setUpdater(warehouseStorageEntity.getUpdater());
        warehouseStorageDTO.setUpdateTime(warehouseStorageEntity.getUpdateTime());
        warehouseStorageDTO.setCreator(warehouseStorageEntity.getCreator());
        warehouseStorageDTO.setCreateTime(warehouseStorageEntity.getCreateTime());
        warehouseStorageDTO.setPersonContact(warehouseStorageEntity.getPersonContact());
        warehouseStorageDTO.setPhone(warehouseStorageEntity.getPhone());
        warehouseStorageDTO.setTenantId(warehouseStorageEntity.getTenantId());
        warehouseStorageDTO.setSku(warehouseStorageEntity.getSku());
        warehouseStorageDTO.setWarehousePic(warehouseStorageEntity.getWarehousePic());
        if(warehouseStorageEntity.getAdminEntity() != null){
            warehouseStorageDTO.setManageAdminName(warehouseStorageEntity.getAdminEntity().getManageAdminName());
        }
        warehouseStorageDTO.setWarehouseStorageCenterBusDTO(WarehouseStorageCenterEntityConverter.entity2BusDTO(warehouseStorageEntity.getWarehouseStorageCenterBusEntity()));
        if(!CollectionUtils.isEmpty(warehouseStorageEntity.getWarehouseTakeStandardEntities())){
            warehouseStorageDTO.setWarehouseTakeStandardDTOs(warehouseStorageEntity.getWarehouseTakeStandardEntities().stream()
                    .map(WarehouseStorageCenterEntityConverter::takeStandard2DTO).collect(Collectors.toList()));
        }
        if(!CollectionUtils.isEmpty(warehouseStorageEntity.getWarehouseStorageFenceEntities())){
            warehouseStorageDTO.setRcWarehouseStorageFenceDTOs(warehouseStorageEntity.getWarehouseStorageFenceEntities().stream()
                    .map(WarehouseStorageCenterEntityConverter::warehouseFenceEntity2DTO).collect(Collectors.toList()));
        }
        if(!CollectionUtils.isEmpty(warehouseStorageEntity.getWarehouseStorageCenterWorkEntities())){
            warehouseStorageDTO.setWarehouseStorageCenterWorkDTOs(warehouseStorageEntity.getWarehouseStorageCenterWorkEntities().stream()
                    .map(WarehouseStorageCenterEntityConverter::workEntity2DTO).collect(Collectors.toList()));
        }

        return warehouseStorageDTO;
    }


    public static WarehouseStorageCenterBusDTO entity2BusDTO(WarehouseStorageCenterBusEntity warehouseStorageCenterBusEntity){
        if(warehouseStorageCenterBusEntity == null){
            return null;
        }
        WarehouseStorageCenterBusDTO warehouseStorageCenterBusDTO = new WarehouseStorageCenterBusDTO();

        warehouseStorageCenterBusDTO.setId(warehouseStorageCenterBusEntity.getId());
        warehouseStorageCenterBusDTO.setWarehouseNo(warehouseStorageCenterBusEntity.getWarehouseNo());
        warehouseStorageCenterBusDTO.setCapacity(warehouseStorageCenterBusEntity.getCapacity());
        warehouseStorageCenterBusDTO.setAdvanceDay(warehouseStorageCenterBusEntity.getAdvanceDay());
        warehouseStorageCenterBusDTO.setCreateTime(warehouseStorageCenterBusEntity.getCreateTime());
        warehouseStorageCenterBusDTO.setUpdateTime(warehouseStorageCenterBusEntity.getUpdateTime());
        warehouseStorageCenterBusDTO.setIsDelete(warehouseStorageCenterBusEntity.getIsDelete());

        return warehouseStorageCenterBusDTO;
    }

    public static WarehouseTakeStandardDTO takeStandard2DTO(WarehouseTakeStandardEntity warehouseTakeStandardEntity) {
        if(warehouseTakeStandardEntity == null){
            return null;
        }
        WarehouseTakeStandardDTO warehouseTakeStandardDTO = new WarehouseTakeStandardDTO();

        warehouseTakeStandardDTO.setId(warehouseTakeStandardEntity.getId());
        warehouseTakeStandardDTO.setCreateTime(warehouseTakeStandardEntity.getCreateTime());
        warehouseTakeStandardDTO.setUpdateTime(warehouseTakeStandardEntity.getUpdateTime());
        warehouseTakeStandardDTO.setStandardType(warehouseTakeStandardEntity.getStandardType());
        warehouseTakeStandardDTO.setStorageLocation(warehouseTakeStandardEntity.getStorageLocation());
        warehouseTakeStandardDTO.setWarehouseNo(warehouseTakeStandardEntity.getWarehouseNo());
        warehouseTakeStandardDTO.setProveType(warehouseTakeStandardEntity.getProveType());
        warehouseTakeStandardDTO.setCreateAdminId(warehouseTakeStandardEntity.getCreateAdminId());

        return warehouseTakeStandardDTO;
    }

    public static WncWarehouseStorageFenceDTO warehouseFenceEntity2DTO(WncWarehouseStorageFenceEntity rcWarehouseStorageFenceEntity){
        if(rcWarehouseStorageFenceEntity == null){
            return null;
        }
        WncWarehouseStorageFenceDTO rcWarehouseStorageFenceDTO = new WncWarehouseStorageFenceDTO();
        rcWarehouseStorageFenceDTO.setId(rcWarehouseStorageFenceEntity.getId());
        rcWarehouseStorageFenceDTO.setCreateTime(rcWarehouseStorageFenceEntity.getCreateTime());
        rcWarehouseStorageFenceDTO.setUpdateTime(rcWarehouseStorageFenceEntity.getUpdateTime());
        rcWarehouseStorageFenceDTO.setProvince(rcWarehouseStorageFenceEntity.getProvince());
        rcWarehouseStorageFenceDTO.setCity(rcWarehouseStorageFenceEntity.getCity());
        rcWarehouseStorageFenceDTO.setArea(rcWarehouseStorageFenceEntity.getArea());
        rcWarehouseStorageFenceDTO.setWarehouseNo(rcWarehouseStorageFenceEntity.getWarehouseNo());
        rcWarehouseStorageFenceDTO.setTenantId(rcWarehouseStorageFenceEntity.getTenantId());
        rcWarehouseStorageFenceDTO.setLastOperatorName(rcWarehouseStorageFenceEntity.getLastOperatorName());
        return rcWarehouseStorageFenceDTO;
    }

    public static WarehouseStorageEntity xmWarehouseStorageUpdateInput2Entity(XmWarehouseStorageUpdateInput xmWarehouseStorageUpdateInput) {
        if (xmWarehouseStorageUpdateInput == null) {
            return null;
        }
        WarehouseStorageEntity warehouseStorageEntity = new WarehouseStorageEntity();
        warehouseStorageEntity.setId(xmWarehouseStorageUpdateInput.getId());
        warehouseStorageEntity.setStatus(xmWarehouseStorageUpdateInput.getStatus());
        warehouseStorageEntity.setAddress(xmWarehouseStorageUpdateInput.getAddress());
        warehouseStorageEntity.setWarehouseName(xmWarehouseStorageUpdateInput.getWarehouseName());
        warehouseStorageEntity.setPoiNote(xmWarehouseStorageUpdateInput.getPoiNote());
        warehouseStorageEntity.setPersonContact(xmWarehouseStorageUpdateInput.getPersonContact());
        warehouseStorageEntity.setPhone(xmWarehouseStorageUpdateInput.getPhone());
        warehouseStorageEntity.setAreaManageId(xmWarehouseStorageUpdateInput.getAreaManageId());
        warehouseStorageEntity.setType(xmWarehouseStorageUpdateInput.getType());
        warehouseStorageEntity.setManageAdminId(xmWarehouseStorageUpdateInput.getManageAdminId());
        warehouseStorageEntity.setMailToAddress(xmWarehouseStorageUpdateInput.getMailToAddress());
        warehouseStorageEntity.setWarehousePic(xmWarehouseStorageUpdateInput.getWarehousePic());
        if (xmWarehouseStorageUpdateInput.getCapacity() != null && xmWarehouseStorageUpdateInput.getAdvanceDay() != null) {
            WarehouseStorageCenterBusEntity warehouseStorageCenterBusEntity = new WarehouseStorageCenterBusEntity();
            if(xmWarehouseStorageUpdateInput.getCapacity() != null){
                warehouseStorageCenterBusEntity.setCapacity(xmWarehouseStorageUpdateInput.getCapacity().multiply(BigDecimal.valueOf(1000)).longValue());
            }
            warehouseStorageCenterBusEntity.setAdvanceDay(xmWarehouseStorageUpdateInput.getAdvanceDay());
            warehouseStorageEntity.setWarehouseStorageCenterBusEntity(warehouseStorageCenterBusEntity);
        }
        if (!CollectionUtils.isEmpty(xmWarehouseStorageUpdateInput.getWarehouseTakeStandardSaveInputs())) {
            List<WarehouseTakeStandardEntity> warehouseTakeStandardEntities = xmWarehouseStorageUpdateInput.getWarehouseTakeStandardSaveInputs().stream()
                    .map(WarehouseStorageCenterEntityConverter::warehouseTakeStandardSaveInput2Entity)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());

            warehouseStorageEntity.setWarehouseTakeStandardEntities(warehouseTakeStandardEntities);
        }
        if (!CollectionUtils.isEmpty(xmWarehouseStorageUpdateInput.getWorkTimeInputs())) {
            warehouseStorageEntity.setWarehouseStorageCenterWorkEntities(xmWarehouseStorageUpdateInput.getWorkTimeInputs().stream()
                    .map(WarehouseStorageCenterEntityConverter::input2WorkTimeEntity).collect(Collectors.toList()));
        }

        return warehouseStorageEntity;
    }



    public static WarehouseStorageEntity xmWarehouseStorageSave2Entity(XmWarehouseStorageSaveInput warehouseStorageSaveInput) {
        if (warehouseStorageSaveInput == null) {
            return null;
        }
        WarehouseStorageEntity warehouseStorageEntity = new WarehouseStorageEntity();
        warehouseStorageEntity.setWarehouseName(warehouseStorageSaveInput.getWarehouseName());
        warehouseStorageEntity.setStatus(warehouseStorageSaveInput.getStatus());
        warehouseStorageEntity.setType(warehouseStorageSaveInput.getType());
        warehouseStorageEntity.setAddress(warehouseStorageSaveInput.getAddress());
        warehouseStorageEntity.setPoiNote(warehouseStorageSaveInput.getPoiNote());
        warehouseStorageEntity.setPersonContact(warehouseStorageSaveInput.getPersonContact());
        warehouseStorageEntity.setPhone(warehouseStorageSaveInput.getPhone());
        warehouseStorageEntity.setTenantId(warehouseStorageSaveInput.getTenantId());
        warehouseStorageEntity.setCreator(warehouseStorageSaveInput.getCreator());
        warehouseStorageEntity.setAreaManageId(warehouseStorageSaveInput.getAreaManageId());
        warehouseStorageEntity.setMailToAddress(warehouseStorageSaveInput.getMailToAddress());
        warehouseStorageEntity.setManageAdminId(warehouseStorageSaveInput.getManageAdminId());
        warehouseStorageEntity.setWarehousePic(warehouseStorageSaveInput.getWarehousePic());
        if (warehouseStorageSaveInput.getCapacity() != null && warehouseStorageSaveInput.getAdvanceDay() != null) {
            WarehouseStorageCenterBusEntity warehouseStorageCenterBusEntity = new WarehouseStorageCenterBusEntity();
            warehouseStorageCenterBusEntity.setAdvanceDay(warehouseStorageSaveInput.getAdvanceDay());
            warehouseStorageCenterBusEntity.setCapacity(warehouseStorageSaveInput.getCapacity().longValue());
            warehouseStorageEntity.setWarehouseStorageCenterBusEntity(warehouseStorageCenterBusEntity);
        }
        if (!CollectionUtils.isEmpty(warehouseStorageSaveInput.getWarehouseTakeStandardSaveInputs())) {
            List<WarehouseTakeStandardEntity> warehouseTakeStandardEntities = warehouseStorageSaveInput.getWarehouseTakeStandardSaveInputs().stream()
                    .map(WarehouseStorageCenterEntityConverter::warehouseTakeStandardSaveInput2Entity)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());

            warehouseStorageEntity.setWarehouseTakeStandardEntities(warehouseTakeStandardEntities);
        }
        if (!CollectionUtils.isEmpty(warehouseStorageSaveInput.getWorkTimeInputs())) {
            warehouseStorageEntity.setWarehouseStorageCenterWorkEntities(warehouseStorageSaveInput.getWorkTimeInputs().stream()
                    .map(WarehouseStorageCenterEntityConverter::input2WorkTimeEntity).collect(Collectors.toList())
            );
        }

        return warehouseStorageEntity;
    }

    public static WarehouseStorageCenterWorkEntity input2WorkTimeEntity(WorkTimeInput workTimeInput){
        if(workTimeInput == null){
            return null;
        }

        WarehouseStorageCenterWorkEntity workEntity = new WarehouseStorageCenterWorkEntity();

        workEntity.setWorkStartTime(workTimeInput.getStartTime());
        workEntity.setWorkEndTime(workTimeInput.getEndTime());

        return workEntity;
    }

    public static WarehouseStorageCenterWorkDTO workEntity2DTO(WarehouseStorageCenterWorkEntity warehouseStorageCenterWorkEntity){
        if(warehouseStorageCenterWorkEntity == null){
            return null;
        }

        WarehouseStorageCenterWorkDTO warehouseStorageCenterWorkDTO = new WarehouseStorageCenterWorkDTO();

        warehouseStorageCenterWorkDTO.setId(warehouseStorageCenterWorkEntity.getId());
        warehouseStorageCenterWorkDTO.setWarehouseStorageCenterBusinessId(warehouseStorageCenterWorkEntity.getWarehouseStorageCenterBusinessId());
        warehouseStorageCenterWorkDTO.setWarehouseNo(warehouseStorageCenterWorkEntity.getWarehouseNo());
        warehouseStorageCenterWorkDTO.setWorkStartTime(warehouseStorageCenterWorkEntity.getWorkStartTime());
        warehouseStorageCenterWorkDTO.setWorkEndTime(warehouseStorageCenterWorkEntity.getWorkEndTime());
        warehouseStorageCenterWorkDTO.setIsDelete(warehouseStorageCenterWorkEntity.getIsDelete());
        warehouseStorageCenterWorkDTO.setCreateTime(warehouseStorageCenterWorkEntity.getCreateTime());
        warehouseStorageCenterWorkDTO.setUpdateTime(warehouseStorageCenterWorkEntity.getUpdateTime());

        return warehouseStorageCenterWorkDTO;
    }
}