package net.summerfarm.wnc.application.fastmall;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.fastmall.dto.WncFastMallDTO;
import net.summerfarm.wnc.api.fastmall.service.WncFastMallService;
import net.summerfarm.wnc.application.fastmall.converter.WncFastMallEntityConverter;
import net.summerfarm.wnc.domain.fastmall.WncFastMallRepository;
import net.summerfarm.wnc.domain.fastmall.entity.WncFastMallEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/4/11 16:37<br/>
 *
 * <AUTHOR> />
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class WncFastMallServiceImpl implements WncFastMallService {

    @Resource
    private WncFastMallRepository wncFastMallRepository;

    @Override
    public List<WncFastMallDTO> queryFastMallList() {
        List<WncFastMallEntity> wncFastMallEntities = wncFastMallRepository.queryFastMallList();
        return wncFastMallEntities.stream().map(WncFastMallEntityConverter::entity2DTO).collect(Collectors.toList());
    }
}
