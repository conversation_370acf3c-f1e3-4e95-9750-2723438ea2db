package net.summerfarm.wnc.application.fence.converter;

import net.summerfarm.wnc.api.fence.dto.FenceAreaDTO;
import net.summerfarm.wnc.api.fence.input.FenceAreaCommand;
import net.summerfarm.wnc.domain.fence.entity.AdCodeMsgEntity;

/**
 * Description:围栏区域转换器
 * date: 2023/8/25 17:51
 *
 * <AUTHOR>
 */
public class FenceAreaConverter {

    public static FenceAreaDTO entity2dto(AdCodeMsgEntity adCodeMsgEntity){
        if(adCodeMsgEntity == null){
            return null;
        }
        FenceAreaDTO fenceAreaDTO = new FenceAreaDTO();
        fenceAreaDTO.setId(adCodeMsgEntity.getId());
        fenceAreaDTO.setAdCode(adCodeMsgEntity.getAdCode());
        fenceAreaDTO.setProvince(adCodeMsgEntity.getProvince());
        fenceAreaDTO.setCity(adCodeMsgEntity.getCity());
        fenceAreaDTO.setArea(adCodeMsgEntity.getArea());
        fenceAreaDTO.setLevel(adCodeMsgEntity.getLevel());
        return fenceAreaDTO;
    }

    public static AdCodeMsgEntity command2entity(FenceAreaCommand fenceAreaCommand){
        if (fenceAreaCommand == null){
            return null;
        }
        AdCodeMsgEntity adCodeMsgEntity = new AdCodeMsgEntity();
        adCodeMsgEntity.setAdCode(fenceAreaCommand.getAdCode());
        adCodeMsgEntity.setProvince(fenceAreaCommand.getProvince());
        adCodeMsgEntity.setCity(fenceAreaCommand.getCity());
        adCodeMsgEntity.setArea(fenceAreaCommand.getArea());
        adCodeMsgEntity.setLevel(fenceAreaCommand.getLevel());
        return adCodeMsgEntity;
    }
}
