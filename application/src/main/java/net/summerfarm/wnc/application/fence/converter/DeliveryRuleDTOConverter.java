package net.summerfarm.wnc.application.fence.converter;

import net.summerfarm.wnc.api.fence.dto.StopDeliveryDTO;
import net.summerfarm.wnc.application.fence.dto.DeliveryRuleDTO;
import net.summerfarm.wnc.domain.deliveryRule.entity.ContactDeliveryRuleEntity;
import net.summerfarm.wnc.domain.fence.entity.DeliveryFenceEntity;
import net.summerfarm.wnc.domain.fence.entity.FenceDeliveryEntity;
import net.summerfarm.wnc.domain.fence.entity.StopDeliveryEntity;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/11/14 16:23<br/>
 *
 * <AUTHOR> />
 */
public class DeliveryRuleDTOConverter {

    public static DeliveryRuleDTO fenceDelivery2DTO(FenceDeliveryEntity entity){
        if(entity == null){
            return null;
        }
        DeliveryRuleDTO dto = new DeliveryRuleDTO();

        dto.setFrequentMethod(entity.getFrequentMethod());
        dto.setWeekDeliveryFrequent(entity.getDeliveryFrequent());
        dto.setDeliveryFrequentInterval(entity.getDeliveryFrequentInterval());
        dto.setBeginCalculateDate(entity.getBeginCalculateDate());

        return dto;
    }

    public static DeliveryRuleDTO contactDelivery2DTO(ContactDeliveryRuleEntity entity){
        if(entity == null){
            return null;
        }
        DeliveryRuleDTO dto = new DeliveryRuleDTO();

        dto.setFrequentMethod(entity.getFrequentMethod());
        dto.setWeekDeliveryFrequent(entity.getWeekDeliveryFrequent());
        dto.setDeliveryFrequentInterval(entity.getDeliveryFrequentInterval());
        dto.setBeginCalculateDate(entity.getBeginCalculateDate());

        return dto;
    }

    public static DeliveryRuleDTO deliveryFence2DTO(DeliveryFenceEntity entity){
        if(entity == null){
            return null;
        }
        FenceDeliveryEntity fenceDeliveryEntity = entity.getFenceDeliveryEntity();
        DeliveryRuleDTO dto = fenceDelivery2DTO(fenceDeliveryEntity);
        if(dto != null){
            List<StopDeliveryEntity> stopDeliveryEntitys = entity.getStopDeliveryEntitys();
            if(!CollectionUtils.isEmpty(stopDeliveryEntitys)){
                dto.setStopDeliveryDTOList(stopDeliveryEntitys.stream().map(StopDeliveryConverter::entity2DTO).collect(Collectors.toList()));
            }
        }

        return dto;
    }
}
