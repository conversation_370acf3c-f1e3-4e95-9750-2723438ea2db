package net.summerfarm.wnc.application.fence.converter;

import net.summerfarm.wnc.api.fence.dto.StopDeliveryDTO;
import net.summerfarm.wnc.domain.fence.entity.StopDeliveryEntity;

/**
 * Description: 停配转化类
 * date: 2023/10/8 15:25<br/>
 *
 * <AUTHOR> />
 */
public class StopDeliveryConverter {

    public static StopDeliveryDTO entity2DTO(StopDeliveryEntity entity){
        if(entity == null){
            return null;
        }

        StopDeliveryDTO dto = new StopDeliveryDTO();

        dto.setId(entity.getId());
        dto.setCreateTime(entity.getCreateTime());
        dto.setUpdateTime(entity.getUpdateTime());
        dto.setStoreNo(entity.getStoreNo());
        dto.setShutdownStartTime(entity.getShutdownStartTime());
        dto.setShutdownEndTime(entity.getShutdownEndTime());
        dto.setDeleteFlag(entity.getDeleteFlag());

        return dto;
    }

    public static StopDeliveryEntity dto2Entity(StopDeliveryDTO dto){
        if(dto == null){
            return null;
        }

        StopDeliveryEntity entity = new StopDeliveryEntity();

        entity.setId(dto.getId());
        entity.setCreateTime(dto.getCreateTime());
        entity.setUpdateTime(dto.getUpdateTime());
        entity.setStoreNo(dto.getStoreNo());
        entity.setShutdownStartTime(dto.getShutdownStartTime());
        entity.setShutdownEndTime(dto.getShutdownEndTime());
        entity.setDeleteFlag(dto.getDeleteFlag());

        return entity;
    }
}
