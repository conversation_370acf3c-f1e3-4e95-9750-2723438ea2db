package net.summerfarm.wnc.application.tool;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.tool.dto.FenceRelateVO;
import net.summerfarm.wnc.api.tool.dto.StoreAddressVO;
import net.summerfarm.wnc.api.tool.service.ToolService;
import net.summerfarm.wnc.application.tool.converter.StoreAddressConverter;
import net.summerfarm.wnc.common.constants.AppConsts;
import net.summerfarm.wnc.common.query.fence.FenceQuery;
import net.summerfarm.wnc.common.query.fence.SkuAddressQuery;
import net.summerfarm.wnc.common.query.fence.StoreQuery;
import net.summerfarm.wnc.common.query.warehouse.SkuWarehouseMappingQuery;
import net.summerfarm.wnc.domain.fence.DeliveryFenceRepository;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.summerfarm.wnc.domain.warehouse.WarehouseInventoryMappingRepository;
import net.summerfarm.wnc.domain.warehouse.WarehouseLogisticsCenterRepository;
import net.summerfarm.wnc.domain.warehouse.WarehouseStorageCenterRepository;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseInventoryMappingEntity;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseLogisticsCenterEntity;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseStorageEntity;
import net.summerfarm.wnc.facade.gc.GcQueryFacade;
import net.summerfarm.wnc.facade.gc.dto.ProductSkuDTO;
import net.summerfarm.wnc.facade.mall.MallQueryFacade;
import net.summerfarm.wnc.facade.mall.dto.AreaDTO;
import net.summerfarm.wnc.facade.mall.input.AreaInput;
import net.summerfarm.wnc.facade.userCenter.UserCenterQueryFacade;
import net.summerfarm.wnc.facade.userCenter.dto.AddressDTO;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description:工具箱接口实现类
 * date: 2023/12/8 15:26
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ToolServiceImpl implements ToolService {

    private final DeliveryFenceRepository deliveryFenceRepository;
    private final WarehouseLogisticsCenterRepository warehouseLogisticsCenterRepository;
    private final WarehouseStorageCenterRepository warehouseStorageCenterRepository;
    private final WarehouseInventoryMappingRepository warehouseInventoryMappingRepository;
    private final UserCenterQueryFacade userCenterQueryFacade;
    private final MallQueryFacade mallQueryFacade;

    @Override
    public List<StoreAddressVO> queryAddressList(StoreQuery storeQuery) {
        if (storeQuery.getMerchantId() == null && StrUtil.isBlank(storeQuery.getPhone())){
            throw new BizException("门店ID或手机号至少传一项");
        }
        List<AddressDTO> addressDTOList = userCenterQueryFacade.queryAddressByQuery(storeQuery);
        return addressDTOList.stream().map(StoreAddressConverter::dto2vo).collect(Collectors.toList());
    }

    @Override
    public FenceRelateVO queryFenceRelateInfo(SkuAddressQuery skuAddressQuery) {
        //前端组件进入页面会调用一次特殊处理
        if (Objects.equals(AppConsts.Frontend.UNDEFINED, skuAddressQuery.getCity())){
            return null;
        }
        FenceEntity fenceEntity = deliveryFenceRepository.queryOneFenceByProvinceCityArea(FenceQuery.builder()
                .city(skuAddressQuery.getCity())
                .area(skuAddressQuery.getArea()).build());
        if (fenceEntity == null){
            throw new BizException("无法匹配到围栏，请检查录入信息");
        }
        FenceRelateVO fenceRelateVO = new FenceRelateVO();
        fenceRelateVO.setFenceName(fenceEntity.getFenceName());
        WarehouseLogisticsCenterEntity warehouseLogisticsCenterEntity = warehouseLogisticsCenterRepository.queryByUk(fenceEntity.getStoreNo());
        if (warehouseLogisticsCenterEntity == null){
            throw new BizException("无效城配仓");
        }
        fenceRelateVO.setStoreName(warehouseLogisticsCenterEntity.getStoreName());
        if (StrUtil.isNotBlank(skuAddressQuery.getSku())){
            List<WarehouseInventoryMappingEntity> warehouseInventoryMappingEntities = warehouseInventoryMappingRepository.queryMappingByUkList(Collections.singletonList(SkuWarehouseMappingQuery.builder()
                    .sku(skuAddressQuery.getSku())
                    .storeNo(warehouseLogisticsCenterEntity.getStoreNo())
                    .build()));
            if (CollectionUtils.isEmpty(warehouseInventoryMappingEntities)){
                fenceRelateVO.setWarehouseName("无法匹配到库存仓");
            }else {
                Integer warehouseNo = warehouseInventoryMappingEntities.get(0).getWarehouseNo();
                WarehouseStorageEntity warehouseStorageEntity = warehouseStorageCenterRepository.queryByWarehouseNo(warehouseNo);
                if (warehouseStorageEntity == null){
                    throw new BizException("无效库存仓");
                }
                fenceRelateVO.setWarehouseName(warehouseStorageEntity.getWarehouseName());
            }
        }
        List<AreaDTO> areaDTOList = mallQueryFacade.queryAreas(AreaInput.builder().areaNos(Lists.newArrayList(fenceEntity.getAreaNo())).build());
        if (CollectionUtils.isEmpty(areaDTOList)){
            throw new BizException("无效运营服务区域");
        }
        AreaDTO areaDTO = areaDTOList.get(0);
        fenceRelateVO.setAreaName(areaDTO.getAreaName());
        fenceRelateVO.setLargeAreaName(areaDTO.getLargeAreaName());
        return fenceRelateVO;
    }
}
