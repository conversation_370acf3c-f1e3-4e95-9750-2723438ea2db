package net.summerfarm.wnc.application.inbound.controller.config.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2024-09-12 14:00:16
 * @version 1.0
 *
 */
@Data
public class WncTenantStoreSkuBlackConfigVO implements Serializable{
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime updateTime;

	/**
	 * 租户ID
	 */
	private Long tenantId;

	/**
	 * 城配仓编号
	 */
	private Integer storeNo;

	/**
	 * sku
	 */
	private String sku;



}