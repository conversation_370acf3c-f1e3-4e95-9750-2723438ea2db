package net.summerfarm.wnc.application.service.config;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Iterators;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.wnc.application.service.config.dto.FullCategoryWarehouseSkuWhiteConfigExcelDataDTO;
import net.summerfarm.wnc.common.config.WncConfig;
import net.summerfarm.wnc.common.enums.SkuSubTypEnum;
import net.summerfarm.wnc.common.enums.WncDownloadCenterTypeEnum;
import net.summerfarm.wnc.domain.config.entity.WncFullCategoryWarehouseSkuWhiteConfigEntity;
import net.summerfarm.wnc.domain.config.param.command.WncFullCategoryWarehouseSkuWhiteConfigCommandParam;
import net.summerfarm.wnc.domain.config.param.query.WncFullCategoryWarehouseSkuWhiteConfigQueryParam;
import net.summerfarm.wnc.domain.config.repository.WncFullCategoryWarehouseSkuWhiteConfigCommandRepository;
import net.summerfarm.wnc.domain.config.repository.WncFullCategoryWarehouseSkuWhiteConfigQueryRepository;
import net.summerfarm.wnc.domain.warehouse.WarehouseStorageCenterRepository;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseStorageEntity;
import net.summerfarm.wnc.facade.gc.GcQueryFacade;
import net.summerfarm.wnc.facade.gc.dto.ProductSkuDetailDTO;
import net.xianmu.download.support.dto.DownloadCenterDataMsg;
import net.xianmu.download.support.handler.DownloadCenterImportDefaultHandler;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 三方仓全品类T+2配送白名单的Excel导入处理类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class FullCategoryWarehouseSkuWhiteConfigImportHandler extends DownloadCenterImportDefaultHandler<FullCategoryWarehouseSkuWhiteConfigExcelDataDTO> {

    @Autowired
    private GcQueryFacade gcQueryFacade;
    @Autowired
    private WarehouseStorageCenterRepository warehouseStorageCenterRepository;
    @Autowired
    private WncConfig wncConfig;
    @Autowired
    private WncFullCategoryWarehouseSkuWhiteConfigQueryRepository wncFullCategoryWarehouseSkuWhiteConfigQueryRepository;
    @Autowired
    private WncFullCategoryWarehouseSkuWhiteConfigCommandRepository wncFullCategoryWarehouseSkuWhiteConfigCommandRepository;

    @Override
    public DownloadCenterEnum.RequestSource getSource() {
        // download_center_type_config表配置的source
        return DownloadCenterEnum.RequestSource.XIANMU;
    }

    @Override
    public Integer getBizType() {
        // download_center_type_config表配置的type
        return WncDownloadCenterTypeEnum.FULL_CATEGORY_WAREHOUSE_SKU_WHITE_CONFIG.getType();
    }

    @Override
    protected void dealExcelData(List<FullCategoryWarehouseSkuWhiteConfigExcelDataDTO> list, DownloadCenterDataMsg downloadCenterDataMsg) {
        if (CollectionUtils.isEmpty(list)) {
            log.info("导入的三方仓全品类T+2配送白名单为空，直接返回");
            return;
        }
        String operator = (downloadCenterDataMsg.getAuthUser() != null) ? downloadCenterDataMsg.getAuthUser().getUsername() : null;
        log.info("开始处理三方仓全品类T+2配送白名单的导入，size：{}，operator：{}，fileOssUrl：{}", list.size(), operator, downloadCenterDataMsg.getFileOssUrl());
        // 基础信息校验
        checkBasicInfo(list);
        List<FullCategoryWarehouseSkuWhiteConfigExcelDataDTO> checkBasicSuccessList = list.stream().filter(x -> StringUtils.isEmpty(x.getErrorMsg())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(checkBasicSuccessList)) {
            log.info("基础信息校验成功的三方仓全品类T+2配送白名单为空");
            return;
        }
        // 校验导入的白名单是否已存在
        for (List<FullCategoryWarehouseSkuWhiteConfigExcelDataDTO> subExcelDataDTOList : Lists.partition(checkBasicSuccessList, 100)) {
            checkExist(subExcelDataDTOList);
        }
        List<FullCategoryWarehouseSkuWhiteConfigExcelDataDTO> checkSuccessList = list.stream().filter(x -> StringUtils.isEmpty(x.getErrorMsg())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(checkSuccessList)) {
            log.info("校验成功的三方仓全品类T+2配送白名单为空");
            return;
        }
        // 根据库存仓+sku去重
        Map<String, FullCategoryWarehouseSkuWhiteConfigExcelDataDTO> excelDataDTOMap = checkSuccessList.stream().collect(
                Collectors.toMap(x -> x.getWarehouseNo() + "_" + x.getSku().trim(), Function.identity(), (v1, v2) -> v1));
        // 批量插入三方仓全品类T+2配送白名单
        Iterators.partition(excelDataDTOMap.values().iterator(), 100).forEachRemaining(this::batchInsertWhiteConfig);
    }

    private void checkBasicInfo(List<FullCategoryWarehouseSkuWhiteConfigExcelDataDTO> excelDataDTOList) {
        // 查询仓库信息
        List<String> warehouseNameList = excelDataDTOList.stream().map(FullCategoryWarehouseSkuWhiteConfigExcelDataDTO::getWarehouseName)
                .filter(StringUtils::isNotBlank).map(String::trim).distinct().collect(Collectors.toList());
        List<WarehouseStorageEntity> warehouseStorageEntities = warehouseStorageCenterRepository.queryListByWarehouseNames(warehouseNameList);
        Map<String, WarehouseStorageEntity> warehouseMap = warehouseStorageEntities.stream().filter(x -> StringUtils.isNotBlank(x.getWarehouseName()))
                .collect(Collectors.toMap(WarehouseStorageEntity::getWarehouseName, Function.identity(), (v1, v2) -> v1));
        // 查询鲜沐自营仓配置
        List<Integer> selfWarehouseNos = wncConfig.querySelfWarehouseNos();
        // 查询sku信息
        List<String> skuList = excelDataDTOList.stream().map(FullCategoryWarehouseSkuWhiteConfigExcelDataDTO::getSku)
                .filter(StringUtils::isNotBlank).map(String::trim).distinct().collect(Collectors.toList());
        Map<String, ProductSkuDetailDTO> skuDetailMap = gcQueryFacade.querySkuListIsUseInfoMap(skuList);

        for (FullCategoryWarehouseSkuWhiteConfigExcelDataDTO excelDataDTO : excelDataDTOList) {
            if (StringUtils.isBlank(excelDataDTO.getWarehouseName())) {
                excelDataDTO.appendErrorMsg("仓库名称为空或者不存在");
            }
            if (StringUtils.isBlank(excelDataDTO.getSku())) {
                excelDataDTO.appendErrorMsg("sku为空或者不存在");
            }
            if (StringUtils.isNotEmpty(excelDataDTO.getErrorMsg())) {
                continue;
            }
            WarehouseStorageEntity warehouseStorageEntity = warehouseMap.get(excelDataDTO.getWarehouseName().trim());
            if (warehouseStorageEntity == null) {
                excelDataDTO.appendErrorMsg("根据仓库名称找不到对应的库存仓");
                continue;
            }
            if (selfWarehouseNos.contains(warehouseStorageEntity.getWarehouseNo())) {
                excelDataDTO.appendErrorMsg("该仓库非三方仓仓库");
                continue;
            }
            // 保存仓库号
            excelDataDTO.setWarehouseNo(warehouseStorageEntity.getWarehouseNo());
            ProductSkuDetailDTO skuDetail = skuDetailMap.get(excelDataDTO.getSku().trim());
            if (skuDetail == null) {
                excelDataDTO.appendErrorMsg("找不到该sku的信息");
                continue;
            }
            if (!SkuSubTypEnum.SELF_SALE_NO_WAREHOUSE.getCode().equals(skuDetail.getSubAgentType())) {
                excelDataDTO.appendErrorMsg("该sku非代销不入仓品");
            }
        }
    }

    private void checkExist(List<FullCategoryWarehouseSkuWhiteConfigExcelDataDTO> excelDataDTOList) {
        List<String> skus = excelDataDTOList.stream().map(x -> x.getSku().trim()).distinct().collect(Collectors.toList());
        List<Integer> warehouseNos = excelDataDTOList.stream().map(FullCategoryWarehouseSkuWhiteConfigExcelDataDTO::getWarehouseNo).distinct().collect(Collectors.toList());
        List<WncFullCategoryWarehouseSkuWhiteConfigEntity> whiteConfigList = wncFullCategoryWarehouseSkuWhiteConfigQueryRepository.selectByCondition(
                WncFullCategoryWarehouseSkuWhiteConfigQueryParam.builder()
                        .warehouseNos(warehouseNos)
                        .skus(skus)
                        .build());
        List<String> warehouseAndSkuList = whiteConfigList.stream().map(x -> x.getWarehouseNo() + "_" + x.getSku()).distinct().collect(Collectors.toList());
        for (FullCategoryWarehouseSkuWhiteConfigExcelDataDTO excelDataDTO : excelDataDTOList) {
            if (warehouseAndSkuList.contains(excelDataDTO.getWarehouseNo() + "_" + excelDataDTO.getSku().trim())) {
                excelDataDTO.appendErrorMsg("已存在该库存仓该sku的白名单配置");
            }
        }
    }

    private void batchInsertWhiteConfig(List<FullCategoryWarehouseSkuWhiteConfigExcelDataDTO> subExcelDataDTOList) {
        try {
            List<WncFullCategoryWarehouseSkuWhiteConfigCommandParam> paramList = subExcelDataDTOList.stream().map(x -> {
                WncFullCategoryWarehouseSkuWhiteConfigCommandParam param = new WncFullCategoryWarehouseSkuWhiteConfigCommandParam();
                param.setWarehouseNo(x.getWarehouseNo());
                param.setSku(x.getSku().trim());
                return param;
            }).collect(Collectors.toList());
            wncFullCategoryWarehouseSkuWhiteConfigCommandRepository.batchInsert(paramList);
        } catch (Exception ex) {
            log.error("批量插入三方仓全品类T+2配送白名单失败，subExcelDataDTOList:{}", JSON.toJSONString(subExcelDataDTOList), ex);
            subExcelDataDTOList.forEach(x -> x.appendErrorMsg("保存失败请重试"));
        }
    }

}
