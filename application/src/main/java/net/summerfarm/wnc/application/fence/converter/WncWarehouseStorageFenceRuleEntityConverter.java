package net.summerfarm.wnc.application.fence.converter;

import net.summerfarm.wnc.api.fence.dto.ConflictWarehouseJsonDTO;
import net.summerfarm.wnc.api.fence.dto.WncWarehouseStorageFenceRuleDTO;
import net.summerfarm.wnc.api.fence.input.ConflictWarehouseJsonUpdateCommand;
import net.summerfarm.wnc.domain.warehouse.entity.ConflictWarehouseJsonEntity;
import net.summerfarm.wnc.domain.warehouse.entity.WncWarehouseStorageFenceRuleEntity;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/4/15 14:01<br/>
 *
 * <AUTHOR> />
 */
public class WncWarehouseStorageFenceRuleEntityConverter {

    public static WncWarehouseStorageFenceRuleDTO entity2DTO(WncWarehouseStorageFenceRuleEntity wncWarehouseStorageFenceRuleEntity){
        if(wncWarehouseStorageFenceRuleEntity == null){
            return null;
        }
        WncWarehouseStorageFenceRuleDTO wncWarehouseStorageFenceRuleDTO = new WncWarehouseStorageFenceRuleDTO();
        wncWarehouseStorageFenceRuleDTO.setId(wncWarehouseStorageFenceRuleEntity.getId());
        wncWarehouseStorageFenceRuleDTO.setCreateTime(wncWarehouseStorageFenceRuleEntity.getCreateTime());
        wncWarehouseStorageFenceRuleDTO.setUpdateTime(wncWarehouseStorageFenceRuleEntity.getUpdateTime());
        wncWarehouseStorageFenceRuleDTO.setProvince(wncWarehouseStorageFenceRuleEntity.getProvince());
        wncWarehouseStorageFenceRuleDTO.setCity(wncWarehouseStorageFenceRuleEntity.getCity());
        wncWarehouseStorageFenceRuleDTO.setArea(wncWarehouseStorageFenceRuleEntity.getArea());
        wncWarehouseStorageFenceRuleDTO.setDeliveryRule(wncWarehouseStorageFenceRuleEntity.getDeliveryRule());
        if(!CollectionUtils.isEmpty(wncWarehouseStorageFenceRuleEntity.getConflictWarehouseJsonEntityList())){
            List<ConflictWarehouseJsonDTO> conflictWarehouseJsonDTOS = wncWarehouseStorageFenceRuleEntity.getConflictWarehouseJsonEntityList().stream()
                    .map(WncWarehouseStorageFenceRuleEntityConverter::conflictEntity2DTO)
                    .collect(Collectors.toList());
            wncWarehouseStorageFenceRuleDTO.setConflictWarehouseJsonDTOList(conflictWarehouseJsonDTOS);
        }
        wncWarehouseStorageFenceRuleDTO.setWarehouseNameList(wncWarehouseStorageFenceRuleEntity.getWarehouseNameList());
        wncWarehouseStorageFenceRuleDTO.setLastOperatorName(wncWarehouseStorageFenceRuleEntity.getLastOperatorName());
        wncWarehouseStorageFenceRuleDTO.setTenantId(wncWarehouseStorageFenceRuleEntity.getTenantId());
        return wncWarehouseStorageFenceRuleDTO;
    }

    public static ConflictWarehouseJsonDTO conflictEntity2DTO(ConflictWarehouseJsonEntity conflictWarehouseJsonEntity){
        if(conflictWarehouseJsonEntity == null){
            return null;
        }
        ConflictWarehouseJsonDTO conflictWarehouseJsonDTO = new ConflictWarehouseJsonDTO();
        conflictWarehouseJsonDTO.setWarehouseNo(conflictWarehouseJsonEntity.getWarehouseNo());
        conflictWarehouseJsonDTO.setWarehouseName(conflictWarehouseJsonEntity.getWarehouseName());
        conflictWarehouseJsonDTO.setTenantId(conflictWarehouseJsonEntity.getTenentId());
        return conflictWarehouseJsonDTO;
    }

    public static ConflictWarehouseJsonEntity command2Entity(ConflictWarehouseJsonUpdateCommand conflictWarehouseJsonUpdateCommand){
        if(conflictWarehouseJsonUpdateCommand == null){
            return null;
        }
        ConflictWarehouseJsonEntity conflictWarehouseJsonEntity = new ConflictWarehouseJsonEntity();
        conflictWarehouseJsonEntity.setWarehouseNo(conflictWarehouseJsonUpdateCommand.getWarehouseNo());
        conflictWarehouseJsonEntity.setWarehouseName(conflictWarehouseJsonUpdateCommand.getWarehouseName());
        return conflictWarehouseJsonEntity;
    }
}
