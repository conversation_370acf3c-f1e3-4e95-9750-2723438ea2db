package net.summerfarm.wnc.application.path.converter;

import net.summerfarm.wnc.api.path.dto.PathDTO;
import net.summerfarm.wnc.api.path.dto.SkuPathMappingDTO;
import net.summerfarm.wnc.common.enums.PathConfigEnums;
import net.summerfarm.wnc.domain.path.entity.PathConfigEntity;
import net.summerfarm.wnc.domain.path.entity.SkuPathMappingEntity;
import org.springframework.util.CollectionUtils;

import java.util.stream.Collectors;

/**
 * Description: 路线转换类<br/>
 * date: 2023/11/29 13:54<br/>
 *
 * <AUTHOR> />
 */
public class PathDTOConverter {

    public static PathDTO entity2DTO(PathConfigEntity entity){
        if(entity == null){
            return null;
        }
        PathDTO dto = new PathDTO();

        dto.setId(entity.getId());
        dto.setCreateTime(entity.getCreateTime());
        dto.setUpdateTime(entity.getUpdateTime());
        dto.setBeginOutNo(entity.getBeginOutNo());
        dto.setEndOutNo(entity.getEndOutNo());
        dto.setBusinseeType(entity.getBusinseeType() != null ? entity.getBusinseeType().getValue() : null);
        dto.setFrequentMethod(entity.getFrequentMethod() != null ? entity.getFrequentMethod().getValue() : null);
        dto.setFrequent(entity.getFrequent());
        dto.setTravelDuratio(entity.getTravelDuratio());
        dto.setLastTime(entity.getLastTime());
        dto.setCreator(entity.getCreator());
        dto.setUpdater(entity.getUpdater());
        if(!CollectionUtils.isEmpty(entity.getSkuPathMappingEntities())){
            dto.setSkuPathMappingDTOList(entity.getSkuPathMappingEntities().stream().map(PathDTOConverter::skuPathMappingEntity2DTO).collect(Collectors.toList()));
        }

        return dto;
    }

    public static PathConfigEntity dto2Entity(PathDTO dto){
        if(dto == null){
            return null;
        }
        PathConfigEntity entity = new PathConfigEntity();

        entity.setId(dto.getId());
        entity.setCreateTime(dto.getCreateTime());
        entity.setUpdateTime(dto.getUpdateTime());
        entity.setBeginOutNo(dto.getBeginOutNo());
        entity.setEndOutNo(dto.getEndOutNo());
        entity.setBusinseeType(PathConfigEnums.BusinseeType.getBusinseeTypeByValue(dto.getBusinseeType()));
        entity.setFrequentMethod(PathConfigEnums.FrequentMethod.getFrequentMethodByValue(dto.getFrequentMethod()));
        entity.setFrequent(dto.getFrequent());
        entity.setTravelDuratio(dto.getTravelDuratio());
        entity.setLastTime(dto.getLastTime());
        entity.setCreator(dto.getCreator());
        entity.setUpdater(dto.getUpdater());

        return entity;
    }

    public static SkuPathMappingDTO skuPathMappingEntity2DTO(SkuPathMappingEntity entity){
        if(entity == null){
            return null;
        }
        SkuPathMappingDTO dto = new SkuPathMappingDTO();

        dto.setId(entity.getId());
        dto.setCreateTime(entity.getCreateTime());
        dto.setUpdateTime(entity.getUpdateTime());
        dto.setSku(entity.getSku());
        dto.setWarehouseNo(entity.getWarehouseNo());
        dto.setPathId(entity.getPathId());
        dto.setUpdater(entity.getUpdater());
        dto.setCreator(entity.getCreator());

        return dto;
    }

    public static SkuPathMappingEntity dto2SkuPathMappingEntity(SkuPathMappingDTO dto){
        if(dto == null){
            return null;
        }
        SkuPathMappingEntity entity = new SkuPathMappingEntity();

        entity.setId(dto.getId());
        entity.setCreateTime(dto.getCreateTime());
        entity.setUpdateTime(dto.getUpdateTime());
        entity.setSku(dto.getSku());
        entity.setWarehouseNo(dto.getWarehouseNo());
        entity.setPathId(dto.getPathId());
        entity.setUpdater(dto.getUpdater());
        entity.setCreator(dto.getCreator());

        return entity;
    }
}
