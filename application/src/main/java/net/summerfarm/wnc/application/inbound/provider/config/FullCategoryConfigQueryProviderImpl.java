package net.summerfarm.wnc.application.inbound.provider.config;

import net.summerfarm.wnc.client.provider.config.FullCategoryConfigQueryProvider;
import net.summerfarm.wnc.client.req.config.FullCategoryWarehouseSkuWhiteConfigQueryReq;
import net.summerfarm.wnc.client.req.config.WarehouseAndSku;
import net.summerfarm.wnc.client.resp.config.FullCategoryWarehouseSkuWhiteConfigResultResp;
import net.summerfarm.wnc.domain.config.repository.WncFullCategoryWarehouseSkuWhiteConfigQueryRepository;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 全品类配置查询服务
 *
 * <AUTHOR>
 */
@DubboService
public class FullCategoryConfigQueryProviderImpl implements FullCategoryConfigQueryProvider {

    @Autowired
    private WncFullCategoryWarehouseSkuWhiteConfigQueryRepository wncFullCategoryWarehouseSkuWhiteConfigQueryRepository;

    @Override
    public DubboResponse<FullCategoryWarehouseSkuWhiteConfigResultResp> queryFullCategoryWarehouseSkuWhiteConfig(@Valid FullCategoryWarehouseSkuWhiteConfigQueryReq req) {
        List<Integer> warehouseNos = req.getWarehouseAndSkuList().stream().map(WarehouseAndSku::getWarehouseNo).distinct().collect(Collectors.toList());
        List<String> skus = req.getWarehouseAndSkuList().stream().map(WarehouseAndSku::getSku).distinct().collect(Collectors.toList());
        Map<String, List<Integer>> whiteConfigMap = wncFullCategoryWarehouseSkuWhiteConfigQueryRepository.selectWhiteConfigMapBySkusWarehouseNos(skus, warehouseNos);
        if (MapUtils.isEmpty(whiteConfigMap)) {
            return DubboResponse.getOK(new FullCategoryWarehouseSkuWhiteConfigResultResp());
        }

        FullCategoryWarehouseSkuWhiteConfigResultResp resp = new FullCategoryWarehouseSkuWhiteConfigResultResp();
        List<WarehouseAndSku> whiteConfigList = req.getWarehouseAndSkuList().stream().filter(
                x -> CollectionUtils.isNotEmpty(whiteConfigMap.get(x.getSku())) && whiteConfigMap.get(x.getSku()).contains(x.getWarehouseNo())
        ).collect(Collectors.toList());
        resp.setWhiteConfigList(whiteConfigList);
        return DubboResponse.getOK(resp);
    }

}
