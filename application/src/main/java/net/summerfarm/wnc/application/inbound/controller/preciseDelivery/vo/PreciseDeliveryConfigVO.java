package net.summerfarm.wnc.application.inbound.controller.preciseDelivery.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Description:精准送配置视图对象
 * date: 2024/1/18 18:07
 *
 * <AUTHOR>
 */
@Data
public class PreciseDeliveryConfigVO implements Serializable {

    private static final long serialVersionUID = -6157147983996201571L;

    /**
     * 配置ID
     */
    private Long id;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 精准送配置区域集合
     */
    private List<PreciseDeliveryConfigAreaVO> areaList;

    /**
     * 精准送配置时效集合
     */
    private List<PreciseDeliveryConfigTimeVO> timeList;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
