package net.summerfarm.wnc.application.inbound.controller.warehouseMapping.vo;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2025-06-11 15:38:55
 * @version 1.0
 *
 */
@Data
public class WncWarehouseThirdMappingVO implements Serializable{
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 仓库编号
	 */
	private Integer warehouseNo;

	/**
	 * 三方来源名称
	 */
	private String thirdSourceName;

	/**
	 * 三方仓库编号
	 */
	private String thirdWarehouseNo;

	/**
	 * 租户id
	 */
	private Long tenantId;

	/**
	 * 开放平台appkey
	 */
	private String openPlatformAppKey;



}