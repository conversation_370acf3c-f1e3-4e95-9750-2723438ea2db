package net.summerfarm.wnc.application.inbound.controller.warehouseMapping.assembler;


import net.summerfarm.wnc.application.inbound.controller.warehouseMapping.vo.WncWarehouseThirdMappingVO;
import net.summerfarm.wnc.domain.warehouseMapping.entity.WncWarehouseThirdMappingEntity;
import net.summerfarm.wnc.application.inbound.controller.warehouseMapping.input.command.WncWarehouseThirdMappingCommandInput;
import net.summerfarm.wnc.application.inbound.controller.warehouseMapping.input.query.WncWarehouseThirdMappingQueryInput;
import net.summerfarm.wnc.domain.warehouseMapping.param.query.WncWarehouseThirdMappingQueryParam;
import net.summerfarm.wnc.domain.warehouseMapping.param.command.WncWarehouseThirdMappingCommandParam;

import java.util.ArrayList;
import java.util.List;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025-06-11 15:38:55
 * @version 1.0
 *
 */
public class WncWarehouseThirdMappingAssembler {

    private WncWarehouseThirdMappingAssembler() {
        // 无需实现
    }


// ------------------------------- request ----------------------------
    public static WncWarehouseThirdMappingQueryParam toWncWarehouseThirdMappingQueryParam(WncWarehouseThirdMappingQueryInput wncWarehouseThirdMappingQueryInput) {
        if (wncWarehouseThirdMappingQueryInput == null) {
            return null;
        }
        WncWarehouseThirdMappingQueryParam wncWarehouseThirdMappingQueryParam = new WncWarehouseThirdMappingQueryParam();
        wncWarehouseThirdMappingQueryParam.setId(wncWarehouseThirdMappingQueryInput.getId());
        wncWarehouseThirdMappingQueryParam.setCreateTime(wncWarehouseThirdMappingQueryInput.getCreateTime());
        wncWarehouseThirdMappingQueryParam.setUpdateTime(wncWarehouseThirdMappingQueryInput.getUpdateTime());
        wncWarehouseThirdMappingQueryParam.setWarehouseNo(wncWarehouseThirdMappingQueryInput.getWarehouseNo());
        wncWarehouseThirdMappingQueryParam.setThirdSourceName(wncWarehouseThirdMappingQueryInput.getThirdSourceName());
        wncWarehouseThirdMappingQueryParam.setThirdWarehouseNo(wncWarehouseThirdMappingQueryInput.getThirdWarehouseNo());
        wncWarehouseThirdMappingQueryParam.setTenantId(wncWarehouseThirdMappingQueryInput.getTenantId());
        wncWarehouseThirdMappingQueryParam.setOpenPlatformAppKey(wncWarehouseThirdMappingQueryInput.getOpenPlatformAppKey());
        wncWarehouseThirdMappingQueryParam.setPageIndex(wncWarehouseThirdMappingQueryInput.getPageIndex());
        wncWarehouseThirdMappingQueryParam.setPageSize(wncWarehouseThirdMappingQueryInput.getPageSize());
        return wncWarehouseThirdMappingQueryParam;
    }





    public static WncWarehouseThirdMappingCommandParam buildCreateParam(WncWarehouseThirdMappingCommandInput wncWarehouseThirdMappingCommandInput) {
        if (wncWarehouseThirdMappingCommandInput == null) {
            return null;
        }
        WncWarehouseThirdMappingCommandParam wncWarehouseThirdMappingCommandParam = new WncWarehouseThirdMappingCommandParam();
        wncWarehouseThirdMappingCommandParam.setId(wncWarehouseThirdMappingCommandInput.getId());
        wncWarehouseThirdMappingCommandParam.setCreateTime(wncWarehouseThirdMappingCommandInput.getCreateTime());
        wncWarehouseThirdMappingCommandParam.setUpdateTime(wncWarehouseThirdMappingCommandInput.getUpdateTime());
        wncWarehouseThirdMappingCommandParam.setWarehouseNo(wncWarehouseThirdMappingCommandInput.getWarehouseNo());
        wncWarehouseThirdMappingCommandParam.setThirdSourceName(wncWarehouseThirdMappingCommandInput.getThirdSourceName());
        wncWarehouseThirdMappingCommandParam.setThirdWarehouseNo(wncWarehouseThirdMappingCommandInput.getThirdWarehouseNo());
        wncWarehouseThirdMappingCommandParam.setTenantId(wncWarehouseThirdMappingCommandInput.getTenantId());
        wncWarehouseThirdMappingCommandParam.setOpenPlatformAppKey(wncWarehouseThirdMappingCommandInput.getOpenPlatformAppKey());
        return wncWarehouseThirdMappingCommandParam;
    }


    public static WncWarehouseThirdMappingCommandParam buildUpdateParam(WncWarehouseThirdMappingCommandInput wncWarehouseThirdMappingCommandInput) {
        if (wncWarehouseThirdMappingCommandInput == null) {
            return null;
        }
        WncWarehouseThirdMappingCommandParam wncWarehouseThirdMappingCommandParam = new WncWarehouseThirdMappingCommandParam();
        wncWarehouseThirdMappingCommandParam.setId(wncWarehouseThirdMappingCommandInput.getId());
        wncWarehouseThirdMappingCommandParam.setCreateTime(wncWarehouseThirdMappingCommandInput.getCreateTime());
        wncWarehouseThirdMappingCommandParam.setUpdateTime(wncWarehouseThirdMappingCommandInput.getUpdateTime());
        wncWarehouseThirdMappingCommandParam.setWarehouseNo(wncWarehouseThirdMappingCommandInput.getWarehouseNo());
        wncWarehouseThirdMappingCommandParam.setThirdSourceName(wncWarehouseThirdMappingCommandInput.getThirdSourceName());
        wncWarehouseThirdMappingCommandParam.setThirdWarehouseNo(wncWarehouseThirdMappingCommandInput.getThirdWarehouseNo());
        wncWarehouseThirdMappingCommandParam.setTenantId(wncWarehouseThirdMappingCommandInput.getTenantId());
        wncWarehouseThirdMappingCommandParam.setOpenPlatformAppKey(wncWarehouseThirdMappingCommandInput.getOpenPlatformAppKey());
        return wncWarehouseThirdMappingCommandParam;
    }




// ------------------------------- response ----------------------------

    public static List<WncWarehouseThirdMappingVO> toWncWarehouseThirdMappingVOList(List<WncWarehouseThirdMappingEntity> wncWarehouseThirdMappingEntityList) {
        if (wncWarehouseThirdMappingEntityList == null) {
            return Collections.emptyList();
        }
        List<WncWarehouseThirdMappingVO> wncWarehouseThirdMappingVOList = new ArrayList<>();
        for (WncWarehouseThirdMappingEntity wncWarehouseThirdMappingEntity : wncWarehouseThirdMappingEntityList) {
            wncWarehouseThirdMappingVOList.add(toWncWarehouseThirdMappingVO(wncWarehouseThirdMappingEntity));
        }
        return wncWarehouseThirdMappingVOList;
}


   public static WncWarehouseThirdMappingVO toWncWarehouseThirdMappingVO(WncWarehouseThirdMappingEntity wncWarehouseThirdMappingEntity) {
       if (wncWarehouseThirdMappingEntity == null) {
            return null;
       }
       WncWarehouseThirdMappingVO wncWarehouseThirdMappingVO = new WncWarehouseThirdMappingVO();
       wncWarehouseThirdMappingVO.setId(wncWarehouseThirdMappingEntity.getId());
       wncWarehouseThirdMappingVO.setCreateTime(wncWarehouseThirdMappingEntity.getCreateTime());
       wncWarehouseThirdMappingVO.setUpdateTime(wncWarehouseThirdMappingEntity.getUpdateTime());
       wncWarehouseThirdMappingVO.setWarehouseNo(wncWarehouseThirdMappingEntity.getWarehouseNo());
       wncWarehouseThirdMappingVO.setThirdSourceName(wncWarehouseThirdMappingEntity.getThirdSourceName());
       wncWarehouseThirdMappingVO.setThirdWarehouseNo(wncWarehouseThirdMappingEntity.getThirdWarehouseNo());
       wncWarehouseThirdMappingVO.setTenantId(wncWarehouseThirdMappingEntity.getTenantId());
       wncWarehouseThirdMappingVO.setOpenPlatformAppKey(wncWarehouseThirdMappingEntity.getOpenPlatformAppKey());
       return wncWarehouseThirdMappingVO;
   }

}
