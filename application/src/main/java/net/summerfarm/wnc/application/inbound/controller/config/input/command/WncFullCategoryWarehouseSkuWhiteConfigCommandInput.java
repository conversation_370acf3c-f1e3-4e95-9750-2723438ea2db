package net.summerfarm.wnc.application.inbound.controller.config.input.command;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * <AUTHOR>
 * @date 2025-01-06 15:31:26
 * @version 1.0
 *
 */
@Data
public class WncFullCategoryWarehouseSkuWhiteConfigCommandInput implements Serializable{
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * 库存仓编号
	 */
	@NotNull(message = "仓库不能为空")
	private Integer warehouseNo;

	/**
	 * sku
	 */
	@NotBlank(message = "sku不能为空")
	private String sku;

}