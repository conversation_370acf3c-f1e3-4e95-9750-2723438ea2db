package net.summerfarm.wnc.application.config;

import lombok.RequiredArgsConstructor;
import net.summerfarm.wnc.api.config.dto.ContactConfigDTO;
import net.summerfarm.wnc.api.config.service.ContactConfigQueryService;
import net.summerfarm.wnc.application.config.converter.ContactConfigConverter;
import net.summerfarm.wnc.common.enums.ContactConfigEnums;
import net.summerfarm.wnc.common.query.config.ContactConfigQuery;
import net.summerfarm.wnc.domain.config.repository.ContactConfigQueryRepository;
import net.summerfarm.wnc.domain.config.entity.ContactConfigEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:联系人配置服务实现
 * date: 2023/9/22 17:22
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ContactConfigQueryServiceImpl implements ContactConfigQueryService {

    private final ContactConfigQueryRepository contactConfigQueryRepository;

    @Override
    public ContactConfigDTO queryDetail(ContactConfigQuery contactConfigQuery) {
        ContactConfigEnums.Source source = ContactConfigEnums.Source.getSourceByTenantId(contactConfigQuery.getTenantId());
        ContactConfigEntity contactConfigEntity = contactConfigQueryRepository.queryByUk(source, contactConfigQuery.getOuterContactId());
        return ContactConfigConverter.entity2Dto(contactConfigEntity);
    }

    @Override
    public List<ContactConfigDTO> queryConfigList(ContactConfigQuery contactConfigQuery) {
        //目前只有SAAS批量查询的RPC入口，这里校验一下outerContactIds参数 避免查询全表
        if (CollectionUtils.isEmpty(contactConfigQuery.getOuterContactIds())){
            return Collections.emptyList();
        }
        List<ContactConfigEntity> contactConfigEntities = contactConfigQueryRepository.queryList(contactConfigQuery);
        if (CollectionUtils.isEmpty(contactConfigEntities)){
            return Collections.emptyList();
        }

        return contactConfigEntities.stream().map(ContactConfigConverter::entity2Dto).collect(Collectors.toList());
    }
}
