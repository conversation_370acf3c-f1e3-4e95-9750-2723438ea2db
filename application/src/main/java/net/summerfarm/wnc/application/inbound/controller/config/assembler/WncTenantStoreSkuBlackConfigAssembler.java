package net.summerfarm.wnc.application.inbound.controller.config.assembler;


import net.summerfarm.wnc.application.inbound.controller.config.vo.WncTenantStoreSkuBlackConfigVO;
import net.summerfarm.wnc.domain.config.entity.TenantStoreSkuBlackConfigEntity;
import net.summerfarm.wnc.application.inbound.controller.config.input.command.WncTenantStoreSkuBlackConfigCommandInput;
import net.summerfarm.wnc.application.inbound.controller.config.input.query.WncTenantStoreSkuBlackConfigQueryInput;
import net.summerfarm.wnc.domain.config.param.query.WncTenantStoreSkuBlackConfigQueryParam;
import net.summerfarm.wnc.domain.config.param.command.WncTenantStoreSkuBlackConfigCommandParam;

import java.util.ArrayList;
import java.util.List;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-09-12 14:00:16
 * @version 1.0
 *
 */
public class WncTenantStoreSkuBlackConfigAssembler {

    private WncTenantStoreSkuBlackConfigAssembler() {
        // 无需实现
    }


// ------------------------------- request ----------------------------
    public static WncTenantStoreSkuBlackConfigQueryParam toWncTenantStoreSkuBlackConfigQueryParam(WncTenantStoreSkuBlackConfigQueryInput wncTenantStoreSkuBlackConfigQueryInput) {
        if (wncTenantStoreSkuBlackConfigQueryInput == null) {
            return null;
        }
        WncTenantStoreSkuBlackConfigQueryParam wncTenantStoreSkuBlackConfigQueryParam = new WncTenantStoreSkuBlackConfigQueryParam();
        wncTenantStoreSkuBlackConfigQueryParam.setId(wncTenantStoreSkuBlackConfigQueryInput.getId());
        wncTenantStoreSkuBlackConfigQueryParam.setCreateTime(wncTenantStoreSkuBlackConfigQueryInput.getCreateTime());
        wncTenantStoreSkuBlackConfigQueryParam.setUpdateTime(wncTenantStoreSkuBlackConfigQueryInput.getUpdateTime());
        wncTenantStoreSkuBlackConfigQueryParam.setTenantId(wncTenantStoreSkuBlackConfigQueryInput.getTenantId());
        wncTenantStoreSkuBlackConfigQueryParam.setStoreNo(wncTenantStoreSkuBlackConfigQueryInput.getStoreNo());
        wncTenantStoreSkuBlackConfigQueryParam.setSku(wncTenantStoreSkuBlackConfigQueryInput.getSku());
        wncTenantStoreSkuBlackConfigQueryParam.setPageIndex(wncTenantStoreSkuBlackConfigQueryInput.getPageIndex());
        wncTenantStoreSkuBlackConfigQueryParam.setPageSize(wncTenantStoreSkuBlackConfigQueryInput.getPageSize());
        return wncTenantStoreSkuBlackConfigQueryParam;
    }





    public static WncTenantStoreSkuBlackConfigCommandParam buildCreateParam(WncTenantStoreSkuBlackConfigCommandInput wncTenantStoreSkuBlackConfigCommandInput) {
        if (wncTenantStoreSkuBlackConfigCommandInput == null) {
            return null;
        }
        WncTenantStoreSkuBlackConfigCommandParam wncTenantStoreSkuBlackConfigCommandParam = new WncTenantStoreSkuBlackConfigCommandParam();
        wncTenantStoreSkuBlackConfigCommandParam.setId(wncTenantStoreSkuBlackConfigCommandInput.getId());
        wncTenantStoreSkuBlackConfigCommandParam.setTenantId(wncTenantStoreSkuBlackConfigCommandInput.getTenantId());
        wncTenantStoreSkuBlackConfigCommandParam.setStoreNo(wncTenantStoreSkuBlackConfigCommandInput.getStoreNo());
        wncTenantStoreSkuBlackConfigCommandParam.setSku(wncTenantStoreSkuBlackConfigCommandInput.getSku());
        return wncTenantStoreSkuBlackConfigCommandParam;
    }


    public static WncTenantStoreSkuBlackConfigCommandParam buildUpdateParam(WncTenantStoreSkuBlackConfigCommandInput wncTenantStoreSkuBlackConfigCommandInput) {
        if (wncTenantStoreSkuBlackConfigCommandInput == null) {
            return null;
        }
        WncTenantStoreSkuBlackConfigCommandParam wncTenantStoreSkuBlackConfigCommandParam = new WncTenantStoreSkuBlackConfigCommandParam();
        wncTenantStoreSkuBlackConfigCommandParam.setId(wncTenantStoreSkuBlackConfigCommandInput.getId());
        wncTenantStoreSkuBlackConfigCommandParam.setTenantId(wncTenantStoreSkuBlackConfigCommandInput.getTenantId());
        wncTenantStoreSkuBlackConfigCommandParam.setStoreNo(wncTenantStoreSkuBlackConfigCommandInput.getStoreNo());
        wncTenantStoreSkuBlackConfigCommandParam.setSku(wncTenantStoreSkuBlackConfigCommandInput.getSku());
        return wncTenantStoreSkuBlackConfigCommandParam;
    }




// ------------------------------- response ----------------------------

    public static List<WncTenantStoreSkuBlackConfigVO> toWncTenantStoreSkuBlackConfigVOList(List<TenantStoreSkuBlackConfigEntity> wncTenantStoreSkuBlackConfigEntityList) {
        if (wncTenantStoreSkuBlackConfigEntityList == null) {
            return Collections.emptyList();
        }
        List<WncTenantStoreSkuBlackConfigVO> wncTenantStoreSkuBlackConfigVOList = new ArrayList<>();
        for (TenantStoreSkuBlackConfigEntity wncTenantStoreSkuBlackConfigEntity : wncTenantStoreSkuBlackConfigEntityList) {
            wncTenantStoreSkuBlackConfigVOList.add(toWncTenantStoreSkuBlackConfigVO(wncTenantStoreSkuBlackConfigEntity));
        }
        return wncTenantStoreSkuBlackConfigVOList;
}


   public static WncTenantStoreSkuBlackConfigVO toWncTenantStoreSkuBlackConfigVO(TenantStoreSkuBlackConfigEntity wncTenantStoreSkuBlackConfigEntity) {
       if (wncTenantStoreSkuBlackConfigEntity == null) {
            return null;
       }
       WncTenantStoreSkuBlackConfigVO wncTenantStoreSkuBlackConfigVO = new WncTenantStoreSkuBlackConfigVO();
       wncTenantStoreSkuBlackConfigVO.setId(wncTenantStoreSkuBlackConfigEntity.getId());
       wncTenantStoreSkuBlackConfigVO.setCreateTime(wncTenantStoreSkuBlackConfigEntity.getCreateTime());
       wncTenantStoreSkuBlackConfigVO.setUpdateTime(wncTenantStoreSkuBlackConfigEntity.getUpdateTime());
       wncTenantStoreSkuBlackConfigVO.setTenantId(wncTenantStoreSkuBlackConfigEntity.getTenantId());
       wncTenantStoreSkuBlackConfigVO.setStoreNo(wncTenantStoreSkuBlackConfigEntity.getStoreNo());
       wncTenantStoreSkuBlackConfigVO.setSku(wncTenantStoreSkuBlackConfigEntity.getSku());
       return wncTenantStoreSkuBlackConfigVO;
   }

}
