package net.summerfarm.wnc.application.config.impl;


import net.summerfarm.wnc.application.config.WncFullCategoryWarehouseSkuWhiteConfigQueryService;
import net.summerfarm.wnc.domain.config.repository.WncFullCategoryWarehouseSkuWhiteConfigQueryRepository;
import net.summerfarm.wnc.domain.config.entity.WncFullCategoryWarehouseSkuWhiteConfigEntity;
import net.summerfarm.wnc.domain.config.param.query.WncFullCategoryWarehouseSkuWhiteConfigQueryParam;
import net.summerfarm.wnc.application.inbound.controller.config.input.query.WncFullCategoryWarehouseSkuWhiteConfigQueryInput;
import net.summerfarm.wnc.application.inbound.controller.config.assembler.WncFullCategoryWarehouseSkuWhiteConfigAssembler;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
*
* <AUTHOR>
* @date 2025-01-06 15:31:26
* @version 1.0
*
*/
@Service
public class WncFullCategoryWarehouseSkuWhiteConfigQueryServiceImpl implements WncFullCategoryWarehouseSkuWhiteConfigQueryService {

    @Autowired
    private WncFullCategoryWarehouseSkuWhiteConfigQueryRepository wncFullCategoryWarehouseSkuWhiteConfigQueryRepository;

    @Override
    public PageInfo<WncFullCategoryWarehouseSkuWhiteConfigEntity> getPage(WncFullCategoryWarehouseSkuWhiteConfigQueryInput input) {
        WncFullCategoryWarehouseSkuWhiteConfigQueryParam queryParam = WncFullCategoryWarehouseSkuWhiteConfigAssembler.toWncFullCategoryWarehouseSkuWhiteConfigQueryParam(input);
        return wncFullCategoryWarehouseSkuWhiteConfigQueryRepository.getPage(queryParam);
    }

    @Override
    public WncFullCategoryWarehouseSkuWhiteConfigEntity getDetail(Long id){
        if (Objects.isNull(id)) {
            throw new BizException("请求参数为空！");
        }
        return wncFullCategoryWarehouseSkuWhiteConfigQueryRepository.selectById(id);
    }
}