package net.summerfarm.wnc.application.warehouse.converter;

import net.summerfarm.wnc.common.message.*;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseStorageCenterBusEntity;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseStorageCenterWorkEntity;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseStorageEntity;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseTakeStandardEntity;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/8/29 16:07<br/>
 *
 * <AUTHOR> />
 */
public class WarehouseMessageConverter {

    public static WarehouseStorageCenterUpdateMessage oldNewWarehouse2Message(WarehouseStorageEntity oldWare,WarehouseStorageEntity newWare){
        if(oldWare == null || newWare == null){
            return null;
        }

        WarehouseStorageCenterUpdateMessage warehouseStorageCenterUpdateMessage = new WarehouseStorageCenterUpdateMessage();
        warehouseStorageCenterUpdateMessage.setOldWarehouseInfo(WarehouseMessageConverter.warehouse2Message(oldWare));
        warehouseStorageCenterUpdateMessage.setNewWarehouseInfo(WarehouseMessageConverter.warehouse2Message(newWare));
        return warehouseStorageCenterUpdateMessage;
    }

    public static WarehouseStorageCenterDetailMessage warehouse2Message(WarehouseStorageEntity warehouseStorageEntity){
        if(warehouseStorageEntity == null){
            return null;
        }

        WarehouseStorageCenterDetailMessage warehouseStorageCenterDetailMessage = new WarehouseStorageCenterDetailMessage();
        warehouseStorageCenterDetailMessage.setId(warehouseStorageEntity.getId());
        warehouseStorageCenterDetailMessage.setWarehouseNo(warehouseStorageEntity.getWarehouseNo());
        warehouseStorageCenterDetailMessage.setWarehouseName(warehouseStorageEntity.getWarehouseName());
        warehouseStorageCenterDetailMessage.setManageAdminId(warehouseStorageEntity.getManageAdminId());
        warehouseStorageCenterDetailMessage.setType(warehouseStorageEntity.getType());
        warehouseStorageCenterDetailMessage.setAreaManageId(warehouseStorageEntity.getAreaManageId());
        warehouseStorageCenterDetailMessage.setStatus(warehouseStorageEntity.getStatus());
        warehouseStorageCenterDetailMessage.setAddress(warehouseStorageEntity.getAddress());
        warehouseStorageCenterDetailMessage.setPoiNote(warehouseStorageEntity.getPoiNote());
        warehouseStorageCenterDetailMessage.setMailToAddress(warehouseStorageEntity.getMailToAddress());
        warehouseStorageCenterDetailMessage.setUpdater(warehouseStorageEntity.getUpdater());
        warehouseStorageCenterDetailMessage.setUpdateTime(warehouseStorageEntity.getUpdateTime());
        warehouseStorageCenterDetailMessage.setCreator(warehouseStorageEntity.getCreator());
        warehouseStorageCenterDetailMessage.setCreateTime(warehouseStorageEntity.getCreateTime());
        warehouseStorageCenterDetailMessage.setPersonContact(warehouseStorageEntity.getPersonContact());
        warehouseStorageCenterDetailMessage.setPhone(warehouseStorageEntity.getPhone());
        warehouseStorageCenterDetailMessage.setTenantId(warehouseStorageEntity.getTenantId());
        warehouseStorageCenterDetailMessage.setManageAdminId(warehouseStorageEntity.getManageAdminId());
        warehouseStorageCenterDetailMessage.setWarehousePic(warehouseStorageEntity.getWarehousePic());
        WarehouseStorageCenterBusEntity warehouseStorageCenterBusEntity = warehouseStorageEntity.getWarehouseStorageCenterBusEntity();
        if(warehouseStorageCenterBusEntity != null){
            warehouseStorageCenterDetailMessage.setAdvanceDay(warehouseStorageCenterBusEntity.getAdvanceDay());
            warehouseStorageCenterDetailMessage.setCapacity(warehouseStorageCenterBusEntity.getCapacity());
        }
        List<WarehouseStorageCenterWorkEntity> warehouseStorageCenterWorkEntities = warehouseStorageEntity.getWarehouseStorageCenterWorkEntities();
        if(!CollectionUtils.isEmpty(warehouseStorageCenterWorkEntities)){
            warehouseStorageCenterDetailMessage.setWorkTimes(warehouseStorageCenterWorkEntities.stream().map(WarehouseMessageConverter::entity2WorkTimeMsg).collect(Collectors.toList()));
        }
        List<WarehouseTakeStandardEntity> warehouseTakeStandardEntities = warehouseStorageEntity.getWarehouseTakeStandardEntities();
        if(!CollectionUtils.isEmpty(warehouseTakeStandardEntities)){
            warehouseStorageCenterDetailMessage.setWarehouseTakeStandard(WarehouseMessageConverter.takeStandardEntity2Msg(warehouseTakeStandardEntities));
        }
        return warehouseStorageCenterDetailMessage;
    }

    public static WorkTimeMessage entity2WorkTimeMsg(WarehouseStorageCenterWorkEntity timeEntity){
        if(timeEntity == null){
            return null;
        }
        WorkTimeMessage workTimeMessage = new WorkTimeMessage();
        workTimeMessage.setStartTime(timeEntity.getWorkStartTime());
        workTimeMessage.setEndTime(timeEntity.getWorkEndTime());
        return workTimeMessage;
    }

    public static List<WarehouseTakeStandardMessage> takeStandardEntity2Msg(List<WarehouseTakeStandardEntity> warehouseTakeStandardDTOs){
        if(CollectionUtils.isEmpty(warehouseTakeStandardDTOs)){
            return null;
        }

        Map<Integer, List<WarehouseTakeStandardEntity>> takeMap = warehouseTakeStandardDTOs.stream().collect(Collectors.groupingBy(WarehouseTakeStandardEntity::getStandardType));
        Iterator<Map.Entry<Integer, List<WarehouseTakeStandardEntity>>> iterator = takeMap.entrySet().iterator();
        List<WarehouseTakeStandardMessage> warehouseTakeStandardVos = new ArrayList<>();
        while (iterator.hasNext()) {
            Map.Entry<Integer, List<WarehouseTakeStandardEntity>> entry = iterator.next();
            Integer standardType = entry.getKey();
            List<WarehouseTakeStandardEntity> standards = entry.getValue();
            List<StorageProveMessage> storageProve = new ArrayList<>();
            Map<Integer, List<WarehouseTakeStandardEntity>> storageMap = standards.stream().collect(Collectors.groupingBy(WarehouseTakeStandardEntity::getStorageLocation));
            Iterator<Map.Entry<Integer, List<WarehouseTakeStandardEntity>>> storageIterator = storageMap.entrySet().iterator();
            while (storageIterator.hasNext()) {
                Map.Entry<Integer, List<WarehouseTakeStandardEntity>> storageEntry = storageIterator.next();
                Integer storageLocation = storageEntry.getKey();
                List<WarehouseTakeStandardEntity> standardList = storageEntry.getValue();
                List<Integer> proveTypes = standardList.stream().map(WarehouseTakeStandardEntity::getProveType).collect(Collectors.toList());
                StorageProveMessage storageProveVO = new StorageProveMessage();
                storageProveVO.setStorageLocation(storageLocation);
                storageProveVO.setProveType(proveTypes);
                storageProve.add(storageProveVO);
            }
            WarehouseTakeStandardMessage warehouseTakeStandardVO = new WarehouseTakeStandardMessage();
            warehouseTakeStandardVO.setStandardType(standardType);
            warehouseTakeStandardVO.setStorageProve(storageProve);
            warehouseTakeStandardVos.add(warehouseTakeStandardVO);
        }
        return warehouseTakeStandardVos;
    }
}
