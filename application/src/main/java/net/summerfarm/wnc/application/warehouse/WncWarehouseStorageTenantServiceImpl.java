package net.summerfarm.wnc.application.warehouse;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.warehouse.dto.WncWarehouseStorageTenantDTO;
import net.summerfarm.wnc.api.warehouse.service.warehouse.WncWarehouseStorageTenantService;
import net.summerfarm.wnc.application.warehouse.converter.WncWarehouseStorageTenantConverter;
import net.summerfarm.wnc.common.query.warehouse.WncWarehouseStorageTenantQuery;
import net.summerfarm.wnc.domain.warehouse.WncWarehouseStorageTenantDomain;
import net.summerfarm.wnc.domain.warehouse.WncWarehouseStorageTenantRepository;
import net.summerfarm.wnc.domain.warehouse.entity.WncWarehouseStorageTenantEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/4/6 11:08<br/>
 *
 * <AUTHOR> />
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class WncWarehouseStorageTenantServiceImpl implements WncWarehouseStorageTenantService {

    private final WncWarehouseStorageTenantDomain rsWarehouseStorageTenantDomain;
    private final WncWarehouseStorageTenantRepository rsWarehouseStorageTenantRepository;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void bindingTenant(WncWarehouseStorageTenantDTO rsWarehouseStorageTenantDTO) {
        rsWarehouseStorageTenantDomain.bindingTenant(WncWarehouseStorageTenantConverter.dto2Entity(rsWarehouseStorageTenantDTO));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void unbindingTenant(WncWarehouseStorageTenantDTO rsWarehouseStorageTenantDTO) {
        rsWarehouseStorageTenantDomain.unbindingTenant(WncWarehouseStorageTenantConverter.dto2Entity(rsWarehouseStorageTenantDTO));
    }

    @Override
    public List<WncWarehouseStorageTenantDTO> queryTenantWarehouseList(WncWarehouseStorageTenantQuery rsWarehouseStorageTenantQuery) {
        List<WncWarehouseStorageTenantEntity> rsWarehouseStorageTenantEntities = rsWarehouseStorageTenantRepository.queryList(rsWarehouseStorageTenantQuery);
        return rsWarehouseStorageTenantEntities.stream().map(WncWarehouseStorageTenantConverter::entity2Dto).collect(Collectors.toList());
    }
}
