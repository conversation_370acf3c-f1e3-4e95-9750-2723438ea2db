package net.summerfarm.wnc.application.inbound.controller.fence.converter;

import net.summerfarm.wnc.api.fence.dto.WncTenantGlobalFenceRuleDTO;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.TenantGlobalFenceRuleVO;

/**
 * Description: <br/>
 * date: 2023/4/15 16:13<br/>
 *
 * <AUTHOR> />
 */
public class TenantGlobalFenceRuleVOConverter {

    public static TenantGlobalFenceRuleVO dto2Vo(WncTenantGlobalFenceRuleDTO wncTenantGlobalFenceRuleDTO){
        if(wncTenantGlobalFenceRuleDTO == null){
            return null;
        }
        TenantGlobalFenceRuleVO tenantGlobalFenceRuleVO = new TenantGlobalFenceRuleVO();
        tenantGlobalFenceRuleVO.setTenantId(wncTenantGlobalFenceRuleDTO.getTenantId());
        tenantGlobalFenceRuleVO.setGlobalDeliveryRule(wncTenantGlobalFenceRuleDTO.getGlobalDeliveryRule());
        tenantGlobalFenceRuleVO.setCreateTime(wncTenantGlobalFenceRuleDTO.getCreateTime());
        tenantGlobalFenceRuleVO.setId(wncTenantGlobalFenceRuleDTO.getId());
        tenantGlobalFenceRuleVO.setUpdateTime(wncTenantGlobalFenceRuleDTO.getUpdateTime());
        return tenantGlobalFenceRuleVO;
    }
}
