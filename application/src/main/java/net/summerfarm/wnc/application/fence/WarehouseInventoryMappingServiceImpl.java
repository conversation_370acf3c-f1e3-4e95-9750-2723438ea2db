package net.summerfarm.wnc.application.fence;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.alibaba.fastjson.JSON;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.fence.dto.WarehouseInventoryMappingDTO;
import net.summerfarm.wnc.api.fence.service.WarehouseInventoryMappingService;
import net.summerfarm.wnc.application.exce.converter.AreaSkuManyWarePojoConverter;
import net.summerfarm.wnc.application.exce.pojo.AreaSkuManyWarehousePojo;
import net.summerfarm.wnc.application.fence.converter.WarehouseInventoryMappingDTOConverter;
import net.summerfarm.wnc.common.config.WncConfig;
import net.summerfarm.wnc.common.enums.AreaSkuEnums;
import net.summerfarm.wnc.common.enums.ConfigEnums;
import net.summerfarm.wnc.common.enums.FenceEnums;
import net.summerfarm.wnc.common.query.fence.AreaByListWarehouseAndSkuQuery;
import net.summerfarm.wnc.common.query.fence.FenceQuery;
import net.summerfarm.wnc.common.query.warehouse.WarehouseInventoryMappingQuery;
import net.summerfarm.wnc.domain.ConfigRepository;
import net.summerfarm.wnc.domain.fence.AreaRepository;
import net.summerfarm.wnc.domain.fence.AreaSkuRepository;
import net.summerfarm.wnc.domain.fence.FenceDomainService;
import net.summerfarm.wnc.domain.fence.FenceRepository;
import net.summerfarm.wnc.domain.fence.dataObject.AreaSkuManyWarehouseDO;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.summerfarm.wnc.domain.fence.entity.SkuManyWarehouseEntity;
import net.summerfarm.wnc.domain.invertory.InventoryRepository;
import net.summerfarm.wnc.domain.warehouse.WarehouseInventoryMappingDomainService;
import net.summerfarm.wnc.domain.warehouse.WarehouseInventoryMappingRepository;
import net.summerfarm.wnc.domain.warehouse.WarehouseLogisticsMappingRepository;
import net.summerfarm.wnc.domain.warehouse.entity.ConfigEntity;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseInventoryMappingEntity;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseLogisticsMappingEntity;
import net.summerfarm.wnc.facade.gc.GcQueryFacade;
import net.summerfarm.wnc.facade.gc.dto.ProductSkuDetailDTO;
import net.xianmu.common.exception.BizException;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import net.xianmu.robot.feishu.FeishuBotUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/8/2 11:38<br/>
 *
 * <AUTHOR> />
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class WarehouseInventoryMappingServiceImpl implements WarehouseInventoryMappingService {

    private final AreaRepository areaRepository;
    private final FenceRepository fenceRepository;
    private final AreaSkuRepository areaSkuRepository;
    private final WarehouseLogisticsMappingRepository warehouseLogisticsMappingRepository;
    private final WarehouseInventoryMappingRepository warehouseInventoryMappingRepository;
    private final WarehouseInventoryMappingDomainService warehouseInventoryMappingDomainService;
    private final ConfigRepository configRepository;
    private final InventoryRepository inventoryRepository;
    private final FenceDomainService fenceDomainService;
    private final GcQueryFacade gcQueryFacade;
    private final WncConfig wncConfig;

    @Override
    public List<Integer> queryOpenAreaList() {
        return areaRepository.queryOpenAreaList();
    }

    @Override
    public void handleAreaMappingQuestion(Integer areaNo, boolean saasFlag) {
        //根据运营区域编号查询围栏信息
        List<FenceEntity> fenceEntities = fenceRepository.queryValidFenceByAreaNo(areaNo);
        if(CollectionUtils.isEmpty(fenceEntities)){
            return;
        }
        //获取第一个数据
        FenceEntity firstFenceEntity = fenceEntities.get(0);
        //根据packId查询对应的城配仓编号
        List<Integer> packStoreNoList = fenceRepository.queryValidStoreNoByPackId(firstFenceEntity.getPackId());
        //过滤出城配仓不同的数据
        List<Integer> storeNoList = fenceEntities.stream().map(FenceEntity::getStoreNo).filter(storeNo -> !packStoreNoList.contains(storeNo)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(storeNoList)){
            return;
        }
        List<String> skuList = null;
        //鲜沐
        if(!saasFlag){
            //根据运营区域查询存在上架的sku
            skuList = areaSkuRepository.queryAreaOnSaleSku(areaNo);
        }else{
            //saas未上架的商品映射的查询
            skuList = areaSkuRepository.queryAreaNoSaleSku(areaNo);
            //过滤出Saas代仓的商品
            if(CollectionUtils.isEmpty(skuList)){
                return;
            }
            List<String> dubboSkuList = gcQueryFacade.querySaasProxySkuListBySkus(skuList);
            log.info("gcQueryFacade querySaasProxyListBySkus:{}",dubboSkuList);
            skuList = inventoryRepository.queryProxyListBySkus(skuList);
            log.info("inventoryRepository queryProxyListBySkus:{}",skuList);
            if(!skuList.containsAll(dubboSkuList) || skuList.size() != dubboSkuList.size()){
                log.error("handleAreaMappingQuestion 通过数据库查询代仓信息和通过Dubbo查询信息数据不一致，已使用数据库查询进行数据处理\\n");
            }
        }
        if(CollectionUtils.isEmpty(skuList)){
            return;
        }

        //查询城配仓对应的库存仓
        List<WarehouseLogisticsMappingEntity> warehouseLogisticsMappingEntities = warehouseLogisticsMappingRepository.queryByStoreNoList(storeNoList);
        //查询围栏组的sku、城配仓、库存仓之间的关系
        List<WarehouseInventoryMappingEntity> packInventoryMappingEntities = warehouseInventoryMappingRepository.queryList(WarehouseInventoryMappingQuery.builder()
                .skus(skuList)
                .storeNos(packStoreNoList).build());
        //查询不在围栏组的sku、城配仓、库存仓之间的关系
        List<WarehouseInventoryMappingEntity> storeInventoryMappingEntities = warehouseInventoryMappingRepository.queryList(WarehouseInventoryMappingQuery.builder()
                .skus(skuList)
                .storeNos(storeNoList).build());

        //生成数据
        List<WarehouseInventoryMappingEntity> needCreateMapping = handleCreateShortMapping(packInventoryMappingEntities, storeInventoryMappingEntities, warehouseLogisticsMappingEntities, storeNoList);
        //插入数据
        warehouseInventoryMappingDomainService.batchSave(needCreateMapping);
        try {
            ConfigEntity configEntity = configRepository.queryByKey("WNC_MAPPING_CHEAK");
            if(configEntity == null){
                return;
            }
            //发送到飞书
            Map<Integer, List<WarehouseInventoryMappingEntity>> storeNoMappingMap = needCreateMapping.stream().collect(Collectors.groupingBy(WarehouseInventoryMappingEntity::getStoreNo));
            storeNoMappingMap.keySet().forEach(storeNo ->{
                FeishuBotUtil.sendTextMsg(configEntity.getValue(), "运营区域编号:" + areaNo + ",城配仓编号:" + storeNo + ",补齐映射数据条数:" + storeNoMappingMap.get(storeNo).size());
                if(saasFlag){
                    log.info("补充Saas代仓映射关系:{}",JSON.toJSONString(needCreateMapping));
                }else{
                    log.info("补充映射关系:{}",JSON.toJSONString(needCreateMapping));
                }
            });
            if(CollectionUtils.isEmpty(storeNoMappingMap)){
                log.info("运营区域编号:{},巡检无需补充数据Nice",areaNo);
            }
        } catch (Exception e) {
            log.error("定时任务补充映射关系发送飞书异常", e);
        }
    }

    @Override
    public void mappingHandleInit(Integer areaNo, Integer storeNo, Integer packId) {
        //根据packId查询对应的城配仓编号
        List<Integer> packStoreNoList = fenceRepository.queryValidStoreNoByPackId(packId);
        if(CollectionUtils.isEmpty(packStoreNoList)){
            return;
        }
        //过滤出城配仓不同的数据
        List<Integer> packNoSelfStoreNoList = packStoreNoList.stream().filter(packStoreNo -> !Objects.equals(storeNo,packStoreNo)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(packNoSelfStoreNoList)){
            return;
        }

        Map<Integer,List<String>> onSaleSkuListMap = areaSkuRepository.querySkuByAreaNo(areaNo);
        if(CollectionUtils.isEmpty(onSaleSkuListMap)){
            log.info("onSaleSkuListMap is empty");
            return;
        }
        //上架的sku
        List<String> upSkuList = onSaleSkuListMap.get(AreaSkuEnums.onSale.UP.getValue());
        if(CollectionUtils.isEmpty(upSkuList)){
            log.info("upSkuList is empty");
            return;
        }
        List<String> skuList = new ArrayList<>(upSkuList);
        //下架的sku
        List<String> downSkuList = onSaleSkuListMap.get(AreaSkuEnums.onSale.DOWN.getValue());
        skuList.addAll(inventoryRepository.queryProxyListBySkus(downSkuList));
        List<String> saasDubboProxyListSkus = gcQueryFacade.querySaasProxySkuListBySkus(downSkuList);
        List<String> saasDbProxyListSkus = inventoryRepository.queryProxyListBySkus(downSkuList);
        log.info("gcQueryFacade saasProxyListSkus:{}",JSON.toJSONString(saasDubboProxyListSkus));
        log.info("inventoryRepository queryProxyListBySkus:{}",JSON.toJSONString(saasDbProxyListSkus));
        if(!saasDbProxyListSkus.containsAll(saasDubboProxyListSkus) || saasDbProxyListSkus.size() != saasDubboProxyListSkus.size()){
            log.error("mappingHandleInit 通过数据库查询代仓信息和通过Dubbo查询信息数据不一致，已使用数据库查询进行数据处理\\n");
        }
        skuList.addAll(saasDbProxyListSkus);

        //查询城配仓对应的库存仓
        List<WarehouseLogisticsMappingEntity> warehouseLogisticsMappingEntities = warehouseLogisticsMappingRepository.queryByStoreNoList(Collections.singletonList(storeNo));
        //查询围栏组排除自己城配仓的sku、城配仓、库存仓之间的关系
        List<WarehouseInventoryMappingEntity> packNoSelfInventoryMappingEntities = warehouseInventoryMappingRepository.queryList(WarehouseInventoryMappingQuery.builder()
                .skus(skuList)
                .storeNos(packNoSelfStoreNoList).build());
        //查询自己sku、城配仓、库存仓之间的关系
        List<WarehouseInventoryMappingEntity> storeInventoryMappingEntities = warehouseInventoryMappingRepository.queryList(WarehouseInventoryMappingQuery.builder()
                .skus(skuList)
                .storeNos(Collections.singletonList(storeNo)).build());

        Map<Integer, List<WarehouseLogisticsMappingEntity>> storeNoWarehouseListMap = warehouseLogisticsMappingEntities.stream().collect(Collectors.groupingBy(WarehouseLogisticsMappingEntity::getStoreNo));

        //pack里面有的sku 相同运营区域的其他城配仓都要有，除非没有对应的库存仓
        Map<String, List<WarehouseInventoryMappingEntity>> packNoSelfSkuWarehouseNoMap = packNoSelfInventoryMappingEntities.stream().collect(Collectors.groupingBy(WarehouseInventoryMappingEntity::getSku));

        //城配仓当前自己的存在映射关系
        Map<String, Integer> skuWarehouseNoMap = storeInventoryMappingEntities.stream().collect(Collectors.toMap(WarehouseInventoryMappingEntity::getSku, WarehouseInventoryMappingEntity::getWarehouseNo,(oldValue,newValue)->newValue));
        Map<String, WarehouseInventoryMappingEntity> skuStoreWarehouseNoMap = storeInventoryMappingEntities.stream().collect(Collectors.toMap(skuInventory -> skuInventory.getSku() +"#"+ skuInventory.getStoreNo(), Function.identity()));

        LocalDateTime now = LocalDateTime.now();
        List<WarehouseInventoryMappingEntity> needCreateMapping = new ArrayList();
        for (String sku : packNoSelfSkuWarehouseNoMap.keySet()) {
            //sku为空或者原城配仓存在映射则跳过
            if(StringUtil.isBlank(sku) || skuWarehouseNoMap.get(sku) != null){
                continue;
            }
            List<WarehouseInventoryMappingEntity> packWarehouseInventoryMappings = packNoSelfSkuWarehouseNoMap.get(sku);
            if(CollectionUtils.isEmpty(packWarehouseInventoryMappings)){
                continue;
            }
            List<WarehouseLogisticsMappingEntity> warehouseList = storeNoWarehouseListMap.get(storeNo);
            if(CollectionUtils.isEmpty(warehouseList)){
                continue;
            }
            Integer packNoSelfWarehouseNo = null;
            for (WarehouseInventoryMappingEntity wa : packWarehouseInventoryMappings) {
                //不包含此仓库则不处理
                if(!warehouseList.stream().map(WarehouseLogisticsMappingEntity::getWarehouseNo).collect(Collectors.toList()).contains(wa.getWarehouseNo())){
                    continue;
                }
                packNoSelfWarehouseNo = wa.getWarehouseNo();
                break;
            }
            if(packNoSelfWarehouseNo == null){
                continue;
            }

            //已经sku和城配仓的映射存在不处理
            if(skuStoreWarehouseNoMap.get(sku + "#" + storeNo) != null){
                continue;
            }
            WarehouseInventoryMappingEntity warehouseInventoryMappingEntity = new WarehouseInventoryMappingEntity();
            warehouseInventoryMappingEntity.setWarehouseNo(packNoSelfWarehouseNo);
            warehouseInventoryMappingEntity.setStoreNo(storeNo);
            warehouseInventoryMappingEntity.setSku(sku);
            warehouseInventoryMappingEntity.setCreator(1448);
            warehouseInventoryMappingEntity.setCreateTime(now);

            needCreateMapping.add(warehouseInventoryMappingEntity);
        }

        //插入数据
        warehouseInventoryMappingDomainService.batchSave(needCreateMapping);
        try {
            ConfigEntity configEntity = configRepository.queryByKey("WNC_MAPPING_CHEAK");
            if(configEntity == null){
                return;
            }
            //发送到飞书
            Map<Integer, List<WarehouseInventoryMappingEntity>> storeNoMappingMap = needCreateMapping.stream().collect(Collectors.groupingBy(WarehouseInventoryMappingEntity::getStoreNo));
            storeNoMappingMap.keySet().forEach(storeNoNeed ->{
                FeishuBotUtil.sendTextMsg(configEntity.getValue(), "围栏开启运补充映射关系:营区域编号:" + areaNo + ",城配仓编号:" + storeNoNeed + ",补齐映射数据条数:" + storeNoMappingMap.get(storeNo).size());
                log.info("围栏开启补充映射关系:{}",JSON.toJSONString(needCreateMapping));
            });
            if(CollectionUtils.isEmpty(storeNoMappingMap)){
                log.info("围栏开启补充运营区域编号:{},巡检无需补充数据Nice",areaNo);
            }
        } catch (Exception e) {
            log.error("围栏开启补充映射关系发送飞书异常", e);
        }
    }

    /**
     * 处理生成缺失的数据
     * @param packInventoryMappingEntities 围栏组的映射关系
     * @param storeInventoryMappingEntities 不在围栏组的映射关系
     * @param warehouseLogisticsMappingEntities 不在围栏组的城配仓库的映射关系
     * @param storeNoList 需要添加的映射的城配仓编号
     * @return 遗漏的映射关系
     */
    private List<WarehouseInventoryMappingEntity> handleCreateShortMapping(List<WarehouseInventoryMappingEntity> packInventoryMappingEntities, List<WarehouseInventoryMappingEntity> storeInventoryMappingEntities, List<WarehouseLogisticsMappingEntity> warehouseLogisticsMappingEntities, List<Integer> storeNoList) {
        Map<Integer, List<WarehouseLogisticsMappingEntity>> storeNoWarehouseListMap = warehouseLogisticsMappingEntities.stream().collect(Collectors.groupingBy(WarehouseLogisticsMappingEntity::getStoreNo));

        //pack里面有的sku 相同运营区域的其他城配仓都要有，除非没有对应的库存仓
        Map<String, Integer> packSkuWarehouseNoMap = packInventoryMappingEntities.stream().collect(Collectors.toMap(e -> e.getStoreNo() +"#"+ e.getSku(), WarehouseInventoryMappingEntity::getWarehouseNo));
        Map<String, Integer> storeNoSkuWarehouseNoMap = storeInventoryMappingEntities.stream().collect(Collectors.toMap(e -> e.getStoreNo() +"#"+ e.getSku(), WarehouseInventoryMappingEntity::getWarehouseNo));

        LocalDateTime now = LocalDateTime.now();
        List<WarehouseInventoryMappingEntity> needCreateMapping = new ArrayList();
        for (String storeNoSku : packSkuWarehouseNoMap.keySet()) {
            if(storeNoSkuWarehouseNoMap.get(storeNoSku) != null){
                continue;
            }
            //不存在，需要补充数据
            Integer warehouseNo = packSkuWarehouseNoMap.get(storeNoSku);
            //校验合法性
            String[] storeNoSkuSplit = storeNoSku.split("#");
            String storeNo = storeNoSkuSplit[0];
            String sku = storeNoSkuSplit[1];
            if(StringUtil.isBlank(storeNo) || StringUtil.isBlank(sku)){
                continue;
            }
            //是否存在城配仓的库存仓
            storeNoList.forEach(areaStoreNo ->{
                if(storeNoSkuWarehouseNoMap.get(areaStoreNo+"#"+sku) != null){
                    return;
                }
                List<WarehouseLogisticsMappingEntity> warehouseList = storeNoWarehouseListMap.get(areaStoreNo);
                if(CollectionUtils.isEmpty(warehouseList)){
                    return;
                }
                List<Integer> warehouseNoList = warehouseList.stream().map(WarehouseLogisticsMappingEntity::getWarehouseNo).collect(Collectors.toList());
                if(!warehouseNoList.contains(warehouseNo)){
                    return;
                }
                WarehouseInventoryMappingEntity warehouseInventoryMappingEntity = new WarehouseInventoryMappingEntity();
                warehouseInventoryMappingEntity.setWarehouseNo(warehouseNo);
                warehouseInventoryMappingEntity.setStoreNo(areaStoreNo);
                warehouseInventoryMappingEntity.setSku(sku);
                warehouseInventoryMappingEntity.setCreator(1448);
                warehouseInventoryMappingEntity.setCreateTime(now);

                needCreateMapping.add(warehouseInventoryMappingEntity);
            });
        }

        return needCreateMapping.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public void mappingDataFix(Integer areaNo) {
        if(areaNo == null){
            return;
        }
        //根据运营区域查询对应的围栏的城配仓
        List<Integer> storeNos = fenceRepository.queryValidFenceByAreaNo(areaNo).stream().map(FenceEntity::getStoreNo).collect(Collectors.toList());
        //查询运营区域对应的库存仓有哪些
        List<Integer> warehouseNos = areaSkuRepository.queryWareNoByAreaNoStoreNo(areaNo,storeNos);
        log.info("查询运营区域对应的库存仓有:{}",JSON.toJSONString(warehouseNos));
        if(CollectionUtils.isEmpty(warehouseNos)){
            return;
        }
        //查询是否存在运营区域相同sku存在多个仓库信息
        List<SkuManyWarehouseEntity> skuManyWarehouseList = areaSkuRepository.querySkuManyWarehouse(areaNo,warehouseNos);
        //不存在直接结束
        if(CollectionUtils.isEmpty(skuManyWarehouseList)){
            return;
        }
        //存在需要修改
        warehouseInventoryMappingDomainService.mappingDataFix(skuManyWarehouseList);
    }

    @Override
    public void mappingDataAllFenceaFix(Integer beginId,Integer endId) {
        List<FenceEntity> fenceEntities = fenceRepository.queryList(FenceQuery.builder()
                .status(FenceEnums.Status.VALID.getValue())
                .beginId(beginId)
                .endId(endId)
                .build());
        Set<String> keys = fenceEntities.stream().filter(fence -> fence.getAreaNo() != null && fence.getStoreNo() != null && fence.getPackId() != null)
                .map(fence -> fence.getAreaNo() + "," + fence.getStoreNo() + "," + fence.getPackId())
                .collect(Collectors.toSet());
        for (String key : keys) {
            String[] split = key.split(",");
            this.mappingHandleInit(Integer.parseInt(split[0]),Integer.parseInt(split[1]),Integer.parseInt(split[2]));
        }
    }

    @Override
    public void areaSkuManyWarehouseWarningJob() {
        //查询所有的有效的运营区域信息
        List<Integer> areaList = areaRepository.queryOpenAreaList();
        if(CollectionUtils.isEmpty(areaList)){
            return;
        }
        List<AreaSkuManyWarehouseDO> result = new ArrayList<>();
        for (Integer areaNo : areaList) {
            //查询运营服务区的sku存在多仓映射关系的查询
            List<AreaSkuManyWarehouseDO> areaSkuManyWarehouseDOList = fenceRepository.queryAreaSkuManyWarehouse(areaNo);
            log.info("areaSkuManyWarehouseWarningJob areaNo:{},多仓映射关系:{}",areaNo,JSON.toJSONString(areaSkuManyWarehouseDOList));
            if(CollectionUtils.isEmpty(areaSkuManyWarehouseDOList)){
                continue;
            }
            // 过滤出状态在用的SKU
            List<String> skuList = areaSkuManyWarehouseDOList.stream().map(AreaSkuManyWarehouseDO::getSku).distinct().collect(Collectors.toList());
            Map<String, ProductSkuDetailDTO> isUseSkuInfoMap = gcQueryFacade.querySkuListIsUseInfoMap(skuList);
            areaSkuManyWarehouseDOList = areaSkuManyWarehouseDOList.stream().filter(areaSkuManyWarehouseDO -> isUseSkuInfoMap.get(areaSkuManyWarehouseDO.getSku()) != null).collect(Collectors.toList());

            result.addAll(areaSkuManyWarehouseDOList);
            if(result.size() > 10000){
                log.error("areaSkuManyWarehouseWarningJob 导出多仓映射信息超过10000",new BizException("多仓映射信息超过10000"));
                break;
            }
        }
        if(CollectionUtils.isEmpty(result)){
            return;
        }

        List<AreaSkuManyWarehousePojo> exelPojoList = result.stream().map(AreaSkuManyWarePojoConverter::convert).collect(Collectors.toList());

        String fileName = String.format("%s运营区域多仓映射.xls", DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        File file = new File(fileName);
        FileOutputStream out = null;
        try {
            out = new FileOutputStream(file);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
        ExcelWriter excelWriter = EasyExcel.write(out).build();
        WriteSheet writeSheet = EasyExcel.writerSheet("多仓映射信息").needHead(Boolean.TRUE).build();
        WriteTable writeTable = EasyExcel.writerTable(1).head(AreaSkuManyWarehousePojo.class).needHead(Boolean.TRUE).build();
        try {
            excelWriter.write(exelPojoList, writeSheet,writeTable);
        } catch (Exception e) {
            log.error("导出多仓映射信息失败",e);
            throw new BizException(e.getMessage());
        }finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
        //上传到OSS
        OssUploadResult ossUploadResult = OssUploadUtil.upload(fileName, file, OSSExpiredLabelEnum.HALF_A_MONTH);
        try {
            file.delete();
        } catch (Exception e) {
            log.info("删除文件异常");
        }
        //将下载链接发送到群里面
        String feiShuNoticeUrl = "";
        String areaSkuManyWarehouseWarningJobFeiShuNoticeUrl = wncConfig.getAreaSkuManyWarehouseWarningJobFeiShuNoticeUrl();
        if(!StringUtils.isEmpty(areaSkuManyWarehouseWarningJobFeiShuNoticeUrl)){
            feiShuNoticeUrl = areaSkuManyWarehouseWarningJobFeiShuNoticeUrl;
        }else{
            ConfigEntity configEntity = configRepository.queryByKey(ConfigEnums.Key.AREA_SKU_MANY_WAREHOUSE_WARNING.getValue());
            if(configEntity == null){
                log.error("areaSkuManyWarehouseWarningJob configEntity is null");
                return;
            }
            feiShuNoticeUrl = configEntity.getValue();
        }

        //飞书消息通知
        String textMsg = "运营区域存在sku多仓映射关系" + "\n\n" +
                "总数:" + exelPojoList.size() + "\n\n" +
                "下载链接：" + ossUploadResult.getUrl() + "\n\n";
        FeishuBotUtil.sendMarkdownMsgAndAtAll(feiShuNoticeUrl, textMsg);
    }

    @Override
    public List<Integer> queryStoreNoByWarehouseAndSku(Integer warehouseNo, String sku) {
        List<WarehouseInventoryMappingEntity> warehouseInventoryMappingEntities = warehouseInventoryMappingRepository.queryValideStoreWarehouseNoMappingBySkuWarehouseNos(Collections.singletonList(sku),
                Collections.singletonList(warehouseNo));

        return warehouseInventoryMappingEntities.stream().map(WarehouseInventoryMappingEntity::getStoreNo).collect(Collectors.toList());
    }

    @Override
    public List<Integer> queryWarehouseBySkuAndAreaNo(String sku, Integer areaNo) {
        if(areaNo == null || StringUtils.isEmpty(sku)){
            throw new BizException("运营区域和sku都不能为空");
        }
        //根据运营区域查询围栏
        List<FenceEntity> fenceEntities = fenceRepository.queryList(FenceQuery.builder()
                .status(FenceEnums.Status.VALID.getValue())
                .areaNo(areaNo)
                .build());
        if(CollectionUtils.isEmpty(fenceEntities)){
            return Collections.emptyList();
        }
        List<Integer> storeNos = fenceEntities.stream().map(FenceEntity::getStoreNo).filter(Objects::nonNull).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(storeNos)){
            return Collections.emptyList();
        }
        //根据sku+storeNo查询映射关系
        List<WarehouseInventoryMappingEntity> warehouseInventoryMappingEntities = warehouseInventoryMappingRepository.queryValideStoreWarehouseNoMappingBySkuStoreNos(Collections.singletonList(sku),
                storeNos);

        return warehouseInventoryMappingEntities.stream().map(WarehouseInventoryMappingEntity::getWarehouseNo).collect(Collectors.toList());
    }

    @Override
    public List<WarehouseInventoryMappingDTO> queryAreaByListWarehouseAndSku(List<AreaByListWarehouseAndSkuQuery> query) {
        //查询映射
        List<WarehouseInventoryMappingEntity> skuWarehouseMappingList = warehouseInventoryMappingDomainService.queryMappingListBySkuWarehouseNo(query);
        if(CollectionUtils.isEmpty(skuWarehouseMappingList)){
            return Collections.emptyList();
        }
        //根据映射关系获取城配仓
        List<Integer> storeNos = skuWarehouseMappingList.stream().map(WarehouseInventoryMappingEntity::getStoreNo).distinct().collect(Collectors.toList());
        //根据城配仓获取有效围栏上的有效的运营区域的围栏
        List<FenceEntity> fenceEntities= fenceDomainService.queryValidFenceAreaByStoreNos(storeNos);
        Map<Integer, List<FenceEntity>> storeNoFenceMap = fenceEntities.stream().collect(Collectors.groupingBy(FenceEntity::getStoreNo));

        //配置运营区域编号
        List<WarehouseInventoryMappingDTO> mappingDTOS = skuWarehouseMappingList.stream().map(WarehouseInventoryMappingDTOConverter::dto2Entity).collect(Collectors.toList());
        mappingDTOS.forEach(mappingDTO -> {
            List<FenceEntity> fenceMapList = storeNoFenceMap.get(mappingDTO.getStoreNo());
            if(CollectionUtils.isEmpty(fenceMapList)){
                return;
            }
            mappingDTO.setAreaNos(fenceMapList.stream().map(FenceEntity::getAreaNo).distinct().collect(Collectors.toList()));
        });
        Map<String, List<WarehouseInventoryMappingDTO>> warehouseNoSkuMap = mappingDTOS.stream().collect(Collectors.groupingBy(dto -> dto.getWarehouseNo() + "#" + dto.getSku()));

        //根据请求返回对应的数据结构
        return query.stream().map(skuWarehouse -> {
            List<WarehouseInventoryMappingDTO> mappingDtoList = warehouseNoSkuMap.get(skuWarehouse.getWarehouseNo() + "#" + skuWarehouse.getSku());
            WarehouseInventoryMappingDTO dto = WarehouseInventoryMappingDTO.builder()
                    .sku(skuWarehouse.getSku())
                    .warehouseNo(skuWarehouse.getWarehouseNo())
                    .build();
            if(CollectionUtils.isEmpty(mappingDtoList)){
                return dto;
            }
            dto.setAreaNos(mappingDtoList.stream().map(WarehouseInventoryMappingDTO::getAreaNos)
                    .filter(Objects::nonNull)
                    .flatMap(Collection::stream)
                    .distinct().collect(Collectors.toList()));
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<WarehouseInventoryMappingDTO> queryBatchWarehouseMappingByAreaAndSkus(Integer areaNo, List<String> skuList) {
        if(areaNo == null || CollectionUtils.isEmpty(skuList)){
            throw new BizException("运营区域编号、SKU集合不能为空");
        }

        //根据运营区域查询围栏
        List<FenceEntity> fenceEntities = fenceRepository.queryList(FenceQuery.builder()
                .status(FenceEnums.Status.VALID.getValue())
                .areaNo(areaNo)
                .build());

        if(CollectionUtils.isEmpty(fenceEntities)){
            return Collections.emptyList();
        }

        List<Integer> storeNos = fenceEntities.stream().map(FenceEntity::getStoreNo).collect(Collectors.toList());
        //根据sku+storeNo查询映射关系
        List<WarehouseInventoryMappingEntity> warehouseInventoryMappingEntities = warehouseInventoryMappingRepository.queryValideStoreWarehouseNoMappingBySkuStoreNos(skuList,storeNos);
        if(CollectionUtils.isEmpty(warehouseInventoryMappingEntities)){
            return Collections.emptyList();
        }

        List<WarehouseInventoryMappingDTO> warehouseInventoryMappingDTOList = warehouseInventoryMappingEntities.stream().map(WarehouseInventoryMappingDTOConverter::dto2Entity).collect(Collectors.toList());
        warehouseInventoryMappingDTOList.forEach(warehouseInventoryMappingDTO -> {
            warehouseInventoryMappingDTO.setAreaNo(areaNo);
        });
        return warehouseInventoryMappingDTOList;
    }
}
