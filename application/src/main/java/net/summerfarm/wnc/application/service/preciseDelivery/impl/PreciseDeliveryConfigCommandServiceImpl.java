package net.summerfarm.wnc.application.service.preciseDelivery.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.application.inbound.controller.preciseDelivery.input.PreciseDeliveryConfigCreateCommandInput;
import net.summerfarm.wnc.application.inbound.controller.preciseDelivery.input.PreciseDeliveryConfigInitCommandInput;
import net.summerfarm.wnc.application.inbound.controller.preciseDelivery.input.PreciseDeliveryConfigRemoveCommandInput;
import net.summerfarm.wnc.application.inbound.controller.preciseDelivery.input.PreciseDeliveryConfigUpdateCommandInput;
import net.summerfarm.wnc.application.inbound.converter.preciseDelivery.PreciseDeliveryConfigParamConverter;
import net.summerfarm.wnc.application.service.preciseDelivery.PreciseDeliveryConfigCommandService;
import net.summerfarm.wnc.common.constants.AppConsts;
import net.summerfarm.wnc.common.enums.FenceEnums;
import net.summerfarm.wnc.common.query.fence.FenceQuery;
import net.summerfarm.wnc.domain.ConfigRepository;
import net.summerfarm.wnc.domain.fence.FenceRepository;
import net.summerfarm.wnc.domain.fence.entity.AdCodeMsgEntity;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.summerfarm.wnc.domain.preciseDelivery.PreciseDeliveryConfigDomainService;
import net.summerfarm.wnc.domain.preciseDelivery.PreciseDeliveryConfigQueryRepository;
import net.summerfarm.wnc.domain.preciseDelivery.PreciseDeliveryConfigValidator;
import net.summerfarm.wnc.domain.preciseDelivery.entity.PreciseDeliveryConfigEntity;
import net.summerfarm.wnc.domain.preciseDelivery.param.PreciseDeliveryConfigAreaCommandParam;
import net.summerfarm.wnc.domain.preciseDelivery.param.PreciseDeliveryConfigCommandParam;
import net.summerfarm.wnc.domain.preciseDelivery.param.PreciseDeliveryConfigTimeCommandParam;
import net.summerfarm.wnc.domain.warehouse.entity.ConfigEntity;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.user.UserInfoHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:精准送配置操作服务实现
 * date: 2024/1/22 16:58
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PreciseDeliveryConfigCommandServiceImpl implements PreciseDeliveryConfigCommandService {

    private final PreciseDeliveryConfigDomainService preciseDeliveryConfigDomainService;
    private final PreciseDeliveryConfigValidator preciseDeliveryConfigValidator;
    private final PreciseDeliveryConfigQueryRepository preciseDeliveryConfigQueryRepository;
    private final ConfigRepository configRepository;
    private final FenceRepository fenceRepository;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveConfig(PreciseDeliveryConfigCreateCommandInput commandInput) {
        PreciseDeliveryConfigCommandParam param = PreciseDeliveryConfigParamConverter.input2Param(commandInput);
        preciseDeliveryConfigValidator.validateConfig(param);
        param.create(UserInfoHolder.getUserRealName());
        preciseDeliveryConfigDomainService.saveConfig(param);

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void editConfig(PreciseDeliveryConfigUpdateCommandInput commandInput) {
        PreciseDeliveryConfigCommandParam param = PreciseDeliveryConfigParamConverter.input2Param(commandInput);
        preciseDeliveryConfigValidator.validateConfig(param);
        //有效性校验
        PreciseDeliveryConfigEntity configEntity = preciseDeliveryConfigQueryRepository.query(commandInput.getConfigId());
        if (configEntity == null) {
            throw new BizException("无效精准送配置");
        }
        param.update(UserInfoHolder.getUserRealName());
        preciseDeliveryConfigDomainService.editConfig(param);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeConfig(PreciseDeliveryConfigRemoveCommandInput commandInput) {
        //有效性校验
        PreciseDeliveryConfigEntity configEntity = preciseDeliveryConfigQueryRepository.query(commandInput.getConfigId());
        if (configEntity == null) {
            throw new BizException("无效精准送配置");
        }
        //有效性校验
        preciseDeliveryConfigDomainService.removeConfig(commandInput.getConfigId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void dataInit() {
        //查询原先精准送已配置的运营服务区域
        LocalTime local08 = LocalTime.parse("08:00");
        LocalTime local09 = LocalTime.parse("09:00");
        LocalTime local10 = LocalTime.parse("10:00");
        LocalTime local11 = LocalTime.parse("11:00");
        LocalTime local12 = LocalTime.parse("12:00");
        LocalTime local14 = LocalTime.parse("14:00");
        PreciseDeliveryConfigTimeCommandParam param08_09 = new PreciseDeliveryConfigTimeCommandParam();
        param08_09.setBeginTime(local08);
        param08_09.setEndTime(local09);
        PreciseDeliveryConfigTimeCommandParam param09_10 = new PreciseDeliveryConfigTimeCommandParam();
        param09_10.setBeginTime(local09);
        param09_10.setEndTime(local10);
        PreciseDeliveryConfigTimeCommandParam param10_11 = new PreciseDeliveryConfigTimeCommandParam();
        param10_11.setBeginTime(local10);
        param10_11.setEndTime(local11);
        PreciseDeliveryConfigTimeCommandParam param11_12 = new PreciseDeliveryConfigTimeCommandParam();
        param11_12.setBeginTime(local11);
        param11_12.setEndTime(local12);
        PreciseDeliveryConfigTimeCommandParam param12_14 = new PreciseDeliveryConfigTimeCommandParam();
        param12_14.setBeginTime(local12);
        param12_14.setEndTime(local14);
        List<PreciseDeliveryConfigTimeCommandParam> timeFrameParam = Lists.newArrayList(param08_09, param09_10, param10_11, param11_12, param12_14);
        List<PreciseDeliveryConfigTimeCommandParam> notSupportTenTimeFrameParam = Lists.newArrayList(param10_11, param11_12, param12_14);

        ConfigEntity timeFrames = configRepository.queryByKey("timeFrames");
        ConfigEntity notSupportTenTimeFrames = configRepository.queryByKey("notSupportTenTimeFrames");
        if (timeFrames != null && StrUtil.isNotBlank(timeFrames.getValue())){
            List<Integer> areaNos = Arrays.stream(timeFrames.getValue().split(AppConsts.Symbol.COMMA)).map(Integer::parseInt).collect(Collectors.toList());

            List<Integer> notSupportTenTimeFramesAreaNos = Lists.newArrayList();
            if (notSupportTenTimeFrames != null && StrUtil.isNotBlank(notSupportTenTimeFrames.getValue())){
                notSupportTenTimeFramesAreaNos = Arrays.stream(notSupportTenTimeFrames.getValue().split(AppConsts.Symbol.COMMA)).map(Integer::parseInt).collect(Collectors.toList());
                this.initConfigByAreaNos(notSupportTenTimeFramesAreaNos, notSupportTenTimeFrameParam);
            }
            List<Integer> supportTenTimeFramesAreaNos = Lists.newArrayList();
            //已知测试运营服务区域 写死
            List<Integer> testAreaNos = Lists.newArrayList(44222);
            for (Integer areaNo : areaNos) {
                if (notSupportTenTimeFramesAreaNos.contains(areaNo)){
                    continue;
                }
                //测试运营服务区域不进行初始化
                if (testAreaNos.contains(areaNo)){
                    continue;
                }
                supportTenTimeFramesAreaNos.add(areaNo);
            }
            log.info("初始化精准送配置默认运营服务区参数列表：{},包含10点以前运营服务区参数列表：{},10点以后运营服务区参数列表：{}",
                    JSON.toJSONString(areaNos), JSON.toJSONString(supportTenTimeFramesAreaNos), JSON.toJSONString(notSupportTenTimeFrameParam));
            this.initConfigByAreaNos(supportTenTimeFramesAreaNos, timeFrameParam);

        }


    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void initConfigByAreaNos(PreciseDeliveryConfigInitCommandInput commandInput) {
        List<PreciseDeliveryConfigTimeCommandParam> timeList = commandInput.getTimeList().stream().map(PreciseDeliveryConfigParamConverter::timeInput2Param).collect(Collectors.toList());
        this.initConfigByAreaNos(commandInput.getAreaNos(), timeList);
    }

    private void initConfigByAreaNos(List<Integer> areaNos, List<PreciseDeliveryConfigTimeCommandParam> timeFrameParam) {
        if (CollectionUtils.isEmpty(areaNos)){
            throw new BizException("运营服务区域编号不能为空");
        }
        if (CollectionUtils.isEmpty(timeFrameParam)){
            throw new BizException("精准送配置时间段不能为空");
        }
        //根据运营服务区域编号查询运营服务区域所覆盖围栏对应的行政区域信息
        List<FenceEntity> fenceEntities = fenceRepository.queryListWithArea(FenceQuery.builder().areaNos(areaNos).status(FenceEnums.Status.VALID.getValue()).build());
        if (CollectionUtils.isEmpty(fenceEntities)){
            throw new BizException("运营服务区域无匹配围栏");
        }
        List<AdCodeMsgEntity> adCodeMsgEntities = fenceEntities.stream().map(FenceEntity::getAdCodeMsgEntities).flatMap(Collection::stream).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(adCodeMsgEntities)){
            throw new BizException("运营服务区域无匹配围栏对应行政区域");
        }
        Map<String/*province#city*/, List<AdCodeMsgEntity>> cityAreaMap = adCodeMsgEntities.stream()
                .collect(Collectors.groupingBy(e -> e.getProvince() + AppConsts.Symbol.HASH_TAG + e.getCity()));
        List<PreciseDeliveryConfigCommandParam> paramList = Lists.newArrayList();
        for (Map.Entry<String, List<AdCodeMsgEntity>> entry : cityAreaMap.entrySet()) {
            String[] keys = Optional.ofNullable(entry.getKey()).orElse("").split(AppConsts.Symbol.HASH_TAG);
            String province = keys[0];
            String city = keys[1];
            List<PreciseDeliveryConfigAreaCommandParam> areaList = Optional.ofNullable(entry.getValue()).orElse(Lists.newArrayList())
                    .stream().map(e -> {
                        PreciseDeliveryConfigAreaCommandParam areaParam = new PreciseDeliveryConfigAreaCommandParam();
                        areaParam.setAdCode(e.getAdCode());
                        areaParam.setCity(e.getCity());
                        areaParam.setArea(e.getArea());
                        areaParam.setCreateTime(LocalDateTime.now());
                        return areaParam;
                    }).collect(Collectors.toList());
            PreciseDeliveryConfigCommandParam param = new PreciseDeliveryConfigCommandParam();
            param.setProvince(province);
            param.setCity(city);
            param.setAreaList(areaList);
            param.setTimeList(timeFrameParam);
            param.setCreator("初始化");
            param.setCreateTime(LocalDateTime.now());
            paramList.add(param);
        }
        log.info("初始化精准送配置参数列表：{}", paramList);
        for (PreciseDeliveryConfigCommandParam param : paramList) {
            preciseDeliveryConfigValidator.validateConfig(param);
            preciseDeliveryConfigDomainService.saveConfig(param);
        }
    }
}
