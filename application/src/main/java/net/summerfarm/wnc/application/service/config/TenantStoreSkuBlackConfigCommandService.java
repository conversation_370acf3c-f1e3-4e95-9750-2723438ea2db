package net.summerfarm.wnc.application.service.config;

import net.summerfarm.wnc.domain.config.entity.TenantStoreSkuBlackConfigEntity;
import net.summerfarm.wnc.application.inbound.controller.config.input.command.WncTenantStoreSkuBlackConfigCommandInput;


/**
 * @date 2024-09-12 14:00:16
 * @version 1.0
 */
public interface TenantStoreSkuBlackConfigCommandService {

    /**
     * @description: 新增
     * @return WncTenantStoreSkuBlackConfigEntity
     **/
    TenantStoreSkuBlackConfigEntity insert(WncTenantStoreSkuBlackConfigCommandInput input);


    /**
     * @description: 更新
     * @return:
     **/
    int update(WncTenantStoreSkuBlackConfigCommandInput input);



    /**
    * @description: 删除
    * @return:
    **/
    int delete(Long id);

}