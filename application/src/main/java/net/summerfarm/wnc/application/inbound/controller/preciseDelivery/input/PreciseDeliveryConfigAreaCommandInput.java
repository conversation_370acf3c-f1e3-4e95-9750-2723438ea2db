package net.summerfarm.wnc.application.inbound.controller.preciseDelivery.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * Description:精准送配置区域命令实体
 * date: 2024/1/18 18:40
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PreciseDeliveryConfigAreaCommandInput implements Serializable {

    private static final long serialVersionUID = -3105017788633073589L;

    /**
     * 区域编码
     */
    @NotBlank(message = "区域编码不能为空")
    private String adCode;

    /**
     * 市
     */
    @NotBlank(message = "市不能为空")
    private String city;

    /**
     * 区
     */
    private String area;
}
