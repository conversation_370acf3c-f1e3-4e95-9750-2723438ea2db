package net.summerfarm.wnc.application.changeTask.converter;

import net.summerfarm.wnc.api.changeTask.dto.FenceChangeTaskOrderDTO;
import net.summerfarm.wnc.common.enums.FenceChangeTaskDetailEnums;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskOrderEntity;
import net.summerfarm.wnc.facade.ofc.dto.FulfillmentOrderDTO;

/**
 * Description:围栏切仓任务订单明细转换器
 * date: 2023/8/25 17:38
 *
 * <AUTHOR>
 */
public class FenceChangeTaskOrderConverter {

    public static FenceChangeTaskOrderDTO entity2DTO(FenceChangeTaskOrderEntity fenceChangeTaskOrderEntity){
        if(fenceChangeTaskOrderEntity == null){
            return null;
        }
        FenceChangeTaskOrderDTO fenceChangeTaskOrderDTO = new FenceChangeTaskOrderDTO();
        fenceChangeTaskOrderDTO.setId(fenceChangeTaskOrderEntity.getId());
        fenceChangeTaskOrderDTO.setTaskId(fenceChangeTaskOrderEntity.getTaskId());
        fenceChangeTaskOrderDTO.setOuterOrderId(fenceChangeTaskOrderEntity.getOuterOrderId());
        fenceChangeTaskOrderDTO.setSource(fenceChangeTaskOrderEntity.getSource().getValue());
        fenceChangeTaskOrderDTO.setSourceDesc(fenceChangeTaskOrderEntity.getSource().getContent());
        fenceChangeTaskOrderDTO.setOuterContactId(fenceChangeTaskOrderEntity.getOuterContactId());
        fenceChangeTaskOrderDTO.setDeliveryTime(fenceChangeTaskOrderEntity.getDeliveryTime());
        fenceChangeTaskOrderDTO.setOuterClientId(fenceChangeTaskOrderEntity.getOuterClientId());
        fenceChangeTaskOrderDTO.setOuterClientName(fenceChangeTaskOrderEntity.getOuterClientName());
        fenceChangeTaskOrderDTO.setStatus(fenceChangeTaskOrderEntity.getStatus().getValue());
        fenceChangeTaskOrderDTO.setStatusDesc(fenceChangeTaskOrderEntity.getStatus().getContent());
        fenceChangeTaskOrderDTO.setRemark(fenceChangeTaskOrderEntity.getRemark());
        fenceChangeTaskOrderDTO.setFulfillConfirmTime(fenceChangeTaskOrderEntity.getFulfillConfirmTime());
        fenceChangeTaskOrderDTO.setCreateTime(fenceChangeTaskOrderEntity.getCreateTime());
        fenceChangeTaskOrderDTO.setUpdateTime(fenceChangeTaskOrderEntity.getUpdateTime());
        return fenceChangeTaskOrderDTO;
    }

    public static FenceChangeTaskOrderEntity dto2Entity(FulfillmentOrderDTO fulfillmentOrderDTO){
        if(fulfillmentOrderDTO == null){
            return null;
        }
        FenceChangeTaskOrderEntity fenceChangeTaskOrderEntity = new FenceChangeTaskOrderEntity();
        fenceChangeTaskOrderEntity.setOuterOrderId(fulfillmentOrderDTO.getOuterOrderId());
        fenceChangeTaskOrderEntity.setSource(FenceChangeTaskDetailEnums.Source.getSourceByValue(fulfillmentOrderDTO.getSource()));
        fenceChangeTaskOrderEntity.setOuterContactId(fulfillmentOrderDTO.getOuterContactId());
        fenceChangeTaskOrderEntity.setDeliveryTime(fulfillmentOrderDTO.getDeliveryTime());
        fenceChangeTaskOrderEntity.setOuterClientId(fulfillmentOrderDTO.getOuterClientId());
        fenceChangeTaskOrderEntity.setOuterClientName(fulfillmentOrderDTO.getOuterClientName());
        fenceChangeTaskOrderEntity.setFulfillConfirmTime(fulfillmentOrderDTO.getFulfillConfirmTime());
        return fenceChangeTaskOrderEntity;
    }
}
