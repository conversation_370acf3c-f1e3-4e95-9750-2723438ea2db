package net.summerfarm.wnc.application.inbound.controller.fence.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/4/15 14:11<br/>
 *
 * <AUTHOR> />
 */
@Data
public class FenceDeliveryRuleVO implements Serializable {
    /**
     * primary key
     */
    private Long id;
    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 配送规则 0 距离最短 1手动设置优先级
     */
    private Integer deliveryRule;

    /**
     * 仓库优先级JSON规则
     */
    private List<ConflictWarehouseJsonVO> conflictWarehouseJsonVOList;
    /**
     * 最后操作人名称
     */
    private String lastOperatorName;
    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;
}
