package net.summerfarm.wnc.application.fence;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.common.constants.AppConsts;
import net.summerfarm.wnc.common.enums.WncContactDeliveryRuleEnums;
import net.summerfarm.wnc.common.query.deliveryRule.ContactDeliveryRuleQuery;
import net.summerfarm.wnc.domain.ConfigRepository;
import net.summerfarm.wnc.domain.deliveryRule.ContactDeliveryRuleRepository;
import net.summerfarm.wnc.domain.deliveryRule.entity.ContactDeliveryRuleEntity;
import net.summerfarm.wnc.domain.fence.FenceDomainService;
import net.summerfarm.wnc.domain.fence.entity.AdCodeMsgEntity;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.summerfarm.wnc.domain.warehouse.WarehouseLogisticsCenterRepository;
import net.summerfarm.wnc.domain.warehouse.entity.ConfigEntity;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseLogisticsCenterEntity;
import net.summerfarm.wnc.facade.userCenter.UserCenterQueryFacade;
import net.summerfarm.wnc.facade.userCenter.dto.StoreAddressDTO;
import net.summerfarm.wnc.facade.userCenter.input.AddressInput;
import net.xianmu.common.exception.BizException;
import net.xianmu.robot.feishu.FeishuBotUtil;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Description:围栏消息发送器
 * date: 2023/12/4 17:27
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FenceSender {

    private final ConfigRepository configRepository;
    private final WarehouseLogisticsCenterRepository warehouseLogisticsCenterRepository;
    private final ContactDeliveryRuleRepository contactDeliveryRuleRepository;
    private final FenceDomainService fenceDomainService;
    private final UserCenterQueryFacade userCenterQueryFacade;

    public void sendDeliveryFrequentChangeMsg(FenceEntity editedFenceEntity) {
        WarehouseLogisticsCenterEntity logisticsCenter = warehouseLogisticsCenterRepository.queryByUk(editedFenceEntity.getStoreNo());
        String storeName = logisticsCenter == null ? "" : logisticsCenter.getStoreName();
        FenceEntity fenceEntity = fenceDomainService.queryDetail(editedFenceEntity.getId());
        if (fenceEntity == null){
            return;
        }
        List<AdCodeMsgEntity> fenceAreas = fenceEntity.getAdCodeMsgEntities();
        if (CollectionUtils.isEmpty(fenceAreas)){
            return;
        }
        //查询鲜沐定制化配送周期的客户地址
        List<ContactDeliveryRuleEntity> contactDeliveryRuleEntities = contactDeliveryRuleRepository.queryList(ContactDeliveryRuleQuery.builder().systemSource(WncContactDeliveryRuleEnums.SystemSource.XM.getValue()).build());
        if (CollectionUtils.isEmpty(contactDeliveryRuleEntities)){
            return;
        }
        List<Long> contactIds = contactDeliveryRuleEntities.stream().map(ContactDeliveryRuleEntity::getOutBusinessNo).map(Long::parseLong).collect(Collectors.toList());
        Optional<String> firstCity = fenceAreas.stream().map(AdCodeMsgEntity::getCity).findFirst();
        if (!firstCity.isPresent()){
            throw new BizException("围栏覆盖区域获取行政市异常");
        }
        List<String> areas = fenceAreas.stream().map(AdCodeMsgEntity::getArea).filter(StrUtil::isNotBlank).collect(Collectors.toList());
        List<StoreAddressDTO> storesWithSpecialRule = userCenterQueryFacade.queryFenceAreaStores(AddressInput.builder()
                .city(firstCity.get()).areas(areas).contactIds(contactIds).tenantId(AppConsts.Tenant.XM_TENANT_ID).status(1).build());
        if (CollectionUtils.isEmpty(storesWithSpecialRule)){
            return;
        }
        String contactInfoListStr = storesWithSpecialRule.stream().map(e -> String.format("%d-%s(%s)", e.getMId(), e.getMName(), e.getPhone())).collect(Collectors.joining(AppConsts.Symbol.COMMA));

        //机器人url
        ConfigEntity configEntity = configRepository.queryByKey(AppConsts.ConfigKey.CHANGE_FENCE_FREQUENT_CONTACT_NOTICE);
        if (configEntity == null || StrUtil.isBlank(configEntity.getValue())){
            log.error("configKey:[{}]未找到飞书消息发送url，请检查", AppConsts.ConfigKey.CHANGE_FENCE_FREQUENT_CONTACT_NOTICE);
            return;
        }

        StringBuilder textMsg = new StringBuilder();
        textMsg.append("围栏配送周期变更提醒").append("\n")
                .append("围栏编号：").append(editedFenceEntity.getId()).append("\n")
                .append("围栏名称：").append(editedFenceEntity.getFenceName()).append("\n")
                .append("配送周期：").append(editedFenceEntity.getFenceDeliveryEntity().getDeliveryFrequentStr()).append("\n")
                .append("城配仓：").append(storeName).append("\n")
                .append("定制化配送门店清单：").append(contactInfoListStr);
        //飞书消息通知
        FeishuBotUtil.sendMarkdownMsgAndAtAll(configEntity.getValue(),textMsg.toString());
    }
}
