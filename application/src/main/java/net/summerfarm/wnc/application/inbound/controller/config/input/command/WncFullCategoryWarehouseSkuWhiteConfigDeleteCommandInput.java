package net.summerfarm.wnc.application.inbound.controller.config.input.command;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-01-06 15:31:26
 */
@Data
public class WncFullCategoryWarehouseSkuWhiteConfigDeleteCommandInput implements Serializable {

    /**
     * primary key
     */
    @NotNull(message = "id不能为空")
    private Long id;

}