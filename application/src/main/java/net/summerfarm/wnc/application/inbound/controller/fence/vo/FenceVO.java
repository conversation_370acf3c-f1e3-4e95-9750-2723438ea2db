package net.summerfarm.wnc.application.inbound.controller.fence.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import net.summerfarm.wnc.api.fence.dto.FenceAreaDTO;
import net.summerfarm.wnc.api.fence.dto.FenceDeliveryDTO;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Description:围栏视图对象
 * date: 2023/10/26 19:00
 *
 * <AUTHOR>
 */
@Data
public class FenceVO implements Serializable {

    private static final long serialVersionUID = 104773376456273250L;

    /**
     * 围栏ID
     */
    private Integer fenceId;

    /**
     * 围栏名称
     */
    private String fenceName;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 运营服务区编号
     */
    private Integer areaNo;

    /**
     * 运营区域名称
     */
    private String areaName;

    /**
     * 城配仓名称
     */
    private String storeName;

    /**
     * 行政城市名称
     */
    private String cityName;

    /**
     * 类型，0：新建围栏，1：围栏拆分
     */
    private Integer type;

    /**
     * 类型描述
     */
    private String typeDesc;

    /**
     * 状态，0：正常，1：删除，3：暂停
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     *  是否在切仓中
     */
    private Boolean changeStatus;

    /**
     * 围栏区域信息
     */
    private List<FenceAreaDTO> fenceAreaDTOList;

    /**
     * 配送周期信息
     */
    private FenceDeliveryDTO fenceDeliveryDTO;

    /**
     * 下单渠道类型逗号分隔 1000鲜沐平台客户 2000鲜沐大客户 3000 Saas客户
     */
    private String orderChannelType;

    /**
     * 渠道白名单信息
     */
    private List<FenceChannelBusinessWhiteConfigVO> fenceChannelBusinessWhiteConfigVOS;
}
