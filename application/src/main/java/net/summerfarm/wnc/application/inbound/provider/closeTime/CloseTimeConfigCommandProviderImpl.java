package net.summerfarm.wnc.application.inbound.provider.closeTime;

import net.summerfarm.wnc.application.inbound.converter.closeTime.CloseTimeAreaConfigParamConverter;
import net.summerfarm.wnc.client.provider.closeTime.CloseTimeConfigCommandProvider;
import net.summerfarm.wnc.client.req.closeTime.CloseTimeAreaConfigAdjustBatchCommandReq;
import net.summerfarm.wnc.domain.closeTime.CloseTimeConfigCommandDomainService;
import net.summerfarm.wnc.domain.closeTime.param.CloseTimeAreaConfigCommandParam;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description:截单配置操作提供者实现类
 * date: 2024/3/20 10:21
 *
 * <AUTHOR>
 */
@DubboService
public class CloseTimeConfigCommandProviderImpl implements CloseTimeConfigCommandProvider {

    @Resource
    private CloseTimeConfigCommandDomainService closeTimeConfigCommandDomainService;

    @Override
    public DubboResponse<Void> batchAdjustCloseTimeAreaConfig(@Valid CloseTimeAreaConfigAdjustBatchCommandReq batchCommandReq) {
        List<CloseTimeAreaConfigCommandParam> commandParams = batchCommandReq.getCommandReqs().stream().map(CloseTimeAreaConfigParamConverter::req2Param).collect(Collectors.toList());
        closeTimeConfigCommandDomainService.editAreaConfig(commandParams);
        return DubboResponse.getOK();
    }
}
