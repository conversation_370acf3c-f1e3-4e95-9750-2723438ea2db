package net.summerfarm.wnc.application.inbound.controller.fence.converter;

import net.summerfarm.wnc.api.fence.dto.ConflictWarehouseJsonDTO;
import net.summerfarm.wnc.api.fence.dto.WncWarehouseStorageFenceRuleDTO;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.ConflictWarehouseJsonVO;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.FenceDeliveryRuleVO;
import org.apache.commons.collections.CollectionUtils;

import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/4/15 14:15<br/>
 *
 * <AUTHOR> />
 */
public class FenceDeliveryRuleVOConverter {
    public static FenceDeliveryRuleVO dto2Vo(WncWarehouseStorageFenceRuleDTO wncWarehouseStorageFenceRuleDTO) {
        if(wncWarehouseStorageFenceRuleDTO == null){
            return null;
        }
        FenceDeliveryRuleVO fenceDeliveryRuleVO = new FenceDeliveryRuleVO();
        fenceDeliveryRuleVO.setId(wncWarehouseStorageFenceRuleDTO.getId());
        fenceDeliveryRuleVO.setProvince(wncWarehouseStorageFenceRuleDTO.getProvince());
        fenceDeliveryRuleVO.setCity(wncWarehouseStorageFenceRuleDTO.getCity());
        fenceDeliveryRuleVO.setArea(wncWarehouseStorageFenceRuleDTO.getArea());
        fenceDeliveryRuleVO.setDeliveryRule(wncWarehouseStorageFenceRuleDTO.getDeliveryRule());
        if(!CollectionUtils.isEmpty(wncWarehouseStorageFenceRuleDTO.getConflictWarehouseJsonDTOList())){
            fenceDeliveryRuleVO.setConflictWarehouseJsonVOList(wncWarehouseStorageFenceRuleDTO.getConflictWarehouseJsonDTOList().stream()
                    .map(FenceDeliveryRuleVOConverter::conflictDTO2VO).collect(Collectors.toList()));
        }
        fenceDeliveryRuleVO.setLastOperatorName(wncWarehouseStorageFenceRuleDTO.getLastOperatorName());
        fenceDeliveryRuleVO.setCreateTime(wncWarehouseStorageFenceRuleDTO.getCreateTime());
        fenceDeliveryRuleVO.setUpdateTime(wncWarehouseStorageFenceRuleDTO.getUpdateTime());
        return fenceDeliveryRuleVO;
    }

    public static ConflictWarehouseJsonVO conflictDTO2VO(ConflictWarehouseJsonDTO conflictWarehouseJsonDTO){
        if(conflictWarehouseJsonDTO == null){
            return null;
        }
        ConflictWarehouseJsonVO conflictWarehouseJsonVO = new ConflictWarehouseJsonVO();
        conflictWarehouseJsonVO.setWarehouseNo(conflictWarehouseJsonDTO.getWarehouseNo());
        conflictWarehouseJsonVO.setWarehouseName(conflictWarehouseJsonDTO.getWarehouseName());
        conflictWarehouseJsonVO.setTenantId(conflictWarehouseJsonDTO.getTenantId());
        return conflictWarehouseJsonVO;
    }
}
