package net.summerfarm.wnc.application.inbound.provider.warehouseMapping.converter;

import net.summerfarm.wnc.client.resp.warehouseMapping.WarehouseThirdMappingResp;
import net.summerfarm.wnc.domain.warehouseMapping.entity.WncWarehouseThirdMappingEntity;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: 转换类
 * date: 2025/6/11 15:21<br/>
 *
 * <AUTHOR> />
 */
public class WarehouseThirdMappingDubboConverter {

    public static WarehouseThirdMappingResp convert(List<WncWarehouseThirdMappingEntity> entitys) {
        if(CollectionUtils.isEmpty(entitys)){
            return new WarehouseThirdMappingResp();
        }
        WarehouseThirdMappingResp warehouseThirdMappingResp = new WarehouseThirdMappingResp();

        warehouseThirdMappingResp.setWarehouseThirdMappingList(entitys.stream()
                .map(WarehouseThirdMappingDubboConverter::mappingConverter)
                .collect(Collectors.toList()));

        return warehouseThirdMappingResp;
    }

    public static WarehouseThirdMappingResp.WarehouseThirdMapping mappingConverter(WncWarehouseThirdMappingEntity entity) {
        if(entity == null){
            return null;
        }
        WarehouseThirdMappingResp.WarehouseThirdMapping warehouseThirdMapping = new WarehouseThirdMappingResp.WarehouseThirdMapping();
        warehouseThirdMapping.setThirdWarehouseNo(entity.getThirdWarehouseNo());
        warehouseThirdMapping.setOpenPlatformAppKey(entity.getOpenPlatformAppKey());
        warehouseThirdMapping.setWarehouseNo(entity.getWarehouseNo());
        warehouseThirdMapping.setTenantId(entity.getTenantId());
        warehouseThirdMapping.setThirdSourceName(entity.getThirdSourceName());

        return warehouseThirdMapping;
    }
}
