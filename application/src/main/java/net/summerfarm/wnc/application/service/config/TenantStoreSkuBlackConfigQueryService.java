package net.summerfarm.wnc.application.service.config;


import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.domain.config.entity.TenantStoreSkuBlackConfigEntity;
import net.summerfarm.wnc.application.inbound.controller.config.input.query.WncTenantStoreSkuBlackConfigQueryInput;

/**
 *
 * @date 2024-09-12 14:00:16
 * @version 1.0
 *
 */
public interface TenantStoreSkuBlackConfigQueryService {

    /**
     * @description: 新增
     * @return WncTenantStoreSkuBlackConfigEntity
     **/
    PageInfo<TenantStoreSkuBlackConfigEntity> getPage(WncTenantStoreSkuBlackConfigQueryInput input);

    /**
     * @description: 更新
     * @return: java.lang.Boolean
     **/
    TenantStoreSkuBlackConfigEntity getDetail(Long id);

}