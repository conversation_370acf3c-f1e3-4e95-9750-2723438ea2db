package net.summerfarm.wnc.application.inbound.controller.preciseDelivery.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * Description:精准送配置删除命令实体
 * date: 2024/1/18 18:36
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PreciseDeliveryConfigRemoveCommandInput implements Serializable {

    private static final long serialVersionUID = -234087075453815100L;

    /**
     * 精准送配置ID
     */
    @NotNull(message = "精准送配置ID不能为空")
    private Long configId;
}
