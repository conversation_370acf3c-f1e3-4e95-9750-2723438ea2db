package net.summerfarm.wnc.application.path.converter;

import net.summerfarm.wnc.api.path.dto.SkuPathMappingDTO;
import net.summerfarm.wnc.domain.path.entity.PathConfigEntity;
import net.summerfarm.wnc.domain.path.entity.SkuPathMappingEntity;

/**
 * Description: <br/>
 * date: 2023/11/30 15:18<br/>
 *
 * <AUTHOR> />
 */
public class SkuPathMappingDTOConverter {

    public static SkuPathMappingEntity dto2Entity(SkuPathMappingDTO dto) {
        if(dto == null){
            return null;
        }
        SkuPathMappingEntity entity = new SkuPathMappingEntity();
        entity.setId(dto.getId());
        entity.setCreateTime(dto.getCreateTime());
        entity.setUpdateTime(dto.getUpdateTime());
        entity.setWarehouseNo(dto.getWarehouseNo());
        entity.setSku(dto.getSku());
        entity.setPathId(dto.getPathId());
        entity.setCreator(dto.getCreator());
        entity.setUpdater(dto.getUpdater());
        return entity;
    }
}
