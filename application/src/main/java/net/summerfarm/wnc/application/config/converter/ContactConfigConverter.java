package net.summerfarm.wnc.application.config.converter;

import net.summerfarm.wnc.api.config.dto.ContactConfigDTO;
import net.summerfarm.wnc.api.config.input.ContactConfigCreateCommandInput;
import net.summerfarm.wnc.api.config.input.ContactConfigUpdateCommandInput;
import net.summerfarm.wnc.api.fence.input.AddressQueryInput;
import net.summerfarm.wnc.common.enums.ContactConfigEnums;
import net.summerfarm.wnc.domain.config.entity.ContactConfigEntity;
import net.summerfarm.wnc.domain.config.param.command.ContactConfigCommandParam;
import net.summerfarm.wnc.domain.fence.param.AddressParam;

/**
 * Description:联系人配置实体转换器
 * date: 2023/9/22 18:42
 *
 * <AUTHOR>
 */
public class ContactConfigConverter {

    public static ContactConfigDTO entity2Dto(ContactConfigEntity contactConfigEntity){
        if (contactConfigEntity == null){
            return null;
        }
        ContactConfigDTO contactConfigDTO = new ContactConfigDTO();
        contactConfigDTO.setId(contactConfigEntity.getId());
        contactConfigDTO.setSource(contactConfigEntity.getSource().getValue());
        contactConfigDTO.setSourceDesc(contactConfigEntity.getSource().getContent());
        contactConfigDTO.setTenantId(contactConfigEntity.getTenantId());
        contactConfigDTO.setOuterContactId(contactConfigEntity.getOuterContactId());
        contactConfigDTO.setStoreNo(contactConfigEntity.getStoreNo());
        contactConfigDTO.setCreator(contactConfigEntity.getCreator());
        contactConfigDTO.setCreateTime(contactConfigEntity.getCreateTime());
        return contactConfigDTO;

    }

    public static ContactConfigEntity command2Entity(ContactConfigCreateCommandInput contactConfigCreateCommandInput){
        if (contactConfigCreateCommandInput == null){
            return null;
        }
        ContactConfigEntity contactConfigEntity = new ContactConfigEntity();
        contactConfigEntity.setSource(ContactConfigEnums.Source.getSourceByTenantId(contactConfigCreateCommandInput.getTenantId()));
        contactConfigEntity.setTenantId(contactConfigCreateCommandInput.getTenantId());
        contactConfigEntity.setOuterContactId(contactConfigCreateCommandInput.getOuterContactId());
        contactConfigEntity.setStoreNo(contactConfigCreateCommandInput.getStoreNo());
        return contactConfigEntity;

    }

    public static ContactConfigEntity updateCommand2Entity(ContactConfigUpdateCommandInput contactConfigUpdateCommandInput){
        if (contactConfigUpdateCommandInput == null){
            return null;
        }
        ContactConfigEntity contactConfigEntity = new ContactConfigEntity();
        contactConfigEntity.setSource(ContactConfigEnums.Source.getSourceByTenantId(contactConfigUpdateCommandInput.getTenantId()));
        contactConfigEntity.setTenantId(contactConfigUpdateCommandInput.getTenantId());
        contactConfigEntity.setOuterContactId(contactConfigUpdateCommandInput.getOuterContactId());
        contactConfigEntity.setStoreNo(contactConfigUpdateCommandInput.getStoreNo());
        return contactConfigEntity;

    }

    public static ContactConfigCommandParam updateCommand2Param(ContactConfigUpdateCommandInput contactConfigUpdateCommandInput){

        if (contactConfigUpdateCommandInput == null){
            return null;
        }
        ContactConfigCommandParam contactConfigCommandParam = new ContactConfigCommandParam();
        contactConfigCommandParam.setSource(ContactConfigEnums.Source.getSourceByTenantId(contactConfigUpdateCommandInput.getTenantId()));
        contactConfigCommandParam.setTenantId(contactConfigUpdateCommandInput.getTenantId());
        contactConfigCommandParam.setOuterContactId(contactConfigUpdateCommandInput.getOuterContactId());
        contactConfigCommandParam.setFulfillmentMethod(contactConfigUpdateCommandInput.getFulfillmentMethod());
        contactConfigCommandParam.setStoreNo(contactConfigUpdateCommandInput.getStoreNo());

        AddressQueryInput addressQuery = contactConfigUpdateCommandInput.getAddressQuery();

        AddressParam addressParam = new AddressParam();
        addressParam.setProvince(addressQuery.getProvince());
        addressParam.setCity(addressQuery.getCity());
        addressParam.setArea(addressQuery.getArea());
        addressParam.setAddress(addressQuery.getAddress());
        contactConfigCommandParam.setAddressParam(addressParam);
        return contactConfigCommandParam;


    }
}
