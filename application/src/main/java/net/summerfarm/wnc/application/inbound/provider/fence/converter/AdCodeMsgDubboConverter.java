package net.summerfarm.wnc.application.inbound.provider.fence.converter;

import net.summerfarm.wnc.client.resp.fence.AdCodeMsgResp;
import net.summerfarm.wnc.domain.fence.dataObject.AdCodeMsgFenceStoreDO;
import net.summerfarm.wnc.domain.fence.entity.AdCodeMsgEntity;

/**
 * Description: <br/>
 * date: 2024/11/15 10:43<br/>
 *
 * <AUTHOR> />
 */
public class AdCodeMsgDubboConverter {

    public static AdCodeMsgResp entity2Resp(AdCodeMsgEntity entity){
        if(entity == null){
            return null;
        }
        AdCodeMsgResp resp = new AdCodeMsgResp();

        resp.setId(entity.getId());
        resp.setAdCode(entity.getAdCode());
        resp.setProvince(entity.getProvince());
        resp.setCity(entity.getCity());
        resp.setArea(entity.getArea());
        resp.setLevel(entity.getLevel());
        resp.setGdId(entity.getGdId());
        resp.setAddTime(entity.getAddTime());
        resp.setUpdateTime(entity.getUpdateTime());
        resp.setStatus(entity.getStatus());
        resp.setFenceId(entity.getFenceId());

        return resp;
    }

    public static AdCodeMsgResp do2Resp(AdCodeMsgFenceStoreDO doObj){
        if(doObj == null){
            return null;
        }

        AdCodeMsgResp resp = new AdCodeMsgResp();
        resp.setId(doObj.getId());
        resp.setAdCode(doObj.getAdCode());
        resp.setProvince(doObj.getProvince());
        resp.setCity(doObj.getCity());
        resp.setArea(doObj.getArea());
        resp.setLevel(doObj.getLevel());
        resp.setGdId(doObj.getGdId());
        resp.setAddTime(doObj.getAddTime());
        resp.setUpdateTime(doObj.getUpdateTime());
        resp.setStatus(doObj.getStatus());
        resp.setFenceId(doObj.getFenceId());
        resp.setFenceName(doObj.getFenceName());
        resp.setFenceStatus(doObj.getFenceStatus());
        resp.setStoreNo(doObj.getStoreNo());
        resp.setStoreName(doObj.getStoreName());
        resp.setStoreStatus(doObj.getStoreStatus());
        return resp;
    }
}
