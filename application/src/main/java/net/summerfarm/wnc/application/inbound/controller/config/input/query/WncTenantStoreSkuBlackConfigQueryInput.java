package net.summerfarm.wnc.application.inbound.controller.config.input.query;

import lombok.Data;
import java.io.Serializable;
import net.xianmu.common.input.BasePageInput;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2024-09-12 14:00:16
 * @version 1.0
 *
 */
@Data
public class WncTenantStoreSkuBlackConfigQueryInput extends BasePageInput implements Serializable{
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 租户ID
	 */
	private Long tenantId;

	/**
	 * 城配仓编号
	 */
	private Integer storeNo;

	/**
	 * sku
	 */
	private String sku;



}