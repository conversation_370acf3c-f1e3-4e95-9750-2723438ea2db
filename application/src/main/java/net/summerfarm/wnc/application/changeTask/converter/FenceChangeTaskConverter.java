package net.summerfarm.wnc.application.changeTask.converter;

import net.summerfarm.wnc.api.changeTask.dto.FenceChangeRemarkDTO;
import net.summerfarm.wnc.api.changeTask.dto.FenceChangeTaskDTO;
import net.summerfarm.wnc.api.changeTask.input.FenceChangeTaskCommand;
import net.summerfarm.wnc.application.fence.converter.FenceAreaConverter;
import net.summerfarm.wnc.common.enums.FenceChangeTaskEnums;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskEntity;
import net.summerfarm.wnc.domain.changeTask.vo.FenceChangeRemarkVO;
import net.summerfarm.wnc.domain.fence.entity.AdCodeMsgEntity;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Description:围栏切仓任务转换器
 * date: 2023/8/25 17:38
 *
 * <AUTHOR>
 */
public class FenceChangeTaskConverter {

    public static FenceChangeTaskDTO entity2DTO(FenceChangeTaskEntity fenceChangeTaskEntity){
        if(fenceChangeTaskEntity == null){
            return null;
        }
        FenceChangeTaskDTO fenceChangeTaskDTO = new FenceChangeTaskDTO();
        fenceChangeTaskDTO.setId(fenceChangeTaskEntity.getId());
        fenceChangeTaskDTO.setFenceName(fenceChangeTaskEntity.getFenceName());
        fenceChangeTaskDTO.setFenceId(fenceChangeTaskEntity.getFenceId());
        fenceChangeTaskDTO.setType(fenceChangeTaskEntity.getType().getValue());
        fenceChangeTaskDTO.setTypeDesc(fenceChangeTaskEntity.getType().getContent());
        fenceChangeTaskDTO.setStatus(fenceChangeTaskEntity.getStatus().getValue());
        fenceChangeTaskDTO.setStatusDesc(fenceChangeTaskEntity.getStatus().getContent());
        fenceChangeTaskDTO.setAreaNo(fenceChangeTaskEntity.getAreaNo());
        fenceChangeTaskDTO.setTargetNo(fenceChangeTaskEntity.getTargetNo());

        List<AdCodeMsgEntity> adCodeMsgEntities = fenceChangeTaskEntity.getAdCodeMsgEntities();
        fenceChangeTaskDTO.setFenceAreaDTOList(adCodeMsgEntities.stream().map(FenceAreaConverter::entity2dto).collect(Collectors.toList()));
        fenceChangeTaskDTO.setFenceChangeRemarkDTO(remarkVo2DTO(fenceChangeTaskEntity.getFenceChangeRemarkVO()));
        fenceChangeTaskDTO.setExeTime(fenceChangeTaskEntity.getExeTime());
        fenceChangeTaskDTO.setCreateTime(fenceChangeTaskEntity.getCreateTime());
        fenceChangeTaskDTO.setUpdateTime(fenceChangeTaskEntity.getUpdateTime());
        fenceChangeTaskDTO.setCreatorId(fenceChangeTaskEntity.getCreatorId());
        fenceChangeTaskDTO.setCreator(fenceChangeTaskEntity.getCreator());
        fenceChangeTaskDTO.setUpdater(fenceChangeTaskEntity.getUpdater());
        return fenceChangeTaskDTO;
    }

    public static FenceChangeRemarkDTO remarkVo2DTO(FenceChangeRemarkVO fenceChangeRemarkVO){
        if (fenceChangeRemarkVO == null){
            return null;
        }
        FenceChangeRemarkDTO fenceChangeRemarkDTO = new FenceChangeRemarkDTO();
        fenceChangeRemarkDTO.setOldWarehouses(fenceChangeRemarkVO.getOldWarehouses());
        fenceChangeRemarkDTO.setNewWarehouses(fenceChangeRemarkVO.getNewWarehouses());
        fenceChangeRemarkDTO.setOldArea(fenceChangeRemarkVO.getOldArea());
        fenceChangeRemarkDTO.setNewArea(fenceChangeRemarkVO.getNewArea());
        fenceChangeRemarkDTO.setOldStore(fenceChangeRemarkVO.getOldStore());
        fenceChangeRemarkDTO.setNewStore(fenceChangeRemarkVO.getNewStore());
        fenceChangeRemarkDTO.setExeTime(fenceChangeRemarkVO.getExeTime());
        return fenceChangeRemarkDTO;
    }

    public static FenceChangeTaskEntity command2Entity(FenceChangeTaskCommand fenceChangeTaskCommand){
        if(fenceChangeTaskCommand == null){
            return null;
        }
        FenceChangeTaskEntity fenceChangeTaskEntity = new FenceChangeTaskEntity();
        fenceChangeTaskEntity.setFenceId(fenceChangeTaskCommand.getFenceId());
        fenceChangeTaskEntity.setType(FenceChangeTaskEnums.Type.getTypeByValue(fenceChangeTaskCommand.getType()));
        fenceChangeTaskEntity.setTargetNo(fenceChangeTaskCommand.getTargetNo());
        List<AdCodeMsgEntity> adCodeMsgEntities = fenceChangeTaskCommand.getChangeAcmIds().stream().map(e -> {
            AdCodeMsgEntity adCodeMsgEntity = new AdCodeMsgEntity();
            adCodeMsgEntity.setId(e);
            return adCodeMsgEntity;
        }).collect(Collectors.toList());
        fenceChangeTaskEntity.setAdCodeMsgEntities(adCodeMsgEntities);
//        fenceChangeTaskEntity.setStatus();
//        fenceChangeTaskEntity.setAreaNo();
//        fenceChangeTaskEntity.setFenceChangeRemarkVO();
//        fenceChangeTaskEntity.setExeTime();
//        fenceChangeTaskEntity.setCreateTime();
//        fenceChangeTaskEntity.setUpdateTime();
//        fenceChangeTaskEntity.setOperator();
//        fenceChangeTaskEntity.setCreator();
        return fenceChangeTaskEntity;
    }
}
