package net.summerfarm.wnc.application.service.preciseDelivery;

import net.summerfarm.wnc.application.inbound.controller.preciseDelivery.input.PreciseDeliveryConfigCreateCommandInput;
import net.summerfarm.wnc.application.inbound.controller.preciseDelivery.input.PreciseDeliveryConfigInitCommandInput;
import net.summerfarm.wnc.application.inbound.controller.preciseDelivery.input.PreciseDeliveryConfigRemoveCommandInput;
import net.summerfarm.wnc.application.inbound.controller.preciseDelivery.input.PreciseDeliveryConfigUpdateCommandInput;

/**
 * Description: 精准送配置操作服务
 * date: 2024/1/18 19:06
 *
 * <AUTHOR>
 */
public interface PreciseDeliveryConfigCommandService {

    /**
     * 创建精准送配置
     *
     * @param commandInput 命令
     */
    void saveConfig(PreciseDeliveryConfigCreateCommandInput commandInput);

    /**
     * 更新精准送配置
     *
     * @param commandInput 命令
     */
    void editConfig(PreciseDeliveryConfigUpdateCommandInput commandInput);

    /**
     * 删除精准送配置
     *
     * @param commandInput 命令
     */
    void removeConfig(PreciseDeliveryConfigRemoveCommandInput commandInput);

    /**
     * 精准送配置初始化
     */
    void dataInit();

    /**
     * 初始化精准送配置，兼容原先按运营服务区域配置的能力
     * @param commandInput 命令
     */
    void initConfigByAreaNos(PreciseDeliveryConfigInitCommandInput commandInput);
}
