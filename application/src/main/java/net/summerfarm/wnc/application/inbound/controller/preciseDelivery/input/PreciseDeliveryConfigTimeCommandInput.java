package net.summerfarm.wnc.application.inbound.controller.preciseDelivery.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * Description:精准送配置时效命令实体
 * date: 2024/1/18 18:40
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PreciseDeliveryConfigTimeCommandInput implements Serializable {

    private static final long serialVersionUID = -2355265285268619153L;

    /**
     * 开始时间
     */
    @NotBlank(message = "开始时间不能为空")
    private String beginTime;

    /**
     * 结束时间
     */
    @NotBlank(message = "结束时间不能为空")
    private String endTime;
}
