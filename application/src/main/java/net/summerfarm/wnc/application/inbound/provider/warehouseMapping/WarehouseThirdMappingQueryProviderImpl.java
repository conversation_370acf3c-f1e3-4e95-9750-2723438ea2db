package net.summerfarm.wnc.application.inbound.provider.warehouseMapping;

import net.summerfarm.wnc.application.inbound.provider.warehouseMapping.converter.WarehouseThirdMappingDubboConverter;
import net.summerfarm.wnc.client.provider.warehouseMapping.WarehouseThirdMappingQueryProvider;
import net.summerfarm.wnc.client.req.warehouseMapping.WarehouseThirdMappingQueryReq;
import net.summerfarm.wnc.client.resp.warehouseMapping.WarehouseThirdMappingResp;
import net.summerfarm.wnc.domain.warehouseMapping.entity.WncWarehouseThirdMappingEntity;
import net.summerfarm.wnc.domain.warehouseMapping.param.query.WncWarehouseThirdMappingQueryParam;
import net.summerfarm.wnc.domain.warehouseMapping.repository.WncWarehouseThirdMappingQueryRepository;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 *
 * @date 2025-06-11 15:08:31
 * @version 1.0
 *
 */
@DubboService
public class WarehouseThirdMappingQueryProviderImpl implements WarehouseThirdMappingQueryProvider {

    @Resource
    private WncWarehouseThirdMappingQueryRepository wncWarehouseThirdMappingQueryRepository;

    @Override
    public DubboResponse<WarehouseThirdMappingResp> queryWarehouseThirdMapping(@Valid WarehouseThirdMappingQueryReq req) {
        WncWarehouseThirdMappingQueryParam queryParam = new WncWarehouseThirdMappingQueryParam();
        queryParam.setWarehouseNo(req.getWarehouseNo());
        queryParam.setOpenPlatformAppKey(req.getOpenPlatformAppKey());
        queryParam.setTenantId(req.getTenantId());
        List<WncWarehouseThirdMappingEntity> wncWarehouseThirdMappingEntities = wncWarehouseThirdMappingQueryRepository.selectByCondition(queryParam);

        return DubboResponse.getOK(WarehouseThirdMappingDubboConverter.convert(wncWarehouseThirdMappingEntities));
    }
}
