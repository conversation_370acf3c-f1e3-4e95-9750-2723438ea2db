package net.summerfarm.wnc.application.fastmall.converter;

import net.summerfarm.wnc.api.fastmall.dto.WncFastMallDTO;
import net.summerfarm.wnc.domain.fastmall.entity.WncFastMallEntity;

/**
 * Description: <br/>
 * date: 2023/4/11 16:52<br/>
 *
 * <AUTHOR> />
 */
public class WncFastMallEntityConverter {

    public static WncFastMallDTO entity2DTO(WncFastMallEntity wncFastMallEntity){
        if(wncFastMallEntity == null){
            return null;
        }
        WncFastMallDTO wncFastMallDTO = new WncFastMallDTO();
        wncFastMallDTO.setId(wncFastMallEntity.getId());
        wncFastMallDTO.setName(wncFastMallEntity.getName());
        wncFastMallDTO.setCreateTime(wncFastMallEntity.getCreateTime());
        wncFastMallDTO.setUpdateTime(wncFastMallEntity.getUpdateTime());
        return wncFastMallDTO;
    }
}
