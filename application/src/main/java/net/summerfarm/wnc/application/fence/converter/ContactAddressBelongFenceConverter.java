package net.summerfarm.wnc.application.fence.converter;

import net.summerfarm.wnc.api.fence.dto.AddressBelongFenceDTO;
import net.summerfarm.wnc.domain.fence.entity.AddressBelongFenceEntity;

/**
 * Description:联系人地址归属围栏转换器
 * date: 2024/1/8 16:13
 *
 * <AUTHOR>
 */
public class ContactAddressBelongFenceConverter {

    public static AddressBelongFenceDTO entity2dto(AddressBelongFenceEntity addressBelongFenceEntity){
        if(addressBelongFenceEntity == null){
            return null;
        }
        AddressBelongFenceDTO addressBelongFenceDTO = new AddressBelongFenceDTO();
        addressBelongFenceDTO.setCity(addressBelongFenceEntity.getCity());
        addressBelongFenceDTO.setArea(addressBelongFenceEntity.getArea());
        addressBelongFenceDTO.setFenceDTO(FenceEntityConverter.entity2dto(addressBelongFenceEntity.getFenceEntity()));
        return addressBelongFenceDTO;
    }
}
