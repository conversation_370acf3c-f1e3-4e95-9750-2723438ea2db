package net.summerfarm.wnc.application.inbound.controller.fence;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.api.fence.dto.FenceAreaFilterDTO;
import net.summerfarm.wnc.api.fence.dto.FenceCityDTO;
import net.summerfarm.wnc.api.fence.dto.FenceDTO;
import net.summerfarm.wnc.api.fence.input.FenceEditCommand;
import net.summerfarm.wnc.api.fence.input.FenceSaveCommand;
import net.summerfarm.wnc.api.fence.input.FenceStatusCommand;
import net.summerfarm.wnc.api.fence.service.FenceService;
import net.summerfarm.wnc.api.base.BaseController;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.FenceChannelBusinessWhiteConfigVO;
import net.summerfarm.wnc.common.constants.RedisKeys;
import net.summerfarm.wnc.common.query.fence.FenceCityQuery;
import net.summerfarm.wnc.common.query.fence.FenceIdQuery;
import net.summerfarm.wnc.common.query.fence.FencePageQuery;
import net.summerfarm.wnc.application.inbound.controller.fence.converter.FenceVOConverter;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.FenceVO;
import net.summerfarm.wnc.domain.fence.entity.FenceChannelBusinessWhiteConfigEntity;
import net.summerfarm.wnc.domain.fence.param.query.FenceChannelBusinessWhiteConfigQueryParam;
import net.summerfarm.wnc.domain.fence.repository.FenceChannelBusinessWhiteConfigQueryRepository;
import net.xianmu.common.result.CommonResult;
import net.xianmu.redis.support.lock.annotation.XmLock;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description:围栏
 * date: 2023/10/26 17:08
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fence")
public class FenceController extends BaseController {

    @Resource
    private FenceService fenceService;
    @Resource
    private FenceChannelBusinessWhiteConfigQueryRepository fenceChannelBusinessWhiteConfigQueryRepository;

    /**
     * 分页查询围栏列表
     * @param fencePageQuery 查询
     * @return 结果
     */
    @PostMapping("/query/page")
    @RequiresPermissions(value = {"fence:select-list", "SUPER_ADMIN"}, logical = Logical.OR)
    public CommonResult<PageInfo<FenceVO>> queryPage(@RequestBody FencePageQuery fencePageQuery) {
        PageInfo<FenceVO> pageInfo = FenceVOConverter.dtoPage2VoPage(fenceService.queryPage(fencePageQuery));
        return CommonResult.ok(pageInfo);
    }

    /**
     * 查询围栏列表
     * 场景：截单围栏-编辑围栏-切仓任务
     * @return 结果
     */
    @PostMapping("/query/list")
    public CommonResult<List<FenceVO>> queryList() {
        List<FenceDTO> fenceDTOList = fenceService.queryList();
        return CommonResult.ok(fenceDTOList.stream().map(FenceVOConverter::dto2Vo).collect(Collectors.toList()));
    }



    /**
     * 查询围栏详情
     * @param fenceIdQuery 查询
     * @return 结果
     */
    @PostMapping("/query/detail")
    @RequiresPermissions(value = {"fence:select-detail", "SUPER_ADMIN"}, logical = Logical.OR)
    public CommonResult<FenceVO> queryDetail(@RequestBody @Validated FenceIdQuery fenceIdQuery) {
        FenceVO fenceVO = FenceVOConverter.dto2Vo(fenceService.queryDetail(fenceIdQuery));
        // 围栏渠道信息
        if(fenceVO != null){
            // 查询围栏渠道详情信息
            List<FenceChannelBusinessWhiteConfigEntity> fenceChannelBusinessWhiteConfigEntities = fenceChannelBusinessWhiteConfigQueryRepository.selectByCondition(FenceChannelBusinessWhiteConfigQueryParam.builder()
                    .fenceId(fenceVO.getFenceId())
                    .build());
            fenceVO.setFenceChannelBusinessWhiteConfigVOS(FenceVOConverter.entityList2VOList(fenceChannelBusinessWhiteConfigEntities));
        }

        return CommonResult.ok(fenceVO);
    }

    /**
     * 新增围栏
     * @param fenceSaveCommand 命令
     * @return 结果
     */
    @PostMapping("/upsert/save")
    @RequiresPermissions(value = {"fence:save", "SUPER_ADMIN"}, logical = Logical.OR)
    @XmLock(prefixKey = RedisKeys.GLOBAL_FENCE_CREATE, key = "(pack_id)", message = "操作频繁，请稍后重试")
    public CommonResult<Void> saveFence(@RequestBody @Validated FenceSaveCommand fenceSaveCommand) {
        fenceService.saveFence(fenceSaveCommand);
        return CommonResult.ok();
    }

    /**
     * 编辑围栏
     * @param fenceEditCommand 命令
     * @return 结果
     */
    @PostMapping("/upsert/edit")
    @RequiresPermissions(value = {"area-manage:update", "SUPER_ADMIN"}, logical = Logical.OR)
    @XmLock(prefixKey = RedisKeys.GLOBAL_FENCE_UPDATE, key = "{fenceEditCommand.fenceId}", message = "操作频繁，请稍后重试")
    public CommonResult<Void> editFence(@RequestBody @Validated FenceEditCommand fenceEditCommand) {
        fenceService.editFence(fenceEditCommand);
        return CommonResult.ok();
    }

    /**
     * 更新围栏状态
     * @param fenceStatusCommand 命令
     * @return 结果
     */
    @PostMapping("/upsert/change-status")
    @RequiresPermissions(value = {"area-manage:update", "SUPER_ADMIN"}, logical = Logical.OR)
    public CommonResult<Void> changeFenceStatus(@RequestBody @Validated FenceStatusCommand fenceStatusCommand) {
        fenceService.changeFenceStatus(fenceStatusCommand);
        return CommonResult.ok();
    }

    /**
     * 查询有效围栏信息
     * 场景：订单管理-订单操作-根据围栏指定区域查询订单
     * @return 结果
     */
    @PostMapping("/query/valid-list")
    public CommonResult<List<FenceAreaFilterDTO>> queryValidList() {
        return CommonResult.ok(fenceService.queryValidList());
    }

    /**
     * 查询城市区域信息
     * 场景：仓配管理-截单围栏-新建围栏/编辑围栏-区/县筛选框的置灰数据
     * @return 结果
     */
    @PostMapping("/city-area/query/list")
    public CommonResult<List<FenceCityDTO>> queryCityAreaList(@RequestBody @Validated FenceCityQuery fenceCityQuery) {
        return CommonResult.ok(fenceService.queryCityAreaList(fenceCityQuery));
    }
}
