package net.summerfarm.wnc.application.inbound.controller.preciseDelivery.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Description:精准送配置区域详情视图对象
 * date: 2024/1/18 18:09
 *
 * <AUTHOR>
 */
@Data
public class PreciseDeliveryConfigAreaVO implements Serializable {

    private static final long serialVersionUID = -3439192238675922980L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 配置ID
     */
    private Long configId;

    /**
     * 区域编码
     */
    private String adCode;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
