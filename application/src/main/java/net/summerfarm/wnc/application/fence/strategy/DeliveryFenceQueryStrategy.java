package net.summerfarm.wnc.application.fence.strategy;

import net.summerfarm.wnc.client.enums.SourceEnum;
import net.summerfarm.wnc.common.query.fence.DeliveryFenceDateQuery;
import net.summerfarm.wnc.domain.fence.entity.OutLandContactEntity;

/**
 * <AUTHOR>
 * @Date 2023-06-20
 **/
public interface DeliveryFenceQueryStrategy {

	/**
	 * 获取来源
	 *
	 * @return
	 */
	SourceEnum getSource();


	/**
	 * 查询联系人
	 *
	 * @param query
	 * @return
	 */
	OutLandContactEntity getContact(DeliveryFenceDateQuery query);


}
