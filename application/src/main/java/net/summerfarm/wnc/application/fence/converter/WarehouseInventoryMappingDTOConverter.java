package net.summerfarm.wnc.application.fence.converter;

import net.summerfarm.wnc.api.fence.dto.WarehouseInventoryMappingDTO;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseInventoryMappingEntity;

/**
 * Description: <br/>
 * date: 2024/5/21 15:53<br/>
 *
 * <AUTHOR> />
 */
public class WarehouseInventoryMappingDTOConverter {

    public static WarehouseInventoryMappingDTO dto2Entity(WarehouseInventoryMappingEntity entity) {
        if(entity == null){
            return null;
        }
        WarehouseInventoryMappingDTO dto = new WarehouseInventoryMappingDTO();
        dto.setId(entity.getId());
        dto.setWarehouseNo(entity.getWarehouseNo());
        dto.setStoreNo(entity.getStoreNo());
        dto.setSku(entity.getSku());
        dto.setSaleLockQuantity(entity.getSaleLockQuantity());
        dto.setReserveQuantity(entity.getReserveQuantity());
        dto.setSupportReserved(entity.getSupportReserved());
        dto.setUpdater(entity.getUpdater());
        dto.setUpdateTime(entity.getUpdateTime());
        dto.setCreator(entity.getCreator());
        dto.setCreateTime(entity.getCreateTime());
        dto.setSubType(entity.getSubType());
        return dto;
    }
}
