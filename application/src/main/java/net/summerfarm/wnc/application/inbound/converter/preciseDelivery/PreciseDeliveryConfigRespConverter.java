package net.summerfarm.wnc.application.inbound.converter.preciseDelivery;

import net.summerfarm.wnc.client.resp.preciseDelivery.PreciseDeliveryConfigTimeResp;
import net.summerfarm.wnc.domain.preciseDelivery.valueObject.PreciseDeliveryConfigTimeValueObject;

/**
 * Description:精准送配置响应实体转换器
 * date: 2024/1/23 11:42
 *
 * <AUTHOR>
 */
public class PreciseDeliveryConfigRespConverter {

    public static PreciseDeliveryConfigTimeResp timeEntity2Resp(PreciseDeliveryConfigTimeValueObject valueObject){
        if (valueObject == null) {
            return null;
        }
        PreciseDeliveryConfigTimeResp preciseDeliveryConfigTimeResp = new PreciseDeliveryConfigTimeResp();
        preciseDeliveryConfigTimeResp.setBeginTime(valueObject.getBeginTime());
        preciseDeliveryConfigTimeResp.setEndTime(valueObject.getEndTime());
        return preciseDeliveryConfigTimeResp;
    }
}
