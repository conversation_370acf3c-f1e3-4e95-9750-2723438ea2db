package net.summerfarm.wnc.application.config;


import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.domain.config.entity.WncFullCategoryWarehouseSkuWhiteConfigEntity;
import net.summerfarm.wnc.domain.config.param.query.WncFullCategoryWarehouseSkuWhiteConfigQueryParam;
import net.summerfarm.wnc.application.inbound.controller.config.input.query.WncFullCategoryWarehouseSkuWhiteConfigQueryInput;

/**
 *
 * @date 2025-01-06 15:31:26
 * @version 1.0
 *
 */
public interface WncFullCategoryWarehouseSkuWhiteConfigQueryService {

    /**
     * @description: 新增
     * @return WncFullCategoryWarehouseSkuWhiteConfigEntity
     **/
    PageInfo<WncFullCategoryWarehouseSkuWhiteConfigEntity> getPage(WncFullCategoryWarehouseSkuWhiteConfigQueryInput input);

    /**
     * @description: 更新
     * @return: java.lang.Boolean
     **/
    WncFullCategoryWarehouseSkuWhiteConfigEntity getDetail(Long id);

}