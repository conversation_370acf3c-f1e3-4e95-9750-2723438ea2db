package net.summerfarm.wnc.application.inbound.controller.fence;

import cn.hutool.core.bean.BeanUtil;
import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.api.fence.dto.WncWarehouseStorageFenceRuleDTO;
import net.summerfarm.wnc.api.fence.input.FenceDeliveryRuleUpdateCommand;
import net.summerfarm.wnc.api.fence.input.GlobalFenceRuleUpdateCommand;
import net.summerfarm.wnc.api.fence.service.WncWarehouseStorageFenceRuleService;
import net.summerfarm.wnc.api.base.BaseController;
import net.summerfarm.wnc.common.query.fence.WncWarehouseStorageFenceRuleQuery;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.FenceDeliveryRuleVO;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.TenantGlobalFenceRuleVO;
import net.summerfarm.wnc.application.inbound.controller.fence.converter.FenceDeliveryRuleVOConverter;
import net.summerfarm.wnc.application.inbound.controller.fence.converter.TenantGlobalFenceRuleVOConverter;
import net.xianmu.common.result.CommonResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.stream.Collectors;

/**
 * 区域配送规则
 * <AUTHOR> />
 */
@RestController
@RequestMapping("/warehouse-fence-rule")
public class WarehouseStorageFenceRuleController extends BaseController {

    @Resource
    private WncWarehouseStorageFenceRuleService wncWarehouseStorageFenceRuleService;

    /**
     * 查询多仓覆盖运营服务区配送规则
     * @param wncWarehouseStorageFenceRuleQuery 查询
     * @return 结果
     */
    @PostMapping("/query/fence-delivery-rule")
    public CommonResult<PageInfo<FenceDeliveryRuleVO>> queryFenceDeliveryRule(@RequestBody WncWarehouseStorageFenceRuleQuery wncWarehouseStorageFenceRuleQuery) {
        wncWarehouseStorageFenceRuleQuery.setTenantId(getTenantId());
        PageInfo<WncWarehouseStorageFenceRuleDTO> wncWarehouseStorageFenceDTOS= wncWarehouseStorageFenceRuleService.queryFenceDeliveryRule(wncWarehouseStorageFenceRuleQuery);

        PageInfo<FenceDeliveryRuleVO> fenceDeliveryRuleVOPageInfo = new PageInfo();
        BeanUtil.copyProperties(wncWarehouseStorageFenceDTOS,fenceDeliveryRuleVOPageInfo);
        fenceDeliveryRuleVOPageInfo.setList(wncWarehouseStorageFenceDTOS.getList().stream().map(FenceDeliveryRuleVOConverter::dto2Vo).collect(Collectors.toList()));
        return CommonResult.ok(fenceDeliveryRuleVOPageInfo);
    }

    /**
     * 更新仓库的优先级等信息
     * @param fenceDeliveryRuleUpdateCommand 更新
     * @return 结果
     */
    @PostMapping("/upsert/fence-delivery-rule")
    public CommonResult<Void> updateFenceDeliveryRule(@RequestBody @Validated FenceDeliveryRuleUpdateCommand fenceDeliveryRuleUpdateCommand) {
        fenceDeliveryRuleUpdateCommand.setOperatorName(getBizUserName());
        wncWarehouseStorageFenceRuleService.updateFenceDeliveryRule(fenceDeliveryRuleUpdateCommand);
        return CommonResult.ok();
    }

    /**
     * 查询租户全局优先级
     * @return 结果
     */
    @PostMapping("/query/global-fence-rule")
    public CommonResult<TenantGlobalFenceRuleVO> queryGlobalFenceRule() {
        return CommonResult.ok(TenantGlobalFenceRuleVOConverter.dto2Vo(wncWarehouseStorageFenceRuleService.queryGlobalFenceRule(getTenantId())));
    }

    /**
     * 修改租户全局优先级
     * @param globalFenceRuleUpdateCommand 修改
     * @return 结果
     */
    @PostMapping("/upsert/global-fence-rule")
    public CommonResult<Void> updateGlobalFenceRule(@RequestBody @Validated GlobalFenceRuleUpdateCommand globalFenceRuleUpdateCommand) {
        globalFenceRuleUpdateCommand.setTenantId(getTenantId());
        wncWarehouseStorageFenceRuleService.updateGlobalFenceRule(globalFenceRuleUpdateCommand);
        return CommonResult.ok();
    }

}
