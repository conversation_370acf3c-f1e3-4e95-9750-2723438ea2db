package net.summerfarm.wnc.application.inbound.converter.closeTime;

import net.summerfarm.wnc.client.req.closeTime.CloseTimeAreaAdjustCommandReq;
import net.summerfarm.wnc.client.req.closeTime.CloseTimeAreaConfigQueryReq;
import net.summerfarm.wnc.client.resp.closeTime.CloseTimeAreaConfigResp;
import net.summerfarm.wnc.domain.closeTime.entity.CloseTimeAreaConfigEntity;
import net.summerfarm.wnc.domain.closeTime.param.CloseTimeAreaConfigCommandParam;
import net.summerfarm.wnc.domain.closeTime.param.CloseTimeAreaConfigQueryParam;

/**
 * Description:截单时间配置响应实体转换器
 * date: 2024/3/20 11:18
 *
 * <AUTHOR>
 */
public class CloseTimeAreaConfigRespConverter {

    public static CloseTimeAreaConfigResp entity2Resp(CloseTimeAreaConfigEntity entity){
        if (entity == null){
            return null;
        }
        CloseTimeAreaConfigResp closeTimeAreaConfigResp = new CloseTimeAreaConfigResp();
        closeTimeAreaConfigResp.setId(entity.getId());
        closeTimeAreaConfigResp.setTenantId(entity.getTenantId());
        closeTimeAreaConfigResp.setProvince(entity.getProvince());
        closeTimeAreaConfigResp.setCity(entity.getCity());
        closeTimeAreaConfigResp.setArea(entity.getArea());
        closeTimeAreaConfigResp.setCloseTime(entity.getCloseTime());
        closeTimeAreaConfigResp.setUpdateCloseTime(entity.getUpdateCloseTime());
        if (entity.getUpdateFlag() != null){
            closeTimeAreaConfigResp.setUpdateFlag(entity.getUpdateFlag().getValue());
        }
        closeTimeAreaConfigResp.setCreator(entity.getCreator());
        closeTimeAreaConfigResp.setCreateTime(entity.getCreateTime());
        closeTimeAreaConfigResp.setUpdater(entity.getUpdater());
        closeTimeAreaConfigResp.setUpdateTime(entity.getUpdateTime());
        return closeTimeAreaConfigResp;


    }

}
