package net.summerfarm.wnc.application.warehouse;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.warehouse.dto.SkuWarehouseMappingDTO;
import net.summerfarm.wnc.api.warehouse.service.warehouse.WncSkuWarehouseMappingService;
import net.summerfarm.wnc.application.warehouse.converter.WarehouseInventoryMappingEntityConverter;
import net.summerfarm.wnc.common.query.warehouse.SkuWarehouseMappingQuery;
import net.summerfarm.wnc.domain.warehouse.WarehouseInventoryMappingRepository;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseInventoryMappingEntity;
import net.summerfarm.wnc.facade.gc.dto.ProductSkuDTO;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:SKU库存仓映射接口实现类
 * date: 2023/9/4 11:33
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class WncSkuWarehouseMappingServiceImpl implements WncSkuWarehouseMappingService {

    @Resource
    private WarehouseInventoryMappingRepository warehouseInventoryMappingRepository;

    @Override
    public List<SkuWarehouseMappingDTO> querySkuWarehouseMappings(List<SkuWarehouseMappingQuery> skuWarehouseMappingQueries) {
        List<WarehouseInventoryMappingEntity> warehouseInventoryMappingEntities = warehouseInventoryMappingRepository.queryListBySkuStoreNo(skuWarehouseMappingQueries);
        return warehouseInventoryMappingEntities.stream().map(WarehouseInventoryMappingEntityConverter::entity2Dto).collect(Collectors.toList());
    }

    @Override
    public Map<String, Integer> queryMapping2SkuStoreNoMap(List<String> skus, Integer storeNo) {
        if(storeNo == null || CollectionUtils.isEmpty(skus)){
            log.error("queryMapping2SkuStoreNoMap storeNo or skus is null",new RuntimeException("请求参数非法"));
            return Collections.emptyMap();
        }

        //组装查询报文
        ArrayList<SkuWarehouseMappingQuery> skuWarehouseMappingQueries = new ArrayList<>();
        skus.forEach(sku -> {
            SkuWarehouseMappingQuery query = SkuWarehouseMappingQuery.builder()
                    .sku(sku)
                    .storeNo(storeNo).build();
            skuWarehouseMappingQueries.add(query);
        });

        //查询映射关系
        List<WarehouseInventoryMappingEntity> warehouseInventoryMappingEntities = warehouseInventoryMappingRepository.queryListBySkuStoreNo(skuWarehouseMappingQueries);
        warehouseInventoryMappingEntities = warehouseInventoryMappingEntities.stream().filter(mapping -> Objects.nonNull(mapping.getWarehouseNo())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(warehouseInventoryMappingEntities)){
            log.info("查询自营-代销不入仓商品对应仓库映射关系失败，请检查，请求数据为:{}", JSON.toJSONString(skuWarehouseMappingQueries));
            return Collections.emptyMap();
        }
        return warehouseInventoryMappingEntities.stream().collect(Collectors.toMap(mapping -> mapping.getSku() + "#" + mapping.getStoreNo(), WarehouseInventoryMappingEntity::getWarehouseNo, (oldData, newData) -> newData));
    }
}
