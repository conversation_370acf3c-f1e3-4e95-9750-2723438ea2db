package net.summerfarm.wnc.application.inbound.provider.warehouseMapping;

import net.summerfarm.wnc.api.fence.dto.WarehouseInventoryMappingDTO;
import net.summerfarm.wnc.api.fence.service.WarehouseInventoryMappingService;
import net.summerfarm.wnc.client.provider.warehouseMapping.WarehouseInventoryMappingQueryProvider;
import net.summerfarm.wnc.client.req.warehouseMapping.BatchMappingWarehouseQueryReq;
import net.summerfarm.wnc.client.req.warehouseMapping.SkuAreaQueryReq;
import net.summerfarm.wnc.client.resp.warehouseMapping.AreaWarehouseResp;
import net.summerfarm.wnc.client.resp.warehouseMapping.BatchMappingWarehouseResp;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Description: 地址仓库商品关系查询服务<br/>
 * date: 2024/4/10 16:42<br/>
 *
 * <AUTHOR> />
 */
@DubboService
public class WarehouseInventoryMappingQueryProviderImpl implements WarehouseInventoryMappingQueryProvider {
    @Resource
    private WarehouseInventoryMappingService warehouseInventoryMappingService;

    @Override
    public DubboResponse<AreaWarehouseResp> queryWarehouseBySkuAndAreaNo(@Valid SkuAreaQueryReq skuAreaQueryReq) {
        AreaWarehouseResp resp = new AreaWarehouseResp();
        resp.setWarehouseNos(warehouseInventoryMappingService.queryWarehouseBySkuAndAreaNo(skuAreaQueryReq.getSku(), skuAreaQueryReq.getAreaNo()));
        return DubboResponse.getOK(resp);
    }

    @Override
    public DubboResponse<List<BatchMappingWarehouseResp>> queryBatchWarehouseMapping(@Valid BatchMappingWarehouseQueryReq batchMappingWarehouseQueryReq) {
        Integer areaNo = batchMappingWarehouseQueryReq.getAreaNo();
        List<String> skuList = batchMappingWarehouseQueryReq.getSkuList();
        List<WarehouseInventoryMappingDTO> warehouseInventoryMappingDTOList = warehouseInventoryMappingService.queryBatchWarehouseMappingByAreaAndSkus(areaNo,skuList);

        Map<String, List<WarehouseInventoryMappingDTO>> areaNoSku2MappingMap = warehouseInventoryMappingDTOList.stream().collect(Collectors.groupingBy(mapping -> mapping.getAreaNo() + "#" + mapping.getSku()));
        List<BatchMappingWarehouseResp> respList = new ArrayList<>();
        areaNoSku2MappingMap.forEach((k, v) -> {
            BatchMappingWarehouseResp resp = new BatchMappingWarehouseResp();
            String[] areaNoSkuArray = k.split("#");

            resp.setAreaNo(Integer.parseInt(areaNoSkuArray[0]));
            resp.setSku(areaNoSkuArray[1]);
            resp.setWarehouseNos(v.stream().map(WarehouseInventoryMappingDTO::getWarehouseNo).distinct().collect(Collectors.toList()));

            respList.add(resp);
        });

        return DubboResponse.getOK(respList);
    }


}
