package net.summerfarm.wnc.application.fence;

import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.wnc.api.fence.dto.*;
import net.summerfarm.wnc.api.fence.input.FenceEditCommand;
import net.summerfarm.wnc.api.fence.input.FenceSaveCommand;
import net.summerfarm.wnc.api.fence.input.FenceStatusCommand;
import net.summerfarm.wnc.api.fence.service.FenceService;
import net.summerfarm.wnc.api.fence.service.WarehouseInventoryMappingService;
import net.summerfarm.wnc.application.fence.converter.FenceAreaConverter;
import net.summerfarm.wnc.application.fence.converter.FenceEntityConverter;
import net.summerfarm.wnc.common.constants.AppConsts;
import net.summerfarm.wnc.common.enums.AdCodeMsgEnums;
import net.summerfarm.wnc.common.enums.FenceChangeTaskEnums;
import net.summerfarm.wnc.common.enums.FenceEnums;
import net.summerfarm.wnc.common.query.fence.*;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskDomainService;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskEntity;
import net.summerfarm.wnc.domain.fence.*;
import net.summerfarm.wnc.domain.fence.entity.AdCodeMsgEntity;
import net.summerfarm.wnc.domain.fence.entity.AreaEntity;
import net.summerfarm.wnc.domain.fence.entity.FenceChannelBusinessWhiteConfigEntity;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.summerfarm.wnc.domain.fence.param.command.FenceChannelBusinessWhiteConfigCommandParam;
import net.summerfarm.wnc.domain.fence.repository.FenceChannelBusinessWhiteConfigCommandRepository;
import net.summerfarm.wnc.domain.fence.service.FenceChannelBusinessWhiteConfigCommandDomainService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.user.UserBase;
import net.xianmu.common.user.UserInfoHolder;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:鲜沐围栏接口实现
 * date: 2023/10/26 18:55
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FenceServiceImpl implements FenceService {

    private final FenceRepository fenceRepository;
    private final FenceChangeTaskDomainService fenceChangeTaskDomainService;
    private final FenceValidator fenceValidator;
    private final FenceDomainService fenceDomainService;
    private final WarehouseInventoryMappingService warehouseInventoryMappingService;
    private final AdCodeMsgRepository adCodeMsgRepository;
    private final FenceSender fenceSender;
    private final AreaRepository areaRepository;
    private final FenceChannelBusinessWhiteConfigCommandRepository fenceChannelBusinessWhiteConfigCommandRepository;
    private final FenceChannelBusinessWhiteConfigCommandDomainService fenceChannelBusinessWhiteConfigCommandDomainService;

    @Override
    public PageInfo<FenceDTO> queryPage(FencePageQuery fencePageQuery) {
        if (Objects.equals(fencePageQuery.getStatus(), FenceEnums.Status.STOP)){
            fencePageQuery.setStatus(null);
            fencePageQuery.setStatusList(Arrays.asList(FenceEnums.Status.INVALID.getValue(), FenceEnums.Status.STOP.getValue()));
        }
        PageInfo<FenceEntity> pageInfo = fenceRepository.queryPage(fencePageQuery);
        PageInfo<FenceDTO> dtoPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, dtoPageInfo);
        List<FenceEntity> fenceEntities = Optional.ofNullable(pageInfo.getList()).orElse(new ArrayList<>());
        dtoPageInfo.setList(fenceEntities.stream().map(FenceEntityConverter::entity2dto).collect(Collectors.toList()));
        return dtoPageInfo;
    }

    @Override
    public FenceDTO queryDetail(FenceIdQuery fenceIdQuery) {
        FenceEntity fenceEntity = fenceDomainService.queryDetail(fenceIdQuery.getFenceId());
        if (fenceEntity == null){
            throw new BizException("无效围栏信息");
        }
        FenceDTO fenceDTO = FenceEntityConverter.entity2dto(fenceEntity);
        //判断该围栏是否正在切仓 查询出待处理、区域切换中、订单切换中 围栏切仓任务
        List<FenceChangeTaskEntity> executingFenceChangeTasks = fenceChangeTaskDomainService.queryExecutingFenceChangeTasks(fenceEntity.getId());
        fenceDTO.setChangeStatus(CollectionUtils.isEmpty(executingFenceChangeTasks) ? Boolean.FALSE : Boolean.TRUE);
        //判断该围栏区域是否已发起预约切仓 只需判断待处理、区域切换中的围栏切仓任务即可，订单切换中已执行完区域切仓
        List<Integer> executingAreaIds = executingFenceChangeTasks.stream().filter(e -> !Objects.equals(e.getStatus(), FenceChangeTaskEnums.Status.ORDER_CHANGE_ING))
                .map(FenceChangeTaskEntity::getAdCodeMsgEntities).flatMap(Collection::stream)
                .map(AdCodeMsgEntity::getId).collect(Collectors.toList());
        //处理围栏区域是否可选状态
        if (!CollectionUtils.isEmpty(fenceDTO.getFenceAreaDTOList())){
            fenceDTO.getFenceAreaDTOList().forEach(e -> e.setSelectFlag(executingAreaIds.contains(e.getId()) ? Boolean.FALSE : Boolean.TRUE));
        }
        return fenceDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveFence(FenceSaveCommand fenceSaveCommand) {
        FenceEntity fenceEntity = FenceEntityConverter.saveCommand2entity(fenceSaveCommand);
        fenceValidator.validateFence(fenceEntity);
        //排序配送周期字段
        fenceEntity.getFenceDeliveryEntity().resortDeliveryFrequent();
        //获取打包编号
        Integer packId = fenceDomainService.queryFencePackId(fenceEntity);
        fenceEntity.setPackId(packId);
        //处理库存映射
        fenceDomainService.createInventoryMapping(fenceEntity);
        //获取操作人信息
        UserBase user = UserInfoHolder.getUser();
        fenceEntity.create(user.getBizUserId());
        fenceRepository.saveOrUpdate(fenceEntity);
        List<FenceChannelBusinessWhiteConfigEntity> businessWhiteConfigEntities = fenceEntity.getFenceChannelBusinessWhiteConfigEntities();
        if(!CollectionUtils.isEmpty(businessWhiteConfigEntities)){
            businessWhiteConfigEntities.forEach(e -> e.setFenceId(fenceEntity.getId()));
            fenceChannelBusinessWhiteConfigCommandDomainService.fenceBatchSaveOrUpdate(fenceEntity.getId(),businessWhiteConfigEntities);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editFence(FenceEditCommand fenceEditCommand) {
        FenceEntity editedFenceEntity = FenceEntityConverter.editCommand2entity(fenceEditCommand);
        FenceEntity existedFenceEntity = fenceDomainService.queryDetail(fenceEditCommand.getFenceId());
        if (existedFenceEntity == null){
            throw new BizException("无效围栏信息");
        }
        if (existedFenceEntity.isInValid()){
            throw new BizException("围栏已删除，无法进行操作");
        }
        List<FenceChannelBusinessWhiteConfigEntity> businessWhiteConfigEntities = editedFenceEntity.getFenceChannelBusinessWhiteConfigEntities();
        fenceValidator.validateFenceDelivery(editedFenceEntity.getFenceDeliveryEntity(), existedFenceEntity.getFenceDeliveryEntity().getNextDeliveryDate());
        fenceValidator.validateFenceEditArea(editedFenceEntity, existedFenceEntity);
        fenceValidator.validateFenceChannleType(editedFenceEntity.getOrderChannelType());
        fenceValidator.validateFenceChannelBusinessWhiteConfig(editedFenceEntity.getOrderChannelType(), businessWhiteConfigEntities);
        //排序配送周期字段
        editedFenceEntity.getFenceDeliveryEntity().resortDeliveryFrequent();
        //获取操作人信息
        UserBase user = UserInfoHolder.getUser();
        FenceEntity updateFenceEntity = existedFenceEntity.update(editedFenceEntity, user.getBizUserId());
        fenceRepository.saveOrUpdate(updateFenceEntity);
        // 更新渠道白名单配置
        if(!CollectionUtils.isEmpty(businessWhiteConfigEntities)){
            businessWhiteConfigEntities.forEach(e -> e.setFenceId(updateFenceEntity.getId()));
            fenceChannelBusinessWhiteConfigCommandDomainService.fenceBatchSaveOrUpdate(updateFenceEntity.getId(),businessWhiteConfigEntities);
        }
        //判断围栏配送周期是否发生变化
        if (existedFenceEntity.isDeliveryFrequentChanged(editedFenceEntity)){
            editedFenceEntity.setFenceName(existedFenceEntity.getFenceName());
            editedFenceEntity.setStoreNo(existedFenceEntity.getStoreNo());
            fenceSender.sendDeliveryFrequentChangeMsg(editedFenceEntity);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeFenceStatus(FenceStatusCommand fenceStatusCommand) {
        FenceEnums.Status editedStatus = FenceEnums.Status.getStatusByValue(fenceStatusCommand.getStatus());
        //围栏历史状态 现已不使用
        if (Objects.equals(editedStatus, FenceEnums.Status.INVALID)){
            throw new BizException("无效围栏状态");
        }
        FenceEntity existedFenceEntity = fenceDomainService.queryDetail(fenceStatusCommand.getFenceId());
        if (existedFenceEntity == null){
            throw new BizException("无效围栏信息");
        }
        if (existedFenceEntity.isInValid()){
            throw new BizException("围栏已删除，无法进行操作");
        }
        if(Objects.equals(editedStatus,FenceEnums.Status.VALID) && existedFenceEntity.isValid()){
            throw new BizException("围栏已启用");
        }
        if(Objects.equals(editedStatus,FenceEnums.Status.STOP) && existedFenceEntity.isStop()){
            throw new BizException("围栏已暂停");
        }
        //获取操作人信息
        UserBase user = UserInfoHolder.getUser();
        FenceEntity update = existedFenceEntity.changeStatus(editedStatus, user.getBizUserId());
        fenceRepository.changeStatus(update);
        //原围栏状态暂停的状态 现启用需补充围栏归属运用服务区暂停期间所上架的新品的库存使用关系
        if (Objects.equals(editedStatus, FenceEnums.Status.VALID) && existedFenceEntity.isStop()){
            warehouseInventoryMappingService.mappingHandleInit(existedFenceEntity.getAreaNo(),existedFenceEntity.getStoreNo(),existedFenceEntity.getPackId());
        }

    }

    @Override
    public List<FenceAreaFilterDTO> queryValidList() {
        //获取所有有效围栏信息
        List<FenceEntity> validFences = fenceRepository.queryList(FenceQuery.builder().status(FenceEnums.Status.VALID.getValue()).build());
        if (CollectionUtils.isEmpty(validFences)){
            return Collections.emptyList();
        }

        List<Integer> fenceIds = validFences.stream().map(FenceEntity::getId).collect(Collectors.toList());
        List<AdCodeMsgEntity> validFenceAreas = adCodeMsgRepository.queryList(AdCodeMsgQuery.builder()
                        .fenceIds(fenceIds).statusList(Lists.newArrayList(AdCodeMsgEnums.Status.VALID.getValue())).build());
        Map<Integer, List<AdCodeMsgEntity>> fenceAdCodeMsgMap = validFenceAreas.stream().collect(Collectors.groupingBy(AdCodeMsgEntity::getFenceId));
        List<FenceAreaFilterDTO> fenceAreasVoList = validFences.stream().map(fence -> {
            FenceAreaFilterDTO fenceAreaFilterDTO = new FenceAreaFilterDTO();
            //根据围栏查询数据
            List<AdCodeMsgEntity> adCodeMsgList = Optional.ofNullable(fenceAdCodeMsgMap.get(fence.getId())).orElse(Collections.emptyList());
            Map<String, List<AdCodeMsgEntity>> provinceMap = adCodeMsgList.stream().collect(Collectors.groupingBy(AdCodeMsgEntity::getProvince));

            List<ProvinceDTO> provinceDTOList = new ArrayList<>();
            provinceMap.keySet().forEach(provinceName -> {
                ProvinceDTO provinceDTO = new ProvinceDTO();
                provinceDTO.setProvinceName(provinceName);
                provinceDTO.setCityDTOList(this.getCityList(provinceMap, provinceName));
                provinceDTOList.add(provinceDTO);
            });
            fenceAreaFilterDTO.setFenceId(fence.getId());
            fenceAreaFilterDTO.setFenceName(fence.getFenceName());
            fenceAreaFilterDTO.setStoreNo(fence.getStoreNo());
//            fenceAreaFilterVO.setDeliveryFrequent();
            fenceAreaFilterDTO.setProvinceDTOList(provinceDTOList);
            return fenceAreaFilterDTO;
        }).collect(Collectors.toList());
        fenceAreasVoList = fenceAreasVoList.stream().filter(efficientFenceVo -> efficientFenceVo.getProvinceDTOList().size() > 0).collect(Collectors.toList());
        return fenceAreasVoList;
    }

    /**
     * 组装city
     * @param provinceMap
     * @param provinceName
     * @return
     */
    private List<CityDTO> getCityList(Map<String, List<AdCodeMsgEntity>> provinceMap, String provinceName) {
        List<AdCodeMsgEntity> cityList = provinceMap.get(provinceName);
        //分组去重
        Map<String, List<AdCodeMsgEntity>> cityMap = cityList.stream().filter(bean -> bean.getCity() != null).collect(Collectors.groupingBy(AdCodeMsgEntity::getCity));
        List<CityDTO> cityDTOList = new ArrayList<>();
        cityMap.keySet().forEach(cityName->{
            if(StringUtils.isNotBlank(cityName)){
                CityDTO cityDTO = new CityDTO();
                cityDTO.setCityName(cityName);
                cityDTO.setAreaList(getAreaList(cityMap,cityName));
                cityDTOList.add(cityDTO);
            }
        });
        return cityDTOList;
    }


    /**
     * 组装area
     * @param cityMap
     * @param cityName
     * @return
     */
    private  ArrayList<Map<String,String>> getAreaList(Map<String, List<AdCodeMsgEntity>> cityMap, String cityName) {
        List<AdCodeMsgEntity> areaList = cityMap.get(cityName);
        //分组去重
        Map<String, List<AdCodeMsgEntity>> areaMap = areaList.stream().filter(bean -> bean.getArea() != null).collect(Collectors.groupingBy(AdCodeMsgEntity::getArea));
        ArrayList<Map<String,String>> areaNameList = new ArrayList<Map<String,String>>();
        areaMap.keySet().forEach(areaName ->{
            HashMap<String, String> returnMap = new HashMap<>();
            returnMap.put("label",areaName);
            returnMap.put("value",areaName);
            if(StrUtil.isNotBlank(areaName)){
                areaNameList.add(returnMap);
            }
        });
        return areaNameList;
    }

    @Override
    public List<FenceCityDTO> queryCityAreaList(FenceCityQuery fenceCityQuery) {
        List<AdCodeMsgEntity> adCodeMsgEntities = adCodeMsgRepository.queryList(AdCodeMsgQuery.builder()
                .city(fenceCityQuery.getCityName())
                .statusList(AdCodeMsgEnums.Status.getUsableStatus())
                .build());
        if (CollectionUtils.isEmpty(adCodeMsgEntities)){
            return Collections.emptyList();
        }
        Map<String/*province#city*/, List<AdCodeMsgEntity>> cityAreaMap = adCodeMsgEntities.stream().collect(Collectors.groupingBy(e -> e.getProvince() + AppConsts.Symbol.HASH_TAG + e.getCity()));
        List<FenceCityDTO> fenceCityDTOList = cityAreaMap.entrySet().stream().map(e -> {
            FenceCityDTO fenceCityDTO = new FenceCityDTO();
            String[] keys = e.getKey().split(AppConsts.Symbol.HASH_TAG);
            String provinceName = keys[0];
            String cityName = keys[1];
            List<AdCodeMsgEntity> areaList = e.getValue();
            fenceCityDTO.setProvinceName(provinceName);
            fenceCityDTO.setCityName(cityName);
            Optional<AdCodeMsgEntity> cityOptional = areaList.stream().filter(x -> StrUtil.isBlank(x.getArea())).findFirst();
            List<FenceAreaDTO> fenceAreaDTOList = Lists.newArrayList();
            //判断是否是市级围栏
            boolean isCityFence = cityOptional.isPresent();
            if (!isCityFence) {
                fenceAreaDTOList = areaList.stream().map(FenceAreaConverter::entity2dto).collect(Collectors.toList());
            }
            fenceCityDTO.setFenceAreaDTOList(fenceAreaDTOList);
            return fenceCityDTO;
        }).collect(Collectors.toList());
        return fenceCityDTOList;
    }

    @Override
    public List<FenceDTO> queryList() {
        //获取所有有效围栏信息
        List<FenceEntity> fenceEntityList = fenceRepository.queryList(FenceQuery.builder()
                .statusList(FenceEnums.Status.getUsableStatus()).build());
        if (CollectionUtils.isEmpty(fenceEntityList)){
            return Collections.emptyList();
        }
        return fenceEntityList.stream().map(FenceEntityConverter::entity2dto).collect(Collectors.toList());
    }

    @Override
    public List<FenceDTO> queryListByStatus(List<Integer> statusList) {
        if(CollectionUtils.isEmpty(statusList)){
            return Collections.emptyList();
        }
        //获取围栏信息
        List<FenceEntity> fenceEntityList = fenceRepository.queryList(FenceQuery.builder()
                .statusList(statusList).build());
        if (CollectionUtils.isEmpty(fenceEntityList)){
            return Collections.emptyList();
        }
        return fenceEntityList.stream().map(FenceEntityConverter::entity2dto).collect(Collectors.toList());
    }

    @Override
    public List<Integer> queryValidAreaListByStoreNos(List<Integer> storeNos) {
        if(CollectionUtils.isEmpty(storeNos)){
            return Collections.emptyList();
        }
        List<FenceEntity> fenceEntities = fenceRepository.queryList(FenceQuery.builder().storeNos(storeNos).build());
        if(CollectionUtils.isEmpty(fenceEntities)){
            return Collections.emptyList();
        }
        List<Integer> areaNos = fenceEntities.stream().map(FenceEntity::getAreaNo).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(areaNos)){
            return Collections.emptyList();
        }
        //查询状态有效的运营区域
        List<AreaEntity> areaEntities = areaRepository.queryByAreaNos(areaNos);
        List<AreaEntity> validAreaList = areaEntities.stream().filter(AreaEntity::getStatus).collect(Collectors.toList());

        return validAreaList.stream().map(AreaEntity::getAreaNo).collect(Collectors.toList());
    }
}
