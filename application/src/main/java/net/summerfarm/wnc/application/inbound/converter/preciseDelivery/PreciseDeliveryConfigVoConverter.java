package net.summerfarm.wnc.application.inbound.converter.preciseDelivery;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import net.summerfarm.wnc.application.inbound.controller.preciseDelivery.vo.PreciseDeliveryConfigAreaVO;
import net.summerfarm.wnc.application.inbound.controller.preciseDelivery.vo.PreciseDeliveryConfigTimeVO;
import net.summerfarm.wnc.application.inbound.controller.preciseDelivery.vo.PreciseDeliveryConfigVO;
import net.summerfarm.wnc.domain.preciseDelivery.aggregate.PreciseDeliveryConfigAggregate;
import net.summerfarm.wnc.domain.preciseDelivery.valueObject.PreciseDeliveryConfigAreaValueObject;
import net.summerfarm.wnc.domain.preciseDelivery.valueObject.PreciseDeliveryConfigTimeValueObject;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Description:精准送配置视图转换器
 * date: 2024/1/22 16:19
 *
 * <AUTHOR>
 */
public class PreciseDeliveryConfigVoConverter {

    public static PreciseDeliveryConfigVO aggregate2Vo(PreciseDeliveryConfigAggregate aggregate){
        if (aggregate == null) {
            return null;
        }
        PreciseDeliveryConfigVO preciseDeliveryConfigVO = new PreciseDeliveryConfigVO();
        preciseDeliveryConfigVO.setId(aggregate.getConfigId());
        preciseDeliveryConfigVO.setProvince(aggregate.getProvince());
        preciseDeliveryConfigVO.setCity(aggregate.getCity());
        preciseDeliveryConfigVO.setAreaList(Optional.ofNullable(aggregate.getAreaList())
                .orElse(Lists.newArrayList()).stream().map(PreciseDeliveryConfigVoConverter::areaValueObject2Vo).collect(Collectors.toList()));
        preciseDeliveryConfigVO.setTimeList(Optional.ofNullable(aggregate.getTimeList())
                .orElse(Lists.newArrayList()).stream().map(PreciseDeliveryConfigVoConverter::timeValueObject2Vo).collect(Collectors.toList()));
        preciseDeliveryConfigVO.setCreator(aggregate.getCreator());
        preciseDeliveryConfigVO.setCreateTime(aggregate.getCreateTime());
        preciseDeliveryConfigVO.setUpdater(aggregate.getUpdater());
        preciseDeliveryConfigVO.setUpdateTime(aggregate.getUpdateTime());
        return preciseDeliveryConfigVO;
    }

    public static PreciseDeliveryConfigAreaVO areaValueObject2Vo(PreciseDeliveryConfigAreaValueObject valueObject){
        if (valueObject == null) {
            return null;
        }
        PreciseDeliveryConfigAreaVO preciseDeliveryConfigAreaVO = new PreciseDeliveryConfigAreaVO();
        preciseDeliveryConfigAreaVO.setId(valueObject.getId());
        preciseDeliveryConfigAreaVO.setConfigId(valueObject.getConfigId());
        preciseDeliveryConfigAreaVO.setAdCode(valueObject.getAdCode());
        preciseDeliveryConfigAreaVO.setCity(valueObject.getCity());
        preciseDeliveryConfigAreaVO.setArea(valueObject.getArea());
        preciseDeliveryConfigAreaVO.setCreateTime(valueObject.getCreateTime());
        return preciseDeliveryConfigAreaVO;
    }

    public static PreciseDeliveryConfigTimeVO timeValueObject2Vo(PreciseDeliveryConfigTimeValueObject valueObject){
        if (valueObject == null) {
            return null;
        }
        PreciseDeliveryConfigTimeVO preciseDeliveryConfigTimeVO = new PreciseDeliveryConfigTimeVO();
        preciseDeliveryConfigTimeVO.setId(valueObject.getId());
        preciseDeliveryConfigTimeVO.setConfigId(valueObject.getConfigId());
        //todo 时间转换处理
        preciseDeliveryConfigTimeVO.setBeginTime(String.valueOf(valueObject.getBeginTime()));
        preciseDeliveryConfigTimeVO.setEndTime(String.valueOf(valueObject.getEndTime()));
        preciseDeliveryConfigTimeVO.setCreateTime(valueObject.getCreateTime());
        return preciseDeliveryConfigTimeVO;
    }

    public static PageInfo<PreciseDeliveryConfigVO> aggregatePage2VoPage(PageInfo<PreciseDeliveryConfigAggregate> pageInfo) {
        List<PreciseDeliveryConfigAggregate> preciseDeliveryConfigDTOS = pageInfo.getList();
        List<PreciseDeliveryConfigVO> preciseDeliveryConfigVOS = preciseDeliveryConfigDTOS.stream().map(PreciseDeliveryConfigVoConverter::aggregate2Vo).collect(Collectors.toList());

        PageInfo<PreciseDeliveryConfigVO> voPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, voPageInfo);
        voPageInfo.setList(preciseDeliveryConfigVOS);
        return voPageInfo;
    }
}
