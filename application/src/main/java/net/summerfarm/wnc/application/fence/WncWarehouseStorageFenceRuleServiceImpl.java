package net.summerfarm.wnc.application.fence;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.fence.dto.*;
import net.summerfarm.wnc.api.fence.input.FenceDeliveryRuleUpdateCommand;
import net.summerfarm.wnc.api.fence.input.GlobalFenceRuleUpdateCommand;
import net.summerfarm.wnc.api.fence.service.WncWarehouseStorageFenceRuleService;
import net.summerfarm.wnc.application.fence.converter.WarehouseStorageEntityConverter;
import net.summerfarm.wnc.application.fence.converter.WncWarehouseStorageFenceRuleEntityConverter;
import net.summerfarm.wnc.application.fence.handle.DeliveryDateSkuHandle;
import net.summerfarm.wnc.application.warehouse.converter.WarehouseInventoryMappingEntityConverter;
import net.summerfarm.wnc.common.base.WncAssert;
import net.summerfarm.wnc.common.enums.WarehouseSourceEnum;
import net.summerfarm.wnc.common.enums.WarehouseStorageCenterEnums;
import net.summerfarm.wnc.common.enums.WarehouseStorageFenceRuleEnum;
import net.summerfarm.wnc.common.query.fence.WarehouseStorageFenceRuleQuery;
import net.summerfarm.wnc.common.query.fence.WncWarehouseStorageFenceRuleQuery;
import net.summerfarm.wnc.common.query.warehouse.WarehouseStorageQuery;
import net.summerfarm.wnc.domain.config.service.TenantStoreSkuBlackConfigQueryDomainService;
import net.summerfarm.wnc.domain.fence.WncTenantGlobalFenceRuleDomainService;
import net.summerfarm.wnc.domain.fence.WncWarehouseStorageFenceRuleRepository;
import net.summerfarm.wnc.domain.warehouse.WarehouseInventoryMappingDomainService;
import net.summerfarm.wnc.domain.warehouse.WarehouseStorageCenterDomainService;
import net.summerfarm.wnc.domain.warehouse.entity.*;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/4/6 18:08<br/>
 *
 * <AUTHOR> />
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class WncWarehouseStorageFenceRuleServiceImpl implements WncWarehouseStorageFenceRuleService {

    private final WncWarehouseStorageFenceRuleRepository wncWarehouseStorageFenceRuleRepository;
    private final WarehouseStorageCenterDomainService warehouseStorageCenterDomainService;
    private final WncTenantGlobalFenceRuleDomainService wncTenantGlobalFenceRuleDomainService;
    private final WarehouseInventoryMappingDomainService warehouseInventoryMappingDomainService;
    private final DeliveryDateSkuHandle deliveryDateSkuHandle;
    private final TenantStoreSkuBlackConfigQueryDomainService tenantStoreSkuBlackConfigQueryDomainService;

    @Override
    public List<WncWarehouseStorageFenceRuleDTO> queryWarehouseStorageFence(WncWarehouseStorageFenceRuleQuery wncWarehouseStorageFenceRuleQuery) {
        //查询租户的自营仓仓库信息
        List<WarehouseStorageEntity> warehouseStorageEntities = warehouseStorageCenterDomainService.querySelfWarehouseStorageFence(WarehouseStorageQuery.builder()
                .tenantId(wncWarehouseStorageFenceRuleQuery.getTenantId())
                .city(wncWarehouseStorageFenceRuleQuery.getCity())
                .area(wncWarehouseStorageFenceRuleQuery.getArea())
                .poi(wncWarehouseStorageFenceRuleQuery.getPoi())
                .status(WarehouseStorageCenterEnums.Status.OPEN.getValue())
                .build());
        log.info("自营仓仓库配送仓为:{}", JSON.toJSONString(warehouseStorageEntities));
        //查询租户的代仓仓库信息、鲜沐直供的仓库信息
        List<WarehouseStorageEntity> proxyWarehouseStorageEntities = warehouseStorageCenterDomainService.queryProxyWarehouseFence(WarehouseStorageQuery.builder()
                .tenantId(wncWarehouseStorageFenceRuleQuery.getTenantId())
                .city(wncWarehouseStorageFenceRuleQuery.getCity())
                .area(wncWarehouseStorageFenceRuleQuery.getArea())
                .poi(wncWarehouseStorageFenceRuleQuery.getPoi())
                .status(WarehouseStorageCenterEnums.Status.OPEN.getValue())
                .skuList(wncWarehouseStorageFenceRuleQuery.getSkuList())
                .contactId(wncWarehouseStorageFenceRuleQuery.getContactId())
                .build());
        log.info("查询租户的代仓仓库信息为:{}", JSON.toJSONString(proxyWarehouseStorageEntities));
        if(CollectionUtils.isEmpty(warehouseStorageEntities) && CollectionUtils.isEmpty(proxyWarehouseStorageEntities)){
            log.info("此区域不存在自营仓和仓库绑定信息");
            return Collections.emptyList();
        }

        //仓库顺序规则处理
        List<WarehouseStorageEntity> sortWarehouseList = warehouseStorageCenterDomainService.warehouseSortHandle(warehouseStorageEntities, proxyWarehouseStorageEntities, wncWarehouseStorageFenceRuleQuery);

        DeliveryFenceDTO deliveryFenceDTO = new DeliveryFenceDTO();
        if (!CollectionUtils.isEmpty(proxyWarehouseStorageEntities)) {
            deliveryFenceDTO = deliveryDateSkuHandle.querySkuDelivery(wncWarehouseStorageFenceRuleQuery.getSource(),
                    wncWarehouseStorageFenceRuleQuery.getCity(),
                    wncWarehouseStorageFenceRuleQuery.getArea(),
                    wncWarehouseStorageFenceRuleQuery.getContactId(),
                    wncWarehouseStorageFenceRuleQuery.getMerchantId(),
                    wncWarehouseStorageFenceRuleQuery.getAddOrderFlag(),
                    wncWarehouseStorageFenceRuleQuery.getSkuList(),
                    wncWarehouseStorageFenceRuleQuery.getTenantId());
        }
        //查询映射关系
        List<WarehouseInventoryMappingEntity> warehouseMappingList = warehouseInventoryMappingDomainService.queryMappingWithSkuSubTypeBySkusWarehouseNos(wncWarehouseStorageFenceRuleQuery.getSkuList(),
                sortWarehouseList.stream().map(WarehouseStorageEntity::getWarehouseNo).collect(Collectors.toList()));

        //排除映射关系黑名单
        warehouseMappingList = tenantStoreSkuBlackConfigQueryDomainService.filterNotInBlackConfigMapping(warehouseMappingList,wncWarehouseStorageFenceRuleQuery.getTenantId());

        //按照库存仓分组
        Map<Integer, List<WarehouseInventoryMappingEntity>> warehouseMappingListMap = warehouseMappingList.stream().collect(Collectors.groupingBy(WarehouseInventoryMappingEntity::getWarehouseNo));

        List<WncWarehouseStorageFenceRuleDTO> wncWarehouseStorageFenceRuleDTOs = sortWarehouseList.stream().map(WarehouseStorageEntityConverter::entity2FenceRuleDTO).collect(Collectors.toList());

        List<SkuDeliveryDateDTO> skuDeliveryDates = deliveryFenceDTO.getSkuDeliveryDates();
        Map<String, SkuDeliveryDateDTO> skuDeliveryDateMap = Collections.emptyMap();
        if(!CollectionUtils.isEmpty(skuDeliveryDates)){
            skuDeliveryDateMap = skuDeliveryDates.stream().collect(Collectors.toMap(SkuDeliveryDateDTO::getSku, Function.identity()));
        }
        for (WncWarehouseStorageFenceRuleDTO fenceRuleDTO : wncWarehouseStorageFenceRuleDTOs) {
            fenceRuleDTO.setDeliveryTime(deliveryFenceDTO.getDeliveryTime());
            fenceRuleDTO.setCategoryDeliveryTime(deliveryFenceDTO.getFullCategoryDeliveryDay());
            fenceRuleDTO.setCloseTime(deliveryFenceDTO.getCloseTime());
            fenceRuleDTO.setDeliveryCloseTime(deliveryFenceDTO.getDeliveryCloseTime());
            fenceRuleDTO.setIsEveryDayFlag(deliveryFenceDTO.getIsEveryDayFlag());
            if(CollectionUtils.isEmpty(warehouseMappingListMap.get(fenceRuleDTO.getWarehouseNo()))) {
                continue;
            }
            List<WncSkuStoreNoDTO> skuStoreNoDTOList = warehouseMappingListMap.get(fenceRuleDTO.getWarehouseNo()).stream().map(WarehouseInventoryMappingEntityConverter::entity2WncSkuStoreNoDTO).collect(Collectors.toList());
            //设置商品的配送时效
            for (WncSkuStoreNoDTO skuStoreNoDTO : skuStoreNoDTOList) {
                SkuDeliveryDateDTO skuDeliveryDateDTO = skuDeliveryDateMap.get(skuStoreNoDTO.getSku());
                if(skuDeliveryDateDTO == null){
                    continue;
                }
                skuStoreNoDTO.setDeliveryTime(skuDeliveryDateDTO.getDeliveryDate());
                skuStoreNoDTO.setDeliveryCloseTime(skuDeliveryDateDTO.getDeliveryCloseTime());
            }
            //过滤鲜沐仓城配信息
            if(fenceRuleDTO.getTenantId().intValue() == WarehouseSourceEnum.SUMMERFARM_WAREHOUSE.getCode()){
                if(CollectionUtils.isEmpty(fenceRuleDTO.getStoreNoList())){
                    continue;
                }
                skuStoreNoDTOList = skuStoreNoDTOList.stream().filter(skuStore -> fenceRuleDTO.getStoreNoList().contains(skuStore.getStoreNo())).collect(Collectors.toList());
            }
            fenceRuleDTO.setWncSkuStoreNoDTOS(skuStoreNoDTOList);
        }
        //过滤鲜沐仓没有城配仓的数据
        wncWarehouseStorageFenceRuleDTOs = wncWarehouseStorageFenceRuleDTOs.stream()
                .filter(rule -> !(rule.getTenantId().intValue() == WarehouseSourceEnum.SUMMERFARM_WAREHOUSE.getCode() && CollectionUtils.isEmpty(rule.getWncSkuStoreNoDTOS())))
                .collect(Collectors.toList());
        return wncWarehouseStorageFenceRuleDTOs;
    }

    @Override
    public PageInfo<WncWarehouseStorageFenceRuleDTO> queryFenceDeliveryRule(WncWarehouseStorageFenceRuleQuery wncWarehouseStorageFenceRuleQuery) {
        PageInfo<WncWarehouseStorageFenceRuleEntity> wncWarehouseStorageFenceRuleEntityPageInfo = wncWarehouseStorageFenceRuleRepository.queryPage(wncWarehouseStorageFenceRuleQuery);
        if(wncWarehouseStorageFenceRuleEntityPageInfo.getList() == null){
            PageInfo<WncWarehouseStorageFenceRuleDTO> pageInfo = new PageInfo<>();
            pageInfo.setList(new ArrayList<>());
            return pageInfo;
        }
        List<WncWarehouseStorageFenceRuleDTO> wncWarehouseStorageFenceRuleDTOS = wncWarehouseStorageFenceRuleEntityPageInfo.getList().stream()
                .map(WncWarehouseStorageFenceRuleEntityConverter::entity2DTO).collect(Collectors.toList());
        PageInfo<WncWarehouseStorageFenceRuleDTO> wncWarehouseStorageFenceRuleDTOPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(wncWarehouseStorageFenceRuleEntityPageInfo, wncWarehouseStorageFenceRuleDTOPageInfo);
        wncWarehouseStorageFenceRuleDTOPageInfo.setList(wncWarehouseStorageFenceRuleDTOS);
        return wncWarehouseStorageFenceRuleDTOPageInfo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateFenceDeliveryRule(FenceDeliveryRuleUpdateCommand fenceDeliveryRuleUpdateCommand) {
        WncWarehouseStorageFenceRuleEntity wncWarehouseStorageFenceRuleEntity= wncWarehouseStorageFenceRuleRepository.queryById(fenceDeliveryRuleUpdateCommand.getId());
        if(wncWarehouseStorageFenceRuleEntity == null){
            throw new BizException("不存在此数据信息");
        }
        wncWarehouseStorageFenceRuleEntity.setDeliveryRule(fenceDeliveryRuleUpdateCommand.getDeliveryRule());
        List<ConflictWarehouseJsonEntity> conflictWarehouseJsonEntities = fenceDeliveryRuleUpdateCommand.getConflictWarehouseJsonVOList().stream()
                .map(WncWarehouseStorageFenceRuleEntityConverter::command2Entity)
                .collect(Collectors.toList());

        wncWarehouseStorageFenceRuleEntity.setConflictWarehouseJsonEntityList(conflictWarehouseJsonEntities);
        wncWarehouseStorageFenceRuleEntity.setLastOperatorName(fenceDeliveryRuleUpdateCommand.getOperatorName());

        wncWarehouseStorageFenceRuleRepository.update(wncWarehouseStorageFenceRuleEntity);
    }

    @Override
    public WncTenantGlobalFenceRuleDTO queryGlobalFenceRule(Long tenantId) {
        WncTenantGlobalFenceRuleEntity wncTenantGlobalFenceRuleEntity = wncTenantGlobalFenceRuleDomainService.queryGlobalFenceRule(tenantId);

        WncTenantGlobalFenceRuleDTO wncTenantGlobalFenceRuleDTO = new WncTenantGlobalFenceRuleDTO();
        wncTenantGlobalFenceRuleDTO.setCreateTime(wncTenantGlobalFenceRuleEntity.getCreateTime());
        wncTenantGlobalFenceRuleDTO.setGlobalDeliveryRule(wncTenantGlobalFenceRuleEntity.getGlobalDeliveryRule());
        wncTenantGlobalFenceRuleDTO.setUpdateTime(wncTenantGlobalFenceRuleEntity.getUpdateTime());
        wncTenantGlobalFenceRuleDTO.setId(wncTenantGlobalFenceRuleEntity.getId());
        wncTenantGlobalFenceRuleDTO.setTenantId(wncTenantGlobalFenceRuleEntity.getTenantId());
        return wncTenantGlobalFenceRuleDTO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateGlobalFenceRule(GlobalFenceRuleUpdateCommand globalFenceRuleUpdateCommand) {
        WncTenantGlobalFenceRuleEntity wncTenantGlobalFenceRuleEntity = new WncTenantGlobalFenceRuleEntity();
        wncTenantGlobalFenceRuleEntity.setTenantId(globalFenceRuleUpdateCommand.getTenantId());
        wncTenantGlobalFenceRuleEntity.setGlobalDeliveryRule(globalFenceRuleUpdateCommand.getGlobalDeliveryRule());
        wncTenantGlobalFenceRuleDomainService.updateGlobalFenceRule(wncTenantGlobalFenceRuleEntity);
    }

    @Override
    public Integer getType() {
        return WarehouseStorageFenceRuleEnum.SAAS_WAREHOUSE.getCode();
    }

    @Override
    public List<WarehouseStorageFenceRuleDTO> queryWarehouseStorageFence(WarehouseStorageFenceRuleQuery warehouseStorageFenceRuleQuery) {
        log.info("queryWarehouseStorageFence查询自营仓围栏配送顺序请求报文{}", JSON.toJSONString(warehouseStorageFenceRuleQuery));

        WncAssert.hasText(warehouseStorageFenceRuleQuery.getCity(),"city不能为空");
        WncAssert.notNull(warehouseStorageFenceRuleQuery.getTenantId(),"tenantId不能为空");
        WncAssert.notEmpty(warehouseStorageFenceRuleQuery.getSkuList(),"skuList不能为空");

        List<WncWarehouseStorageFenceRuleDTO> wncWarehouseStorageFenceRuleDTOS = queryWarehouseStorageFence(WncWarehouseStorageFenceRuleQuery.builder()
                .poi(warehouseStorageFenceRuleQuery.getPoi())
                .city(warehouseStorageFenceRuleQuery.getCity())
                .area(warehouseStorageFenceRuleQuery.getArea())
                .skuList(warehouseStorageFenceRuleQuery.getSkuList())
                .tenantId(warehouseStorageFenceRuleQuery.getTenantId())
                .contactId(warehouseStorageFenceRuleQuery.getContactId())
                .merchantId(warehouseStorageFenceRuleQuery.getMerchantId())
                .addOrderFlag(warehouseStorageFenceRuleQuery.getAddOrderFlag())
                .source(warehouseStorageFenceRuleQuery.getSource())
                .build());

        return wncWarehouseStorageFenceRuleDTOS.stream().map(WarehouseStorageEntityConverter::entity2DTO).collect(Collectors.toList());
    }
}
