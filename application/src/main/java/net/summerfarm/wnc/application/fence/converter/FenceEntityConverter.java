package net.summerfarm.wnc.application.fence.converter;

import net.summerfarm.wnc.api.fence.dto.FenceDTO;
import net.summerfarm.wnc.api.fence.input.*;
import net.summerfarm.wnc.common.enums.FenceEnums;
import net.summerfarm.wnc.domain.fence.entity.FenceChannelBusinessWhiteConfigEntity;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:FenceEntity-DTO转换器
 * date: 2023/6/8 14:27
 *
 * <AUTHOR>
 */
public class FenceEntityConverter {

    //todo 修改影响面
    public static FenceDTO entity2dto(FenceEntity fenceEntity){
        if(fenceEntity == null){
            return null;
        }
        FenceDTO fenceDTO = new FenceDTO();
        fenceDTO.setFenceId(fenceEntity.getId());
        fenceDTO.setFenceName(fenceEntity.getFenceName());
        fenceDTO.setStoreNo(fenceEntity.getStoreNo());
        fenceDTO.setAreaNo(fenceEntity.getAreaNo());
        fenceDTO.setAreaName(fenceEntity.getAreaName());
        fenceDTO.setStoreName(fenceEntity.getStoreName());
        fenceDTO.setCityName(fenceEntity.getCityName());
        fenceDTO.setPackId(fenceEntity.getPackId());

        FenceEnums.Type type = fenceEntity.getType();
        if(type != null){
            fenceDTO.setType(type.getValue());
            fenceDTO.setTypeDesc(type.getContent());
        }
        FenceEnums.Status status = fenceEntity.getStatus();
        if(status != null){
            fenceDTO.setStatus(status.getValue());
            fenceDTO.setStatusDesc(status.getContent());
        }
        fenceDTO.setCreateTime(fenceEntity.getCreateTime());
        fenceDTO.setUpdateTime(fenceEntity.getUpdateTime());
        fenceDTO.setUpdater(fenceEntity.getUpdater());

        fenceDTO.setFenceDeliveryDTO(Optional.ofNullable(fenceEntity.getFenceDeliveryEntity()).map(FenceDeliveryConverter::entity2dto).orElse(null));
        if (!CollectionUtils.isEmpty(fenceEntity.getAdCodeMsgEntities())){
            fenceDTO.setFenceAreaDTOList(fenceEntity.getAdCodeMsgEntities().stream().map(FenceAreaConverter::entity2dto).collect(Collectors.toList()));
        }
        fenceDTO.setOrderChannelType(fenceEntity.getOrderChannelType());
        return fenceDTO;
    }

    public static FenceEntity saveCommand2entity(FenceSaveCommand fenceSaveCommand){
        if (fenceSaveCommand == null){
            return null;
        }
        FenceEntity fenceEntity = new FenceEntity();
        fenceEntity.setFenceName(fenceSaveCommand.getFenceName());
        fenceEntity.setStoreNo(fenceSaveCommand.getStoreNo());
        fenceEntity.setAreaNo(fenceSaveCommand.getAreaNo());
        fenceEntity.setType(FenceEnums.Type.getTypeByValue(fenceSaveCommand.getType()));
        List<FenceAreaCommand> fenceAreaCommandList = fenceSaveCommand.getFenceAreaCommandList();
        if (!CollectionUtils.isEmpty(fenceAreaCommandList)){
            fenceEntity.setAdCodeMsgEntities(fenceAreaCommandList.stream().map(FenceAreaConverter::command2entity).collect(Collectors.toList()));
        }
        fenceEntity.setFenceDeliveryEntity(FenceDeliveryConverter.command2entity(fenceSaveCommand.getFenceDeliveryCommand()));
        fenceEntity.setOrderChannelType(fenceSaveCommand.getOrderChannelType());
        List<FenceChannelBusinessWhiteConfigCommandInput> channelBusinessWhiteConfigCommandInputs = fenceSaveCommand.getFenceChannelBusinessWhiteConfigCommandInputList();

        fenceEntity.setFenceChannelBusinessWhiteConfigEntities(FenceEntityConverter.channelBusCommand2EntityList(channelBusinessWhiteConfigCommandInputs));
        return fenceEntity;

    }

    public static List<FenceChannelBusinessWhiteConfigEntity> channelBusCommand2EntityList(List<FenceChannelBusinessWhiteConfigCommandInput> commands){
        if(CollectionUtils.isEmpty(commands)){
            return Collections.emptyList();
        }

        ArrayList<FenceChannelBusinessWhiteConfigEntity> channelBusinessWhiteConfigEntities = new ArrayList<>();
        commands.forEach(command -> {
            String orderChannelType = command.getOrderChannelType();
            List<ScopeChannelBusinessInput> scopeChannelBusinesses = command.getScopeChannelBusinesses();

            scopeChannelBusinesses.forEach(scopeChannelBusiness -> {
                FenceChannelBusinessWhiteConfigEntity entity = new FenceChannelBusinessWhiteConfigEntity();
                entity.setOrderChannelType(orderChannelType);
                entity.setScopeChannelBusinessId(scopeChannelBusiness.getScopeChannelBusinessId());
                entity.setScopeChannelBusinessName(scopeChannelBusiness.getScopeChannelBusinessName());
                entity.setTenantId(scopeChannelBusiness.getTenantId());

                channelBusinessWhiteConfigEntities.add(entity);
            });
        });


        return channelBusinessWhiteConfigEntities;
    }
    public static FenceEntity editCommand2entity(FenceEditCommand fenceEditCommand){
        if (fenceEditCommand == null){
            return null;
        }
        FenceEntity fenceEntity = new FenceEntity();
        fenceEntity.setId(fenceEditCommand.getFenceId());
        List<FenceAreaCommand> fenceAreaCommandList = fenceEditCommand.getFenceAreaCommandList();
        if (!CollectionUtils.isEmpty(fenceAreaCommandList)){
            fenceEntity.setAdCodeMsgEntities(fenceAreaCommandList.stream().map(FenceAreaConverter::command2entity).collect(Collectors.toList()));
        }
        fenceEntity.setFenceDeliveryEntity(FenceDeliveryConverter.command2entity(fenceEditCommand.getFenceDeliveryCommand()));
        fenceEntity.setOrderChannelType(fenceEditCommand.getOrderChannelType());
        List<FenceChannelBusinessWhiteConfigCommandInput> channelBusinessWhiteConfigCommandInputs = fenceEditCommand.getFenceChannelBusinessWhiteConfigCommandInputList();
        fenceEntity.setFenceChannelBusinessWhiteConfigEntities(FenceEntityConverter.channelBusCommand2EntityList(channelBusinessWhiteConfigCommandInputs));
        return fenceEntity;

    }
}
