package net.summerfarm.wnc.application.fence.converter;

import net.summerfarm.wnc.api.fence.dto.WarehouseStorageFenceRuleDTO;
import net.summerfarm.wnc.api.fence.dto.WncWarehouseStorageFenceRuleDTO;
import net.summerfarm.wnc.api.warehouse.dto.WarehouseStorageDTO;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseStorageEntity;

/**
 * Description: <br/>
 * date: 2023/4/10 18:28<br/>
 *
 * <AUTHOR> />
 */
public class WarehouseStorageEntityConverter {

    public static WncWarehouseStorageFenceRuleDTO entity2FenceRuleDTO(WarehouseStorageEntity warehouseStorageEntity){
        if(warehouseStorageEntity == null){
            return null;
        }
        WncWarehouseStorageFenceRuleDTO wncWarehouseStorageFenceRuleDTO = new WncWarehouseStorageFenceRuleDTO();
        wncWarehouseStorageFenceRuleDTO.setDistance(warehouseStorageEntity.getDistance());
        wncWarehouseStorageFenceRuleDTO.setWarehouseNo(warehouseStorageEntity.getWarehouseNo());
        wncWarehouseStorageFenceRuleDTO.setTenantId(warehouseStorageEntity.getTenantId());
        wncWarehouseStorageFenceRuleDTO.setStoreNoList(warehouseStorageEntity.getStoreNoList());
        return wncWarehouseStorageFenceRuleDTO;
    }


    public static WarehouseStorageFenceRuleDTO entity2DTO(WncWarehouseStorageFenceRuleDTO wncWarehouseStorageFenceRuleDTO){
        if(wncWarehouseStorageFenceRuleDTO == null){
            return null;
        }

        WarehouseStorageFenceRuleDTO warehouseStorageFenceRuleDTO = new WarehouseStorageFenceRuleDTO();
        warehouseStorageFenceRuleDTO.setDistance(wncWarehouseStorageFenceRuleDTO.getDistance());
        warehouseStorageFenceRuleDTO.setWarehouseNo(wncWarehouseStorageFenceRuleDTO.getWarehouseNo());
        warehouseStorageFenceRuleDTO.setTenantId(wncWarehouseStorageFenceRuleDTO.getTenantId());
        warehouseStorageFenceRuleDTO.setStoreNoList(wncWarehouseStorageFenceRuleDTO.getStoreNoList());
        warehouseStorageFenceRuleDTO.setWncSkuStoreNoDTOS(wncWarehouseStorageFenceRuleDTO.getWncSkuStoreNoDTOS());
        warehouseStorageFenceRuleDTO.setDeliveryTime(wncWarehouseStorageFenceRuleDTO.getDeliveryTime());
        warehouseStorageFenceRuleDTO.setCloseTime(wncWarehouseStorageFenceRuleDTO.getCloseTime());
        warehouseStorageFenceRuleDTO.setIsEveryDayFlag(wncWarehouseStorageFenceRuleDTO.getIsEveryDayFlag());
        warehouseStorageFenceRuleDTO.setCategoryDeliveryTime(wncWarehouseStorageFenceRuleDTO.getCategoryDeliveryTime());
        warehouseStorageFenceRuleDTO.setDeliveryCloseTime(wncWarehouseStorageFenceRuleDTO.getDeliveryCloseTime());
        return warehouseStorageFenceRuleDTO;
    }

    public static WarehouseStorageDTO entity2DTO(WarehouseStorageEntity entity){
        if(entity == null){
            return null;
        }

        WarehouseStorageDTO dto = new WarehouseStorageDTO();

        dto.setId(entity.getId());
        dto.setWarehouseNo(entity.getWarehouseNo());
        dto.setWarehouseName(entity.getWarehouseName());
        dto.setManageAdminId(entity.getManageAdminId());
        dto.setType(entity.getType());
        dto.setAreaManageId(entity.getAreaManageId());
        dto.setStatus(entity.getStatus());
        dto.setAddress(entity.getAddress());
        dto.setPoiNote(entity.getPoiNote());
        dto.setMailToAddress(entity.getMailToAddress());
        dto.setUpdater(entity.getUpdater());
        dto.setUpdateTime(entity.getUpdateTime());
        dto.setCreator(entity.getCreator());
        dto.setCreateTime(entity.getCreateTime());
        dto.setPersonContact(entity.getPersonContact());
        dto.setPhone(entity.getPhone());
        dto.setTenantId(entity.getTenantId());
        dto.setSku(entity.getSku());

        return dto;
    }

}
