package net.summerfarm.wnc.application.fence;

import cn.hutool.core.convert.Convert;
import net.summerfarm.wnc.api.fence.dto.StopDeliveryDTO;
import net.summerfarm.wnc.application.fence.converter.StopDeliveryConverter;
import net.summerfarm.wnc.application.fence.dto.DeliveryRuleDTO;
import net.summerfarm.wnc.common.enums.FenceDeliveryEnums;
import net.summerfarm.wnc.domain.fence.entity.StopDeliveryEntity;
import net.summerfarm.wnc.domain.stop.StopDeliveryDomainService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023-06-20
 **/
@Component
public class CalcFrenceFrequentDeliveryHelper {

	@Resource
	private StopDeliveryDomainService stopDeliveryDomainService;

	public List<LocalDate> calcFenceFrequentDelivery(List<LocalDate> wantDeliveryTimeList, DeliveryRuleDTO deliveryRuleDTO,
													 LocalDate startTime) {
		List<LocalDate> couldDeliveryDateList = new ArrayList<>();
		List<StopDeliveryDTO> stopDeliveryDTOList = deliveryRuleDTO.getStopDeliveryDTOList();

		List<StopDeliveryEntity> stopDeliveryEntityList = CollectionUtils.isEmpty(stopDeliveryDTOList) ?
				Collections.emptyList() : stopDeliveryDTOList.stream().map(StopDeliveryConverter::dto2Entity).collect(Collectors.toList());
		//停配判断
		startTime = stopDeliveryDomainService.findCouldDeliveryDate(stopDeliveryEntityList, startTime);

		//围栏周计算
		if (Objects.equals(FenceDeliveryEnums.FrequentMethod.WEEK_CALC.getValue(), deliveryRuleDTO.getFrequentMethod())) {
			couldDeliveryDateList = calcFrequentDelivery(wantDeliveryTimeList, startTime, deliveryRuleDTO.getWeekDeliveryFrequent(),stopDeliveryEntityList);
		}
		//间隔计算
		if (Objects.equals(FenceDeliveryEnums.FrequentMethod.INTERVAL_CALC.getValue(), deliveryRuleDTO.getFrequentMethod())) {
			couldDeliveryDateList = calcIntervalDelivery(wantDeliveryTimeList, deliveryRuleDTO, startTime,stopDeliveryEntityList);
		}
		return couldDeliveryDateList;
	}

	/**
	 * 间隔计算
	 * @param wantDeliveryTimeList  想要配送的日期集合
	 * @param deliveryRuleDTO		配送规则
	 * @param startTime				开始日期
	 * @param stopDeliveryEntityList	停配实体
	 */
	private List<LocalDate> calcIntervalDelivery(List<LocalDate> wantDeliveryTimeList, DeliveryRuleDTO deliveryRuleDTO,
									  LocalDate startTime, List<StopDeliveryEntity> stopDeliveryEntityList) {
		List<LocalDate> couldDeliveryDateList = new ArrayList<>();
		if (CollectionUtils.isEmpty(wantDeliveryTimeList)) {
			//没有查询周期参数
			LocalDate targetDate = startTime.isBefore(deliveryRuleDTO.getBeginCalculateDate()) ?
					deliveryRuleDTO.getBeginCalculateDate() : deliveryRuleDTO.getBeginCalculateDate().plusDays(deliveryRuleDTO.getDeliveryFrequentInterval());

			while (startTime.isAfter(targetDate)) {
				targetDate = targetDate.plusDays(deliveryRuleDTO.getDeliveryFrequentInterval());
			}
			while(stopDeliveryDomainService.inStoreStopDeliveryDay(stopDeliveryEntityList,targetDate)){
				targetDate = targetDate.plusDays(deliveryRuleDTO.getDeliveryFrequentInterval());
			}

			couldDeliveryDateList.add(targetDate);
		} else {
			//筛选出晚于最早配送日期的日期
			wantDeliveryTimeList = wantDeliveryTimeList.stream()
					.filter(wantDeliveryTime -> startTime.equals(wantDeliveryTime) || startTime.isBefore(wantDeliveryTime))
					.collect(Collectors.toList());
			//存在查询范围
			wantDeliveryTimeList.forEach(wantDeliveryTime -> {
				//日期一定会大于等于startTime，大于等于开始计算日期
				if (wantDeliveryTime.compareTo(startTime) < 0 && wantDeliveryTime.compareTo(deliveryRuleDTO.getBeginCalculateDate()) < 0) {
					return;
				}
				LocalDate nextDeliveryDay = deliveryRuleDTO.getBeginCalculateDate();
				if (wantDeliveryTime.equals(nextDeliveryDay) && !stopDeliveryDomainService.inStoreStopDeliveryDay(stopDeliveryEntityList,nextDeliveryDay)) {
					couldDeliveryDateList.add(wantDeliveryTime);
					return;
				}
				//看当前想要配送的日期是否在周期上，并且不在城配仓停配范围内
				while (wantDeliveryTime.isAfter(nextDeliveryDay)) {
					nextDeliveryDay = nextDeliveryDay.plusDays(deliveryRuleDTO.getDeliveryFrequentInterval());
					if (wantDeliveryTime.equals(nextDeliveryDay) && !stopDeliveryDomainService.inStoreStopDeliveryDay(stopDeliveryEntityList,nextDeliveryDay)) {
						couldDeliveryDateList.add(wantDeliveryTime);
					}
				}
			});
		}
		return couldDeliveryDateList;
	}

	/**
	 * 计算日期
	 * @param wantDeliveryTimeList  想要配送的日期集合
	 * @param startTime             开始日期
	 * @param deliveryFrequent      按周的周期
	 * @param stopDeliveryEntityList      停配信息
	 */
	public List<LocalDate> calcFrequentDelivery(List<LocalDate> wantDeliveryTimeList, LocalDate startTime,
									  String deliveryFrequent, List<StopDeliveryEntity> stopDeliveryEntityList) {
		List<LocalDate> couldDeliveryDateList = new ArrayList<>();
		//转化
		List<Integer> deliveryFrequentList = Arrays.stream(Convert.toIntArray(deliveryFrequent.split(","))).sorted().collect(Collectors.toList());
		//等于0每天配送
		if (Objects.equals(deliveryFrequentList.get(0), 0)) {
			//没有查询周期参数
			if (CollectionUtils.isEmpty(wantDeliveryTimeList)) {
				couldDeliveryDateList.add(startTime);
				return couldDeliveryDateList;
			}

			wantDeliveryTimeList.stream()
					.filter(wantDeliveryTime -> startTime.equals(wantDeliveryTime) || startTime.isBefore(wantDeliveryTime))
					.filter(wantDeliveryTime -> !stopDeliveryDomainService.inStoreStopDeliveryDay(stopDeliveryEntityList, wantDeliveryTime))
					.forEach(couldDeliveryDateList::add);
		} else {
			LocalDate nextDeliveryDate = startTime;
			while (!deliveryFrequentList.contains(nextDeliveryDate.getDayOfWeek().getValue()) ||
					stopDeliveryDomainService.inStoreStopDeliveryDay(stopDeliveryEntityList, nextDeliveryDate)) {
				nextDeliveryDate = nextDeliveryDate.plusDays(1);
			}

			if (CollectionUtils.isEmpty(wantDeliveryTimeList)) {
				couldDeliveryDateList.add(nextDeliveryDate);
				return couldDeliveryDateList;
			}
			wantDeliveryTimeList.stream()
					.filter(wantDeliveryTime -> (startTime.equals(wantDeliveryTime) || startTime.isBefore(wantDeliveryTime))
							&& deliveryFrequentList.contains(wantDeliveryTime.getDayOfWeek().getValue())
							&& !stopDeliveryDomainService.inStoreStopDeliveryDay(stopDeliveryEntityList, wantDeliveryTime)
					)
					.forEach(couldDeliveryDateList::add);
		}
		return couldDeliveryDateList;
	}


}
