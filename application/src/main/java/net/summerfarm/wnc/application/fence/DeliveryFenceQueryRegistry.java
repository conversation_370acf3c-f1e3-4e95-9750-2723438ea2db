package net.summerfarm.wnc.application.fence;

import com.google.common.collect.Maps;
import lombok.NonNull;
import net.summerfarm.wnc.application.fence.strategy.DeliveryFenceQueryStrategy;
import net.summerfarm.wnc.client.enums.SourceEnum;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 查询配送周期实现注册器
 *
 * <AUTHOR>
 * @Date 2023-06-20
 **/
@Component
public class DeliveryFenceQueryRegistry implements BeanPostProcessor {
	private static final Map<SourceEnum, DeliveryFenceQueryStrategy> REPOS = Maps.newConcurrentMap();

	@Override
	public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
		if (bean instanceof DeliveryFenceQueryStrategy) {
			final SourceEnum entry = ((DeliveryFenceQueryStrategy) bean).getSource();
			if (REPOS.containsKey(entry)) {
				throw new BizException("查询配送时间 " + entry.name() + " 有两种实现，请检查配置");
			}
			REPOS.put(entry, (DeliveryFenceQueryStrategy) bean);
		}
		return bean;
	}

	public DeliveryFenceQueryStrategy getWorker(@NonNull SourceEnum entry) {
		if(REPOS.get(entry) == null){
			throw new BizException("订单类型未匹配到对应的策略");
		}
		return REPOS.get(entry);
	}
}
