package net.summerfarm.wnc.application.inbound.controller.deliveryFence.input;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * Description: 查询截单时间<br/>
 * date: 2024/2/21 16:52<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliveryFenceQueryCloseTimeInput {
    @NotBlank(message = "城市不能为空")
    private String city;

    private String area;

    @NotNull(message = "订单来源不能为空")
    private Integer source;

    private Long contactId;
}
