package net.summerfarm.wnc.application.config;

import net.summerfarm.wnc.domain.config.entity.WncFullCategoryWarehouseSkuWhiteConfigEntity;
import net.summerfarm.wnc.application.inbound.controller.config.input.command.WncFullCategoryWarehouseSkuWhiteConfigCommandInput;


/**
 * @date 2025-01-06 15:31:26
 * @version 1.0
 */
public interface WncFullCategoryWarehouseSkuWhiteConfigCommandService {

    /**
     * @description: 新增
     * @return WncFullCategoryWarehouseSkuWhiteConfigEntity
     **/
    WncFullCategoryWarehouseSkuWhiteConfigEntity insert(WncFullCategoryWarehouseSkuWhiteConfigCommandInput input);


    /**
     * @description: 更新
     * @return:
     **/
    int update(WncFullCategoryWarehouseSkuWhiteConfigCommandInput input);



    /**
    * @description: 删除
    * @return:
    **/
    int delete(Long id);

}