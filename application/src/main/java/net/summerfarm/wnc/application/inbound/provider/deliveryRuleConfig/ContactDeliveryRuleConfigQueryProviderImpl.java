package net.summerfarm.wnc.application.inbound.provider.deliveryRuleConfig;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.deliveryRule.dto.ContactDeliveryRuleDTO;
import net.summerfarm.wnc.api.deliveryRule.service.ContactDeliveryRuleService;
import net.summerfarm.wnc.api.fence.dto.FenceDTO;
import net.summerfarm.wnc.api.fence.service.DeliveryFenceService;
import net.summerfarm.wnc.client.provider.deliveryRuleConfig.ContactDeliveryRuleConfigQueryProvider;
import net.summerfarm.wnc.client.req.deliveryRuleConfig.DeliveryRuleConfigQueryReq;
import net.summerfarm.wnc.client.resp.deliveryRuleConfig.ContactConfigResp;
import net.summerfarm.wnc.client.resp.deliveryRuleConfig.DeliveryRuleResp;
import net.summerfarm.wnc.client.resp.deliveryRuleConfig.OrderDeliveryRuleConfigResp;
import net.summerfarm.wnc.common.config.WncConfig;
import net.summerfarm.wnc.common.enums.WncContactDeliveryRuleEnums;
import net.summerfarm.wnc.common.query.deliveryRule.ContactDeliveryRuleQuery;
import net.summerfarm.wnc.common.query.warehouse.StoreNoQuery;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * Description: 地址城配仓、配送周期配置查询服务<br/>
 * date: 2024/4/16 18:07<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@DubboService
public class ContactDeliveryRuleConfigQueryProviderImpl implements ContactDeliveryRuleConfigQueryProvider {

    @Resource
    private ContactDeliveryRuleService contactDeliveryRuleService;
    @Resource
    private DeliveryFenceService deliveryFenceService;
    @Resource
    private WncConfig wncConfig;

    @Override
    public DubboResponse<OrderDeliveryRuleConfigResp> queryOuterOrderDeliveryRuleConfig(@Valid DeliveryRuleConfigQueryReq req) {
        if (wncConfig.isCustomFencePoiWarningSwitchOpen() && StringUtils.isEmpty(req.getPoi())) {
            log.error("\n poi参数为空 ContactDeliveryRuleConfigQueryProvider queryOuterOrderDeliveryRuleConfig poi is null\n");
        }
        //查询指定城配仓
        FenceDTO fenceDTO = deliveryFenceService.queryStoreNo(StoreNoQuery.builder()
                .city(req.getCity())
                .area(req.getArea())
                .contactId(req.getContactId())
                .tenantId(req.getTenantId()).build());

        //查询配送周期
        ContactDeliveryRuleDTO contactDeliveryRuleDTO = contactDeliveryRuleService.queryContactDeliveryRule(ContactDeliveryRuleQuery.builder()
                .outBusinessNo(String.valueOf(req.getContactId()))
                .systemSource(WncContactDeliveryRuleEnums.SystemSource.getSourceByTenantId(req.getTenantId()).getValue())
                .tenantId(req.getTenantId())
                .build());

        //组装信息
        OrderDeliveryRuleConfigResp resp = new OrderDeliveryRuleConfigResp();

        ContactConfigResp contactConfigResp = new ContactConfigResp();
        contactConfigResp.setContactId(req.getContactId());
        if(fenceDTO != null){
            contactConfigResp.setStoreNo(fenceDTO.getStoreNo());
            contactConfigResp.setStoreName(fenceDTO.getStoreName());
        }
        contactConfigResp.setTenantId(req.getTenantId());

        if(contactDeliveryRuleDTO != null){
            DeliveryRuleResp deliveryRuleResp = new DeliveryRuleResp();
            deliveryRuleResp.setContactId(contactDeliveryRuleDTO.getOutBusinessNo() == null ? null : Long.parseLong(contactDeliveryRuleDTO.getOutBusinessNo()));
            deliveryRuleResp.setBeginCalculateDate(contactDeliveryRuleDTO.getBeginCalculateDate());
            deliveryRuleResp.setDeliveryFrequentInterval(contactDeliveryRuleDTO.getDeliveryFrequentInterval());
            deliveryRuleResp.setFrequentMethod(contactDeliveryRuleDTO.getFrequentMethod());
            deliveryRuleResp.setTenantId(contactDeliveryRuleDTO.getTenantId());
            deliveryRuleResp.setWeekDeliveryFrequent(contactDeliveryRuleDTO.getWeekDeliveryFrequent());

            resp.setDeliveryRuleResp(deliveryRuleResp);
        }

        resp.setContactConfigResp(contactConfigResp);

        return DubboResponse.getOK(resp);
    }
}
