package net.summerfarm.wnc.application.deliveryRule.converter;

import net.summerfarm.wnc.api.deliveryRule.dto.ContactDeliveryRuleDTO;
import net.summerfarm.wnc.domain.deliveryRule.entity.ContactDeliveryRuleEntity;

/**
 * Description: <br/>
 * date: 2023/11/13 16:06<br/>
 *
 * <AUTHOR> />
 */
public class ContactDeliveryRuleDTOConverter {

    public static ContactDeliveryRuleDTO entity2DTO(ContactDeliveryRuleEntity entity){
        if(entity == null){
            return null;
        }
        ContactDeliveryRuleDTO dto = new ContactDeliveryRuleDTO();

        dto.setId(entity.getId());
        dto.setCreateTime(entity.getCreateTime());
        dto.setUpdateTime(entity.getUpdateTime());
        dto.setOutBusinessNo(entity.getOutBusinessNo());
        dto.setSystemSource(entity.getSystemSource());
        dto.setFrequentMethod(entity.getFrequentMethod());
        dto.setWeekDeliveryFrequent(entity.getWeekDeliveryFrequent());
        dto.setDeliveryFrequentInterval(entity.getDeliveryFrequentInterval());
        dto.setBeginCalculateDate(entity.getBeginCalculateDate());
        dto.setTenantId(entity.getTenantId());

        return dto;
    }
}
