package net.summerfarm.wnc.application.inbound.provider.closeTime;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import net.summerfarm.wnc.application.inbound.converter.closeTime.CloseTimeAreaConfigParamConverter;
import net.summerfarm.wnc.application.inbound.converter.closeTime.CloseTimeAreaConfigRespConverter;
import net.summerfarm.wnc.client.provider.closeTime.CloseTimeConfigQueryProvider;
import net.summerfarm.wnc.client.req.closeTime.CloseTimeAreaConfigQueryReq;
import net.summerfarm.wnc.client.resp.closeTime.CloseTimeAreaConfigResp;
import net.summerfarm.wnc.common.constants.AppConsts;
import net.summerfarm.wnc.domain.closeTime.CloseTimeConfigQueryRepository;
import net.summerfarm.wnc.domain.closeTime.entity.CloseTimeAreaConfigEntity;
import net.summerfarm.wnc.domain.closeTime.param.CloseTimeAreaConfigQueryParam;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Description:截单配置查询提供者实现类
 * date: 2024/3/20 10:20
 *
 * <AUTHOR>
 */
@DubboService
public class CloseTimeConfigQueryProviderImpl implements CloseTimeConfigQueryProvider {

    @Resource
    private CloseTimeConfigQueryRepository closeTimeConfigQueryRepository;

    @Override
    public DubboResponse<PageInfo<CloseTimeAreaConfigResp>> queryCloseTimeAreaConfigPage(@Valid CloseTimeAreaConfigQueryReq queryReq) {
        CloseTimeAreaConfigQueryParam queryParam = CloseTimeAreaConfigParamConverter.req2Param(queryReq);
        queryParam.setBrandId(queryParam.getTenantBrandId());

        PageInfo<CloseTimeAreaConfigEntity> queryPageInfo = closeTimeConfigQueryRepository.queryPage(queryParam);
        List<CloseTimeAreaConfigResp> respList = Optional.ofNullable(queryPageInfo.getList()).orElse(Lists.newArrayList())
                .stream().map(CloseTimeAreaConfigRespConverter::entity2Resp).collect(Collectors.toList());
        PageInfo<CloseTimeAreaConfigResp> resultPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(queryPageInfo, resultPageInfo);
        resultPageInfo.setList(respList);
        return DubboResponse.getOK(resultPageInfo);
    }
}
