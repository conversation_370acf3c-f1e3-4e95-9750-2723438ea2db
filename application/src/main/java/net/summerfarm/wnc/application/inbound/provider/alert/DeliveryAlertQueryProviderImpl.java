package net.summerfarm.wnc.application.inbound.provider.alert;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.client.provider.alert.provider.DeliveryAlertQueryProvider;
import net.summerfarm.wnc.client.provider.alert.req.SummerfarmDeliveryAlertTimeQueryReq;
import net.summerfarm.wnc.client.provider.alert.resp.SummerfarmDeliveryAlertTimeResp;
import net.summerfarm.wnc.common.config.RedisKeyConfig;
import net.summerfarm.wnc.common.config.WncConfig;
import net.summerfarm.wnc.common.enums.DeliveryAlertEnums;
import net.summerfarm.wnc.common.query.alert.LastDeliveryTimeQueryParam;
import net.summerfarm.wnc.common.util.RedisCacheUtil;
import net.summerfarm.wnc.domain.alert.DeliveryAlertDomainService;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalTime;
import java.util.List;

/**
 * Description: 配送时间预警查询服务提供者<br/>
 * date: 2024/3/29 11:40<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@DubboService
public class DeliveryAlertQueryProviderImpl implements DeliveryAlertQueryProvider {

    @Resource
    private DeliveryAlertDomainService deliveryAlertDomainService;
    @Resource
    private RedisCacheUtil redisCacheUtil;
    @Resource
    private WncConfig wncConfig;

    @Override
    public DubboResponse<SummerfarmDeliveryAlertTimeResp> querySummerfarmDeliveryAlertTime(@Valid SummerfarmDeliveryAlertTimeQueryReq req) {
        List<Integer> popStoreNos = wncConfig.queryPopStoreNos();
        if (!CollectionUtils.isEmpty(popStoreNos) && popStoreNos.contains(req.getStoreNo())) {
            return DubboResponse.getOK();
        }
        LastDeliveryTimeQueryParam queryParam = LastDeliveryTimeQueryParam.builder()
                .storeNo(req.getStoreNo())
                .outerTenantId(req.getAdminId())
                .outerClientId(req.getMerchantId())
                .channel(DeliveryAlertEnums.Channel.XM_MALL)
                .city(req.getCity())
                .area(req.getArea())
                .build();

        String key = RedisKeyConfig.SUMMERFARM_DELIVERY_ALERT_TIME_CACHE_KEY +
                queryParam.getStoreNo() +
                queryParam.getCity() +
                queryParam.getArea() +
                queryParam.getOuterClientId() +
                queryParam.getOuterTenantId() +
                queryParam.getChannel();

        LocalTime lastDeliveryTime = redisCacheUtil.getCacheObjectValue(key, 60L,
                () -> deliveryAlertDomainService.queryDeliveryAlertTimeFrameList(queryParam),LocalTime.class);

        SummerfarmDeliveryAlertTimeResp resp = new SummerfarmDeliveryAlertTimeResp();
        resp.setLastDeliveryTime(lastDeliveryTime);

        return DubboResponse.getOK(resp);
    }
}
