package net.summerfarm.wnc.application.exce.converter;

import net.summerfarm.wnc.application.exce.pojo.AreaSkuManyWarehousePojo;
import net.summerfarm.wnc.domain.fence.dataObject.AreaSkuManyWarehouseDO;

/**
 * Description: <br/>
 * date: 2023/12/29 11:30<br/>
 *
 * <AUTHOR> />
 */
public class AreaSkuManyWarePojoConverter {

    public static AreaSkuManyWarehousePojo convert(AreaSkuManyWarehouseDO areaSkuManyWarehouseDO) {
        if(areaSkuManyWarehouseDO == null){
            return null;
        }
        AreaSkuManyWarehousePojo pojo = new AreaSkuManyWarehousePojo();

        pojo.setAreaNo(areaSkuManyWarehouseDO.getAreaNo());
        pojo.setAreaName(areaSkuManyWarehouseDO.getAreaName());
        pojo.setSku(areaSkuManyWarehouseDO.getSku());
        pojo.setManyWarehouseName(areaSkuManyWarehouseDO.getManyWarehouseName());
        pojo.setManyFenceName(areaSkuManyWarehouseDO.getManyFenceName());
        pojo.setManyStoreName(areaSkuManyWarehouseDO.getManyStoreName());

        return pojo;
    }
}
