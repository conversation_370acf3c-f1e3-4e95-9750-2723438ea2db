package net.summerfarm.wnc.application.auth;

import net.summerfarm.wnc.api.auth.AuthService;
import net.summerfarm.wnc.common.auth.dto.SaasTokenInfoDTO;
import net.summerfarm.wnc.facade.saas.XmAuthFacade;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Description: <br/>
 * date: 2023/5/4 11:02<br/>
 *
 * <AUTHOR> />
 */
@Service
public class AuthServiceImpl implements AuthService {

    @Resource
    private XmAuthFacade xmAuthFacade;

    @Override
    public SaasTokenInfoDTO querySaasTokenInfo(String adminToTenantKey) {
        return xmAuthFacade.doRpcCheckSaasToken(adminToTenantKey);
    }
}
