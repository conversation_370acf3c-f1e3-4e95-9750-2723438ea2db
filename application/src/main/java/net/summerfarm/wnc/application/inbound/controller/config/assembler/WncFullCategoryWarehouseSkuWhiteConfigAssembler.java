package net.summerfarm.wnc.application.inbound.controller.config.assembler;


import net.summerfarm.wnc.application.inbound.controller.config.vo.WncFullCategoryWarehouseSkuWhiteConfigVO;
import net.summerfarm.wnc.domain.config.entity.WncFullCategoryWarehouseSkuWhiteConfigEntity;
import net.summerfarm.wnc.application.inbound.controller.config.input.command.WncFullCategoryWarehouseSkuWhiteConfigCommandInput;
import net.summerfarm.wnc.application.inbound.controller.config.input.query.WncFullCategoryWarehouseSkuWhiteConfigQueryInput;
import net.summerfarm.wnc.domain.config.param.query.WncFullCategoryWarehouseSkuWhiteConfigQueryParam;
import net.summerfarm.wnc.domain.config.param.command.WncFullCategoryWarehouseSkuWhiteConfigCommandParam;

import java.util.ArrayList;
import java.util.List;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025-01-06 15:31:26
 * @version 1.0
 *
 */
public class WncFullCategoryWarehouseSkuWhiteConfigAssembler {

    private WncFullCategoryWarehouseSkuWhiteConfigAssembler() {
        // 无需实现
    }


// ------------------------------- request ----------------------------
    public static WncFullCategoryWarehouseSkuWhiteConfigQueryParam toWncFullCategoryWarehouseSkuWhiteConfigQueryParam(WncFullCategoryWarehouseSkuWhiteConfigQueryInput wncFullCategoryWarehouseSkuWhiteConfigQueryInput) {
        if (wncFullCategoryWarehouseSkuWhiteConfigQueryInput == null) {
            return null;
        }
        WncFullCategoryWarehouseSkuWhiteConfigQueryParam wncFullCategoryWarehouseSkuWhiteConfigQueryParam = new WncFullCategoryWarehouseSkuWhiteConfigQueryParam();
        wncFullCategoryWarehouseSkuWhiteConfigQueryParam.setId(wncFullCategoryWarehouseSkuWhiteConfigQueryInput.getId());
        wncFullCategoryWarehouseSkuWhiteConfigQueryParam.setCreateTime(wncFullCategoryWarehouseSkuWhiteConfigQueryInput.getCreateTime());
        wncFullCategoryWarehouseSkuWhiteConfigQueryParam.setUpdateTime(wncFullCategoryWarehouseSkuWhiteConfigQueryInput.getUpdateTime());
        wncFullCategoryWarehouseSkuWhiteConfigQueryParam.setWarehouseNo(wncFullCategoryWarehouseSkuWhiteConfigQueryInput.getWarehouseNo());
        wncFullCategoryWarehouseSkuWhiteConfigQueryParam.setSku(wncFullCategoryWarehouseSkuWhiteConfigQueryInput.getSku());
        wncFullCategoryWarehouseSkuWhiteConfigQueryParam.setPageIndex(wncFullCategoryWarehouseSkuWhiteConfigQueryInput.getPageIndex());
        wncFullCategoryWarehouseSkuWhiteConfigQueryParam.setPageSize(wncFullCategoryWarehouseSkuWhiteConfigQueryInput.getPageSize());
        return wncFullCategoryWarehouseSkuWhiteConfigQueryParam;
    }





    public static WncFullCategoryWarehouseSkuWhiteConfigCommandParam buildCreateParam(WncFullCategoryWarehouseSkuWhiteConfigCommandInput wncFullCategoryWarehouseSkuWhiteConfigCommandInput) {
        if (wncFullCategoryWarehouseSkuWhiteConfigCommandInput == null) {
            return null;
        }
        WncFullCategoryWarehouseSkuWhiteConfigCommandParam wncFullCategoryWarehouseSkuWhiteConfigCommandParam = new WncFullCategoryWarehouseSkuWhiteConfigCommandParam();
        wncFullCategoryWarehouseSkuWhiteConfigCommandParam.setId(wncFullCategoryWarehouseSkuWhiteConfigCommandInput.getId());
        wncFullCategoryWarehouseSkuWhiteConfigCommandParam.setWarehouseNo(wncFullCategoryWarehouseSkuWhiteConfigCommandInput.getWarehouseNo());
        wncFullCategoryWarehouseSkuWhiteConfigCommandParam.setSku(wncFullCategoryWarehouseSkuWhiteConfigCommandInput.getSku());
        return wncFullCategoryWarehouseSkuWhiteConfigCommandParam;
    }

    public static WncFullCategoryWarehouseSkuWhiteConfigQueryParam buildQueryParam(WncFullCategoryWarehouseSkuWhiteConfigCommandInput wncFullCategoryWarehouseSkuWhiteConfigCommandInput) {
        if (wncFullCategoryWarehouseSkuWhiteConfigCommandInput == null) {
            return null;
        }
        WncFullCategoryWarehouseSkuWhiteConfigQueryParam queryParam = new WncFullCategoryWarehouseSkuWhiteConfigQueryParam();
        queryParam.setWarehouseNo(wncFullCategoryWarehouseSkuWhiteConfigCommandInput.getWarehouseNo());
        queryParam.setSku(wncFullCategoryWarehouseSkuWhiteConfigCommandInput.getSku());
        return queryParam;
    }


    public static WncFullCategoryWarehouseSkuWhiteConfigCommandParam buildUpdateParam(WncFullCategoryWarehouseSkuWhiteConfigCommandInput wncFullCategoryWarehouseSkuWhiteConfigCommandInput) {
        if (wncFullCategoryWarehouseSkuWhiteConfigCommandInput == null) {
            return null;
        }
        WncFullCategoryWarehouseSkuWhiteConfigCommandParam wncFullCategoryWarehouseSkuWhiteConfigCommandParam = new WncFullCategoryWarehouseSkuWhiteConfigCommandParam();
        wncFullCategoryWarehouseSkuWhiteConfigCommandParam.setId(wncFullCategoryWarehouseSkuWhiteConfigCommandInput.getId());
        wncFullCategoryWarehouseSkuWhiteConfigCommandParam.setWarehouseNo(wncFullCategoryWarehouseSkuWhiteConfigCommandInput.getWarehouseNo());
        wncFullCategoryWarehouseSkuWhiteConfigCommandParam.setSku(wncFullCategoryWarehouseSkuWhiteConfigCommandInput.getSku());
        return wncFullCategoryWarehouseSkuWhiteConfigCommandParam;
    }




// ------------------------------- response ----------------------------

    public static List<WncFullCategoryWarehouseSkuWhiteConfigVO> toWncFullCategoryWarehouseSkuWhiteConfigVOList(List<WncFullCategoryWarehouseSkuWhiteConfigEntity> wncFullCategoryWarehouseSkuWhiteConfigEntityList) {
        if (wncFullCategoryWarehouseSkuWhiteConfigEntityList == null) {
            return Collections.emptyList();
        }
        List<WncFullCategoryWarehouseSkuWhiteConfigVO> wncFullCategoryWarehouseSkuWhiteConfigVOList = new ArrayList<>();
        for (WncFullCategoryWarehouseSkuWhiteConfigEntity wncFullCategoryWarehouseSkuWhiteConfigEntity : wncFullCategoryWarehouseSkuWhiteConfigEntityList) {
            wncFullCategoryWarehouseSkuWhiteConfigVOList.add(toWncFullCategoryWarehouseSkuWhiteConfigVO(wncFullCategoryWarehouseSkuWhiteConfigEntity));
        }
        return wncFullCategoryWarehouseSkuWhiteConfigVOList;
}


   public static WncFullCategoryWarehouseSkuWhiteConfigVO toWncFullCategoryWarehouseSkuWhiteConfigVO(WncFullCategoryWarehouseSkuWhiteConfigEntity wncFullCategoryWarehouseSkuWhiteConfigEntity) {
       if (wncFullCategoryWarehouseSkuWhiteConfigEntity == null) {
            return null;
       }
       WncFullCategoryWarehouseSkuWhiteConfigVO wncFullCategoryWarehouseSkuWhiteConfigVO = new WncFullCategoryWarehouseSkuWhiteConfigVO();
       wncFullCategoryWarehouseSkuWhiteConfigVO.setId(wncFullCategoryWarehouseSkuWhiteConfigEntity.getId());
       wncFullCategoryWarehouseSkuWhiteConfigVO.setCreateTime(wncFullCategoryWarehouseSkuWhiteConfigEntity.getCreateTime());
       wncFullCategoryWarehouseSkuWhiteConfigVO.setUpdateTime(wncFullCategoryWarehouseSkuWhiteConfigEntity.getUpdateTime());
       wncFullCategoryWarehouseSkuWhiteConfigVO.setWarehouseNo(wncFullCategoryWarehouseSkuWhiteConfigEntity.getWarehouseNo());
       wncFullCategoryWarehouseSkuWhiteConfigVO.setSku(wncFullCategoryWarehouseSkuWhiteConfigEntity.getSku());
       return wncFullCategoryWarehouseSkuWhiteConfigVO;
   }

}
