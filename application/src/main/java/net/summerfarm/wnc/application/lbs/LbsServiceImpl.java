package net.summerfarm.wnc.application.lbs;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.gaode.GaoDeUtil;
import net.summerfarm.dto.LocationGeoDTO;
import net.summerfarm.wnc.api.fence.dto.GeoDTO;
import net.summerfarm.wnc.api.fence.dto.LocationDTO;
import net.summerfarm.wnc.api.lbs.service.LbsService;
import net.summerfarm.wnc.common.query.fence.AddressQuery;
import net.summerfarm.wnc.common.util.ExecutorUtil;
import net.summerfarm.wnc.domain.fence.OutLandContactRepository;
import net.xianmu.common.exception.ProviderException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.RejectedExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description:位置服务接口实现
 * date: 2023/11/15 10:38
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class LbsServiceImpl implements LbsService {

    @Resource
    private OutLandContactRepository outLandContactRepository;

    @Override
    public LocationDTO queryPoi(AddressQuery addressQuery) {
        String completeAddress = addressQuery.getCompleteAddress();
        String poi = GaoDeUtil.getPoiByAddressV2(completeAddress);
        if (StrUtil.isBlank(poi)){
            throw new ProviderException("无法获取该地址poi信息，请检查地址合理性");
        }
        LocationDTO locationDTO = new LocationDTO();
        locationDTO.setCompleteAddress(completeAddress);
        locationDTO.setPoi(poi);
        return locationDTO;
    }

    @Override
    public List<LocationDTO> batchQueryPoi(List<AddressQuery> addressQueryList) {
        if (CollectionUtils.isEmpty(addressQueryList)){
            return Collections.emptyList();
        }
        List<String> addresses = addressQueryList.stream().map(AddressQuery::getCompleteAddress).collect(Collectors.toList());
        Map<String, Future<String>> map = Maps.newHashMapWithExpectedSize(addresses.size());
        for (String address : addresses) {
            try {
                Callable<String> callable = () -> GaoDeUtil.getPoiByAddressV2(address);
                Future<String> future = ExecutorUtil.gdPoiExecutor.submit(callable);
                map.put(address, future);
            } catch (RejectedExecutionException e){
                log.warn("根据文本地址批量查询poi超出线程池执行能力限制", e);
                map.put(address, null);
            }
        }
        List<LocationDTO> locationDTOList = new ArrayList<>();
        for (Map.Entry<String, Future<String>> entry : map.entrySet()) {
            LocationDTO locationDTO = new LocationDTO();
            locationDTO.setCompleteAddress(entry.getKey());
            Future<String> future = entry.getValue();
            if(future == null){
                locationDTO.setPoi("");
            }else {
                try {
                    String poi = future.get();
                    locationDTO.setPoi(poi);
                } catch (Exception e) {
                    // 处理异常
                    locationDTO.setPoi("");
                }
            }
            locationDTOList.add(locationDTO);
        }
        return locationDTOList;
    }

    @Override
    public List<LocationDTO> batchQueryLocation(List<AddressQuery> addressQueryList) {
        if (CollectionUtils.isEmpty(addressQueryList)){
            return Collections.emptyList();
        }
        List<String> addresses = addressQueryList.stream().map(AddressQuery::getCompleteAddress).collect(Collectors.toList());
        Map<String, Future<LocationGeoDTO>> map = Maps.newHashMapWithExpectedSize(addresses.size());
        for (String address : addresses) {
            try {
                Callable<LocationGeoDTO> callable = () -> GaoDeUtil.getLocationGeo(address);
                Future<LocationGeoDTO> future = ExecutorUtil.gdGeoExecutor.submit(callable);
                map.put(address, future);
            } catch (RejectedExecutionException e){
                log.warn("根据文本地址批量查询geo超出线程池执行能力限制", e);
                map.put(address, null);
            }
        }
        List<LocationDTO> locationDTOList = new ArrayList<>();
        for (Map.Entry<String, Future<LocationGeoDTO>> entry : map.entrySet()) {
            LocationDTO locationDTO = new LocationDTO();
            locationDTO.setCompleteAddress(entry.getKey());
            Future<LocationGeoDTO> future = entry.getValue();
            if(future == null){
                locationDTO.setPoi("");
                locationDTO.setGeoDTO(null);
            }else {
                try {
                    LocationGeoDTO locationGeoDTO = future.get();
                    GeoDTO geoDTO = new GeoDTO();
                    geoDTO.setProvince(locationGeoDTO.getProvince());
                    geoDTO.setCity(locationGeoDTO.getCity());
                    geoDTO.setArea(locationGeoDTO.getArea());
                    geoDTO.setTownship(locationGeoDTO.getTownship());
                    geoDTO.setStreet(locationGeoDTO.getStreet());
                    geoDTO.setNumber(locationGeoDTO.getNumber());
                    geoDTO.setAddress(locationGeoDTO.getAddress());
                    locationDTO.setGeoDTO(geoDTO);
                    locationDTO.setPoi(locationGeoDTO.getLocation());
                } catch (Exception e) {
                    // 处理异常
                    locationDTO.setPoi("");
                    locationDTO.setGeoDTO(null);
                }
            }
            locationDTOList.add(locationDTO);
        }
        return locationDTOList;
    }

    @Override
    public void updateNingJiAddressPoi() {
        outLandContactRepository.updateNingJiAddressPoi();
    }

    @Override
    public void updateAddressPoi(List<Long> contactIds) {
        outLandContactRepository.updateAddressPoi(contactIds);
    }
}
