package net.summerfarm.wnc.application.warehouse;

import lombok.NonNull;
import net.summerfarm.wnc.api.warehouse.dto.WarehouseBySkuAreaNoDTO;
import net.summerfarm.wnc.api.warehouse.service.warehouse.WncWarehouseCenterBySkuAreaService;
import net.summerfarm.wnc.common.query.warehouse.WarehouseBySkuAreaNoQuery;
import net.summerfarm.wnc.common.query.warehouse.WarehouseInventoryMappingQuery;
import net.summerfarm.wnc.common.query.warehouse.WarehouseStorageQuery;
import net.summerfarm.wnc.domain.fence.DeliveryFenceRepository;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.summerfarm.wnc.domain.warehouse.WarehouseInventoryMappingRepository;
import net.summerfarm.wnc.domain.warehouse.WarehouseStorageCenterRepository;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseInventoryMappingEntity;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseStorageEntity;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023-07-04
 **/
@Service
public class WncWarehouseCenterBySkuAreaServiceImpl implements WncWarehouseCenterBySkuAreaService {
	@Resource
	private DeliveryFenceRepository deliveryFenceRepository;
	@Resource
	private WarehouseInventoryMappingRepository warehouseInventoryMappingRepository;
	@Resource
	private WarehouseStorageCenterRepository warehouseStorageCenterRepository;

	@Override
	public List<WarehouseBySkuAreaNoDTO> queryBySkuArea(@NonNull List<WarehouseBySkuAreaNoQuery> query) {
		if (CollectionUtils.isEmpty(query)) {
			return null;
		}
		Set<Integer> areaNoSets = query.stream().flatMap(a -> a.getAreaNoList().stream()).collect(Collectors.toSet());
		List<FenceEntity> fenceEntities = deliveryFenceRepository.queryListFenceByAreaNo(areaNoSets);
		if (CollectionUtils.isEmpty(fenceEntities)) {
			return null;
		}
		Map<Integer, List<FenceEntity>> totalAreaFenceList = fenceEntities.stream().collect(Collectors.groupingBy(FenceEntity::getAreaNo));

		// 获取库存仓关系map对象
		Map<String, List<WarehouseInventoryMappingEntity>> warehouseInventoryMappingMap = getWarehouseInventoryMappingMap(query, fenceEntities);
		if (warehouseInventoryMappingMap == null) {
			return null;
		}
		List<WarehouseBySkuAreaNoDTO> responseList = Lists.newArrayList();
		for (WarehouseBySkuAreaNoQuery warehouseBySkuAreaNoQuery : query) {
			String sku = warehouseBySkuAreaNoQuery.getSku();
			final List<WarehouseInventoryMappingEntity> existWarehouseInventoryMappingEntityList = warehouseInventoryMappingMap.get(sku);
			if (CollectionUtils.isEmpty(existWarehouseInventoryMappingEntityList)) {
				continue;
			}

			List<Integer> areaNoList = warehouseBySkuAreaNoQuery.getAreaNoList();
			for (Integer areaNo : areaNoList) {
				List<FenceEntity> areaFenceList = totalAreaFenceList.get(areaNo);
				if (CollectionUtils.isEmpty(areaFenceList)) {
					continue;
				}
				Set<Integer> storeNos = areaFenceList.stream().map(FenceEntity::getStoreNo).collect(Collectors.toSet());
				Set<Integer> storeNoWarehouseWarehouseNoSet = existWarehouseInventoryMappingEntityList.stream()
						.filter(a -> storeNos.contains(a.getStoreNo())).map(WarehouseInventoryMappingEntity::getWarehouseNo).collect(Collectors.toSet());
				if (CollectionUtils.isEmpty(storeNoWarehouseWarehouseNoSet)) {
					continue;
				}
				List<WarehouseBySkuAreaNoDTO> warehouseBySkuAreaNoList = storeNoWarehouseWarehouseNoSet.stream().map(a -> {
					WarehouseBySkuAreaNoDTO warehouseBySkuAreaNoDTO = new WarehouseBySkuAreaNoDTO();
					warehouseBySkuAreaNoDTO.setWarehouseNo(a);
					warehouseBySkuAreaNoDTO.setSku(sku);
					warehouseBySkuAreaNoDTO.setAreaNo(areaNo);
					return warehouseBySkuAreaNoDTO;
				}).collect(Collectors.toList());
				responseList.addAll(warehouseBySkuAreaNoList);
			}
		}

		if (CollectionUtils.isEmpty(responseList)) {
			return null;
		}
		//处理库存仓名称信息
		handleWarehouseName(responseList);
		return responseList;
	}

	/**
	 * 获取库存仓关系map对象
	 *
	 * @param query
	 * @param fenceEntities
	 * @return
	 */
	private Map<String, List<WarehouseInventoryMappingEntity>> getWarehouseInventoryMappingMap(List<WarehouseBySkuAreaNoQuery> query, List<FenceEntity> fenceEntities) {
		List<String> skuList = query.stream().map(WarehouseBySkuAreaNoQuery::getSku).collect(Collectors.toList());
		List<Integer> storeNoList = fenceEntities.stream().map(FenceEntity::getStoreNo).collect(Collectors.toList());
		WarehouseInventoryMappingQuery inventoryMappingQuery = new WarehouseInventoryMappingQuery();
		inventoryMappingQuery.setSkus(skuList);
		inventoryMappingQuery.setStoreNos(storeNoList);
		List<WarehouseInventoryMappingEntity> warehouseInventoryMappingEntities = warehouseInventoryMappingRepository.queryList(inventoryMappingQuery);
		if (CollectionUtils.isEmpty(warehouseInventoryMappingEntities)) {
			return null;
		}
		Map<String, List<WarehouseInventoryMappingEntity>> warehouseInventoryMappingMap = warehouseInventoryMappingEntities.stream()
				.collect(Collectors.groupingBy(WarehouseInventoryMappingEntity::getSku));
		return warehouseInventoryMappingMap;
	}

	/**
	 * 处理库存仓名称信息
	 *
	 * @param responseList
	 */
	private void handleWarehouseName(List<WarehouseBySkuAreaNoDTO> responseList) {
		if (CollectionUtils.isEmpty(responseList)) {
			return;
		}
		WarehouseStorageQuery warehouseStorageQuery = new WarehouseStorageQuery();
		warehouseStorageQuery.setWarehouseNos(responseList.stream()
				.map(WarehouseBySkuAreaNoDTO::getWarehouseNo).collect(Collectors.toList()));
		List<WarehouseStorageEntity> warehouseStorageEntities = warehouseStorageCenterRepository.queryWarehouseStorage(warehouseStorageQuery);
		if (!CollectionUtils.isEmpty(warehouseStorageEntities)) {
			Map<Integer, WarehouseStorageEntity> warehouseStorageEntityMap = warehouseStorageEntities.stream().collect(Collectors
					.toMap(WarehouseStorageEntity::getWarehouseNo, Function.identity(), (a, b) -> a));
			for (WarehouseBySkuAreaNoDTO warehouseBySkuAreaNoDTO : responseList) {
				WarehouseStorageEntity warehouseStorageEntity = warehouseStorageEntityMap.get(warehouseBySkuAreaNoDTO.getWarehouseNo());
				if (Objects.nonNull(warehouseStorageEntity)) {
					warehouseBySkuAreaNoDTO.setWarehouseName(warehouseStorageEntity.getWarehouseName());
				}

			}
		}
	}
}
