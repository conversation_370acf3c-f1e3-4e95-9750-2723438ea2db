package net.summerfarm.wnc.application.inbound.controller.preciseDelivery.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * Description:精准送配置更新命令实体
 * date: 2024/1/18 18:34
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PreciseDeliveryConfigUpdateCommandInput implements Serializable {

    private static final long serialVersionUID = -6745841175239823911L;

    /**
     * 精准送配置ID
     */
    @NotNull(message = "精准送配置ID不能为空")
    private Long configId;

    /**
     * 省
     */
    @NotBlank(message = "省不能为空")
    private String province;

    /**
     * 市
     */
    @NotBlank(message = "市不能为空")
    private String city;

    /**
     * 精准送配置区域集合
     */
    @Valid
    @NotEmpty(message = "精准送配置区域不能为空")
    private List<PreciseDeliveryConfigAreaCommandInput> areaList;

    /**
     * 精准送配置时效集合
     */
    @Valid
    @NotEmpty(message = "精准送配置时效不能为空")
    private List<PreciseDeliveryConfigTimeCommandInput> timeList;
}
