package net.summerfarm.wnc.application.inbound.provider.deliveryRuleConfig;

import net.summerfarm.wnc.api.config.input.ContactConfigCreateCommandInput;
import net.summerfarm.wnc.api.config.input.ContactConfigRemoveCommandInput;
import net.summerfarm.wnc.api.config.service.ContactConfigCommandService;
import net.summerfarm.wnc.api.deliveryRule.input.ContactDeliveryRuleDelCommand;
import net.summerfarm.wnc.api.deliveryRule.input.ContactDeliveryRuleSaveUpdateCommand;
import net.summerfarm.wnc.api.deliveryRule.service.ContactDeliveryRuleService;
import net.summerfarm.wnc.client.provider.deliveryRuleConfig.ContactDeliveryRuleConfigCommandProvider;
import net.summerfarm.wnc.client.req.deliveryRuleConfig.ContactConfigCommandReq;
import net.summerfarm.wnc.client.req.deliveryRuleConfig.DeliveryRuleCommandReq;
import net.summerfarm.wnc.client.req.deliveryRuleConfig.DeliveryRuleConfigSaveOrUpdateCommandReq;
import net.summerfarm.wnc.common.enums.WncContactDeliveryRuleEnums;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * Description: 地址城配仓、配送周期配置操作服务<br/>
 * date: 2024/4/16 11:07<br/>
 *
 * <AUTHOR> />
 */
@DubboService
public class ContactDeliveryRuleConfigCommandProviderImpl implements ContactDeliveryRuleConfigCommandProvider {

    @Resource
    private ContactDeliveryRuleService contactDeliveryRuleService;
    @Resource
    private ContactConfigCommandService contactConfigCommandService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public DubboResponse<Void> outerOrderDeliveryRuleConfigSaveOrUpdate(@Valid DeliveryRuleConfigSaveOrUpdateCommandReq req) {
        Long contactId = req.getContactId();
        Long tenantId = req.getTenantId();

        ContactConfigCommandReq contactConfigReq = req.getContactConfigCommandReq();
        DeliveryRuleCommandReq deliveryRuleReq = req.getDeliveryRuleCommandReq();

        if(contactConfigReq != null){
            //保存更新客户指定城配仓
            contactConfigCommandService.saveOrUpdate(ContactConfigCreateCommandInput.builder()
                    .storeNo(contactConfigReq.getStoreNo())
                    .outerContactId(contactId)
                    .tenantId(tenantId)
                    .build());
        }else{
            //删除指定的城配仓配置
            contactConfigCommandService.removeConfig(ContactConfigRemoveCommandInput.builder()
                    .outerContactId(req.getContactId())
                    .tenantId(req.getTenantId())
                    .build());
        }

        if(deliveryRuleReq != null){
            //保存更新配送周期
            contactDeliveryRuleService.saveOrUpdate(ContactDeliveryRuleSaveUpdateCommand.builder()
                    .frequentMethod(deliveryRuleReq.getFrequentMethod())
                    .weekDeliveryFrequent(deliveryRuleReq.getWeekDeliveryFrequent())
                    .deliveryFrequentInterval(deliveryRuleReq.getDeliveryFrequentInterval())
                    .beginCalculateDate(deliveryRuleReq.getBeginCalculateDate())
                    .systemSource(WncContactDeliveryRuleEnums.SystemSource.getSourceByTenantId(tenantId).getValue())
                    .tenantId(tenantId)
                    .outBusinessNo(String.valueOf(contactId))
                    .build());
        }else{
            //删除指定的配送周期
            contactDeliveryRuleService.delteDeliveryRule(ContactDeliveryRuleDelCommand.builder()
                    .outBusinessNo(String.valueOf(req.getContactId()))
                    .systemSource(WncContactDeliveryRuleEnums.SystemSource.getSourceByTenantId(req.getTenantId()).getValue())
                    .build());
        }
        return DubboResponse.getOK();
    }
}
