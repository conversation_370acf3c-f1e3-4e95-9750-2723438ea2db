package net.summerfarm.wnc.application.inbound.scheduler.closeTime;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.domain.closeTime.CloseTimeConfigCommandDomainService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * Description:截单时间区域配置生效任务
 * date: 2024/3/20 10:53
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CloseTimeAreaConfigTakeEffectJob extends XianMuJavaProcessorV2 {

    @Resource
    private CloseTimeConfigCommandDomainService closeTimeConfigCommandDomainService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProcessResult processResult(XmJobInput context) throws Exception {
        //次日凌晨 0点执行
        log.info("截单时间区域配置生效任务开始执行");
        closeTimeConfigCommandDomainService.takeEffectAreaConfig();
        return new ProcessResult(true);
    }
}
