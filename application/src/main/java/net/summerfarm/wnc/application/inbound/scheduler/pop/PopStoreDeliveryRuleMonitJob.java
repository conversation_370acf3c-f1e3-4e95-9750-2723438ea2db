package net.summerfarm.wnc.application.inbound.scheduler.pop;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.common.config.WncConfig;
import net.summerfarm.wnc.domain.config.repository.ContactConfigQueryRepository;
import net.summerfarm.wnc.domain.config.monit.PopMerchantMonit;
import net.summerfarm.wnc.domain.deliveryRule.ContactDeliveryRuleRepository;
import net.xianmu.robot.feishu.FeishuBotUtil;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.List;

/**
 * Description: pop城配仓和配送周期监控告警<br/>
 * date: 2024/8/2 15:16<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Component
public class PopStoreDeliveryRuleMonitJob extends XianMuJavaProcessorV2 {

    @Resource
    private ContactDeliveryRuleRepository contactDeliveryRuleRepository;

    @Resource
    private ContactConfigQueryRepository contactConfigQueryRepository;

    @Resource
    private WncConfig wncConfig;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        Integer adminId = Integer.parseInt(wncConfig.getPopAdminId());
        String popMonitorRobotUrl = wncConfig.getPopMonitorRobotUrl();

        List<PopMerchantMonit> deliveryRuleMonit = contactDeliveryRuleRepository.queryPopNoDeliveryRuleMonit(adminId);
        List<PopMerchantMonit> storeNoMonit = contactConfigQueryRepository.queryPopNoHaveStoreNoMonit(adminId);

        StringBuilder textMsg = new StringBuilder();

        if(CollectionUtils.isNotEmpty(storeNoMonit)){
            textMsg.append("存在POP客户城配仓未指定,门店如下-----------:").append("\n");
            storeNoMonit.forEach(storeNoHave -> {
                textMsg.append(storeNoHave.toString()).append("\n");// NOSONAR
            });

        }
        if(CollectionUtils.isNotEmpty(deliveryRuleMonit)){
            textMsg.append("存在POP客户配送周期未指定,门店如下---------:").append("\n");
            deliveryRuleMonit.forEach(deliveryRule -> {
                textMsg.append(deliveryRule.toString()).append("\n");// NOSONAR
            });
        }
        // 非空发送
        if(StringUtils.isNotBlank(textMsg.toString())){
            FeishuBotUtil.sendMarkdownMsgAndAtAll(popMonitorRobotUrl,textMsg.toString());
        }
        return new ProcessResult(true);
    }
}
