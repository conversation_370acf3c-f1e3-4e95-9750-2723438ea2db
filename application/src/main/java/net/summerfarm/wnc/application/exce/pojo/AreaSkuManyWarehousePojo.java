package net.summerfarm.wnc.application.exce.pojo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * Description: 运营区域sku多仓映射<br/>
 * date: 2023/12/29 11:28<br/>
 *
 * @authr ChenXh<br />
 */
@Data
public class AreaSkuManyWarehousePojo {

    /**
     * 运营区域编号
     */
    @ExcelProperty(value = {"运营区域编号"}, index = 0)
    @ColumnWidth(20)
    private Integer areaNo;

    /**
     * 运营区域名称
     */
    @ExcelProperty(value = {"运营区域名称"}, index = 1)
    @ColumnWidth(20)
    private String areaName;

    /**
     * sku
     */
    @ExcelProperty(value = {"sku"}, index = 2)
    @ColumnWidth(25)
    private String sku;

    /**
     * 多仓库名称
     */
    @ExcelProperty(value = {"多仓库名称"}, index = 3)
    @ColumnWidth(30)
    private String manyWarehouseName;

    /**
     * 多围栏名称
     */
    @ExcelProperty(value = {"多围栏名称"}, index = 4)
    @ColumnWidth(40)
    private String manyFenceName;

    /**
     * 城配仓名称
     */
    @ExcelProperty(value = {"城配仓名称"}, index = 5)
    @ColumnWidth(40)
    private String manyStoreName;
}
