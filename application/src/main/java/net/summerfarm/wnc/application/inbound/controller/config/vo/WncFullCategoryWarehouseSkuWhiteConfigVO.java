package net.summerfarm.wnc.application.inbound.controller.config.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2025-01-06 15:31:26
 * @version 1.0
 *
 */
@Data
public class WncFullCategoryWarehouseSkuWhiteConfigVO implements Serializable{
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime updateTime;

	/**
	 * 库存仓编号
	 */
	private Integer warehouseNo;

	/**
	 * 仓库名称
	 */
	private String warehouseName;

	/**
	 * sku
	 */
	private String sku;



}