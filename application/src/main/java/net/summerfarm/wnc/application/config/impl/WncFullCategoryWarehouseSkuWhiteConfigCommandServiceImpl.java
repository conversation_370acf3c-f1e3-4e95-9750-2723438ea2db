package net.summerfarm.wnc.application.config.impl;


import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.application.config.WncFullCategoryWarehouseSkuWhiteConfigCommandService;
import net.summerfarm.wnc.domain.config.param.query.WncFullCategoryWarehouseSkuWhiteConfigQueryParam;
import net.summerfarm.wnc.domain.config.repository.WncFullCategoryWarehouseSkuWhiteConfigQueryRepository;
import net.summerfarm.wnc.domain.config.service.WncFullCategoryWarehouseSkuWhiteConfigCommandDomainService;
import net.summerfarm.wnc.domain.config.entity.WncFullCategoryWarehouseSkuWhiteConfigEntity;
import net.summerfarm.wnc.domain.config.param.command.WncFullCategoryWarehouseSkuWhiteConfigCommandParam;
import net.summerfarm.wnc.application.inbound.controller.config.input.command.WncFullCategoryWarehouseSkuWhiteConfigCommandInput;
import net.summerfarm.wnc.application.inbound.controller.config.assembler.WncFullCategoryWarehouseSkuWhiteConfigAssembler;
import net.summerfarm.wnc.facade.inventory.SupplierInventoryFacade;
import net.summerfarm.wnc.facade.inventory.input.CleanDefaultSupplierInventoryInput;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.user.UserInfoHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2025-01-06 15:31:26
* @version 1.0
*
*/
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class WncFullCategoryWarehouseSkuWhiteConfigCommandServiceImpl implements WncFullCategoryWarehouseSkuWhiteConfigCommandService {

    @Autowired
    private SupplierInventoryFacade supplierInventoryFacade;
    @Autowired
    private WncFullCategoryWarehouseSkuWhiteConfigCommandDomainService wncFullCategoryWarehouseSkuWhiteConfigCommandDomainService;
    @Autowired
    private WncFullCategoryWarehouseSkuWhiteConfigQueryRepository wncFullCategoryWarehouseSkuWhiteConfigQueryRepository;


    @Override
    public WncFullCategoryWarehouseSkuWhiteConfigEntity insert(WncFullCategoryWarehouseSkuWhiteConfigCommandInput input) {
        log.info("插入三方仓全品类转采白名单配置，operator:{}，input:{}", UserInfoHolder.getUserRealName(), JSON.toJSONString(input));
        WncFullCategoryWarehouseSkuWhiteConfigQueryParam queryParam = WncFullCategoryWarehouseSkuWhiteConfigAssembler.buildQueryParam(input);
        List<WncFullCategoryWarehouseSkuWhiteConfigEntity> entityList = wncFullCategoryWarehouseSkuWhiteConfigQueryRepository.selectByCondition(queryParam);
        if (!CollectionUtils.isEmpty(entityList)) {
            throw new BizException("已存在该库存仓该sku的白名单配置");
        }
        WncFullCategoryWarehouseSkuWhiteConfigCommandParam param = WncFullCategoryWarehouseSkuWhiteConfigAssembler.buildCreateParam(input);
        return wncFullCategoryWarehouseSkuWhiteConfigCommandDomainService.insert(param);
    }


    @Override
    public int update(WncFullCategoryWarehouseSkuWhiteConfigCommandInput input) {
        log.info("更新三方仓全品类转采白名单配置，operator:{}，input:{}", UserInfoHolder.getUserRealName(), JSON.toJSONString(input));
        WncFullCategoryWarehouseSkuWhiteConfigCommandParam param = WncFullCategoryWarehouseSkuWhiteConfigAssembler.buildUpdateParam(input);
        return wncFullCategoryWarehouseSkuWhiteConfigCommandDomainService.update(param);
    }


    @Override
    public int delete(Long id) {
        // 删除白名单配置前需要先将默认供应商库存清零（如果有）
        WncFullCategoryWarehouseSkuWhiteConfigEntity entity = wncFullCategoryWarehouseSkuWhiteConfigQueryRepository.selectById(id);
        if (entity == null) {
            throw new BizException("删除白名单时找不到对应的记录，id：" + id);
        }
        log.info("删除三方仓全品类转采白名单配置，operator:{}，entity:{}", UserInfoHolder.getUserRealName(), JSON.toJSONString(entity));
        CleanDefaultSupplierInventoryInput input = new CleanDefaultSupplierInventoryInput();
        input.setWarehouseNo(entity.getWarehouseNo());
        input.setSkuCode(entity.getSku());
        input.setIdempotentNo("TW_FC_WS_WC_"+ id + "_" + entity.getWarehouseNo() + "_" + entity.getSku());
        try {
            supplierInventoryFacade.cleanDefaultSupplierInventory(input);
        } catch (Exception ex) {
            log.error("清零供应商库存失败，input:{}", JSON.toJSONString(input), ex);
            throw new BizException("删除白名单前清零供应商库存失败，可能部分供应商库存已售出");
        }

        return wncFullCategoryWarehouseSkuWhiteConfigCommandDomainService.delete(id);
    }
}