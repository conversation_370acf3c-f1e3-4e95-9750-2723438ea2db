package net.summerfarm.wnc.application.fence.dto;

import lombok.Data;
import net.summerfarm.wnc.api.fence.dto.StopDeliveryDTO;

import java.time.LocalDate;
import java.util.List;

/**
 * Description: 配送规则<br/>
 * date: 2023/11/14 16:12<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliveryRuleDTO {

    /**
     * 周期方案 1周计算 2间隔计算
     */
    private Integer frequentMethod;

    /**
     * 周的配送周期 0每天 1周一依次
     */
    private String weekDeliveryFrequent;

    /**
     * 配送间隔周期
     */
    private Integer deliveryFrequentInterval;

    /**
     * 开始计算日期
     */
    private LocalDate beginCalculateDate;

    /**
     * 停配信息
     */
    private List<StopDeliveryDTO> stopDeliveryDTOList;
}
