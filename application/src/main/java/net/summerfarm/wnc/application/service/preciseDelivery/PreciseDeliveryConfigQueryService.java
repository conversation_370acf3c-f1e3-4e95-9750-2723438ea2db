package net.summerfarm.wnc.application.service.preciseDelivery;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.application.inbound.controller.preciseDelivery.input.PreciseDeliveryConfigIdQueryInput;
import net.summerfarm.wnc.application.inbound.controller.preciseDelivery.input.PreciseDeliveryConfigPageQueryInput;
import net.summerfarm.wnc.domain.preciseDelivery.aggregate.PreciseDeliveryConfigAggregate;

/**
 * Description: 精准送配置查询服务
 * date: 2024/1/18 19:06
 *
 * <AUTHOR>
 */
public interface PreciseDeliveryConfigQueryService {

    /**
     * 分页查询
     * @param pageQueryInput 查询
     * @return 结果
     */
    PageInfo<PreciseDeliveryConfigAggregate> queryPage(PreciseDeliveryConfigPageQueryInput pageQueryInput);

    /**
     * 查询详情
     * @param configIdQueryInput 查询
     * @return 结果
     */
    PreciseDeliveryConfigAggregate queryDetail(PreciseDeliveryConfigIdQueryInput configIdQueryInput);
}
