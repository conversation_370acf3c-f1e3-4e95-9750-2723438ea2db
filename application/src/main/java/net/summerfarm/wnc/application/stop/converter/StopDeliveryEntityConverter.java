package net.summerfarm.wnc.application.stop.converter;

import net.summerfarm.wnc.api.stop.input.StopDeliveryAddInput;
import net.summerfarm.wnc.domain.fence.entity.StopDeliveryEntity;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/10/9 16:49<br/>
 *
 * <AUTHOR> />
 */
public class StopDeliveryEntityConverter {

    public static List<StopDeliveryEntity> addInput2Entity(StopDeliveryAddInput input){
        if(input == null){
            return Collections.emptyList();
        }

        List<StopDeliveryEntity> stopDeliveryEntities = new ArrayList<>();

        for (Integer storeNo : input.getStoreNos()) {
            StopDeliveryEntity entity = new StopDeliveryEntity();

            entity.setStoreNo(storeNo);
            entity.setShutdownStartTime(input.getShutdownStartTime());
            entity.setShutdownEndTime(input.getShutdownEndTime());

            stopDeliveryEntities.add(entity);
        }

        return stopDeliveryEntities;
    }
}
