package net.summerfarm.wnc.application.inbound.controller.fence.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024-10-12 16:29:33
 * @version 1.0
 *
 */
@Data
public class FenceChannelBusinessWhiteConfigVO implements Serializable{

	/**
	 * 围栏ID
	 */
	private Integer fenceId;

	/**
	 * 下单渠道类型 逗号分隔  1000鲜沐平台客户 2000鲜沐大客户 3000 Saas客户
	 */
	private String orderChannelType;

	/**
	 * 作用域下的业务信息
	 */
	private List<ScopeChannelBusinessVO> scopeChannelBusinesses;
}

