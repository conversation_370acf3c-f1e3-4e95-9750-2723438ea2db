package net.summerfarm.wnc.application.inbound.controller.preciseDelivery.input;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * Description:精准送配置ID查询
 * date: 2024/1/18 18:27
 *
 * <AUTHOR>
 */
@Data
public class PreciseDeliveryConfigIdQueryInput implements Serializable {

    private static final long serialVersionUID = -3927390626551128373L;

    /**
     * 精准送配置ID
     */
    @NotNull(message = "精准送配置ID不能为空")
    private Long configId;
}
