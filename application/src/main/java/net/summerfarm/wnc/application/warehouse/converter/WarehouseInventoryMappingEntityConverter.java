package net.summerfarm.wnc.application.warehouse.converter;

import net.summerfarm.wnc.api.fence.dto.WncSkuStoreNoDTO;
import net.summerfarm.wnc.api.warehouse.dto.SkuWarehouseMappingDTO;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseInventoryMappingEntity;

/**
 * Description: <br/>
 * date: 2023/4/10 18:36<br/>
 *
 * <AUTHOR> />
 */
public class WarehouseInventoryMappingEntityConverter {

    public static WncSkuStoreNoDTO entity2WncSkuStoreNoDTO(WarehouseInventoryMappingEntity warehouseInventoryMappingEntity){
        if(warehouseInventoryMappingEntity == null){
            return null;
        }

        WncSkuStoreNoDTO wncSkuStoreNoDTO = new WncSkuStoreNoDTO();
        wncSkuStoreNoDTO.setSku(warehouseInventoryMappingEntity.getSku());
        wncSkuStoreNoDTO.setStoreNo(warehouseInventoryMappingEntity.getStoreNo());
        wncSkuStoreNoDTO.setSubType(warehouseInventoryMappingEntity.getSubType());

        return wncSkuStoreNoDTO;
    }

    public static SkuWarehouseMappingDTO entity2Dto(WarehouseInventoryMappingEntity warehouseInventoryMappingEntity){
        if(warehouseInventoryMappingEntity == null){
            return null;
        }
        SkuWarehouseMappingDTO skuWarehouseMappingDTO = new SkuWarehouseMappingDTO();
        skuWarehouseMappingDTO.setSku(warehouseInventoryMappingEntity.getSku());
        skuWarehouseMappingDTO.setStoreNo(warehouseInventoryMappingEntity.getStoreNo());
        skuWarehouseMappingDTO.setWarehouseNo(warehouseInventoryMappingEntity.getWarehouseNo());
        return skuWarehouseMappingDTO;
    }
}
