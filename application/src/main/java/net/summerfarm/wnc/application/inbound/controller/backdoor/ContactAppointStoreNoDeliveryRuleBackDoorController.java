package net.summerfarm.wnc.application.inbound.controller.backdoor;

import net.summerfarm.wnc.api.config.input.ContactConfigCreateCommandInput;
import net.summerfarm.wnc.api.config.service.ContactConfigCommandService;
import net.summerfarm.wnc.api.deliveryRule.input.ContactDeliveryRuleSaveUpdateCommand;
import net.summerfarm.wnc.api.deliveryRule.service.ContactDeliveryRuleService;
import net.summerfarm.wnc.domain.fence.OutLandContactRepository;
import net.summerfarm.wnc.domain.fence.entity.OutLandContactEntity;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * Description: 地址指定城配仓、配送周期后门<br/>
 * date: 2024/9/30 11:38<br/>
 *
 * <AUTHOR> />
 */
@RestController
@RequestMapping(value="/contact-appoint-storeNo-deliveryRule-backDoor")
public class ContactAppointStoreNoDeliveryRuleBackDoorController {

    @Resource
    private ContactConfigCommandService contactConfigCommandService;
    @Resource
    private ContactDeliveryRuleService contactDeliveryRuleService;
    @Resource
    private OutLandContactRepository outLandContactRepository;

    @PostMapping("/appointStoreNo")
    public CommonResult<Void> appointStoreNo(@RequestBody @Validated ContactConfigCreateCommandInput input){
        contactConfigCommandService.saveOrUpdate(input);
        return CommonResult.ok();
    }

    @PostMapping("/appointDeliveryRule")
    public CommonResult<Void> appointDeliveryRule(@RequestBody @Validated ContactDeliveryRuleSaveUpdateCommand command){
        contactDeliveryRuleService.saveOrUpdate(command);
        return CommonResult.ok();
    }

    @PostMapping("/contactAppointStoreNo")
    public CommonResult<Void> contactAppointStoreNo(Long contactId, Integer newStoreNo, Integer oldStoreNo){
        if(contactId == null || newStoreNo == null || oldStoreNo == null){
            throw new BizException("参数错误");
        }
        List<OutLandContactEntity> outLandContactEntities = outLandContactRepository.queryByIds(Collections.singletonList(contactId));
        if(CollectionUtils.isEmpty(outLandContactEntities)){
            throw new BizException("数据不存在");
        }
        OutLandContactEntity outLandContactEntity = outLandContactEntities.get(0);
        if(!Objects.equals(oldStoreNo,outLandContactEntity.getStoreNo())){
            throw new BizException("旧城配仓编号不对应");
        }
        outLandContactRepository.contactAppointStoreNo(contactId,newStoreNo);
        return CommonResult.ok();
    }
}
