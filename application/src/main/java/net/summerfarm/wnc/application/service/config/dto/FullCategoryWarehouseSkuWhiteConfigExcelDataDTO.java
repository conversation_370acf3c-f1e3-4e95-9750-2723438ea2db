package net.summerfarm.wnc.application.service.config.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import net.xianmu.download.support.dto.ImportExcelBaseDTO;

/**
 * 三方仓全品类T+2配送白名单Excel导入对象
 *
 * <AUTHOR>
 */
@Data
public class FullCategoryWarehouseSkuWhiteConfigExcelDataDTO extends ImportExcelBaseDTO {

    /**
     * 仓库号
     */
    @ExcelIgnore
    private Integer warehouseNo;

    /**
     * 仓库名称
     */
    @ExcelProperty("仓库名称（必填）")
    private String warehouseName;

    /**
     * sku
     */
    @ExcelProperty("SKU（必填）")
    private String sku;

}
