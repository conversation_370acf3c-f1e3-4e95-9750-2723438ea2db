package net.summerfarm.wnc.application.inbound.controller.deliveryFence;

import net.summerfarm.wnc.api.fence.dto.DeliveryFenceCloseTimeQuery;
import net.summerfarm.wnc.api.fence.service.DeliveryFenceService;
import net.summerfarm.wnc.application.inbound.controller.deliveryFence.input.DeliveryFenceQueryCloseTimeInput;
import net.summerfarm.wnc.client.enums.SourceEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalTime;

/**
 * Description: 配送围栏<br/>
 * date: 2024/2/21 16:48<br/>
 *
 * <AUTHOR> />
 */
@RestController
@RequestMapping("/delivery-fence")
public class DeliveryFenceController {

    @Resource
    private DeliveryFenceService deliveryFenceService;
    @PostMapping("/query/close-time")
    public CommonResult<LocalTime> queryCloseTime(@RequestBody @Validated DeliveryFenceQueryCloseTimeInput input) {
        SourceEnum sourceEnum = SourceEnum.orderSourceMap.get(input.getSource());
        if(sourceEnum == null){
            throw new BizException("类型未获取到");
        }
        DeliveryFenceCloseTimeQuery query = DeliveryFenceCloseTimeQuery.builder()
                .city(input.getCity())
                .area(input.getArea())
                .source(sourceEnum)
                .contactId(input.getContactId())
                .build();
        LocalTime localTime = deliveryFenceService.queryCloseTime(query);
        return CommonResult.ok(localTime);
    }
}
