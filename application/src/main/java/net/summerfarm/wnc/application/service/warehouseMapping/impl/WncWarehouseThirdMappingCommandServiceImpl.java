package net.summerfarm.wnc.application.service.warehouseMapping.impl;


import net.summerfarm.wnc.application.inbound.controller.warehouseMapping.assembler.WncWarehouseThirdMappingAssembler;
import net.summerfarm.wnc.application.inbound.controller.warehouseMapping.input.command.WncWarehouseThirdMappingCommandInput;
import net.summerfarm.wnc.application.service.warehouseMapping.WncWarehouseThirdMappingCommandService;
import net.summerfarm.wnc.domain.warehouseMapping.entity.WncWarehouseThirdMappingEntity;
import net.summerfarm.wnc.domain.warehouseMapping.param.command.WncWarehouseThirdMappingCommandParam;
import net.summerfarm.wnc.domain.warehouseMapping.service.WncWarehouseThirdMappingCommandDomainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
*
* <AUTHOR>
* @date 2025-06-11 15:08:31
* @version 1.0
*
*/
@Service
@Transactional(rollbackFor = Exception.class)
public class WncWarehouseThirdMappingCommandServiceImpl implements WncWarehouseThirdMappingCommandService {

    @Autowired
    private WncWarehouseThirdMappingCommandDomainService wncWarehouseThirdMappingCommandDomainService;


    @Override
    public WncWarehouseThirdMappingEntity insert(WncWarehouseThirdMappingCommandInput input) {
        WncWarehouseThirdMappingCommandParam param = WncWarehouseThirdMappingAssembler.buildCreateParam(input);
        return wncWarehouseThirdMappingCommandDomainService.insert(param);
    }


    @Override
    public int update(WncWarehouseThirdMappingCommandInput input) {
        WncWarehouseThirdMappingCommandParam param = WncWarehouseThirdMappingAssembler.buildUpdateParam(input);
        return wncWarehouseThirdMappingCommandDomainService.update(param);
    }


    @Override
    public int delete(Long id) {
        return wncWarehouseThirdMappingCommandDomainService.delete(id);
    }
}