package net.summerfarm.wnc.application.service.closeTime.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.application.service.closeTime.CloseTimeConfigCommandService;
import net.summerfarm.wnc.domain.closeTime.CloseTimeConfigCommandDomainService;
import net.summerfarm.wnc.domain.closeTime.CloseTimeConfigValidator;
import net.summerfarm.wnc.domain.closeTime.param.CloseTimeAreaConfigCommandParam;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

/**
 * Description:截单时间区域配置操作服务实现
 * date: 2024/3/20 18:52
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CloseTimeConfigCommandServiceImpl implements CloseTimeConfigCommandService {

    private final CloseTimeConfigCommandDomainService closeTimeConfigCommandDomainService;
    private final CloseTimeConfigValidator closeTimeConfigValidator;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editAreaConfig(List<CloseTimeAreaConfigCommandParam> commandParams) {
        closeTimeConfigValidator.validateParams(commandParams);
        closeTimeConfigCommandDomainService.editAreaConfig(commandParams);
    }
}
