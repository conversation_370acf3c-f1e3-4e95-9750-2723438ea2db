package net.summerfarm.wnc.application.warehouse.converter;

import net.summerfarm.wnc.common.message.WarehouseLogisticsCenterUpsertMessage;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseLogisticsCenterEntity;

/**
 * Description: 城配仓消息转化类
 * date: 2023/10/9 13:38<br/>
 *
 * <AUTHOR> />
 */
public class WarehouseLogisticsMessageConverter {

    public static WarehouseLogisticsCenterUpsertMessage entity2Msg(WarehouseLogisticsCenterEntity entity){
        if(entity == null){
            return null;
        }
        WarehouseLogisticsCenterUpsertMessage msg = new WarehouseLogisticsCenterUpsertMessage();

        msg.setId(entity.getId());
        msg.setStoreNo(entity.getStoreNo());
        msg.setStoreName(entity.getStoreName());
        msg.setStatus(entity.getStatus());
        msg.setManageAdminId(entity.getManageAdminId());
        msg.setPoiNote(entity.getPoiNote());
        msg.setAddress(entity.getAddress());
        msg.setCloseOrderType(entity.getCloseOrderType());
        msg.setOriginStoreNo(entity.getOriginStoreNo());
        msg.setSotFinishTime(entity.getSotFinishTime());
        msg.setCreator(entity.getCreator());
        msg.setUpdater(entity.getUpdater());
        msg.setUpdateTime(entity.getUpdateTime());
        msg.setCreateTime(entity.getCreateTime());
        msg.setCloseTime(entity.getCloseTime());
        msg.setUpdateCloseTime(entity.getUpdateCloseTime());
        msg.setPersonContact(entity.getPersonContact());
        msg.setPhone(entity.getPhone());
        msg.setRegion(entity.getRegion());
        msg.setStorePic(entity.getStorePic());

        return msg;
    }
}
