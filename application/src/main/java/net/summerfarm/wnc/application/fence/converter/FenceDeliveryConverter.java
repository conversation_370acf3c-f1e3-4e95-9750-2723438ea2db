package net.summerfarm.wnc.application.fence.converter;

import net.summerfarm.wnc.api.fence.dto.FenceDeliveryDTO;
import net.summerfarm.wnc.api.fence.input.FenceDeliveryCommand;
import net.summerfarm.wnc.domain.fence.entity.FenceDeliveryEntity;

/**
 * Description:围栏配送周期转换器
 * date: 2023/11/1 11:21
 *
 * <AUTHOR>
 */
public class FenceDeliveryConverter {

    public static FenceDeliveryDTO entity2dto(FenceDeliveryEntity fenceDeliveryEntity){
        if(fenceDeliveryEntity == null){
            return null;
        }
        FenceDeliveryDTO fenceDeliveryDTO = new FenceDeliveryDTO();
        fenceDeliveryDTO.setId(fenceDeliveryEntity.getId());
        fenceDeliveryDTO.setFenceId(fenceDeliveryEntity.getFenceId());
        fenceDeliveryDTO.setNextDeliveryDate(fenceDeliveryEntity.getNextDeliveryDate());
        fenceDeliveryDTO.setFrequentMethod(fenceDeliveryEntity.getFrequentMethod());
        fenceDeliveryDTO.setDeliveryFrequent(fenceDeliveryEntity.getDeliveryFrequent());
        fenceDeliveryDTO.setDeliveryFrequentInterval(fenceDeliveryEntity.getDeliveryFrequentInterval());
        fenceDeliveryDTO.setBeginCalculateDate(fenceDeliveryEntity.getBeginCalculateDate());
        fenceDeliveryDTO.setCreator(fenceDeliveryEntity.getCreator());
        fenceDeliveryDTO.setCreateTime(fenceDeliveryEntity.getCreateTime());
        fenceDeliveryDTO.setUpdater(fenceDeliveryEntity.getUpdater());
        fenceDeliveryDTO.setUpdateTime(fenceDeliveryEntity.getUpdateTime());
        fenceDeliveryDTO.setDeleteFlag(fenceDeliveryEntity.getDeleteFlag());
        return fenceDeliveryDTO;

    }

    public static FenceDeliveryEntity command2entity(FenceDeliveryCommand fenceDeliveryCommand){
        if (fenceDeliveryCommand == null){
            return null;
        }
        FenceDeliveryEntity fenceDeliveryEntity = new FenceDeliveryEntity();
        fenceDeliveryEntity.setDeliveryFrequent(fenceDeliveryCommand.getDeliveryFrequent());
        fenceDeliveryEntity.setNextDeliveryDate(fenceDeliveryCommand.getNextDeliveryDate());
        fenceDeliveryEntity.setFrequentMethod(fenceDeliveryCommand.getFrequentMethod());
        fenceDeliveryEntity.setDeliveryFrequentInterval(fenceDeliveryCommand.getDeliveryFrequentInterval());
        fenceDeliveryEntity.setBeginCalculateDate(fenceDeliveryCommand.getBeginCalculateDate());
        return fenceDeliveryEntity;
    }
}
