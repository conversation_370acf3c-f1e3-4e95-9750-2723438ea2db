package net.summerfarm.wnc.application.warehouse;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.tms.enums.DistSiteEnums;
import net.summerfarm.wnc.api.warehouse.dto.WarehouseLogisticsCenterDTO;
import net.summerfarm.wnc.api.warehouse.dto.WarehouseStorageDTO;
import net.summerfarm.wnc.api.warehouse.input.LogisticsMappingDeleteInput;
import net.summerfarm.wnc.api.warehouse.input.WarehouseLogisticsSaveInput;
import net.summerfarm.wnc.api.warehouse.input.WarehouseLogisticsUpdateInput;
import net.summerfarm.wnc.api.warehouse.service.warehouse.WarehouseLogisticsService;
import net.summerfarm.wnc.application.warehouse.converter.WarehouseLogisticsConverter;
import net.summerfarm.wnc.application.warehouse.converter.WarehouseLogisticsMessageConverter;
import net.summerfarm.wnc.application.warehouse.converter.WarehouseMessageConverter;
import net.summerfarm.wnc.client.mq.WncMqConstants;
import net.summerfarm.wnc.common.enums.FenceEnums;
import net.summerfarm.wnc.common.enums.WarehouseLogisticsCenterEnums;
import net.summerfarm.wnc.common.message.WarehouseLogisticsCenterUpsertMessage;
import net.summerfarm.wnc.common.message.WarehouseStorageCenterUpdateMessage;
import net.summerfarm.wnc.common.query.fence.FenceQuery;
import net.summerfarm.wnc.common.query.warehouse.WarehouseLogisticsQuery;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskRepository;
import net.summerfarm.wnc.domain.fence.AreaRepository;
import net.summerfarm.wnc.domain.fence.FenceRepository;
import net.summerfarm.wnc.domain.fence.entity.AreaEntity;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.summerfarm.wnc.domain.warehouse.WarehouseLogisticsCenterDomainService;
import net.summerfarm.wnc.domain.warehouse.WarehouseLogisticsCenterRepository;
import net.summerfarm.wnc.domain.warehouse.WarehouseLogisticsCenterValidator;
import net.summerfarm.wnc.domain.warehouse.WarehouseLogisticsMappingRepository;
import net.summerfarm.wnc.domain.warehouse.entity.StoreSiteEntity;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseLogisticsCenterEntity;
import net.summerfarm.wnc.facade.auth.AuthQueryFacade;
import net.summerfarm.wnc.facade.tms.TmsQueryFacade;
import net.summerfarm.wnc.facade.tms.dto.SiteDTO;
import net.xianmu.common.exception.BizException;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: 城配仓
 * date: 2023/9/19 15:31<br/>
 *
 * <AUTHOR> />
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class WarehouseLogisticsServiceImpl implements WarehouseLogisticsService {

    private final WarehouseLogisticsCenterRepository warehouseLogisticsCenterRepository;
    private final TmsQueryFacade tmsQueryFacade;
    private final WarehouseLogisticsCenterDomainService warehouseLogisticsCenterDomainService;
    private final WarehouseLogisticsCenterValidator warehouseLogisticsCenterValidator;
    private final AuthQueryFacade authQueryFacade;
    private final MqProducer mqProducer;
    private final AreaRepository areaRepository;
    private final FenceRepository fenceRepository;

    @Override
    public PageInfo<WarehouseLogisticsCenterDTO> queryLogisticsList(WarehouseLogisticsQuery warehouseLogisticsQuery) {
        PageInfo<WarehouseLogisticsCenterDTO> resultPage = new PageInfo<>();
        //打卡状态
        List<SiteDTO> siteDTOList = tmsQueryFacade.queryStoreSiteList();
        if(warehouseLogisticsQuery.getPunchState() != null){
            List<String> storeNos;
            if(Objects.equals(warehouseLogisticsQuery.getPunchState(), DistSiteEnums.State.NEED_PUNCH.getValue())){
                storeNos = siteDTOList.stream().filter(site -> Objects.equals(DistSiteEnums.State.NEED_PUNCH.getValue(), site.getState())).map(SiteDTO::getOutBusinessNo).collect(Collectors.toList());
            }else{
                storeNos = siteDTOList.stream().filter(site -> !Objects.equals(DistSiteEnums.State.NEED_PUNCH.getValue(), site.getState())).map(SiteDTO::getOutBusinessNo).collect(Collectors.toList());
            }
            warehouseLogisticsQuery.setStoreNos(storeNos.stream().filter(StrUtil::isNotBlank).map(Integer::parseInt).collect(Collectors.toList()));
        }
        //分页查询
        PageInfo<WarehouseLogisticsCenterEntity> logisticsCenterEntityPageInfo = warehouseLogisticsCenterRepository.queryPageList(warehouseLogisticsQuery);
        if(logisticsCenterEntityPageInfo == null || CollectionUtils.isEmpty(logisticsCenterEntityPageInfo.getList())){
            return resultPage;
        }
        List<WarehouseLogisticsCenterEntity> logisticsCenterEntities = logisticsCenterEntityPageInfo.getList();
        //城配仓库映射关系
        warehouseLogisticsCenterDomainService.handleLogisticsMapping(logisticsCenterEntities);
        //停配规则状态
        warehouseLogisticsCenterDomainService.handleStopDeliveryStatus(logisticsCenterEntities);
        //打卡状态
        Map<String, SiteDTO> storeNoSiteMap = siteDTOList.stream().collect(Collectors.toMap(SiteDTO::getOutBusinessNo, Function.identity()));
        logisticsCenterEntities.forEach(store ->{
            SiteDTO site = storeNoSiteMap.get(String.valueOf(store.getStoreNo()));
            if(site == null){
                return;
            }
            store.setStoreSiteEntity(StoreSiteEntity.builder().punchState(site.getState()).build());
        });

        BeanUtils.copyProperties(logisticsCenterEntityPageInfo,resultPage);
        List<WarehouseLogisticsCenterDTO> dtoList = logisticsCenterEntities.stream().map(WarehouseLogisticsConverter::entity2DTO).collect(Collectors.toList());
        resultPage.setList(dtoList);
        return resultPage;
    }

    @Override
    public WarehouseLogisticsCenterDTO queryLogisticsDetail(Integer id) {
        WarehouseLogisticsCenterEntity storeEntity = warehouseLogisticsCenterRepository.queryById(id);
        if(storeEntity == null){
            throw new BizException("不存在此城配仓信息");
        }
        //城配和仓库的查询映射关系
        warehouseLogisticsCenterDomainService.handleLogisticsMapping(Collections.singletonList(storeEntity));
        //停配规则状态
        warehouseLogisticsCenterDomainService.handleStopDeliveryStatus(Collections.singletonList(storeEntity));
        //打卡状态
        List<SiteDTO> siteDTOList = tmsQueryFacade.queryStoreSiteList();
        //打卡状态
        Map<String, SiteDTO> storeNoSiteMap = siteDTOList.stream().collect(Collectors.toMap(SiteDTO::getOutBusinessNo, Function.identity()));
        SiteDTO siteDTO = storeNoSiteMap.get(String.valueOf(storeEntity.getStoreNo()));
        if(siteDTO != null){
            storeEntity.setStoreSiteEntity(StoreSiteEntity.builder()
                    .punchState(siteDTO.getState())
                    .punchDistance(siteDTO.getPunchDistance())
                    .outTime(siteDTO.getOutTime()).intelligencePath(siteDTO.getIntelligencePath()).build());
        }

        //城配仓负责人
        if(storeEntity.getManageAdminId() != null){
            storeEntity.setManageAdminName(authQueryFacade.queryAdminRealName(storeEntity.getManageAdminId().longValue()));
        }
        return WarehouseLogisticsConverter.entity2DTO(storeEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateLogistics(WarehouseLogisticsUpdateInput warehouseLogisticsUpdateCommand) {
        WarehouseLogisticsCenterDTO warehouseLogisticsCenterDTO = WarehouseLogisticsConverter.updateInput2DTO(warehouseLogisticsUpdateCommand);
        WarehouseLogisticsCenterEntity warehouseLogisticsCenterEntity = warehouseLogisticsCenterRepository.queryById(warehouseLogisticsCenterDTO.getId());
        if(warehouseLogisticsCenterEntity == null){
            throw new BizException("不存在此城配仓信息");
        }
        //校验围栏
        warehouseLogisticsCenterValidator.validateFence(warehouseLogisticsCenterEntity, warehouseLogisticsUpdateCommand.getStatus());
        warehouseLogisticsCenterDTO.setStoreNo(warehouseLogisticsCenterEntity.getStoreNo());

        //更新信息
        warehouseLogisticsCenterDomainService.updateLogistics(WarehouseLogisticsConverter.entity2DTO(warehouseLogisticsCenterDTO));

        //事务之后发送消息通知TMS点位信息
        WarehouseLogisticsCenterEntity newWarehouseLogisticsCenter = warehouseLogisticsCenterRepository.queryById(warehouseLogisticsCenterDTO.getId());
        WarehouseLogisticsCenterUpsertMessage msg = WarehouseLogisticsMessageConverter.entity2Msg(newWarehouseLogisticsCenter);

        msg.setPunchDistance(warehouseLogisticsUpdateCommand.getPunchDistance());
        msg.setOutTime(warehouseLogisticsUpdateCommand.getOutTime());
        msg.setPunchState(warehouseLogisticsUpdateCommand.getPunchState());
        msg.setIntelligencePath(warehouseLogisticsUpdateCommand.getIntelligencePath());

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    mqProducer.send(WncMqConstants.Topic.TOPIC_WNC_WAREHOUSE,WncMqConstants.Tag.TAG_WNC_WAREHOUSE_LOGISTICS_UPSERT,msg);
                } catch (Throwable e) {
                    log.error("发送更新鲜沐城配仓Mq消息异常:{}", JSONObject.toJSONString(msg), e);
                }
            }
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void mappingDelete(LogisticsMappingDeleteInput input) {
        warehouseLogisticsCenterDomainService.mappingDelete(input.getStoreNo(),input.getWarehouseNo());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void logisticsSave(WarehouseLogisticsSaveInput input) {
        WarehouseLogisticsCenterDTO warehouseLogisticsCenterDTO = WarehouseLogisticsConverter.saveInput2DTO(input);
        WarehouseLogisticsCenterEntity entity = WarehouseLogisticsConverter.entity2DTO(warehouseLogisticsCenterDTO);
        //保存信息
        warehouseLogisticsCenterDomainService.logisticsSave(entity);

        //事务之后发送消息通知TMS点位信息
        WarehouseLogisticsCenterEntity newWarehouseLogisticsCenter = warehouseLogisticsCenterRepository.queryByUk(entity.getStoreNo());
        WarehouseLogisticsCenterUpsertMessage msg = WarehouseLogisticsMessageConverter.entity2Msg(newWarehouseLogisticsCenter);

        msg.setPunchDistance(input.getPunchDistance());
        msg.setOutTime(input.getOutTime());
        msg.setPunchState(input.getPunchState());
        msg.setIntelligencePath(input.getIntelligencePath());

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    mqProducer.send(WncMqConstants.Topic.TOPIC_WNC_WAREHOUSE,WncMqConstants.Tag.TAG_WNC_WAREHOUSE_LOGISTICS_UPSERT,msg);
                } catch (Throwable e) {
                    log.error("发送新增鲜沐城配仓Mq消息异常:{}", JSONObject.toJSONString(msg), e);
                }
            }
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void closeTimeCancel(Integer storeNo) {
        warehouseLogisticsCenterDomainService.closeTimeCancel(storeNo);
    }

    @Override
    public List<WarehouseLogisticsCenterDTO> queryLogisticsAll(Integer status) {
        List<WarehouseLogisticsCenterEntity> warehouseList = warehouseLogisticsCenterRepository.queryList(WarehouseLogisticsQuery.builder().status(status).build());
        //查询tms点位信息
        List<SiteDTO> siteDTOList = tmsQueryFacade.queryStoreSiteList();
        //打卡、出仓、智能排线等状态
        Map<String, SiteDTO> storeNoSiteMap = siteDTOList.stream().collect(Collectors.toMap(SiteDTO::getOutBusinessNo, Function.identity()));

        for (WarehouseLogisticsCenterEntity storeEntity : warehouseList) {
            SiteDTO siteDTO = storeNoSiteMap.get(String.valueOf(storeEntity.getStoreNo()));
            if(siteDTO != null){
                storeEntity.setStoreSiteEntity(StoreSiteEntity.builder()
                        .punchState(siteDTO.getState())
                        .punchDistance(siteDTO.getPunchDistance())
                        .outTime(siteDTO.getOutTime()).intelligencePath(siteDTO.getIntelligencePath()).build());
            }
        }
        //城配和仓库的查询映射关系
        warehouseLogisticsCenterDomainService.handleLogisticsMapping(warehouseList);

        return warehouseList.stream().map(WarehouseLogisticsConverter::entity2DTO).collect(Collectors.toList());
    }

    @Override
    public List<WarehouseLogisticsCenterDTO> queryWarehouseLogisticsList(WarehouseLogisticsQuery query) {
        return warehouseLogisticsCenterRepository.queryLogisticsList(query).stream().map(WarehouseLogisticsConverter::entity2DTO).collect(Collectors.toList());
    }

    @Override
    public Boolean queryIsSupportAddOrderByStoreNo(Integer storeNo) {
        if(storeNo == null){
            throw new BizException("城配仓编号不能为空");
        }
        List<FenceEntity> fenceEntities = fenceRepository.queryList(FenceQuery.builder()
                .status(FenceEnums.Status.VALID.getValue())
                .storeNo(storeNo).build());
        if(CollectionUtils.isEmpty(fenceEntities)){
            return false;
        }
        List<Integer> areaNos = fenceEntities.stream().map(FenceEntity::getAreaNo).collect(Collectors.toList());
        List<AreaEntity> areaEntities = areaRepository.queryByAreaNos(areaNos);
        if(CollectionUtils.isEmpty(areaEntities)){
            return false;
        }
        return areaEntities.stream().anyMatch(area -> Objects.equals(area.getSupportAddOrder(), 0));
    }
}
