package net.summerfarm.wnc.application.path;

import cn.hutool.core.bean.BeanUtil;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.path.dto.PathDTO;
import net.summerfarm.wnc.api.path.dto.SkuPathMappingDTO;
import net.summerfarm.wnc.api.path.service.PathService;
import net.summerfarm.wnc.application.path.converter.PathDTOConverter;
import net.summerfarm.wnc.application.path.converter.SkuPathMappingDTOConverter;
import net.summerfarm.wnc.common.enums.PathConfigEnums;
import net.summerfarm.wnc.common.query.path.PathQuery;
import net.summerfarm.wnc.domain.path.PathConfigRepository;
import net.summerfarm.wnc.domain.path.PathDomainService;
import net.summerfarm.wnc.domain.path.SkuPathMappingRepository;
import net.summerfarm.wnc.domain.path.entity.PathConfigEntity;
import net.summerfarm.wnc.domain.path.entity.SkuPathMappingEntity;
import net.summerfarm.wnc.domain.warehouse.WarehouseStorageCenterRepository;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseStorageEntity;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.user.UserInfoHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 * Description: 路线配置服务<br/>
 * date: 2023/11/13 15:05<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PathServiceImpl implements PathService {

    private final PathConfigRepository pathConfigRepository;
    private final SkuPathMappingRepository skuPathMappingRepository;
    private final PathDomainService pathDomainService;
    private final WarehouseStorageCenterRepository warehouseStorageCenterRepository;


    @Override
    public List<PathDTO> queryPathConfigList(PathQuery pathQuery) {
        List<PathConfigEntity> pathConfigEntities = pathConfigRepository.queryPathConfigList(pathQuery);
        return pathConfigEntities.stream().map(PathDTOConverter::entity2DTO).collect(Collectors.toList());
    }

    @Override
    public PathDTO queryPathConfigDetail(PathQuery pathQuery) {
        PathConfigEntity pathConfigEntity = pathConfigRepository.queryByUk(pathQuery.getBeginOutNo(),pathQuery.getEndOutNo(),pathQuery.getBusinseeType());
        return PathDTOConverter.entity2DTO(pathConfigEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveOrUpdateConfig(PathDTO pathDTO) {
        //参数校验
        PathConfigEntity entity = PathDTOConverter.dto2Entity(pathDTO);
        String[] frequentArray = entity.getFrequent().split(",");
        for (String frequentStr : frequentArray) {
            try {
                Integer.parseInt(frequentStr);
            } catch (NumberFormatException e) {
                throw new BizException("周期只能为数字1到7");
            }
        }
        //判断下一个日期是否在周期里面
        LocalDateTime lastTime = pathDTO.getLastTime();
        int weekDay = lastTime.getDayOfWeek().getValue();
        if(!Arrays.asList(frequentArray).contains(String.valueOf(weekDay))){
            throw new BizException("下一个次日期不在设置规则里面请检查");
        }
        pathDomainService.saveOrUpdateConfig(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void skuPathBatchSaveOrUpdate(List<SkuPathMappingDTO> skuPathMappingDTOListReq) {
        if(CollectionUtils.isEmpty(skuPathMappingDTOListReq)){
            return;
        }
        List<String> skuList = skuPathMappingDTOListReq.stream().map(SkuPathMappingDTO::getSku).collect(Collectors.toList());
        List<Integer> supWarehouseList = skuPathMappingDTOListReq.stream().map(SkuPathMappingDTO::getSupWarehouseNo).collect(Collectors.toList());
        List<Integer> warehouseList = skuPathMappingDTOListReq.stream().map(SkuPathMappingDTO::getWarehouseNo).collect(Collectors.toList());

        //查询数据信息
        List<SkuPathMappingEntity> skuPathMappingEntities  = skuPathMappingRepository.queryList(skuList,warehouseList);

        //判断是否存在
        Map<String, Long> skuWarehouseNo2IdMap = skuPathMappingEntities.stream().collect(Collectors.toMap(skuPath -> skuPath.getSku() + "#" + skuPath.getWarehouseNo(), SkuPathMappingEntity::getId));
        List<SkuPathMappingDTO> skuPathMappingSaveList = new ArrayList<>();
        List<SkuPathMappingDTO> skuPathMappingUpdateList = new ArrayList<>();
        
        //路线合理性校验
        List<PathConfigEntity> pathConfigEntities = pathConfigRepository.queryByBeginOutNosWithType(supWarehouseList,PathConfigEnums.BusinseeType.REALLOCATE.getValue());
        if(CollectionUtils.isEmpty(pathConfigEntities)){
            throw new BizException("没有合适的路线信息");
        }
        Map<String, Long> beginEndNo2PathIdMap = pathConfigEntities.stream().collect(Collectors.toMap(pathConfigEntity -> pathConfigEntity.getBeginOutNo() + "#" + pathConfigEntity.getEndOutNo(), PathConfigEntity::getId));
        //查询仓库名称信息
        List<WarehouseStorageEntity> warehouseStorageList = warehouseStorageCenterRepository.queryListByWarehouseNos(warehouseList);
        Map<Integer,String> warehouseNo2NameMap = warehouseStorageList.stream().collect(Collectors.toMap(WarehouseStorageEntity::getWarehouseNo,WarehouseStorageEntity::getWarehouseName,(oldValue, newValue) -> newValue));

        for (SkuPathMappingDTO skuPathMappingReq : skuPathMappingDTOListReq) {
            String sku = skuPathMappingReq.getSku();
            Integer warehouseNo = skuPathMappingReq.getWarehouseNo();
            String skuWarehouse = sku + "#" + warehouseNo;
            //路线是否存在、合理 上级支援仓库+仓库是否存在合适的路线
            String supWarehouseWarehouseNo = skuPathMappingReq.getSupWarehouseNo() + "#" + warehouseNo;
            if(beginEndNo2PathIdMap.get(supWarehouseWarehouseNo) == null){
                throw new BizException(sku + "在" + warehouseNo2NameMap.get(warehouseNo) + "没有合适的路线信息");
            }
            skuPathMappingReq.setPathId(beginEndNo2PathIdMap.get(supWarehouseWarehouseNo));
            Long skuMappingId = skuWarehouseNo2IdMap.get(skuWarehouse);
            if(skuMappingId != null){
                skuPathMappingReq.setId(skuMappingId);
                skuPathMappingReq.setUpdater(UserInfoHolder.getUserRealName());
                skuPathMappingUpdateList.add(skuPathMappingReq);
            }else{
                skuPathMappingReq.setCreator(UserInfoHolder.getUserRealName());
                skuPathMappingSaveList.add(skuPathMappingReq);
            }
        }

        //存在的进行更新不存在的进行新增
        skuPathMappingRepository.batchSave(skuPathMappingSaveList.stream().map(SkuPathMappingDTOConverter::dto2Entity).collect(Collectors.toList()));
        skuPathMappingRepository.batchUpdate(skuPathMappingUpdateList.stream().map(SkuPathMappingDTOConverter::dto2Entity).collect(Collectors.toList()));
    }


    @Override
    public List<PathDTO> querySkuPathMappingList(List<PathQuery> queries) {
        if(CollectionUtils.isEmpty(queries)){
            return Collections.emptyList();
        }
        List<String> skuList = queries.stream().map(PathQuery::getSku).collect(Collectors.toList());
        List<Integer> warehouseList = queries.stream().map(PathQuery::getWarehouseNo).collect(Collectors.toList());
        //sku warehouse 组合
        List<String> skuWarehouseList = queries.stream().map(query -> query.getSku() + "#" + query.getWarehouseNo()).collect(Collectors.toList());

        //查询数据
        List<PathConfigEntity> pathConfigEntities = pathConfigRepository.queryPathConfigWithSkuPathMappingList(skuList,warehouseList);
        //过滤相关数据
        for (PathConfigEntity pathConfigEntity : pathConfigEntities) {
            List<SkuPathMappingEntity> skuPathMappingEntities = pathConfigEntity.getSkuPathMappingEntities();
            if(CollectionUtils.isEmpty(skuPathMappingEntities)){
                continue;
            }
            List<SkuPathMappingEntity> filterSkuPathMappingEntities = skuPathMappingEntities.stream()
                    .filter(skuPath -> skuWarehouseList.contains(skuPath.getSku() + "#" + skuPath.getWarehouseNo()))
                    .distinct()
                    .collect(Collectors.toList());

            pathConfigEntity.setSkuPathMappingEntities(filterSkuPathMappingEntities);
        }

        return pathConfigEntities.stream().map(PathDTOConverter::entity2DTO).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updatePathLastTime() {
        pathDomainService.updatePathLastTime();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void initData() {
        pathConfigRepository.initData();
    }

    @Override
    public List<PathDTO> queryExpectLastTimeReallocatePath(PathQuery query) {
        //调拨相关的配置
        List<PathConfigEntity> pathConfigEntities = pathConfigRepository.queryPathConfigList(query);
        //按照路线期望开始时间进行过滤处理
        List<PathConfigEntity> resultEntityList = pathDomainService.expectLastTimeReallocatePath(pathConfigEntities, query.getExpectLastTime());
        return resultEntityList.stream().map(PathDTOConverter::entity2DTO).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void pathDelete(Long id) {
        //查询路线下面是否存在sku的配置信息，存在则不能删除
        PathConfigEntity pathConfigEntity = pathConfigRepository.queryById(id);
        if(pathConfigEntity == null){
            throw new BizException("路线不存在");
        }
        //查询是否存在sku的配置信息
        List<SkuPathMappingEntity> skuPathMappingEntities = skuPathMappingRepository.queryByPathId(id);
        if(!CollectionUtils.isEmpty(skuPathMappingEntities)){
            throw new BizException("路线存在商品配置不能删除");
        }

        pathConfigRepository.deleteById(id);
    }

    @Override
    public PageInfo<PathDTO> queryPagePathConfig(PathQuery query) {
        PageInfo<PathConfigEntity> pathConfigEntities = pathConfigRepository.queryPagePathConfig(query);
        PageInfo<PathDTO> pathDTOPageInfo = new PageInfo<>();
        BeanUtil.copyProperties(pathConfigEntities, pathDTOPageInfo);
        if(pathConfigEntities.getList() == null){
            return pathDTOPageInfo;
        }
        List<PathDTO> pathDTOList = pathConfigEntities.getList().stream().map(PathDTOConverter::entity2DTO).collect(Collectors.toList());
        pathDTOPageInfo.setList(pathDTOList);

        return pathDTOPageInfo;
    }

    @Override
    public PathDTO queryPathConfigDetailById(Long pathId) {
        return PathDTOConverter.entity2DTO(pathConfigRepository.queryById(pathId));
    }

    @Override
    public List<SkuPathMappingDTO> queryBelowSkuPathMappingList(List<PathQuery> queries) {
        if(CollectionUtils.isEmpty(queries)){
            return Collections.emptyList();
        }
        //sku supWarehouseNo 组合
        List<String> skuSupWarehouseNoList = queries.stream().map(query -> query.getSku() + "#" + query.getSupWarehouseNo()).collect(Collectors.toList());

        List<String> skus = queries.stream().map(PathQuery::getSku).filter(Objects::nonNull).collect(Collectors.toList());
        List<Integer> supWarehouseList = queries.stream().map(PathQuery::getSupWarehouseNo).filter(Objects::nonNull).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(skus) || CollectionUtils.isEmpty(supWarehouseList)){
            return Collections.emptyList();
        }
        List<PathConfigEntity> pathConfigEntities = pathConfigRepository.queryByBeginOutNosWithType(supWarehouseList, PathConfigEnums.BusinseeType.REALLOCATE.getValue());
        if(CollectionUtils.isEmpty(pathConfigEntities)){
            return Collections.emptyList();
        }

        List<Long> pathIdList = pathConfigEntities.stream().map(PathConfigEntity::getId).collect(Collectors.toList());
        //根据上级路线信息和sku查询商品信息
        List<SkuPathMappingDTO> skuPathMappingDTOList = skuPathMappingRepository.queryListBySkusPathIdList(skus, pathIdList).stream().map(PathDTOConverter::skuPathMappingEntity2DTO).collect(Collectors.toList());
        //路线上级支援仓map
        Map<Long, Integer> pathIdSupWarehouseNoMap = pathConfigEntities.stream().collect(Collectors.toMap(PathConfigEntity::getId, PathConfigEntity::getBeginOutNo));

        skuPathMappingDTOList.forEach(skuPathMappingDTO ->{
            skuPathMappingDTO.setSupWarehouseNo(pathIdSupWarehouseNoMap.get(skuPathMappingDTO.getPathId()));
        });
        //过滤相关数据
        skuPathMappingDTOList = skuPathMappingDTOList.stream().filter(s -> skuSupWarehouseNoList.contains(s.getSku() + "#" + s.getSupWarehouseNo())).collect(Collectors.toList());

        return skuPathMappingDTOList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void skuPathMappingBatchDelete(List<SkuPathMappingDTO> dtos) {
        if(CollectionUtils.isEmpty(dtos)){
            return;
        }
        List<SkuPathMappingEntity> skuPathMappingEntities = dtos.stream().map(PathDTOConverter::dto2SkuPathMappingEntity).collect(Collectors.toList());
        skuPathMappingRepository.batchDeleteByWarehouseNosSkus(skuPathMappingEntities);
    }
}
