package net.summerfarm.wnc.application.warehouse.converter;

import net.summerfarm.wnc.api.warehouse.dto.WncWarehouseStorageTenantDTO;
import net.summerfarm.wnc.domain.warehouse.entity.WncWarehouseStorageTenantEntity;

/**
 * Description: <br/>
 * date: 2023/4/6 11:18<br/>
 *
 * <AUTHOR> />
 */
public class WncWarehouseStorageTenantConverter {

    public static WncWarehouseStorageTenantEntity dto2Entity(WncWarehouseStorageTenantDTO rsWarehouseStorageTenantDTO){
        if(rsWarehouseStorageTenantDTO == null){
            return null;
        }
        WncWarehouseStorageTenantEntity rsWarehouseStorageTenantEntity = new WncWarehouseStorageTenantEntity();
        rsWarehouseStorageTenantEntity.setId(rsWarehouseStorageTenantDTO.getId());
        rsWarehouseStorageTenantEntity.setUpdateTime(rsWarehouseStorageTenantDTO.getUpdateTime());
        rsWarehouseStorageTenantEntity.setCreateTime(rsWarehouseStorageTenantDTO.getCreateTime());
        rsWarehouseStorageTenantEntity.setWarehouseNo(rsWarehouseStorageTenantDTO.getWarehouseNo());
        rsWarehouseStorageTenantEntity.setTenantId(rsWarehouseStorageTenantDTO.getTenantId());
        return rsWarehouseStorageTenantEntity;
    }

    public static WncWarehouseStorageTenantDTO entity2Dto(WncWarehouseStorageTenantEntity rsWarehouseStorageTenantEntity){
        if(rsWarehouseStorageTenantEntity == null){
            return null;
        }
        WncWarehouseStorageTenantDTO storageTenantDTO = new WncWarehouseStorageTenantDTO();

        storageTenantDTO.setId(rsWarehouseStorageTenantEntity.getId());
        storageTenantDTO.setUpdateTime(rsWarehouseStorageTenantEntity.getUpdateTime());
        storageTenantDTO.setCreateTime(rsWarehouseStorageTenantEntity.getCreateTime());
        storageTenantDTO.setWarehouseNo(rsWarehouseStorageTenantEntity.getWarehouseNo());
        storageTenantDTO.setTenantId(rsWarehouseStorageTenantEntity.getTenantId());

        return storageTenantDTO;
    }
}
