package net.summerfarm.wnc.application.service.config.impl;


import net.summerfarm.wnc.application.service.config.TenantStoreSkuBlackConfigQueryService;
import net.summerfarm.wnc.domain.config.entity.TenantStoreSkuBlackConfigEntity;
import net.summerfarm.wnc.domain.config.repository.TenantStoreSkuBlackConfigQueryRepository;
import net.summerfarm.wnc.domain.config.param.query.WncTenantStoreSkuBlackConfigQueryParam;
import net.summerfarm.wnc.application.inbound.controller.config.input.query.WncTenantStoreSkuBlackConfigQueryInput;
import net.summerfarm.wnc.application.inbound.controller.config.assembler.WncTenantStoreSkuBlackConfigAssembler;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
*
* <AUTHOR>
* @date 2024-09-12 14:00:16
* @version 1.0
*
*/
@Service
public class WncTenantStoreSkuBlackConfigQueryServiceImpl implements TenantStoreSkuBlackConfigQueryService {

    @Autowired
    private TenantStoreSkuBlackConfigQueryRepository tenantStoreSkuBlackConfigQueryRepository;

    @Override
    public PageInfo<TenantStoreSkuBlackConfigEntity> getPage(WncTenantStoreSkuBlackConfigQueryInput input) {
        WncTenantStoreSkuBlackConfigQueryParam queryParam = WncTenantStoreSkuBlackConfigAssembler.toWncTenantStoreSkuBlackConfigQueryParam(input);
        return tenantStoreSkuBlackConfigQueryRepository.getPage(queryParam);
    }

    @Override
    public TenantStoreSkuBlackConfigEntity getDetail(Long id){
        if (Objects.isNull(id)) {
            throw new BizException("请求参数为空！");
        }
        return tenantStoreSkuBlackConfigQueryRepository.selectById(id);
    }
}