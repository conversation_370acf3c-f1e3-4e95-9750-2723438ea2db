package net.summerfarm.wnc.application.inbound.controller.preciseDelivery.input;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * Description:精准送配置初始化命令实体
 * date: 2024/1/25 15:43
 *
 * <AUTHOR>
 */
@Data
public class PreciseDeliveryConfigInitCommandInput implements Serializable {

    private static final long serialVersionUID = -7831885445853240131L;

    /**
     * 运营服务区域编号集合
     */
    @NotEmpty(message = "运营服务区域编号集合不能为空")
    private List<Integer> areaNos;

    /**
     * 精准送配置时效集合
     */
    @Valid
    @NotEmpty(message = "精准送配置时效不能为空")
    private List<PreciseDeliveryConfigTimeCommandInput> timeList;

}
