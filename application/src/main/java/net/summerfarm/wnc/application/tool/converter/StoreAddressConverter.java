package net.summerfarm.wnc.application.tool.converter;

import net.summerfarm.wnc.api.tool.dto.StoreAddressVO;
import net.summerfarm.wnc.facade.userCenter.dto.AddressDTO;

/**
 * Description:门店地址转换器
 * date: 2023/12/8 18:17
 *
 * <AUTHOR>
 */
public class StoreAddressConverter {

    public static StoreAddressVO dto2vo(AddressDTO addressDTO) {
        if (addressDTO == null) {
            return null;
        }
        StoreAddressVO storeAddressVO = new StoreAddressVO();
        storeAddressVO.setProvince(addressDTO.getProvince());
        storeAddressVO.setCity(addressDTO.getCity());
        storeAddressVO.setArea(addressDTO.getArea());
        storeAddressVO.setAddress(addressDTO.getAddress());
        return storeAddressVO;
    }
}
