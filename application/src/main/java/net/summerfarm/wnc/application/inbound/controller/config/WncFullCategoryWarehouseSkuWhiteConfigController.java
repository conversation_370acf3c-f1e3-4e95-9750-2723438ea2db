package net.summerfarm.wnc.application.inbound.controller.config;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.application.config.WncFullCategoryWarehouseSkuWhiteConfigCommandService;
import net.summerfarm.wnc.application.config.WncFullCategoryWarehouseSkuWhiteConfigQueryService;
import net.summerfarm.wnc.application.inbound.controller.config.assembler.WncFullCategoryWarehouseSkuWhiteConfigAssembler;
import net.summerfarm.wnc.application.inbound.controller.config.input.command.WncFullCategoryWarehouseSkuWhiteConfigCommandInput;
import net.summerfarm.wnc.application.inbound.controller.config.input.command.WncFullCategoryWarehouseSkuWhiteConfigDeleteCommandInput;
import net.summerfarm.wnc.application.inbound.controller.config.input.query.WncFullCategoryWarehouseSkuWhiteConfigDetailQueryInput;
import net.summerfarm.wnc.application.inbound.controller.config.input.query.WncFullCategoryWarehouseSkuWhiteConfigQueryInput;
import net.summerfarm.wnc.application.inbound.controller.config.vo.WncFullCategoryWarehouseSkuWhiteConfigVO;
import net.summerfarm.wnc.common.config.WncConfig;
import net.summerfarm.wnc.common.converter.PageInfoConverter;
import net.summerfarm.wnc.common.enums.SkuSubTypEnum;
import net.summerfarm.wnc.domain.config.entity.WncFullCategoryWarehouseSkuWhiteConfigEntity;
import net.summerfarm.wnc.domain.warehouse.WarehouseStorageCenterRepository;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseStorageEntity;
import net.summerfarm.wnc.facade.gc.GcQueryFacade;
import net.summerfarm.wnc.facade.gc.dto.ProductSkuDTO;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @version 1.0
 * @Title 全品类仓加SKU白名单配置
 * @Description 全品类仓加SKU白名单配置功能模块
 * @date 2025-01-06 15:31:26
 */
@RestController
@RequestMapping(value = "/wncFullCategoryWarehouseSkuWhiteConfig")
public class WncFullCategoryWarehouseSkuWhiteConfigController {

    @Autowired
    private WncFullCategoryWarehouseSkuWhiteConfigCommandService wncFullCategoryWarehouseSkuWhiteConfigCommandService;
    @Autowired
    private WncFullCategoryWarehouseSkuWhiteConfigQueryService wncFullCategoryWarehouseSkuWhiteConfigQueryService;
    @Autowired
    private WarehouseStorageCenterRepository warehouseStorageCenterRepository;
    @Autowired
    private GcQueryFacade gcQueryFacade;
    @Autowired
    private WncConfig wncConfig;

    /**
     * 全品类仓加SKU白名单配置列表
     *
     * @return WncFullCategoryWarehouseSkuWhiteConfigVO
     */
    @PostMapping(value = "/query/page")
    public CommonResult<PageInfo<WncFullCategoryWarehouseSkuWhiteConfigVO>> getPage(@RequestBody WncFullCategoryWarehouseSkuWhiteConfigQueryInput input) {
        PageInfo<WncFullCategoryWarehouseSkuWhiteConfigEntity> page = wncFullCategoryWarehouseSkuWhiteConfigQueryService.getPage(input);
        PageInfo<WncFullCategoryWarehouseSkuWhiteConfigVO> pageResp = PageInfoConverter.toPageResp(page, WncFullCategoryWarehouseSkuWhiteConfigAssembler::toWncFullCategoryWarehouseSkuWhiteConfigVO);
        fillWncFullCategoryWarehouseSkuWhiteConfigVO(pageResp.getList());
        return CommonResult.ok(pageResp);
    }

    /**
     * 获取详情
     *
     * @return WncFullCategoryWarehouseSkuWhiteConfigVO
     */
    @PostMapping(value = "/query/detail")
    public CommonResult<WncFullCategoryWarehouseSkuWhiteConfigVO> detail(@Valid @RequestBody WncFullCategoryWarehouseSkuWhiteConfigDetailQueryInput input) {
        WncFullCategoryWarehouseSkuWhiteConfigVO vo = WncFullCategoryWarehouseSkuWhiteConfigAssembler.toWncFullCategoryWarehouseSkuWhiteConfigVO(wncFullCategoryWarehouseSkuWhiteConfigQueryService.getDetail(input.getId()));
        if (vo != null) {
            fillWncFullCategoryWarehouseSkuWhiteConfigVO(Collections.singletonList(vo));
        }
        return CommonResult.ok(vo);
    }


    /**
     * 新增
     *
     * @return WncFullCategoryWarehouseSkuWhiteConfigVO
     */
    @PostMapping(value = "/upsert/insert")
    public CommonResult<WncFullCategoryWarehouseSkuWhiteConfigVO> insert(@Valid @RequestBody WncFullCategoryWarehouseSkuWhiteConfigCommandInput input) {
        validateWncFullCategoryWarehouseSkuWhiteConfigCommandInput(input);
        return CommonResult.ok(WncFullCategoryWarehouseSkuWhiteConfigAssembler.toWncFullCategoryWarehouseSkuWhiteConfigVO(wncFullCategoryWarehouseSkuWhiteConfigCommandService.insert(input)));
    }

    /**
     * 修改
     *
     * @return
     */
    @PostMapping(value = "/upsert/update")
    public CommonResult<Integer> update(@Valid @RequestBody WncFullCategoryWarehouseSkuWhiteConfigCommandInput input) {
        validateWncFullCategoryWarehouseSkuWhiteConfigCommandInput(input);
        return CommonResult.ok(wncFullCategoryWarehouseSkuWhiteConfigCommandService.update(input));
    }

    /**
     * 删除
     *
     * @return
     */
    @PostMapping(value = "/upsert/delete")
    public CommonResult<Integer> delete(@Valid @RequestBody WncFullCategoryWarehouseSkuWhiteConfigDeleteCommandInput input) {
        return CommonResult.ok(wncFullCategoryWarehouseSkuWhiteConfigCommandService.delete(input.getId()));
    }

    private void fillWncFullCategoryWarehouseSkuWhiteConfigVO(List<WncFullCategoryWarehouseSkuWhiteConfigVO> voList) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        List<Integer> warehouseNoList = voList.stream().map(WncFullCategoryWarehouseSkuWhiteConfigVO::getWarehouseNo).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(warehouseNoList)) {
            return;
        }
        List<WarehouseStorageEntity> warehouseStorageEntities = warehouseStorageCenterRepository.queryListByWarehouseNos(warehouseNoList);
        Map<Integer, String> warehouseNameMap = warehouseStorageEntities.stream().collect(Collectors.toMap(WarehouseStorageEntity::getWarehouseNo, WarehouseStorageEntity::getWarehouseName, (v1, v2) -> v1));
        for (WncFullCategoryWarehouseSkuWhiteConfigVO vo : voList) {
            vo.setWarehouseName(warehouseNameMap.get(vo.getWarehouseNo()));
        }
    }

    private void validateWncFullCategoryWarehouseSkuWhiteConfigCommandInput(WncFullCategoryWarehouseSkuWhiteConfigCommandInput input) {
        WarehouseStorageEntity warehouseStorageEntity = warehouseStorageCenterRepository.queryByWarehouseNo(input.getWarehouseNo());
        if (warehouseStorageEntity == null) {
            throw new BizException("找不到对应的库存仓");
        }
        List<Integer> selfWarehouseNos = wncConfig.querySelfWarehouseNos();
        if (selfWarehouseNos.contains(input.getWarehouseNo())) {
            throw new BizException("该仓库非三方仓仓库");
        }
        List<ProductSkuDTO> productSkuDTOList = gcQueryFacade.querySkuListInfo(Collections.singletonList(input.getSku()));
        if (CollectionUtils.isEmpty(productSkuDTOList)) {
            throw new BizException("找不到对应的sku");
        }
        if (!SkuSubTypEnum.SELF_SALE_NO_WAREHOUSE.getCode().equals(productSkuDTOList.get(0).getSubType())) {
            throw new BizException("该sku非代销不入仓品");
        }
    }

}

