package net.summerfarm.wnc.application.fence.strategy;

import net.summerfarm.wnc.client.enums.SourceEnum;
import net.summerfarm.wnc.common.query.fence.DeliveryFenceDateQuery;
import net.summerfarm.wnc.domain.fence.OutLandContactRepository;
import net.summerfarm.wnc.domain.fence.entity.OutLandContactEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @Date 2023-06-20
 **/
@Service
public class PopAfterSaleQueryDeliveryTimeStrategy implements DeliveryFenceQueryStrategy {
    @Autowired
    private OutLandContactRepository outLandContactRepository;


    @Override
    public SourceEnum getSource() {
        return SourceEnum.POP_AFTER_SALE;
    }

    @Override
    public OutLandContactEntity getContact(DeliveryFenceDateQuery query) {
        return outLandContactRepository.queryWithRuleByUk(query.getContactId());
    }

}
