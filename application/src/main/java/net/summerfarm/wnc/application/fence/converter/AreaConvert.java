package net.summerfarm.wnc.application.fence.converter;

import net.summerfarm.wnc.api.fence.dto.area.AreaDTO;
import net.summerfarm.wnc.common.constants.AppConsts;
import net.summerfarm.wnc.domain.fence.entity.AreaEntity;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @Date 2023-08-15
 **/
@Mapper(componentModel = AppConsts.MapStructConstants.COMPONENT_MODEL_SPRING)
public interface AreaConvert {

	/**
	 * entityToDTO
	 *
	 * @param areaEntity
	 * @return
	 */
	AreaDTO entityToDTO(AreaEntity areaEntity);
}
