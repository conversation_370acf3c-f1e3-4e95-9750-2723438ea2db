package net.summerfarm.wnc.application.inbound.converter.closeTime;

import net.summerfarm.wnc.client.req.closeTime.CloseTimeAreaAdjustCommandReq;
import net.summerfarm.wnc.client.req.closeTime.CloseTimeAreaConfigQueryReq;
import net.summerfarm.wnc.domain.closeTime.param.CloseTimeAreaConfigCommandParam;
import net.summerfarm.wnc.domain.closeTime.param.CloseTimeAreaConfigQueryParam;

/**
 * Description:截单时间配置参数转换器
 * date: 2024/3/20 11:18
 *
 * <AUTHOR>
 */
public class CloseTimeAreaConfigParamConverter {

    public static CloseTimeAreaConfigCommandParam req2Param(CloseTimeAreaAdjustCommandReq commandReq){
        if (commandReq == null){
            return null;
        }
        CloseTimeAreaConfigCommandParam closeTimeAreaConfigCommandParam = new CloseTimeAreaConfigCommandParam();
        closeTimeAreaConfigCommandParam.setTenantId(commandReq.getTenantId());
        closeTimeAreaConfigCommandParam.setProvince(commandReq.getProvince());
        closeTimeAreaConfigCommandParam.setCity(commandReq.getCity());
        closeTimeAreaConfigCommandParam.setArea(commandReq.getArea());
        closeTimeAreaConfigCommandParam.setCloseTime(commandReq.getCloseTime());
        return closeTimeAreaConfigCommandParam;

    }

    public static CloseTimeAreaConfigQueryParam req2Param(CloseTimeAreaConfigQueryReq queryReq){
        if (queryReq == null){
            return null;
        }
        CloseTimeAreaConfigQueryParam closeTimeAreaConfigQueryParam = new CloseTimeAreaConfigQueryParam();
        closeTimeAreaConfigQueryParam.setTenantId(queryReq.getTenantId());
        closeTimeAreaConfigQueryParam.setPageIndex(queryReq.getPageIndex());
        closeTimeAreaConfigQueryParam.setPageSize(queryReq.getPageSize());
        closeTimeAreaConfigQueryParam.setSortList(queryReq.getSortList());
        return closeTimeAreaConfigQueryParam;

    }
}
