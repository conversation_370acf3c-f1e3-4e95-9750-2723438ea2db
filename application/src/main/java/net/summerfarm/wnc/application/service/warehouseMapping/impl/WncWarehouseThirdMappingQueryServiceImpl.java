package net.summerfarm.wnc.application.service.warehouseMapping.impl;


import net.summerfarm.wnc.application.service.warehouseMapping.WncWarehouseThirdMappingQueryService;
import net.summerfarm.wnc.domain.warehouseMapping.repository.WncWarehouseThirdMappingQueryRepository;
import net.summerfarm.wnc.domain.warehouseMapping.entity.WncWarehouseThirdMappingEntity;
import net.summerfarm.wnc.domain.warehouseMapping.param.query.WncWarehouseThirdMappingQueryParam;
import net.summerfarm.wnc.application.inbound.controller.warehouseMapping.input.query.WncWarehouseThirdMappingQueryInput;
import net.summerfarm.wnc.application.inbound.controller.warehouseMapping.assembler.WncWarehouseThirdMappingAssembler;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
*
* <AUTHOR>
* @date 2025-06-11 15:08:31
* @version 1.0
*
*/
@Service
public class WncWarehouseThirdMappingQueryServiceImpl implements WncWarehouseThirdMappingQueryService {

    @Autowired
    private WncWarehouseThirdMappingQueryRepository wncWarehouseThirdMappingQueryRepository;

    @Override
    public PageInfo<WncWarehouseThirdMappingEntity> getPage(WncWarehouseThirdMappingQueryInput input) {
        WncWarehouseThirdMappingQueryParam queryParam = WncWarehouseThirdMappingAssembler.toWncWarehouseThirdMappingQueryParam(input);
        return wncWarehouseThirdMappingQueryRepository.getPage(queryParam);
    }

    @Override
    public WncWarehouseThirdMappingEntity getDetail(Long id){
        if (Objects.isNull(id)) {
            throw new BizException("请求参数为空！");
        }
        return wncWarehouseThirdMappingQueryRepository.selectById(id);
    }
}