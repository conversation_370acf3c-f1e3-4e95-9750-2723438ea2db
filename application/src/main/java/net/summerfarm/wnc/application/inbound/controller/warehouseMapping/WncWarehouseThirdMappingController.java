

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.application.inbound.controller.warehouseMapping.assembler.WncWarehouseThirdMappingAssembler;
import net.summerfarm.wnc.domain.warehouseMapping.entity.WncWarehouseThirdMappingEntity;
import net.summerfarm.wnc.application.inbound.controller.warehouseMapping.input.command.WncWarehouseThirdMappingCommandInput;
import net.summerfarm.wnc.application.inbound.controller.warehouseMapping.vo.WncWarehouseThirdMappingVO;
import net.summerfarm.wnc.domain.warehouseMapping.param.query.WncWarehouseThirdMappingQueryParam;
import net.summerfarm.wnc.application.inbound.controller.warehouseMapping.input.query.WncWarehouseThirdMappingQueryInput;
import net.summerfarm.wnc.common.converter.PageInfoConverter;
import net.summerfarm.wnc.application.service.warehouseMapping.WncWarehouseThirdMappingCommandService;
import net.summerfarm.wnc.application.service.warehouseMapping.WncWarehouseThirdMappingQueryService;
import net.xianmu.common.result.CommonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.PathVariable;


/**
 * @Title 仓库三方映射
 * @Description 仓库三方映射功能模块
 * <AUTHOR>
 * @date 2025-06-11 15:38:55
 * @version 1.0
 */
@RestController
@RequestMapping(value="/wncWarehouseThirdMapping")
public class WncWarehouseThirdMappingController{

	@Autowired
	private WncWarehouseThirdMappingCommandService wncWarehouseThirdMappingCommandService;
	@Autowired
	private WncWarehouseThirdMappingQueryService wncWarehouseThirdMappingQueryService;


	/**
	 * 仓库三方映射列表
	 * @return WncWarehouseThirdMappingVO
	 */
	@PostMapping(value="/query/page")
	public CommonResult<PageInfo<WncWarehouseThirdMappingVO>> getPage(@RequestBody WncWarehouseThirdMappingQueryInput input){
		PageInfo<WncWarehouseThirdMappingEntity> page = wncWarehouseThirdMappingQueryService.getPage(input);
		return CommonResult.ok(PageInfoConverter.toPageResp(page, WncWarehouseThirdMappingAssembler::toWncWarehouseThirdMappingVO));
	}

	/**
	* 获取详情
	* @return WncWarehouseThirdMappingVO
	*/
	@PostMapping(value = "/query/detail/{id}")
	public CommonResult<WncWarehouseThirdMappingVO> detail(@PathVariable Long id){
		return CommonResult.ok(WncWarehouseThirdMappingAssembler.toWncWarehouseThirdMappingVO(wncWarehouseThirdMappingQueryService.getDetail(id)));
	}


	/**
	 * 新增
	 * @return WncWarehouseThirdMappingVO
	 */
	@PostMapping(value = "/upsert/insert")
	public CommonResult<WncWarehouseThirdMappingVO> insert(@RequestBody WncWarehouseThirdMappingCommandInput input) {
		return CommonResult.ok(WncWarehouseThirdMappingAssembler.toWncWarehouseThirdMappingVO(wncWarehouseThirdMappingCommandService.insert(input)));
	}

	/**
	 * 修改
	 * @return
	 */
	@PostMapping(value = "/upsert/update")
	public CommonResult<Integer> update(@RequestBody WncWarehouseThirdMappingCommandInput input){
		return CommonResult.ok(wncWarehouseThirdMappingCommandService.update(input));
	}

	/**
	* 删除
	* @return
	*/
	@PostMapping(value = "/upsert/delete/{id}")
	public CommonResult<Integer> delete(@PathVariable Long id){
		return CommonResult.ok(wncWarehouseThirdMappingCommandService.delete(id));
	}


}

