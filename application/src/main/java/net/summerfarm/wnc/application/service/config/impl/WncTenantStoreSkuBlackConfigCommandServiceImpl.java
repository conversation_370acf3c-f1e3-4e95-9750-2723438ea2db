package net.summerfarm.wnc.application.service.config.impl;


import net.summerfarm.wnc.application.service.config.TenantStoreSkuBlackConfigCommandService;
import net.summerfarm.wnc.domain.config.service.TenantStoreSkuBlackConfigCommandDomainService;
import net.summerfarm.wnc.domain.config.entity.TenantStoreSkuBlackConfigEntity;
import net.summerfarm.wnc.domain.config.param.command.WncTenantStoreSkuBlackConfigCommandParam;
import net.summerfarm.wnc.application.inbound.controller.config.input.command.WncTenantStoreSkuBlackConfigCommandInput;
import net.summerfarm.wnc.application.inbound.controller.config.assembler.WncTenantStoreSkuBlackConfigAssembler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
*
* <AUTHOR>
* @date 2024-09-12 14:00:16
* @version 1.0
*
*/
@Service
@Transactional(rollbackFor = Exception.class)
public class WncTenantStoreSkuBlackConfigCommandServiceImpl implements TenantStoreSkuBlackConfigCommandService {

    @Autowired
    private TenantStoreSkuBlackConfigCommandDomainService wncTenantStoreSkuBlackConfigCommandDomainService;


    @Override
    public TenantStoreSkuBlackConfigEntity insert(WncTenantStoreSkuBlackConfigCommandInput input) {
        WncTenantStoreSkuBlackConfigCommandParam param = WncTenantStoreSkuBlackConfigAssembler.buildCreateParam(input);
        return wncTenantStoreSkuBlackConfigCommandDomainService.insert(param);
    }


    @Override
    public int update(WncTenantStoreSkuBlackConfigCommandInput input) {
        WncTenantStoreSkuBlackConfigCommandParam param = WncTenantStoreSkuBlackConfigAssembler.buildUpdateParam(input);
        return wncTenantStoreSkuBlackConfigCommandDomainService.update(param);
    }


    @Override
    public int delete(Long id) {
        return wncTenantStoreSkuBlackConfigCommandDomainService.delete(id);
    }
}