package net.summerfarm.wnc.application.warehouse.converter;

import net.summerfarm.wnc.api.warehouse.dto.StoreSiteDTO;
import net.summerfarm.wnc.api.warehouse.dto.WarehouseLogisticsCenterDTO;
import net.summerfarm.wnc.api.warehouse.dto.WarehouseStorageDTO;
import net.summerfarm.wnc.api.warehouse.input.WarehouseLogisticsSaveInput;
import net.summerfarm.wnc.api.warehouse.input.WarehouseLogisticsUpdateInput;
import net.summerfarm.wnc.application.fence.converter.StopDeliveryConverter;
import net.summerfarm.wnc.application.fence.converter.WarehouseStorageEntityConverter;
import net.summerfarm.wnc.common.enums.WarehouseLogisticsCenterEnums;
import net.summerfarm.wnc.domain.warehouse.entity.StoreSiteEntity;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseLogisticsCenterEntity;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseStorageEntity;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: 城配仓转化<br/>
 * date: 2023/10/7 18:33<br/>
 *
 * <AUTHOR> />
 */
public class WarehouseLogisticsConverter {

    public static WarehouseLogisticsCenterDTO entity2DTO(WarehouseLogisticsCenterEntity entity){
        if(entity == null){
            return null;
        }
        WarehouseLogisticsCenterDTO warehouseLogisticsCenterDTO = new WarehouseLogisticsCenterDTO();
        warehouseLogisticsCenterDTO.setId(entity.getId());
        warehouseLogisticsCenterDTO.setStoreNo(entity.getStoreNo());
        warehouseLogisticsCenterDTO.setStoreName(entity.getStoreName());
        warehouseLogisticsCenterDTO.setStatus(entity.getStatus());
        warehouseLogisticsCenterDTO.setManageAdminId(entity.getManageAdminId());
        warehouseLogisticsCenterDTO.setPoiNote(entity.getPoiNote());
        warehouseLogisticsCenterDTO.setAddress(entity.getAddress());
        warehouseLogisticsCenterDTO.setCloseOrderType(entity.getCloseOrderType());
        warehouseLogisticsCenterDTO.setOriginStoreNo(entity.getOriginStoreNo());
        warehouseLogisticsCenterDTO.setSotFinishTime(entity.getSotFinishTime());
        warehouseLogisticsCenterDTO.setCreator(entity.getCreator());
        warehouseLogisticsCenterDTO.setUpdater(entity.getUpdater());
        warehouseLogisticsCenterDTO.setUpdateTime(entity.getUpdateTime());
        warehouseLogisticsCenterDTO.setCreateTime(entity.getCreateTime());
        warehouseLogisticsCenterDTO.setCloseTime(entity.getCloseTime());
        warehouseLogisticsCenterDTO.setUpdateCloseTime(entity.getUpdateCloseTime());
        warehouseLogisticsCenterDTO.setPersonContact(entity.getPersonContact());
        warehouseLogisticsCenterDTO.setPhone(entity.getPhone());
        warehouseLogisticsCenterDTO.setRegion(entity.getRegion());
        warehouseLogisticsCenterDTO.setStopDeliveryStatus(entity.getStopDeliveryStatus());
        warehouseLogisticsCenterDTO.setManageAdminName(entity.getManageAdminName());
        warehouseLogisticsCenterDTO.setStorePic(entity.getStorePic());

        if(!CollectionUtils.isEmpty(entity.getWarehouseStorageEntities())){
            List<WarehouseStorageDTO> warehouseStorageDTOList = entity.getWarehouseStorageEntities().stream().map(WarehouseStorageEntityConverter::entity2DTO).collect(Collectors.toList());
            warehouseLogisticsCenterDTO.setWarehouseStorageDTOList(warehouseStorageDTOList);
        }
        if(entity.getStoreSiteEntity() != null){
            warehouseLogisticsCenterDTO.setStoreSiteDTO(WarehouseLogisticsConverter.siteEntity2DTO(entity.getStoreSiteEntity()));
        }
        if(entity.getStopDeliveryEntity() != null){
            warehouseLogisticsCenterDTO.setStopDeliveryDTO(StopDeliveryConverter.entity2DTO(entity.getStopDeliveryEntity()));
        }
        if (entity.getFulfillmentType() != null){
            warehouseLogisticsCenterDTO.setFulfillmentType(entity.getFulfillmentType().getValue());
            warehouseLogisticsCenterDTO.setFulfillmentTypeDesc(entity.getFulfillmentType().getContent());

        }

        return warehouseLogisticsCenterDTO;
    }


    public static StoreSiteDTO siteEntity2DTO(StoreSiteEntity entity){
        if(entity == null){
            return null;
        }
        StoreSiteDTO dto = new StoreSiteDTO();

        dto.setPunchState(entity.getPunchState());
        dto.setPunchDistance(entity.getPunchDistance());
        dto.setOutTime(entity.getOutTime());
        dto.setIntelligencePath(entity.getIntelligencePath());

        return dto;
    }


    public static StoreSiteEntity siteDTO2Entity(StoreSiteDTO dto){
        if(dto == null){
            return null;
        }
        StoreSiteEntity entity = new StoreSiteEntity();

        entity.setPunchState(dto.getPunchState());
        entity.setPunchDistance(dto.getPunchDistance());
        entity.setOutTime(dto.getOutTime());
        entity.setIntelligencePath(dto.getIntelligencePath());

        return entity;
    }


    public static WarehouseLogisticsCenterDTO updateInput2DTO(WarehouseLogisticsUpdateInput input){
        if(input == null){
            return null;
        }
        WarehouseLogisticsCenterDTO dto = new WarehouseLogisticsCenterDTO();
        dto.setId(input.getId());
        dto.setStoreName(input.getStoreName());
        dto.setStatus(input.getStatus());
        dto.setManageAdminId(input.getManageAdminId());
        dto.setPoiNote(input.getPoiNote());
        dto.setAddress(input.getAddress());
        dto.setCloseOrderType(input.getCloseOrderType());
        dto.setUpdateCloseTime(input.getUpdateCloseTime());
        dto.setPersonContact(input.getPersonContact());
        dto.setPhone(input.getPhone());
        dto.setRegion(input.getRegion());
        dto.setStorePic(input.getStorePic());

        if(!CollectionUtils.isEmpty(input.getWarehouseNos())){
            List<WarehouseStorageDTO> warehouseStorageDTOList = new ArrayList<>();
            for (Integer warehouseNo : input.getWarehouseNos()) {
                WarehouseStorageDTO warehouseStorageDTO = new WarehouseStorageDTO();
                warehouseStorageDTO.setWarehouseNo(warehouseNo);
                warehouseStorageDTOList.add(warehouseStorageDTO);
            }
            dto.setWarehouseStorageDTOList(warehouseStorageDTOList);
        }

        dto.setStoreSiteDTO(StoreSiteDTO.builder().punchState(input.getPunchState()).punchDistance(input.getPunchDistance())
                .outTime(input.getOutTime()).intelligencePath(input.getIntelligencePath()).build());
        dto.setManageAdminId(input.getManageAdminId());
        dto.setFulfillmentType(input.getFulfillmentType());
        return dto;
    }

    public static WarehouseLogisticsCenterEntity entity2DTO(WarehouseLogisticsCenterDTO dto){
        if(dto == null){
            return null;
        }
        WarehouseLogisticsCenterEntity entity = new WarehouseLogisticsCenterEntity();

        entity.setId(dto.getId());
        entity.setStoreNo(dto.getStoreNo());
        entity.setStoreName(dto.getStoreName());
        entity.setStatus(dto.getStatus());
        entity.setManageAdminId(dto.getManageAdminId());
        entity.setPoiNote(dto.getPoiNote());
        entity.setAddress(dto.getAddress());
        entity.setCloseOrderType(dto.getCloseOrderType());
        entity.setOriginStoreNo(dto.getOriginStoreNo());
        entity.setSotFinishTime(dto.getSotFinishTime());
        entity.setCreator(dto.getCreator());
        entity.setUpdater(dto.getUpdater());
        entity.setUpdateTime(dto.getUpdateTime());
        entity.setCreateTime(dto.getCreateTime());
        entity.setCloseTime(dto.getCloseTime());
        entity.setUpdateCloseTime(dto.getUpdateCloseTime());
        entity.setPersonContact(dto.getPersonContact());
        entity.setPhone(dto.getPhone());
        entity.setRegion(dto.getRegion());
        entity.setStopDeliveryStatus(dto.getStopDeliveryStatus());
        entity.setManageAdminName(dto.getManageAdminName());
        entity.setStorePic(dto.getStorePic());
        entity.setFulfillmentType(WarehouseLogisticsCenterEnums.FulfillmentType.getTypeByValue(dto.getFulfillmentType()));

        if(!CollectionUtils.isEmpty(dto.getWarehouseStorageDTOList())){
            List<WarehouseStorageEntity> warehouseStorageEntities = new ArrayList<>();
            for (WarehouseStorageDTO warehouseStorageDTO : dto.getWarehouseStorageDTOList()) {
                WarehouseStorageEntity warehouseStorageEntity = new WarehouseStorageEntity();
                warehouseStorageEntity.setWarehouseNo(warehouseStorageDTO.getWarehouseNo());
                warehouseStorageEntities.add(warehouseStorageEntity);
            }
            entity.setWarehouseStorageEntities(warehouseStorageEntities);
        }

        if(dto.getStoreSiteDTO() != null){
            entity.setStoreSiteEntity(WarehouseLogisticsConverter.siteDTO2Entity(dto.getStoreSiteDTO()));
        }

        return entity;
    }


    public static WarehouseLogisticsCenterDTO saveInput2DTO(WarehouseLogisticsSaveInput input){
        if(input == null){
            return null;
        }
        WarehouseLogisticsCenterDTO dto = new WarehouseLogisticsCenterDTO();
        dto.setStoreName(input.getStoreName());
        dto.setStatus(input.getStatus());
        dto.setManageAdminId(input.getManageAdminId());
        dto.setPoiNote(input.getPoiNote());
        dto.setAddress(input.getAddress());
        dto.setCloseOrderType(input.getCloseOrderType());
        dto.setCloseTime(input.getCloseTime());
        dto.setPersonContact(input.getPersonContact());
        dto.setPhone(input.getPhone());
        dto.setRegion(input.getRegion());
        dto.setStorePic(input.getStorePic());
        if(!CollectionUtils.isEmpty(input.getWarehouseNos())){
            List<WarehouseStorageDTO> warehouseStorageDTOList = new ArrayList<>();
            for (Integer warehouseNo : input.getWarehouseNos()) {
                WarehouseStorageDTO warehouseStorageDTO = new WarehouseStorageDTO();
                warehouseStorageDTO.setWarehouseNo(warehouseNo);
                warehouseStorageDTOList.add(warehouseStorageDTO);
            }
            dto.setWarehouseStorageDTOList(warehouseStorageDTOList);
        }

        dto.setStoreSiteDTO(StoreSiteDTO.builder().punchState(input.getPunchState()).punchDistance(input.getPunchDistance())
                .outTime(input.getOutTime()).intelligencePath(input.getIntelligencePath()).build());
        dto.setManageAdminId(input.getManageAdminId());
        dto.setFulfillmentType(input.getFulfillmentType());

        return dto;
    }

}
