package net.summerfarm.wnc.application.inbound.controller.preciseDelivery.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Description:精准送配置区域详情视图对象
 * date: 2024/1/18 18:09
 *
 * <AUTHOR>
 */
@Data
public class PreciseDeliveryConfigTimeVO implements Serializable {

    private static final long serialVersionUID = -3439192238675922980L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 配置ID
     */
    private Long configId;

    /**
     * 开始时间
     */
    private String beginTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

}
