package net.summerfarm.wnc.application.inbound.provider.nacos;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.client.provider.nacos.NacosConfigQueryProvider;
import net.summerfarm.wnc.client.resp.nacos.PopHelpOrderMerchantResp;
import net.summerfarm.wnc.common.config.WncConfig;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Description:  Nacos配置查询接口<br/>
 * date: 2025/4/17 10:41<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@DubboService
public class NacosConfigQueryProviderImpl implements NacosConfigQueryProvider {

    @Resource
    private WncConfig wncConfig;

    @Override
    public DubboResponse<Boolean> judgeIsTenantOfChagee(@Valid @NotNull(message = "租户ID不能为空") Long tenantId) {
        List<Long> chageeTenantIdList = wncConfig.queryChageeTenantIdList();
        if(CollectionUtils.isEmpty(chageeTenantIdList)){
            return DubboResponse.getOK(false);
        }

        return DubboResponse.getOK(chageeTenantIdList.contains(tenantId));
    }

    @Override
    public DubboResponse<Long> queryChageeTenantId() {
        List<Long> chageeTenantIdList = wncConfig.queryChageeTenantIdList();
        if(CollectionUtils.isEmpty(chageeTenantIdList)){
            return DubboResponse.getOK(null);
        }

        return DubboResponse.getOK(chageeTenantIdList.get(0));
    }

    @Override
    public DubboResponse<PopHelpOrderMerchantResp> queryPopHelperOrderMerchantInfo() {
        List<Long> popHelpOrderMerchantIdList = wncConfig.queryPopHelpOrderMerchantIdList();

        PopHelpOrderMerchantResp resp = new PopHelpOrderMerchantResp();
        if(CollectionUtils.isEmpty(popHelpOrderMerchantIdList)){
            return DubboResponse.getOK(resp);
        }
        resp.setMIdList(popHelpOrderMerchantIdList);
        return DubboResponse.getOK(resp);
    }
}
