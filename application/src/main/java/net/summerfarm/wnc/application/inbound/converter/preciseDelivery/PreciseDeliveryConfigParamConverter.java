package net.summerfarm.wnc.application.inbound.converter.preciseDelivery;

import com.google.common.collect.Lists;
import net.summerfarm.wnc.application.inbound.controller.preciseDelivery.input.*;
import net.summerfarm.wnc.client.req.preciseDelivery.PreciseDeliveryConfigReq;
import net.summerfarm.wnc.domain.preciseDelivery.param.PreciseDeliveryConfigAreaCommandParam;
import net.summerfarm.wnc.domain.preciseDelivery.param.PreciseDeliveryConfigCommandParam;
import net.summerfarm.wnc.domain.preciseDelivery.param.PreciseDeliveryConfigQueryParam;
import net.summerfarm.wnc.domain.preciseDelivery.param.PreciseDeliveryConfigTimeCommandParam;
import net.xianmu.common.exception.BizException;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Description:精准送配置参数转换器
 * date: 2024/1/22 16:45
 *
 * <AUTHOR>
 */
public class PreciseDeliveryConfigParamConverter {

    public static PreciseDeliveryConfigQueryParam input2Param(PreciseDeliveryConfigPageQueryInput queryInput){
        if (queryInput == null){
            return null;
        }
        PreciseDeliveryConfigQueryParam preciseDeliveryConfigQueryParam = new PreciseDeliveryConfigQueryParam();
        preciseDeliveryConfigQueryParam.setProvince(queryInput.getProvince());
        preciseDeliveryConfigQueryParam.setCity(queryInput.getCity());
        preciseDeliveryConfigQueryParam.setArea(queryInput.getArea());
        preciseDeliveryConfigQueryParam.setPageIndex(queryInput.getPageIndex());
        preciseDeliveryConfigQueryParam.setPageSize(queryInput.getPageSize());
        preciseDeliveryConfigQueryParam.setSortList(queryInput.getSortList());
        return preciseDeliveryConfigQueryParam;
    }

    public static PreciseDeliveryConfigQueryParam req2Param(PreciseDeliveryConfigReq req){
        if (req == null){
            return null;
        }
        PreciseDeliveryConfigQueryParam preciseDeliveryConfigQueryParam = new PreciseDeliveryConfigQueryParam();
        preciseDeliveryConfigQueryParam.setCity(req.getCity());
        preciseDeliveryConfigQueryParam.setArea(req.getArea());
        return preciseDeliveryConfigQueryParam;
    }

    public static PreciseDeliveryConfigCommandParam input2Param(PreciseDeliveryConfigCreateCommandInput commandInput){
        if (commandInput == null){
            return null;
        }
        PreciseDeliveryConfigCommandParam preciseDeliveryConfigCommandParam = new PreciseDeliveryConfigCommandParam();
        preciseDeliveryConfigCommandParam.setProvince(commandInput.getProvince());
        preciseDeliveryConfigCommandParam.setCity(commandInput.getCity());

        preciseDeliveryConfigCommandParam.setAreaList(Optional.ofNullable(commandInput.getAreaList())
                .orElse(Lists.newArrayList()).stream().map(PreciseDeliveryConfigParamConverter::areaInput2Param).collect(Collectors.toList()));

        preciseDeliveryConfigCommandParam.setTimeList(Optional.ofNullable(commandInput.getTimeList())
                .orElse(Lists.newArrayList()).stream().map(PreciseDeliveryConfigParamConverter::timeInput2Param).collect(Collectors.toList()));

        return preciseDeliveryConfigCommandParam;
    }

    public static PreciseDeliveryConfigCommandParam input2Param(PreciseDeliveryConfigUpdateCommandInput commandInput){
        if (commandInput == null){
            return null;
        }
        PreciseDeliveryConfigCommandParam preciseDeliveryConfigCommandParam = new PreciseDeliveryConfigCommandParam();
        preciseDeliveryConfigCommandParam.setConfigId(commandInput.getConfigId());
        preciseDeliveryConfigCommandParam.setProvince(commandInput.getProvince());
        preciseDeliveryConfigCommandParam.setCity(commandInput.getCity());

        preciseDeliveryConfigCommandParam.setAreaList(Optional.ofNullable(commandInput.getAreaList())
                .orElse(Lists.newArrayList()).stream().map(PreciseDeliveryConfigParamConverter::areaInput2Param).collect(Collectors.toList()));

        preciseDeliveryConfigCommandParam.setTimeList(Optional.ofNullable(commandInput.getTimeList())
                .orElse(Lists.newArrayList()).stream().map(PreciseDeliveryConfigParamConverter::timeInput2Param).collect(Collectors.toList()));
        return preciseDeliveryConfigCommandParam;
    }

    public static PreciseDeliveryConfigAreaCommandParam areaInput2Param(PreciseDeliveryConfigAreaCommandInput commandInput){
        if (commandInput == null){
            return null;
        }
        PreciseDeliveryConfigAreaCommandParam preciseDeliveryConfigAreaCommandParam = new PreciseDeliveryConfigAreaCommandParam();
        preciseDeliveryConfigAreaCommandParam.setAdCode(commandInput.getAdCode());
        preciseDeliveryConfigAreaCommandParam.setCity(commandInput.getCity());
        preciseDeliveryConfigAreaCommandParam.setArea(commandInput.getArea());
        return preciseDeliveryConfigAreaCommandParam;
    }

    public static PreciseDeliveryConfigTimeCommandParam timeInput2Param(PreciseDeliveryConfigTimeCommandInput commandInput){
        if (commandInput == null){
            return null;
        }
        PreciseDeliveryConfigTimeCommandParam preciseDeliveryConfigTimeCommandParam = new PreciseDeliveryConfigTimeCommandParam();
        LocalTime beginTime;
        LocalTime endTime;
        try {
            beginTime = LocalTime.parse(commandInput.getBeginTime().trim(), DateTimeFormatter.ofPattern("HH:mm"));
            endTime = LocalTime.parse(commandInput.getEndTime().trim(), DateTimeFormatter.ofPattern("HH:mm"));
        } catch (DateTimeParseException e){
            throw new BizException("时间参数格式非法");
        }

        preciseDeliveryConfigTimeCommandParam.setBeginTime(beginTime);
        preciseDeliveryConfigTimeCommandParam.setEndTime(endTime);
        return preciseDeliveryConfigTimeCommandParam;
    }
}
