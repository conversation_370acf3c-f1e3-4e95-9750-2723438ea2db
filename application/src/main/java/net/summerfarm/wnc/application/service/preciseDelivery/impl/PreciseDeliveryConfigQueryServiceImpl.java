package net.summerfarm.wnc.application.service.preciseDelivery.impl;

import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import net.summerfarm.wnc.application.inbound.converter.preciseDelivery.PreciseDeliveryConfigParamConverter;
import net.summerfarm.wnc.application.service.preciseDelivery.PreciseDeliveryConfigQueryService;
import net.summerfarm.wnc.application.inbound.controller.preciseDelivery.input.PreciseDeliveryConfigIdQueryInput;
import net.summerfarm.wnc.application.inbound.controller.preciseDelivery.input.PreciseDeliveryConfigPageQueryInput;
import net.summerfarm.wnc.domain.preciseDelivery.PreciseDeliveryConfigQueryRepository;
import net.summerfarm.wnc.domain.preciseDelivery.aggregate.PreciseDeliveryConfigAggregate;
import net.summerfarm.wnc.domain.preciseDelivery.param.PreciseDeliveryConfigQueryParam;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;

/**
 * Description:精准送配置查询服务实现
 * date: 2024/1/22 11:00
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class PreciseDeliveryConfigQueryServiceImpl implements PreciseDeliveryConfigQueryService {

    private final PreciseDeliveryConfigQueryRepository preciseDeliveryConfigQueryRepository;

    @Override
    public PageInfo<PreciseDeliveryConfigAggregate> queryPage(PreciseDeliveryConfigPageQueryInput pageQueryInput) {
        PreciseDeliveryConfigQueryParam pageQueryParam = PreciseDeliveryConfigParamConverter.input2Param(pageQueryInput);
        PageInfo<PreciseDeliveryConfigAggregate> pageInfo = preciseDeliveryConfigQueryRepository.queryPage(pageQueryParam);
        return pageInfo;
    }

    @Override
    public PreciseDeliveryConfigAggregate queryDetail(PreciseDeliveryConfigIdQueryInput configIdQueryInput) {
        PreciseDeliveryConfigAggregate aggregate = preciseDeliveryConfigQueryRepository.queryDetail(configIdQueryInput.getConfigId());
        if (aggregate == null) {
            throw new BizException("无效精准送配置");
        }
        return aggregate;
    }
}
