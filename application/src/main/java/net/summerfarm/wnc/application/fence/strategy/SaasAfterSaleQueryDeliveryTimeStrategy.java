package net.summerfarm.wnc.application.fence.strategy;

import net.summerfarm.wnc.client.enums.SourceEnum;
import net.summerfarm.wnc.common.enums.ContactConfigEnums;
import net.summerfarm.wnc.common.enums.WncContactDeliveryRuleEnums;
import net.summerfarm.wnc.common.query.fence.DeliveryFenceDateQuery;
import net.summerfarm.wnc.domain.ConfigRepository;
import net.summerfarm.wnc.domain.config.repository.ContactConfigRepository;
import net.summerfarm.wnc.domain.deliveryRule.ContactDeliveryRuleRepository;
import net.summerfarm.wnc.domain.deliveryRule.entity.ContactDeliveryRuleEntity;
import net.summerfarm.wnc.domain.fence.entity.OutLandContactEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2023-06-20
 **/
@Service
public class SaasAfterSaleQueryDeliveryTimeStrategy implements DeliveryFenceQueryStrategy {

	@Resource
	private ConfigRepository configRepository;
	@Resource
	private ContactDeliveryRuleRepository contactDeliveryRuleRepository;
	@Resource
	private ContactConfigRepository contactConfigRepository;

	@Override
	public SourceEnum getSource() {
		return SourceEnum.SAAS_AFTER_SALE;
	}

	@Override
	public OutLandContactEntity getContact(DeliveryFenceDateQuery query) {
		OutLandContactEntity outLandContactEntity = new OutLandContactEntity();
		outLandContactEntity.setContactId(query.getContactId());
		outLandContactEntity.setCity(query.getCity());
		outLandContactEntity.setArea(query.getArea());
		outLandContactEntity.setSource(ContactConfigEnums.Source.SAAS);
		outLandContactEntity.setTenantId(query.getTenantId());
		if (configRepository.queryNoAreaCity().contains(query.getCity())) {
			query.setArea(null);
			outLandContactEntity.setArea(null);
		}
		//查询Saas客户配送规则
		ContactDeliveryRuleEntity contactDeliveryRuleEntity = contactDeliveryRuleRepository.queryByUk(String.valueOf(query.getContactId()), WncContactDeliveryRuleEnums.SystemSource.SAAS.getValue());
		outLandContactEntity.setContactDeliveryRuleEntity(contactDeliveryRuleEntity);
		//查询指定城配仓信息
		outLandContactEntity.setContactConfigEntity(contactConfigRepository.queryByUk(ContactConfigEnums.Source.SAAS, query.getContactId()));
		return outLandContactEntity;
	}
}
