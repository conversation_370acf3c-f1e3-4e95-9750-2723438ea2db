package net.summerfarm.wnc.application.inbound.controller.backdoor;

import net.summerfarm.wnc.common.enums.FenceChannelBusinessWhiteConfigEnums;
import net.summerfarm.wnc.common.query.fence.FenceQuery;
import net.summerfarm.wnc.domain.fence.FenceRepository;
import net.summerfarm.wnc.domain.fence.entity.FenceChannelBusinessWhiteConfigEntity;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.summerfarm.wnc.domain.fence.repository.FenceChannelBusinessWhiteConfigCommandRepository;
import net.summerfarm.wnc.domain.fence.repository.FenceChannelBusinessWhiteConfigQueryRepository;
import net.xianmu.common.result.CommonResult;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2024/10/15 16:20<br/>
 *
 * <AUTHOR> />
 */
@RestController
@RequestMapping(value="/fence-channel-business-white-config-backDoor")
public class FenceChannelBusinessWhiteConfigBackDoorController {
    @Resource
    private FenceChannelBusinessWhiteConfigQueryRepository fenceChannelBusinessWhiteConfigQueryRepository;
    @Resource
    private FenceRepository fenceRepository;
    @Resource
    private FenceChannelBusinessWhiteConfigCommandRepository fenceChannelBusinessWhiteConfigCommandRepository;

    /**
     * 初始化渠道业务白名单配置
     */
    @PostMapping("/init")
    public CommonResult<Void> initFenceChannelBusiness(){
        // 查询所有的存在的围栏ID
        List<Integer> fenceIdList= fenceChannelBusinessWhiteConfigQueryRepository.queryAllFenceIdList();

        // 查询不在当前记录里面的围栏信息
        List<FenceEntity> fenceEntities = fenceRepository.queryList(FenceQuery.builder()
                .notInFenceIds(fenceIdList)
                .build());

        if(CollectionUtils.isEmpty(fenceEntities)){
            return CommonResult.ok();
        }

        List<FenceChannelBusinessWhiteConfigEntity> saveList = new ArrayList<>();
        fenceEntities.forEach(fenceEntity->{
            String orderChannelType = fenceEntity.getOrderChannelType();
            if(StringUtils.isEmpty(orderChannelType)){
                return;
            }
            List<String> channelTypes = Arrays.stream(orderChannelType.split(",")).distinct().collect(Collectors.toList());
            if(CollectionUtils.isEmpty(channelTypes)){
                return;
            }
            channelTypes.forEach(channelType->{
                FenceChannelBusinessWhiteConfigEntity fenceChannelBusinessWhiteConfigEntity = new FenceChannelBusinessWhiteConfigEntity();
                fenceChannelBusinessWhiteConfigEntity.setFenceId(fenceEntity.getId());
                fenceChannelBusinessWhiteConfigEntity.setOrderChannelType(channelType);
                fenceChannelBusinessWhiteConfigEntity.setScopeChannelBusinessId(FenceChannelBusinessWhiteConfigEnums.ScopeChannelBusinessId.ALL.getValue());
                saveList.add(fenceChannelBusinessWhiteConfigEntity);
            });
        });

        // 批量添加
        fenceChannelBusinessWhiteConfigCommandRepository.batchInsert(saveList);

        return CommonResult.ok();
    }

}
