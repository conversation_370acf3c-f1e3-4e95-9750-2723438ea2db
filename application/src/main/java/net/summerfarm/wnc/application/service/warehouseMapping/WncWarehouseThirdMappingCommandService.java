package net.summerfarm.wnc.application.service.warehouseMapping;

import net.summerfarm.wnc.application.inbound.controller.warehouseMapping.input.command.WncWarehouseThirdMappingCommandInput;
import net.summerfarm.wnc.domain.warehouseMapping.entity.WncWarehouseThirdMappingEntity;


/**
 * @date 2025-06-11 15:08:31
 * @version 1.0
 */
public interface WncWarehouseThirdMappingCommandService {

    /**
     * @description: 新增
     * @return WncWarehouseThirdMappingEntity
     **/
    WncWarehouseThirdMappingEntity insert(WncWarehouseThirdMappingCommandInput input);


    /**
     * @description: 更新
     * @return:
     **/
    int update(WncWarehouseThirdMappingCommandInput input);



    /**
    * @description: 删除
    * @return:
    **/
    int delete(Long id);

}