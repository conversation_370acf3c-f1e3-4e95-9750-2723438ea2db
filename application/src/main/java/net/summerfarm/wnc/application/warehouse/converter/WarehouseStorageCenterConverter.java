package net.summerfarm.wnc.application.warehouse.converter;

import net.summerfarm.wnc.api.warehouse.dto.WarehouseBaseDTO;
import net.summerfarm.wnc.common.constants.AppConsts;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseStorageEntity;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-07-04
 **/
@Mapper(componentModel = AppConsts.MapStructConstants.COMPONENT_MODEL_SPRING)
public interface WarehouseStorageCenterConverter {

	/**
	 * entityToBaseDTO
	 *
	 * @param entityList
	 * @return
	 */
	List<WarehouseBaseDTO> entityToBaseDTO(List<WarehouseStorageEntity> entityList);
}
