package net.summerfarm.wnc.application.fence;

import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.fence.dto.DeliveryFenceDTO;
import net.summerfarm.wnc.api.fence.dto.SkuDeliveryDateDTO;
import net.summerfarm.wnc.api.fence.dto.WarehouseStorageFenceRuleDTO;
import net.summerfarm.wnc.api.fence.dto.WncSkuStoreNoDTO;
import net.summerfarm.wnc.api.fence.service.DeliveryFenceService;
import net.summerfarm.wnc.api.fence.service.XmWarehouseStorageFenceRuleService;
import net.summerfarm.wnc.application.fence.handle.DeliveryDateSkuHandle;
import net.summerfarm.wnc.application.warehouse.converter.WarehouseInventoryMappingEntityConverter;
import net.summerfarm.wnc.client.enums.WarehouseSourceEnum;
import net.summerfarm.wnc.common.base.WncAssert;
import net.summerfarm.wnc.common.enums.WarehouseStorageFenceRuleEnum;
import net.summerfarm.wnc.common.query.fence.WarehouseStorageFenceRuleQuery;
import net.summerfarm.wnc.domain.warehouse.WarehouseInventoryMappingDomainService;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseInventoryMappingEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/8/23 18:36<br/>
 *
 * <AUTHOR> />
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class XmWarehouseStorageFenceRuleServiceImpl implements XmWarehouseStorageFenceRuleService {

    private final DeliveryFenceService deliveryFenceService;
    private final WarehouseInventoryMappingDomainService warehouseInventoryMappingDomainService;
    private final DeliveryDateSkuHandle deliveryDateSkuHandle;

    @Override
    public Integer getType() {
        return WarehouseStorageFenceRuleEnum.SUMMERFARM_WAREHOUSE.getCode();
    }

    @Override
    public List<WarehouseStorageFenceRuleDTO> queryWarehouseStorageFence(WarehouseStorageFenceRuleQuery warehouseStorageFenceRuleQuery) {
        log.info("queryWarehouseStorageFence查询鲜沐围栏配送顺序请求报文{}", JSON.toJSONString(warehouseStorageFenceRuleQuery));

        WncAssert.notNull(warehouseStorageFenceRuleQuery.getStoreNo(),"storeNo不能为空");
        WncAssert.notNull(warehouseStorageFenceRuleQuery.getTenantId(),"tenantId不能为空");
        WncAssert.notEmpty(warehouseStorageFenceRuleQuery.getSkuList(),"skuList不能为空");

        //查询配送日期
        DeliveryFenceDTO deliveryFenceDTO = new DeliveryFenceDTO();
        if(warehouseStorageFenceRuleQuery.getSource() != null && warehouseStorageFenceRuleQuery.getContactId() != null || warehouseStorageFenceRuleQuery.getMerchantId() != null){
            deliveryFenceDTO = deliveryDateSkuHandle.querySkuDelivery(warehouseStorageFenceRuleQuery.getSource(),
                    warehouseStorageFenceRuleQuery.getCity(),
                    warehouseStorageFenceRuleQuery.getArea(),
                    warehouseStorageFenceRuleQuery.getContactId(),
                    warehouseStorageFenceRuleQuery.getMerchantId(),
                    warehouseStorageFenceRuleQuery.getAddOrderFlag(),
                    warehouseStorageFenceRuleQuery.getSkuList(),
                    warehouseStorageFenceRuleQuery.getTenantId());
        }
        List<SkuDeliveryDateDTO> skuDeliveryDates = deliveryFenceDTO.getSkuDeliveryDates();
        Map<String, SkuDeliveryDateDTO> skuDeliveryDateMap = Collections.emptyMap();
        if(!CollectionUtils.isEmpty(skuDeliveryDates)){
            skuDeliveryDateMap = skuDeliveryDates.stream().collect(Collectors.toMap(SkuDeliveryDateDTO::getSku, Function.identity()));
        }

        //根据城配仓+sku集合查询对应的库存仓
        List<WarehouseInventoryMappingEntity> warehouseInventoryMappingEntities = warehouseInventoryMappingDomainService.queryMappingWithSkuSubTypeBySkusStoreNo(warehouseStorageFenceRuleQuery.getSkuList(),warehouseStorageFenceRuleQuery.getStoreNo());
        //按照仓库编号分组
        Map<Integer, List<WarehouseInventoryMappingEntity>> warehouseMappingListMap = warehouseInventoryMappingEntities.stream().collect(Collectors.groupingBy(WarehouseInventoryMappingEntity::getWarehouseNo));

        ArrayList<WarehouseStorageFenceRuleDTO> warehouseStorageFenceRuleDTOS = new ArrayList<>();
        for (Integer warehouseNo : warehouseMappingListMap.keySet()) {
            List<WarehouseInventoryMappingEntity> inventoryMappingList = warehouseMappingListMap.get(warehouseNo);
            if(CollectionUtils.isEmpty(inventoryMappingList)){
                continue;
            }

            WarehouseStorageFenceRuleDTO warehouseStorageFenceRuleDTO = new WarehouseStorageFenceRuleDTO();
            warehouseStorageFenceRuleDTO.setWarehouseNo(warehouseNo);
            warehouseStorageFenceRuleDTO.setTenantId(Long.parseLong(WarehouseSourceEnum.SUMMERFARM_WAREHOUSE.getCode()+""));
            warehouseStorageFenceRuleDTO.setStoreNoList(inventoryMappingList.stream().map(WarehouseInventoryMappingEntity::getStoreNo).collect(Collectors.toList()));
            warehouseStorageFenceRuleDTO.setDeliveryTime(deliveryFenceDTO.getDeliveryTime());
            warehouseStorageFenceRuleDTO.setCategoryDeliveryTime(deliveryFenceDTO.getFullCategoryDeliveryDay());
            warehouseStorageFenceRuleDTO.setCloseTime(deliveryFenceDTO.getCloseTime());
            warehouseStorageFenceRuleDTO.setIsEveryDayFlag(deliveryFenceDTO.getIsEveryDayFlag());
            warehouseStorageFenceRuleDTO.setDeliveryCloseTime(deliveryFenceDTO.getDeliveryCloseTime());

            List<WncSkuStoreNoDTO> skuStoreNoDTOList = warehouseMappingListMap.get(warehouseNo).stream()
                    .map(WarehouseInventoryMappingEntityConverter::entity2WncSkuStoreNoDTO).collect(Collectors.toList());
            //设置商品的配送时效
            for (WncSkuStoreNoDTO skuStoreNoDTO : skuStoreNoDTOList) {
                SkuDeliveryDateDTO skuDeliveryDateDTO = skuDeliveryDateMap.get(skuStoreNoDTO.getSku());
                if(skuDeliveryDateDTO == null){
                    continue;
                }
                skuStoreNoDTO.setDeliveryTime(skuDeliveryDateDTO.getDeliveryDate());
                skuStoreNoDTO.setDeliveryCloseTime(skuDeliveryDateDTO.getDeliveryCloseTime());
            }
            warehouseStorageFenceRuleDTO.setWncSkuStoreNoDTOS(skuStoreNoDTOList);
            warehouseStorageFenceRuleDTOS.add(warehouseStorageFenceRuleDTO);
        }

        return warehouseStorageFenceRuleDTOS;
    }
}
