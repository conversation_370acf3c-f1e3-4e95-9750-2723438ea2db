package net.summerfarm.wnc.application.inbound.provider.preciseDelivery;

import com.google.common.collect.Lists;
import net.summerfarm.wnc.application.inbound.converter.preciseDelivery.PreciseDeliveryConfigRespConverter;
import net.summerfarm.wnc.client.provider.preciseDelivery.PreciseDeliveryConfigQueryProvider;
import net.summerfarm.wnc.client.req.preciseDelivery.PreciseDeliveryConfigReq;
import net.summerfarm.wnc.client.resp.preciseDelivery.PreciseDeliveryConfigResp;
import net.summerfarm.wnc.domain.preciseDelivery.PreciseDeliveryConfigQueryRepository;
import net.summerfarm.wnc.domain.preciseDelivery.valueObject.PreciseDeliveryConfigTimeValueObject;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Description:精准送配置查询提供者实现
 * date: 2024/1/23 11:24
 *
 * <AUTHOR>
 */
@DubboService
public class PreciseDeliveryConfigQueryProviderImpl implements PreciseDeliveryConfigQueryProvider {

    @Resource
    private PreciseDeliveryConfigQueryRepository preciseDeliveryConfigQueryRepository;

    @Override
    public DubboResponse<PreciseDeliveryConfigResp> queryPreciseDeliveryConfig(@Valid PreciseDeliveryConfigReq preciseDeliveryConfigReq) {
        String city = preciseDeliveryConfigReq.getCity();
        String area = preciseDeliveryConfigReq.getArea();
        List<PreciseDeliveryConfigTimeValueObject> timeList = preciseDeliveryConfigQueryRepository.queryTimeListByCityAndArea(city, area);
        PreciseDeliveryConfigResp resp = new PreciseDeliveryConfigResp();
        resp.setCity(city);
        resp.setArea(area);
        resp.setTimeList(Optional.ofNullable(timeList).orElse(Lists.newArrayList()).stream().map(PreciseDeliveryConfigRespConverter::timeEntity2Resp).collect(Collectors.toList()));
        return DubboResponse.getOK(resp);
    }
}
