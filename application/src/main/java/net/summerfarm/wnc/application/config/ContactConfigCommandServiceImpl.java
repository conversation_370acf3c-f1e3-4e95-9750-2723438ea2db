package net.summerfarm.wnc.application.config;

import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.config.dto.ContactConfigUpdateResultDTO;
import net.summerfarm.wnc.api.config.input.ContactConfigCreateCommandInput;
import net.summerfarm.wnc.api.config.input.ContactConfigRemoveCommandInput;
import net.summerfarm.wnc.api.config.input.ContactConfigUpdateCommandInput;
import net.summerfarm.wnc.api.config.service.ContactConfigCommandService;
import net.summerfarm.wnc.application.config.converter.ContactConfigConverter;
import net.summerfarm.wnc.common.enums.ContactConfigEnums;
import net.summerfarm.wnc.common.query.config.ContactConfigQuery;
import net.summerfarm.wnc.domain.config.service.ContactConfigCommandDomainService;
import net.summerfarm.wnc.domain.config.repository.ContactConfigCommandRepository;
import net.summerfarm.wnc.domain.config.repository.ContactConfigQueryRepository;
import net.summerfarm.wnc.domain.config.entity.ContactConfigEntity;
import net.summerfarm.wnc.domain.config.param.command.ContactConfigCommandParam;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.user.UserBase;
import net.xianmu.common.user.UserInfoHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description:联系人配置服务实现
 * date: 2023/9/22 17:22
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ContactConfigCommandServiceImpl implements ContactConfigCommandService {

    private final ContactConfigCommandRepository contactConfigCommandRepository;
    private final ContactConfigQueryRepository contactConfigQueryRepository;
    private final ContactConfigCommandDomainService contactConfigCommandDomainService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeConfig(ContactConfigRemoveCommandInput contactConfigRemoveCommandInput) {
        ContactConfigEnums.Source source = ContactConfigEnums.Source.getSourceByTenantId(contactConfigRemoveCommandInput.getTenantId());
        ContactConfigEntity contactConfigEntity = contactConfigQueryRepository.queryByUk(source, contactConfigRemoveCommandInput.getOuterContactId());
        if (contactConfigEntity == null){
            log.info("无有效联系人配置");
            return;
        }
        contactConfigCommandRepository.remove(contactConfigEntity.getId());

    }

    @Override
    public void createConfig(ContactConfigCreateCommandInput contactConfigCreateCommandInput) {
        ContactConfigEntity contactConfigEntity = ContactConfigConverter.command2Entity(contactConfigCreateCommandInput);
        UserBase user = UserInfoHolder.getUser();
        contactConfigEntity.create(user.getNickname());
        contactConfigCommandRepository.save(contactConfigEntity);
    }

    @Override
    public List<ContactConfigUpdateResultDTO> updateConfig(List<ContactConfigUpdateCommandInput> commands) {
        if (CollectionUtils.isEmpty(commands)){
            return Collections.emptyList();
        }
        List<Long> contactIds = commands.stream().map(ContactConfigUpdateCommandInput::getOuterContactId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(contactIds)){
            return Collections.emptyList();
        }
        List<ContactConfigEntity> contactConfigEntities = contactConfigQueryRepository.queryList(ContactConfigQuery.builder().outerContactIds(contactIds).build());
        contactConfigEntities = Optional.ofNullable(contactConfigEntities).orElse(Lists.newArrayList());

        //根据门店ID+租户进行过滤一下
        Set<String> contactUkSet = commands.stream().map(ContactConfigUpdateCommandInput::buildUk).collect(Collectors.toSet());
        contactConfigEntities = contactConfigEntities.stream().filter(e -> contactUkSet.contains(e.buildUk())).collect(Collectors.toList());

        UserBase user = UserInfoHolder.getUser();
        Map<String, ContactConfigEntity> contactConfigMap = contactConfigEntities.stream().collect(Collectors.toMap(ContactConfigEntity::buildUk, Function.identity(), (oldData, newData) -> newData));
        List<ContactConfigCommandParam> contactConfigCommandParams = commands.stream().map(ContactConfigConverter::updateCommand2Param).collect(Collectors.toList());

        List<ContactConfigUpdateResultDTO> results = Lists.newArrayList();
        for (ContactConfigCommandParam param : contactConfigCommandParams) {
            param.create(user.getNickname());
            ContactConfigUpdateResultDTO result = new ContactConfigUpdateResultDTO(param.getOuterContactId(), param.getTenantId());
            results.add(result);
            ContactConfigEntity existedContactConfig = contactConfigMap.get(param.buildUk());
            try {
                contactConfigCommandDomainService.updateConfig(existedContactConfig, param, user.getNickname());
            }catch (Exception e){
                if (e instanceof BizException){
                    result.updateFail(e.getMessage());
                    continue;
                }
                log.error("SAAS门店 tenantId：{}，outerContactId：{}，调整履约方式失败,异常原因：{}", param.getTenantId(), param.getOuterContactId(), e.getMessage(),e);
                result.updateFail("处理失败");
                continue;
            }
            result.updateSuccess();
        }
        return results;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveOrUpdate(ContactConfigCreateCommandInput input) {
        if(input == null){
            return;
        }
        ContactConfigEnums.Source source = ContactConfigEnums.Source.getSourceByTenantId(input.getTenantId());
        ContactConfigEntity contactConfigEntityData = contactConfigQueryRepository.queryByUk(source, input.getOuterContactId());

        ContactConfigEntity contactConfigEntity = ContactConfigConverter.command2Entity(input);
        if(contactConfigEntityData == null){
            //新增
            UserBase user = UserInfoHolder.getUser();
            contactConfigEntity.create(user == null ? "" : user.getNickname());

            contactConfigCommandDomainService.save(contactConfigEntity);
        }else{
            contactConfigEntity.setId(contactConfigEntityData.getId());
            //更新
            contactConfigCommandDomainService.update(contactConfigEntity);
        }
    }
}
