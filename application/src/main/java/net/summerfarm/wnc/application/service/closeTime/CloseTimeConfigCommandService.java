package net.summerfarm.wnc.application.service.closeTime;

import net.summerfarm.wnc.domain.closeTime.param.CloseTimeAreaConfigCommandParam;

import java.util.List;

/**
 * Description:截单时间区域配置操作服务接口
 * date: 2024/3/20 18:51
 *
 * <AUTHOR>
 */
public interface CloseTimeConfigCommandService {

    /**
     * 编辑截单时间区域配置
     * @param commandParams 操作参数
     */
    void editAreaConfig(List<CloseTimeAreaConfigCommandParam> commandParams);
}
