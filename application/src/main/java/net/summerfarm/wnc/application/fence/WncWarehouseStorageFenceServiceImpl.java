package net.summerfarm.wnc.application.fence;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.fence.service.WncWarehouseStorageFenceService;
import net.summerfarm.wnc.api.warehouse.dto.WarehouseStorageDTO;
import net.summerfarm.wnc.api.warehouse.dto.WncWarehouseStorageFenceDTO;
import net.summerfarm.wnc.application.warehouse.converter.WarehouseStorageCenterEntityConverter;
import net.summerfarm.wnc.common.enums.WarehouseSourceEnum;
import net.summerfarm.wnc.common.query.warehouse.WarehouseStorageFenceQuery;
import net.summerfarm.wnc.common.query.warehouse.WarehouseStorageQuery;
import net.summerfarm.wnc.domain.warehouse.WarehouseStorageCenterDomainService;
import net.summerfarm.wnc.domain.warehouse.WarehouseStorageCenterRepository;
import net.summerfarm.wnc.domain.warehouse.WncWarehouseStorageFenceDomain;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseStorageEntity;
import net.summerfarm.wnc.domain.warehouse.entity.WncWarehouseStorageFenceEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/4/14 11:41<br/>
 *
 * <AUTHOR> />
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class WncWarehouseStorageFenceServiceImpl implements WncWarehouseStorageFenceService {

    private final WncWarehouseStorageFenceDomain wncWarehouseStorageFenceDomain;
    private final WarehouseStorageCenterRepository warehouseStorageCenterRepository;
    private final WarehouseStorageCenterDomainService warehouseStorageCenterDomainService;

    @Override
    public List<WncWarehouseStorageFenceDTO> queryFenceConflict(WarehouseStorageFenceQuery warehouseStorageFenceQuery) {
        warehouseStorageFenceQuery.setNoThisWarehouseNo(warehouseStorageFenceQuery.getWarehouseNo());
        warehouseStorageFenceQuery.setWarehouseNo(null);
        //查询冲突的区域
        List<WncWarehouseStorageFenceEntity> wncWarehouseStorageFenceEntities = wncWarehouseStorageFenceDomain.queryFenceConflict(warehouseStorageFenceQuery);

        return wncWarehouseStorageFenceEntities.stream()
                .map(WarehouseStorageCenterEntityConverter::warehouseFenceEntity2DTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<WarehouseStorageDTO> queryWarehouseSkuFence(List<WarehouseStorageFenceQuery> warehouseStorageFenceQueries) {
        //仓库编号
        List<Integer> warehouseNos = warehouseStorageFenceQueries.stream().map(WarehouseStorageFenceQuery::getWarehouseNos).flatMap(Collection::stream).collect(Collectors.toList());
        List<WarehouseStorageEntity> warehouseStorageEntityList = warehouseStorageCenterRepository.queryList(WarehouseStorageQuery.builder().warehouseNos(warehouseNos).build());
        Map<Integer, WarehouseStorageEntity> warehouseNoStorageMap = warehouseStorageEntityList.stream().collect(Collectors.toMap(WarehouseStorageEntity::getWarehouseNo, Function.identity()));

        List<WarehouseStorageEntity> warehouseStorageEntities = new ArrayList<>();
        for (WarehouseStorageFenceQuery warehouseStorageFenceQuery : warehouseStorageFenceQueries) {
            //过滤出鲜沐仓
            List<Integer> summerFarmWarehouseNos = warehouseStorageFenceQuery.getWarehouseNos().stream().filter(warehouseNo -> warehouseNoStorageMap.get(warehouseNo) != null && Objects.equals(warehouseNoStorageMap.get(warehouseNo).getTenantId().intValue(), WarehouseSourceEnum.SUMMERFARM_WAREHOUSE.getCode())).collect(Collectors.toList());
            //过滤出自营仓
            List<Integer> saasWarehouseNos = warehouseStorageFenceQuery.getWarehouseNos().stream().filter(warehouseNo -> warehouseNoStorageMap.get(warehouseNo) != null && !Objects.equals(warehouseNoStorageMap.get(warehouseNo).getTenantId().intValue(), WarehouseSourceEnum.SUMMERFARM_WAREHOUSE.getCode())).collect(Collectors.toList());

            if(!CollectionUtils.isEmpty(summerFarmWarehouseNos)){
                //查询sku代仓仓库围栏信息
                List<WarehouseStorageEntity> warehouseStorageProxyEntities = warehouseStorageCenterDomainService.querySkuProxyFenceList(WarehouseStorageQuery.builder().sku(warehouseStorageFenceQuery.getSku()).warehouseNos(summerFarmWarehouseNos).build());
                warehouseStorageEntities.addAll(warehouseStorageProxyEntities);
            }
            if(!CollectionUtils.isEmpty(saasWarehouseNos)){
                //查询自营仓仓库信息
                List<WarehouseStorageEntity> warehouseStorageSelfEntities = warehouseStorageCenterDomainService.querySkuSelfFenceList(WarehouseStorageQuery.builder().sku(warehouseStorageFenceQuery.getSku()).warehouseNos(saasWarehouseNos).build());
                warehouseStorageEntities.addAll(warehouseStorageSelfEntities);
            }
        }

        return warehouseStorageEntities.stream().map(WarehouseStorageCenterEntityConverter::entity2DTO).collect(Collectors.toList());
    }

}
