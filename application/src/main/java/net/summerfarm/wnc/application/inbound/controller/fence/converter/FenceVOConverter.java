package net.summerfarm.wnc.application.inbound.controller.fence.converter;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.api.fence.dto.FenceDTO;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.FenceChannelBusinessWhiteConfigVO;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.FenceVO;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.ScopeChannelBusinessVO;
import net.summerfarm.wnc.domain.fence.entity.FenceChannelBusinessWhiteConfigEntity;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Description:围栏视图转换器
 * date: 2023/10/27 10:19
 *
 * <AUTHOR>
 */
public class FenceVOConverter {

    public static FenceVO dto2Vo(FenceDTO fenceDTO){
        if (fenceDTO == null){
            return null;
        }
        FenceVO fenceVO = new FenceVO();
        fenceVO.setFenceId(fenceDTO.getFenceId());
        fenceVO.setFenceName(fenceDTO.getFenceName());
        fenceVO.setStoreNo(fenceDTO.getStoreNo());
        fenceVO.setAreaNo(fenceDTO.getAreaNo());
        fenceVO.setAreaName(fenceDTO.getAreaName());
        fenceVO.setStoreName(fenceDTO.getStoreName());
        fenceVO.setCityName(fenceDTO.getCityName());
        fenceVO.setType(fenceDTO.getType());
        fenceVO.setTypeDesc(fenceDTO.getTypeDesc());
        fenceVO.setStatus(fenceDTO.getStatus());
        fenceVO.setStatusDesc(fenceDTO.getStatusDesc());
        fenceVO.setUpdateTime(fenceDTO.getUpdateTime());
        fenceVO.setUpdater(fenceDTO.getUpdater());
        fenceVO.setChangeStatus(fenceDTO.getChangeStatus());
        fenceVO.setFenceDeliveryDTO(fenceDTO.getFenceDeliveryDTO());
        fenceVO.setFenceAreaDTOList(fenceDTO.getFenceAreaDTOList());
        fenceVO.setOrderChannelType(fenceDTO.getOrderChannelType());
        return fenceVO;
    }

    public static PageInfo<FenceVO> dtoPage2VoPage(PageInfo<FenceDTO> dtoPageInfo) {
        List<FenceDTO> fenceDTOS = dtoPageInfo.getList();
        List<FenceVO> fenceVOS = fenceDTOS.stream().map(FenceVOConverter::dto2Vo).collect(Collectors.toList());

        PageInfo<FenceVO> voPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(dtoPageInfo, voPageInfo);
        voPageInfo.setList(fenceVOS);
        return voPageInfo;
    }

    public static List<FenceChannelBusinessWhiteConfigVO> entityList2VOList(List<FenceChannelBusinessWhiteConfigEntity> configEntities){
        if(CollectionUtils.isEmpty(configEntities)){
            return Collections.emptyList();
        }

        List<FenceChannelBusinessWhiteConfigVO> whiteConfigVOS = new ArrayList<>();

        Map<String, List<FenceChannelBusinessWhiteConfigEntity>> channelTypeMap = configEntities.stream().collect(Collectors.groupingBy(FenceChannelBusinessWhiteConfigEntity::getOrderChannelType));
        channelTypeMap.forEach((channelType, entityList) -> {
            FenceChannelBusinessWhiteConfigVO vo = new FenceChannelBusinessWhiteConfigVO();
            vo.setFenceId(entityList.get(0).getFenceId());
            vo.setOrderChannelType(channelType);

            List<ScopeChannelBusinessVO> scopeChannelBusinesses = new ArrayList<>();
            entityList.forEach(entity -> {
                ScopeChannelBusinessVO scopeChannelBusinessVO = new ScopeChannelBusinessVO();
                scopeChannelBusinessVO.setScopeChannelBusinessId(entity.getScopeChannelBusinessId());
                scopeChannelBusinessVO.setScopeChannelBusinessName(entity.getScopeChannelBusinessName());
                scopeChannelBusinessVO.setTenantId(entity.getTenantId());

                scopeChannelBusinesses.add(scopeChannelBusinessVO);
            });
            vo.setScopeChannelBusinesses(scopeChannelBusinesses);

            whiteConfigVOS.add(vo);
        });

        return whiteConfigVOS;
    }
}
