package net.summerfarm.wnc.application.inbound.controller.config;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.application.inbound.controller.config.assembler.WncTenantStoreSkuBlackConfigAssembler;
import net.summerfarm.wnc.application.inbound.controller.config.input.command.WncTenantStoreSkuBlackConfigDelCommandInput;
import net.summerfarm.wnc.application.inbound.controller.config.input.query.WncTenantStoreSkuBlackConfigDetailQueryInput;
import net.summerfarm.wnc.domain.config.entity.TenantStoreSkuBlackConfigEntity;
import net.summerfarm.wnc.application.inbound.controller.config.input.command.WncTenantStoreSkuBlackConfigCommandInput;
import net.summerfarm.wnc.application.inbound.controller.config.vo.WncTenantStoreSkuBlackConfigVO;
import net.summerfarm.wnc.application.inbound.controller.config.input.query.WncTenantStoreSkuBlackConfigQueryInput;
import net.summerfarm.wnc.common.converter.PageInfoConverter;
import net.summerfarm.wnc.application.service.config.TenantStoreSkuBlackConfigCommandService;
import net.summerfarm.wnc.application.service.config.TenantStoreSkuBlackConfigQueryService;
import net.xianmu.common.result.CommonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.PathVariable;


/**
 * @Title 租户城配仓SKU黑名单配置
 * @Description 租户城配仓SKU黑名单配置功能模块
 * <AUTHOR>
 * @date 2024-09-12 14:00:16
 * @version 1.0
 */
@RestController
@RequestMapping(value="/wncTenantStoreSkuBlackConfig")
public class WncTenantStoreSkuBlackConfigController{

	@Autowired
	private TenantStoreSkuBlackConfigCommandService wncTenantStoreSkuBlackConfigCommandService;
	@Autowired
	private TenantStoreSkuBlackConfigQueryService wncTenantStoreSkuBlackConfigQueryService;


	/**
	 * 租户城配仓SKU黑名单配置列表
	 * @return WncTenantStoreSkuBlackConfigVO
	 */
	@PostMapping(value="/query/page")
	public CommonResult<PageInfo<WncTenantStoreSkuBlackConfigVO>> getPage(@RequestBody WncTenantStoreSkuBlackConfigQueryInput input){
		PageInfo<TenantStoreSkuBlackConfigEntity> page = wncTenantStoreSkuBlackConfigQueryService.getPage(input);
		return CommonResult.ok(PageInfoConverter.toPageResp(page, WncTenantStoreSkuBlackConfigAssembler::toWncTenantStoreSkuBlackConfigVO));
	}

	/**
	* 获取详情
	* @return WncTenantStoreSkuBlackConfigVO
	*/
	@PostMapping(value = "/query/detail")
	public CommonResult<WncTenantStoreSkuBlackConfigVO> detail(@RequestBody WncTenantStoreSkuBlackConfigDetailQueryInput input){
		return CommonResult.ok(WncTenantStoreSkuBlackConfigAssembler.toWncTenantStoreSkuBlackConfigVO(wncTenantStoreSkuBlackConfigQueryService.getDetail(input.getId())));
	}


	/**
	 * 新增
	 * @return WncTenantStoreSkuBlackConfigVO
	 */
	@PostMapping(value = "/upsert/insert")
	public CommonResult<WncTenantStoreSkuBlackConfigVO> insert(@RequestBody WncTenantStoreSkuBlackConfigCommandInput input) {
		return CommonResult.ok(WncTenantStoreSkuBlackConfigAssembler.toWncTenantStoreSkuBlackConfigVO(wncTenantStoreSkuBlackConfigCommandService.insert(input)));
	}

	/**
	 * 修改
	 * @return
	 */
	@PostMapping(value = "/upsert/update")
	public CommonResult<Integer> update(@RequestBody WncTenantStoreSkuBlackConfigCommandInput input){
		return CommonResult.ok(wncTenantStoreSkuBlackConfigCommandService.update(input));
	}

	/**
	* 删除
	* @return
	*/
	@PostMapping(value = "/upsert/delete")
	public CommonResult<Integer> delete(@RequestBody WncTenantStoreSkuBlackConfigDelCommandInput input){
		return CommonResult.ok(wncTenantStoreSkuBlackConfigCommandService.delete(input.getId()));
	}


}

