package net.summerfarm.wnc.api.path.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2023/11/28 14:32<br/>
 *
 * <AUTHOR> />
 */
@Data
public class SkuPathMappingDTO {

    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 创建人名称
     */
    private String creator;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 上级支援仓
     */
    private Integer supWarehouseNo;

    /**
     * 路线ID
     */
    private Long pathId;
}
