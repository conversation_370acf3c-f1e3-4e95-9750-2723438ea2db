package net.summerfarm.wnc.api.warehouse.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Description: 城配点位新
 * date: 2023/10/7 18:28<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StoreSiteDTO {

    /**
     * 是否需要打卡0 不需要 1需要
     */
    private Integer punchState;

    /**
     * 打卡距离
     */
    private BigDecimal punchDistance;

    /**
     * 出仓时间 hh:mm:ss
     */
    private String outTime;

    /**
     * 0智能排线 1手动排线
     */
    private Integer intelligencePath;
}
