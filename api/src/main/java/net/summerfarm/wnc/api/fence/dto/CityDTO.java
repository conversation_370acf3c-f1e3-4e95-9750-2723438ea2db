package net.summerfarm.wnc.api.fence.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Description:围栏归属市视图对象
 * date: 2023/10/27 17:23
 *
 * <AUTHOR>
 */
@Data
public class CityDTO implements Serializable {

    private static final long serialVersionUID = 2382761200979052646L;

    /**
     * 行政市名称
     */
    private String cityName;

    /**
     * 围栏归属行政区集合
     */
    private List<Map<String,String>> areaList;
}
