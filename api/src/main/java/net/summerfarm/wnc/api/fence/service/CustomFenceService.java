package net.summerfarm.wnc.api.fence.service;

import net.summerfarm.wnc.api.fence.input.ContactPoiDistanceCommand;

import java.util.List;

/**
 * Description: 自定义电子围栏服务<br/>
 * date: 2023/12/6 10:29<br/>
 *
 * <AUTHOR> />
 */
public interface CustomFenceService {
    /**
     * 初始化自定义电子围栏
     */
    void initCustomFence();

    /**
     * 新增索引
     */
    void createCustomFenceAreaIndex();

    /**
     * 查询POI对应的电子围栏
     * @param poi
     * @return
     */
    String queryPoiFence(String poi);

    /**
     * 区域信息初始化到ES
     * @param code 高德区域信息
     * @param province 省
     * @param city 市
     * @param area 区
     */
    void adCodeInfo2Es(String code, String province, String city, String area);

    /**
     * 批量比较POI
     * @param beginNum 开始条数
     * @param endNum 结束条数
     */
    void compareContactPoi(Integer beginNum, Integer endNum);

    /**
     * 查询有效的客户的总条数
     * @return 结果
     */
    Long queryContactTotal();

    /**
     * 高德区域查询电子围栏
     * @param adCode 高德区域信息
     * @param province 省
     * @param city 市
     * @param area 区
     */
    void adCodeFenceInit(String adCode, String province, String city, String area);

    /**
     * 计算客户的POI和高德POI
     * @param contactPoiDistanceCommand 条件
     */
    void contactPoiDistance(ContactPoiDistanceCommand contactPoiDistanceCommand);

    /**
     * 清空历史数据
     */
    void deleteContactPoiDistance();
}
