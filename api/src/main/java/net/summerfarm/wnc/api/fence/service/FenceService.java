package net.summerfarm.wnc.api.fence.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.api.fence.dto.FenceAreaFilterDTO;
import net.summerfarm.wnc.api.fence.dto.FenceCityDTO;
import net.summerfarm.wnc.api.fence.dto.FenceDTO;
import net.summerfarm.wnc.api.fence.input.FenceEditCommand;
import net.summerfarm.wnc.api.fence.input.FenceSaveCommand;
import net.summerfarm.wnc.api.fence.input.FenceStatusCommand;
import net.summerfarm.wnc.common.query.fence.FenceCityQuery;
import net.summerfarm.wnc.common.query.fence.FenceIdQuery;
import net.summerfarm.wnc.common.query.fence.FencePageQuery;
import net.summerfarm.wnc.common.query.fence.FenceQuery;

import java.util.ArrayList;
import java.util.List;

/**
 * Description:围栏接口
 * date: 2023/10/26 18:53
 *
 * <AUTHOR>
 */
public interface FenceService {

    /**
     * 分页查询围栏列表
     * @param fencePageQuery 查询
     * @return 结果
     */
    PageInfo<FenceDTO> queryPage(FencePageQuery fencePageQuery);

    /**
     * 查询围栏详情
     * @param fenceIdQuery 查询
     * @return 结果
     */
    FenceDTO queryDetail(FenceIdQuery fenceIdQuery);

    /**
     * 新增围栏
     * @param fenceSaveCommand 命令
     */
    void saveFence(FenceSaveCommand fenceSaveCommand);

    /**
     * 编辑围栏
     * @param fenceEditCommand 命令
     */
    void editFence(FenceEditCommand fenceEditCommand);

    /**
     * 更新围栏状态
     * @param fenceStatusCommand 命令
     */
    void changeFenceStatus(FenceStatusCommand fenceStatusCommand);

    /**
     * 查询有效围栏信息
     * @return 结果
     */
    List<FenceAreaFilterDTO> queryValidList();

    /**
     * 查询城市区域信息
     * @param fenceCityQuery 查询
     * @return 结果
     */
    List<FenceCityDTO> queryCityAreaList(FenceCityQuery fenceCityQuery);

    /**
     * 查询围栏列表
     * @return 结果
     */
    List<FenceDTO> queryList();

    /**
     * 查询围栏根据状态
     * @param statusList 状态
     * @return 结果
     */
    List<FenceDTO> queryListByStatus(List<Integer> statusList);

    /**
     * 根据城配仓编号查询有效的运营区域编号
     * @param storeNos 城配仓编号
     * @return 运营区域编号
     */
    List<Integer> queryValidAreaListByStoreNos(List<Integer> storeNos);
}
