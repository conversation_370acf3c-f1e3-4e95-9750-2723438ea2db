package net.summerfarm.wnc.api.fence.input;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/4/15 14:41<br/>
 *
 * <AUTHOR> />
 */
@Data
public class FenceDeliveryRuleUpdateCommand {
    /**
     * primary key
     */
    @NotNull(message = "id不能为空")
    private Long id;
    /**
     * 配送规则 0 距离最短 1手动设置优先级
     */
    @NotNull(message = "deliveryRule不能为空")
    private Integer deliveryRule;

    /**
     * 仓库优先级JSON规则
     */
    @NotEmpty(message = "conflictWarehouseJsonVOList不能为空")
    private List<ConflictWarehouseJsonUpdateCommand> conflictWarehouseJsonVOList;

    /**
     * 操作人名称 不需要传
     */
    private String operatorName;
}
