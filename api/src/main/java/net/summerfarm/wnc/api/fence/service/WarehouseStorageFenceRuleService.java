package net.summerfarm.wnc.api.fence.service;

import net.summerfarm.wnc.api.fence.dto.WarehouseStorageFenceRuleDTO;
import net.summerfarm.wnc.client.req.WarehouseStorageFenceQueryReq;
import net.summerfarm.wnc.common.query.fence.WarehouseStorageFenceRuleQuery;

import java.util.List;

/**
 * Description: <br/>
 * date: 2023/8/24 9:56<br/>
 *
 * <AUTHOR> />
 */
public interface WarehouseStorageFenceRuleService {

    /**
     * 获取类型
     * @return 类型
     */
    Integer getType();

    /**
     * 查询仓库围栏配送规则
     * @param warehouseStorageFenceRuleQuery 查询
     * @return 结果
     */
    List<WarehouseStorageFenceRuleDTO> queryWarehouseStorageFence(WarehouseStorageFenceRuleQuery warehouseStorageFenceRuleQuery);
}
