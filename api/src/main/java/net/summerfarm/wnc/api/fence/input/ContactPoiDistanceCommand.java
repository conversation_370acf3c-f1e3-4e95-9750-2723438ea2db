package net.summerfarm.wnc.api.fence.input;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/12/15 14:59<br/>
 *
 * <AUTHOR> />
 */
@Data
public class ContactPoiDistanceCommand {

    /**
     * 联系人ID集合
     */
    private List<Long> contactIds;

    /**
     * 开始查询时间
     */
    private LocalDate beginDay;

    /**
     * 结束查询时间
     */
    private LocalDate endDay;
}
