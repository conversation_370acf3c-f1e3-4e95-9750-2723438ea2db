package net.summerfarm.wnc.api.fence.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Description:围栏数据转换对象
 * date: 2023/6/8 11:37
 *
 * <AUTHOR>
 */
@Data
public class FenceDTO {

    /**
     * 围栏ID
     */
    private Integer fenceId;

    /**
     * 围栏名称
     */
    private String fenceName;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 运营服务区编号
     */
    private Integer areaNo;

    /**
     * 运营区域名称
     */
    private String areaName;

    /**
     * 城配仓名称
     */
    private String storeName;

    /**
     * 行政城市名称
     */
    private String cityName;

    /**
     * 类型，0：新建围栏，1：围栏拆分
     */
    private Integer type;

    /**
     * 类型描述
     */
    private String typeDesc;

    /**
     * 状态，0：正常，1：失效，3：暂停
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 是否在切仓中，true：是，false:否
     */
    private Boolean changeStatus;

    /**
     * 打包ID
     */
    private Integer packId;

    /**
     * 围栏区域信息
     */
    private List<FenceAreaDTO> fenceAreaDTOList;

    /**
     * 配送周期信息
     */
    private FenceDeliveryDTO fenceDeliveryDTO;

    /**
     * 下单渠道类型 1000鲜沐平台客户 2000鲜沐大客户 3000 Saas客户
     */
    private String orderChannelType;

    /**
     * 指定城配仓标识 true 指定城配仓 false 非指定城配仓
     */
    private Boolean isStoreNoAppointment;

    /**
     * 履约方式，0：城配履约，1：快递履约
     */
    private Integer fulfillmentMethod;
}
