package net.summerfarm.wnc.api.fence.dto;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Description: 商品配送信息<br/>
 * date: 2023/9/27 14:53<br/>
 *
 * <AUTHOR> />
 */
@Data
public class SkuDeliveryDateDTO {

    /**
     * sku
     */
    private String sku;

    /**
     * 配送日期
     */
    private LocalDate deliveryDate;

    /** 二级类型：1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-经销 **/
    private Integer subType;

    /**
     * 配送截单日期时间
     */
    private LocalDateTime deliveryCloseTime;
}
