package net.summerfarm.wnc.api.path.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.api.path.dto.PathDTO;
import net.summerfarm.wnc.api.path.dto.SkuPathMappingDTO;
import net.summerfarm.wnc.common.query.path.PathQuery;

import java.util.List;

/**
 * Description: 路线、sku信息服务<br/>
 * date: 2023/11/29 10:18<br/>
 *
 * <AUTHOR> />
 */
public interface PathService {

    /**
     * 查询路线配置
     * @param pathQuery 查询
     * @return 结果
     */
    List<PathDTO> queryPathConfigList(PathQuery pathQuery);

    /**
     * 查询路线配置详情
     * @param pathQuery 查询
     * @return 结果
     */
    PathDTO queryPathConfigDetail(PathQuery pathQuery);

    /**
     * 保存或者更新配置
     * @param pathDTO 路线信息
     */
    void saveOrUpdateConfig(PathDTO pathDTO);

    /**
     * sku路线批量保存更新
     * @param skuPathMappingDTOList sku路线信息
     */
    void skuPathBatchSaveOrUpdate(List<SkuPathMappingDTO> skuPathMappingDTOList);

    /**
     * 查询sku路线信息
     * @param queries 查询
     * @return 结果
     */
    List<PathDTO> querySkuPathMappingList(List<PathQuery> queries);

    /**
     * 更新路线下一次路线时间
     */
    void updatePathLastTime();

    /**
     * 初始化数据
     */
    void initData();

    /**
     * 查询调拨预期下一次路线
     * @param query 查询
     * @return 结果
     */
    List<PathDTO> queryExpectLastTimeReallocatePath(PathQuery query);

    /**
     * 删除路线配置
     * @param id 路线信息
     */
    void pathDelete(Long id);

    /**
     * 分页查询调拨路线信息
     * @param query 查询
     * @return 分页结果
     */
    PageInfo<PathDTO> queryPagePathConfig(PathQuery query);

    /**
     * 根据路线ID查询详情
     * @param pathId 查询
     * @return 结果
     */
    PathDTO queryPathConfigDetailById(Long pathId);

    /**
     * 根据sku查询路线信息
     * @param queries 查询
     * @return 结果
     */
    List<SkuPathMappingDTO> queryBelowSkuPathMappingList(List<PathQuery> queries);

    /**
     * 批量删除sku仓库映射配置
     * @param dtos 删除信息
     */
    void skuPathMappingBatchDelete(List<SkuPathMappingDTO> dtos);
}
