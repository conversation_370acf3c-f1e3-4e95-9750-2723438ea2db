package net.summerfarm.wnc.api.fence.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Description:围栏归属省视图对象
 * date: 2023/10/27 17:21
 *
 * <AUTHOR>
 */
@Data
public class ProvinceDTO implements Serializable {

    private static final long serialVersionUID = -4354583531366020982L;

    /**
     * 行政省名称
     */
    private String provinceName;

    /**
     * 围栏归属行政市集合
     */
    private List<CityDTO> cityDTOList;
}
