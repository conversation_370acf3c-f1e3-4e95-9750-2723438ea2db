package net.summerfarm.wnc.api.tool.service;

import net.summerfarm.wnc.api.tool.dto.FenceRelateVO;
import net.summerfarm.wnc.api.tool.dto.StoreAddressVO;
import net.summerfarm.wnc.common.query.fence.SkuAddressQuery;
import net.summerfarm.wnc.common.query.fence.StoreQuery;

import java.util.List;

/**
 * Description:工具箱接口
 * date: 2023/12/8 15:21
 *
 * <AUTHOR>
 */
public interface ToolService {

    /**
     * 查询地址列表
     * @param storeQuery 查询
     * @return 结果
     */
    List<StoreAddressVO> queryAddressList(StoreQuery storeQuery);

    /**
     * 查询围栏关联信息
     * @param skuAddressQuery 查询
     * @return 结果
     */
    FenceRelateVO queryFenceRelateInfo(SkuAddressQuery skuAddressQuery);
}
