package net.summerfarm.wnc.api.fence.input;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * Description:围栏保存命令
 * date: 2023/10/27 10:47
 *
 * <AUTHOR>
 */
@Data
public class FenceSaveCommand implements Serializable {

    private static final long serialVersionUID = -2639877417648103691L;

    /**
     * 围栏名称
     */
    @NotBlank(message = "围栏名称不能为空")
    private String fenceName;

    /**
     * 城配仓编号
     */
    @NotNull(message = "城配仓编号不能为空")
    private Integer storeNo;

    /**
     * 运营服务区编号
     */
    @NotNull(message = "运营服务区编号不能为空")
    private Integer areaNo;

    /**
     * 类型，0：新建围栏，1：围栏拆分
     */
    @NotNull(message = "类型不能为空")
    private Integer type;

    /**
     * 配送周期相关
     */
    @Valid
    @NotNull(message = "围栏配送周期不能为空")
    private FenceDeliveryCommand fenceDeliveryCommand;

    /**
     * 配送区域相关
     */
    @Valid
    private List<FenceAreaCommand> fenceAreaCommandList;

    /**
     * 下单渠道类型 逗号分隔  1000鲜沐平台客户 2000鲜沐大客户 3000 Saas客户
     */
    @NotBlank(message = "下单渠道类型不能为空")
    private String orderChannelType;

    /**
     * 渠道白名单配置
     */
    @NotEmpty(message = "渠道白名单配置不能为空")
    @Valid
    private List<FenceChannelBusinessWhiteConfigCommandInput> fenceChannelBusinessWhiteConfigCommandInputList;
}
