package net.summerfarm.wnc.api.fence.dto;

import lombok.Builder;
import lombok.Data;
import net.summerfarm.wnc.client.enums.SourceEnum;

import java.io.Serializable;

/**
 * 查询围栏截单时间请求参数
 *
 * <AUTHOR>
 * @Date 2023-07-21
 **/
@Data
@Builder
public class DeliveryFenceCloseTimeQuery implements Serializable {
	private static final long serialVersionUID = -4115147790052658789L;

	/**
	 * 城市
	 */
	private String city;

	/**
	 * 区域
	 */
	private String area;

	/**
	 * 订单来源不能为空
	 */
	private SourceEnum source;

	/**
	 * 联系人ID
	 */
	private Long contactId;

	/**
	 * 租户Id
	 */
	private Long tenantId;

	/**
	 * sku
	 */
	private String sku;
}
