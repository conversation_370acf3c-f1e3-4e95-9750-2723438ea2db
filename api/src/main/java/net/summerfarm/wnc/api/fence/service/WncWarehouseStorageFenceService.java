package net.summerfarm.wnc.api.fence.service;

import net.summerfarm.wnc.api.warehouse.dto.WarehouseStorageDTO;
import net.summerfarm.wnc.api.warehouse.dto.WncWarehouseStorageFenceDTO;
import net.summerfarm.wnc.common.query.warehouse.WarehouseStorageFenceQuery;

import java.util.List;

/**
 * Description: <br/>
 * date: 2023/4/14 11:41<br/>
 *
 * <AUTHOR> />
 */
public interface WncWarehouseStorageFenceService {
    /**
     * 查询租户是否存在冲突区域
     * @param warehouseStorageFenceQuery 查询
     * @return 结果
     */
    List<WncWarehouseStorageFenceDTO> queryFenceConflict(WarehouseStorageFenceQuery warehouseStorageFenceQuery);

    /**
     * 根据sku和仓库编号集合查询 围栏信息
     * @param warehouseStorageFenceQuerys 查询
     * @return 结果
     */
    List<WarehouseStorageDTO> queryWarehouseSkuFence(List<WarehouseStorageFenceQuery> warehouseStorageFenceQuerys);
}
