package net.summerfarm.wnc.api.tool.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * Description:围栏关联视图对象
 * date: 2023/12/7 18:57
 *
 * <AUTHOR>
 */
@Data
public class FenceRelateVO implements Serializable {

    private static final long serialVersionUID = -8097491700991372198L;

    /**
     * 围栏名称
     */
    private String fenceName;

    /**
     * 运营大区名称
     */
    private String largeAreaName;

    /**
     * 运营区域名称
     */
    private String areaName;

    /**
     * 库存仓名称
     */
    private String warehouseName;

    /**
     * 城配仓名称
     */
    private String storeName;
}
