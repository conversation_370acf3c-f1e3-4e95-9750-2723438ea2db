package net.summerfarm.wnc.api.fence.dto;

import lombok.Data;
import java.io.Serializable;

/**
 * Description:位置数据转换实体
 * date: 2023/11/14 18:37
 *
 * <AUTHOR>
 */
@Data
public class LocationDTO implements Serializable {

    private static final long serialVersionUID = 5045586236450974690L;

    /**
     * 完整文本地址
     */
    private String completeAddress;

    /**
     * poi
     */
    private String poi;

    /**
     * 高德地理编码
     */
    private GeoDTO geoDTO;


}
