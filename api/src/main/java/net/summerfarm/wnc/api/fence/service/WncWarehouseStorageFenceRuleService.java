package net.summerfarm.wnc.api.fence.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.api.fence.dto.WncTenantGlobalFenceRuleDTO;
import net.summerfarm.wnc.api.fence.dto.WncWarehouseStorageFenceRuleDTO;
import net.summerfarm.wnc.api.fence.input.FenceDeliveryRuleUpdateCommand;
import net.summerfarm.wnc.api.fence.input.GlobalFenceRuleUpdateCommand;
import net.summerfarm.wnc.common.query.fence.WncWarehouseStorageFenceRuleQuery;

import java.util.List;

/**
 * Description: <br/>
 * date: 2023/4/6 18:07<br/>
 *
 * <AUTHOR> />
 */
public interface WncWarehouseStorageFenceRuleService extends WarehouseStorageFenceRuleService{
    /**
     * 查询配送规则相关信息
     * @param wncWarehouseStorageFenceRuleQuery 查询
     * @return 结果
     */
    List<WncWarehouseStorageFenceRuleDTO> queryWarehouseStorageFence(WncWarehouseStorageFenceRuleQuery wncWarehouseStorageFenceRuleQuery);

    /**
     * 分页查询配多仓覆盖运营服务区配送规则
     * @param wncWarehouseStorageFenceRuleQuery 查询
     * @return 结果
     */
    PageInfo<WncWarehouseStorageFenceRuleDTO> queryFenceDeliveryRule(WncWarehouseStorageFenceRuleQuery wncWarehouseStorageFenceRuleQuery);

    /**
     * 更新
     * @param fenceDeliveryRuleUpdateCommand 更新
     */
    void updateFenceDeliveryRule(FenceDeliveryRuleUpdateCommand fenceDeliveryRuleUpdateCommand);

    /**
     * 查询自营仓全局配置
     * @param tenantId 租户ID
     * @return 结果
     */
    WncTenantGlobalFenceRuleDTO queryGlobalFenceRule(Long tenantId);

    /**
     * 修改租户全局优先级
     * @param globalFenceRuleUpdateCommand 修改
     */
    void updateGlobalFenceRule(GlobalFenceRuleUpdateCommand globalFenceRuleUpdateCommand);
}
