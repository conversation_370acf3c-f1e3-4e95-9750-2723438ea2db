package net.summerfarm.wnc.api.config.service;

import net.summerfarm.wnc.api.config.dto.ContactConfigUpdateResultDTO;
import net.summerfarm.wnc.api.config.input.ContactConfigCreateCommandInput;
import net.summerfarm.wnc.api.config.input.ContactConfigRemoveCommandInput;
import net.summerfarm.wnc.api.config.input.ContactConfigUpdateCommandInput;

import java.util.List;

/**
 * Description:联系人配置操作服务
 * date: 2024/1/4 14:26
 *
 * <AUTHOR>
 */
public interface ContactConfigCommandService {

    /**
     * 删除联系人配置
     * @param contactConfigRemoveCommandInput 删除操作
     */
    void removeConfig(ContactConfigRemoveCommandInput contactConfigRemoveCommandInput);

    /**
     * 创建联系人配置
     * @param ContactConfigCreateCommandInput 创建操作
     */
    void createConfig(ContactConfigCreateCommandInput ContactConfigCreateCommandInput);


    /**
     * 更新联系人配置
     * @param commands 更新参数集合
     * @return 结果
     */
    List<ContactConfigUpdateResultDTO> updateConfig(List<ContactConfigUpdateCommandInput> commands);

    /**
     * 保存更新联系人配置
     * @param input 创建更新操作
     */
    void saveOrUpdate(ContactConfigCreateCommandInput input);
}
