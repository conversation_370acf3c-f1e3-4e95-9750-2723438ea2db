package net.summerfarm.wnc.api.fence.dto.area;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 区域对象
 *
 * <AUTHOR>
 * @Date 2023-08-15
 **/
@Data
public class AreaDTO implements Serializable {
	private static final long serialVersionUID = 339049455518665283L;

	private Integer id;

	/**
	 * 城市编号
	 */
	private Integer areaNo;

	/**
	 * 城市名称
	 */
	private String areaName;

	/**
	 * 城市负责人
	 */
	private Integer adminId;

	/**
	 * 父城市编号
	 */
	private Integer parentNo;

	/**
	 * 运费
	 */
	private BigDecimal deliveryFee;

	/**
	 * 是否开放，0不开放，1开放
	 */
	private Boolean status;

	/**
	 * 具体地址
	 */
	private String address;

	/**
	 * 详细信息
	 */
	private String info;

	/**
	 * 快递费
	 */
	private BigDecimal expressFee;


	/**
	 * 运费规则
	 */
	private String deliveryRule;

	/**
	 * 会员规则
	 */
	private String memberRule;

	/**
	 * @see net.summerfarm.enums.PayChannelEnum
	 */
	/**
	 * 收款途径
	 */
	private Integer payChannel;

	/**
	 * 企业支付账号id
	 */
	private Integer companyAccountId;

	/**
	 * 截单映射区域
	 */
	private String mapSection;

	/**
	 * 免配送费日
	 */
	private String freeDay;

	private String mailToAddress;

	/**
	 * 是否发送微信通知 0 否 1 是
	 */
	private Integer weChatNotify;

	/**
	 * 通知标题
	 */
	private String notifyTitle;

	/**
	 * 通知内容
	 */
	private String notifyContent;

	/**
	 * 通知备注
	 */
	private String notifyRemarks;

	/**
	 * 同步城市
	 */
	private Integer originAreaNo;

	/**
	 * 开始/下次配送时间
	 */
	private LocalDate nextDeliveryDate;

	/**
	 * 是否正在切换城市
	 */
	private Boolean changeFlag;

	/**
	 * 切换的城市编号(处理完后清空)
	 */
	private Integer changeStoreNo;

	/**
	 * 切换状态：0、默认 1、预约中 2、大客户停服 3、城市停服
	 */
	private Integer changeStatus;

	/**
	 * 开始切仓时间
	 */
	private LocalDateTime changStartTime;

	/**
	 * 排序失效时间
	 */
	private LocalDateTime sortExpireTime;

	/**
	 * 城市的国家行政区域划分
	 */
	private String administrativeArea;

	/**
	 * 是否支持加单 加单时间
	 */
	private Integer supportAddOrder;

	/**
	 * 是否支持加单 加单时间 0 支持 1 不支持
	 */
	private Integer updateSupportAddOrder;

	private Integer largeAreaNo;

	/**
	 * 仓库类型:0本部仓、1外部仓、2合伙人仓
	 */
	private Integer type;

	/**
	 * bd维度-城市等级
	 */
	private String grade;


}
