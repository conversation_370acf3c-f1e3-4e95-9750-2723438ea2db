package net.summerfarm.wnc.api.fence.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Description:围栏城市数据转换对象
 * date: 2023/10/27 14:10
 *
 * <AUTHOR>
 */
@Data
public class FenceCityDTO implements Serializable {

    private static final long serialVersionUID = 3619165072825338793L;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 围栏区域信息
     */
    private List<FenceAreaDTO> fenceAreaDTOList;
}
