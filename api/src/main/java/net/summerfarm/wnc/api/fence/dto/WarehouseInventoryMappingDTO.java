package net.summerfarm.wnc.api.fence.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: 映射关系DTO<br/>
 * date: 2024/5/21 15:51<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseInventoryMappingDTO {

    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 物流中心编号
     */
    private Integer storeNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 物流中心销售冻结
     */
    private Integer saleLockQuantity;

    /**
     * 预留库存使用数量
     */
    private Integer reserveQuantity;

    /**
     * 是否支持预留库存
     */
    private Integer supportReserved;

    /**
     * 更新人
     */
    private Integer updater;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /** 二级类型：1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-经销 **/
    private Integer subType;

    /**
     * 运营服务区域编号集合
     */
    private List<Integer> areaNos;


    /**
     * 运营服务区域编号
     */
    private Integer areaNo;
}
