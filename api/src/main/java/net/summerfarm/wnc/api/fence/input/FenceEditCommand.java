package net.summerfarm.wnc.api.fence.input;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * Description:围栏编辑命令
 * date: 2023/10/27 10:47
 *
 * <AUTHOR>
 */
@Data
public class FenceEditCommand implements Serializable {

    private static final long serialVersionUID = -950262344978808889L;

    /**
     * 围栏ID
     */
    @NotNull(message = "围栏ID不能为空")
    private Integer fenceId;

    /**
     * 配送周期相关
     */
    @Valid
    @NotNull(message = "围栏配送周期不能为空")
    private FenceDeliveryCommand fenceDeliveryCommand;

    /**
     * 配送区域相关
     */
    @Valid
    private List<FenceAreaCommand> fenceAreaCommandList;

    /**
     * 下单渠道类型 逗号分隔 1000鲜沐平台客户 2000鲜沐大客户 3000 Saas客户
     */
    @NotBlank(message = "下单渠道类型不能为空")
    private String orderChannelType;

    /**
     * 渠道白名单配置
     */
    @NotEmpty(message = "渠道白名单配置不能为空")
    @Valid
    private List<FenceChannelBusinessWhiteConfigCommandInput> fenceChannelBusinessWhiteConfigCommandInputList;
}
