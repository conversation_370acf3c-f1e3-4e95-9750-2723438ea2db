package net.summerfarm.wnc.api.config.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Description:联系人配置更新结果DTO
 * date: 2024/1/2 17:02
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ContactConfigUpdateResultDTO implements Serializable {

    private static final long serialVersionUID = 5668656456016974061L;

    /**
     * 联系人Id
     */
    private Long contactId;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 失败信息
     */
    private String failMessage;

    public ContactConfigUpdateResultDTO(Long contactId, Long tenantId) {
        this.contactId = contactId;
        this.tenantId = tenantId;
    }

    public void updateFail(String failMessage){
        this.success = Boolean.FALSE;
        this.failMessage = failMessage;
    }

    public void updateSuccess(){
        this.success = Boolean.TRUE;
    }


}
