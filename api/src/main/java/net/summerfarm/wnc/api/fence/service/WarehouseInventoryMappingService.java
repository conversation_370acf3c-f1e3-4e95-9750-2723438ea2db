package net.summerfarm.wnc.api.fence.service;

import net.summerfarm.wnc.api.fence.dto.WarehouseInventoryMappingDTO;
import net.summerfarm.wnc.common.query.fence.AreaByListWarehouseAndSkuQuery;

import java.util.List;

/**
 * Description: <br/>
 * date: 2023/8/2 11:37<br/>
 *
 * <AUTHOR> />
 */
public interface WarehouseInventoryMappingService {
    /**
     * 查询有效的运营服务区
     * @return  运营服务区编号集合
     */
    List<Integer> queryOpenAreaList();

    /**
     * 处理映射关系缺失问题
     * @param areaNo 运营区域编号
     * @param saasFlag ture标识Saas false 标识鲜沐
     */
    void handleAreaMappingQuestion(Integer areaNo, boolean saasFlag);

    /**
     * 映射关系处理补充
     * @param areaNo 运营区域
     * @param storeNo 城配仓编号
     * @param packId 围栏组Id
     */
    void mappingHandleInit(Integer areaNo, Integer storeNo, Integer packId);

    /**
     * 修改历史错误数据
     * @param areaNo 运营区域编号
     */
    void mappingDataFix(Integer areaNo);
    /**
     * 围栏映射关系-初始化数据
     */
    void mappingDataAllFenceaFix(Integer beginId,Integer endId);

    /**
     * 运营区域sku多仓映射告警
     */
    void areaSkuManyWarehouseWarningJob();

    /**
     * 根据仓库编号和SKU查询运营区域
     * @param warehouseNo 仓库编号
     * @param sku SKU信息
     * @return 城配仓编号集合
     */
    List<Integer> queryStoreNoByWarehouseAndSku(Integer warehouseNo, String sku);

    /**
     * 根据SKU和运营区域查询仓库编号
     * @param sku SKU信息
     * @param areaNo 运营区域编号
     * @return 仓库编号集合
     */
    List<Integer> queryWarehouseBySkuAndAreaNo(String sku, Integer areaNo);

    /**
     * 批量查询运营区域
     * @param query 查询条件
     * @return
     */
    List<WarehouseInventoryMappingDTO> queryAreaByListWarehouseAndSku(List<AreaByListWarehouseAndSkuQuery> query);

    /**
     * 根据运营区域和SKU集合查询批量查询映射关系
     * @param areaNo 运营区域编号
     * @param skuList SKU集合
     * @return 映射关系
     */
    List<WarehouseInventoryMappingDTO> queryBatchWarehouseMappingByAreaAndSkus(Integer areaNo, List<String> skuList);
}
