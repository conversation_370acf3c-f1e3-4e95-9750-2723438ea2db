package net.summerfarm.wnc.api.fence.dto;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Description: 停配
 * date: 2023/10/8 15:21<br/>
 *
 * <AUTHOR> />
 */
@Data
public class StopDeliveryDTO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建时间
     */
    private LocalDateTime updateTime;

    /**
     * 城配仓
     */
    private Integer storeNo;

    /**
     * 停运开始时间
     */
    private LocalDate shutdownStartTime;

    /**
     * 停运结束时间
     */
    private LocalDate shutdownEndTime;

    /**
     * 是否删除
     */
    private Integer deleteFlag;
}
