package net.summerfarm.wnc.api.config.service;

import net.summerfarm.wnc.api.config.dto.ContactConfigDTO;
import net.summerfarm.wnc.common.query.config.ContactConfigQuery;

import java.util.List;

/**
 * Description:联系人配置查询服务
 * date: 2024/1/4 14:27
 *
 * <AUTHOR>
 */
public interface ContactConfigQueryService {

    /**
     * 查询联系人配置详情
     * @param contactConfigQuery 查询
     * @return 结果
     */
    ContactConfigDTO queryDetail(ContactConfigQuery contactConfigQuery);

    /**
     * 查询联系人配置集合
     * @param contactConfigQuery 查询
     * @return 结果
     */
    List<ContactConfigDTO> queryConfigList(ContactConfigQuery contactConfigQuery);

}
