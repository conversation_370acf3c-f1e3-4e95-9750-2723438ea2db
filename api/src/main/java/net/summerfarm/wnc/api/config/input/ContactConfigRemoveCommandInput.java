package net.summerfarm.wnc.api.config.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * Description:联系人配置删除命令实体
 * date: 2023/9/22 17:31
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContactConfigRemoveCommandInput implements Serializable {

    private static final long serialVersionUID = -368988633353667503L;

    /**
     * 租户ID
     */
    @NotNull(message = "租户ID不能为空")
    private Long tenantId;

    /**
     * 外部联系人ID
     */
    @NotBlank(message = "外部联系人ID不能为空")
    private Long outerContactId;

}
