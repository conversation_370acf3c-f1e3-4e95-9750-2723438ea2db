package net.summerfarm.wnc.api.config.dto;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Description:联系人配置数据传输对象
 * date: 2023/9/22 17:35
 *
 * <AUTHOR>
 */
@Data
public class ContactConfigDTO implements Serializable {

    private static final long serialVersionUID = 5584861033705550181L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 来源，0：鲜沐，1：saas
     */
    private Integer source;

    /**
     * 来源描述
     */
    private String sourceDesc;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 外部联系人ID
     */
    private Long outerContactId;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    public String buildUk(){
        return String.format("%s#%s", this.tenantId, this.outerContactId);
    }
}
