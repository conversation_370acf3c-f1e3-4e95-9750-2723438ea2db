package net.summerfarm.wnc.api.config.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * Description:联系人配置创建命令实体
 * date: 2023/9/22 17:31
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContactConfigCreateCommandInput implements Serializable {

    private static final long serialVersionUID = 6158021405015428503L;

    /**
     * 租户ID
     */
    @NotNull(message = "租户ID不能为空")
    private Long tenantId;

    /**
     * 外部联系人ID
     */
    @NotNull(message = "外部联系人ID不能为空")
    private Long outerContactId;

    /**
     * 城配仓编号
     */
    @NotNull(message = "城配仓编号不能为空")
    private Integer storeNo;
}
