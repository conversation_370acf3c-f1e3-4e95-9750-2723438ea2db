package net.summerfarm.wnc.api.fence.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/4/6 18:24<br/>
 *
 * <AUTHOR> />
 */
@Data
public class WncWarehouseStorageFenceRuleDTO {

    /**
     * primary key
     */
    private Long id;

    /**
     * 仓库到客户的距离 km
     */
    private BigDecimal distance;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * sku和城配仓集合
     */
    private List<WncSkuStoreNoDTO> wncSkuStoreNoDTOS;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 配送时间
     */
    private LocalDate deliveryTime;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 配送规则 0 距离最短 1手动设置优先级
     */
    private Integer deliveryRule;

    /**
     * 仓库优先级JSON规则
     */
    private List<ConflictWarehouseJsonDTO> conflictWarehouseJsonDTOList;

    /**
     * 仓库名称合集
     */
    private String warehouseNameList;

    /**
     * 最后操作人名称
     */
    private String lastOperatorName;
    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 城配仓编号
     */
    private List<Integer> storeNoList;
    /**
     * 城配仓截单时间
     */
    private LocalTime closeTime;

    /**
     *是否日配的标识 0日配，1非日配
     */
    private Integer isEveryDayFlag;

    /**
     * 全品类配送时间
     */
    private LocalDate categoryDeliveryTime;

    /**
     * 配送截单时间
     */
    private LocalDateTime deliveryCloseTime;
}
