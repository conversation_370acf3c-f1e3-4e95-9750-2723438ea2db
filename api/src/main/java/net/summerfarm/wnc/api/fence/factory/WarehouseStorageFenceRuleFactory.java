package net.summerfarm.wnc.api.fence.factory;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.fence.service.WarehouseStorageFenceRuleService;
import net.summerfarm.wnc.client.req.WarehouseStorageFenceQueryReq;
import net.summerfarm.wnc.common.enums.WarehouseStorageFenceRuleEnum;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * Description: <br/>
 * date: 2023/8/23 18:42<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Service
public class WarehouseStorageFenceRuleFactory implements ApplicationContextAware {

    public Map<Integer, WarehouseStorageFenceRuleService> warehouseStorageFenceRuleMap = new HashMap<>();

    public WarehouseStorageFenceRuleService getService(WarehouseStorageFenceQueryReq warehouseStorageFenceQueryReq){
        WarehouseStorageFenceRuleService warehouseStorageFenceRuleService = null;

        if(warehouseStorageFenceQueryReq.getStoreNo() == null){
            warehouseStorageFenceRuleService = warehouseStorageFenceRuleMap.get(WarehouseStorageFenceRuleEnum.SAAS_WAREHOUSE.getCode());
        }else{
            warehouseStorageFenceRuleService = warehouseStorageFenceRuleMap.get(WarehouseStorageFenceRuleEnum.SUMMERFARM_WAREHOUSE.getCode());
        }
        if(warehouseStorageFenceRuleService == null){
            log.error("找不到配送围栏规则任务生成器:{}",warehouseStorageFenceQueryReq.getStoreNo());
            throw new BizException("找不到任务生成器");
        }
        return warehouseStorageFenceRuleService;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, WarehouseStorageFenceRuleService> beansOfType = applicationContext.getBeansOfType(WarehouseStorageFenceRuleService.class);
        beansOfType.forEach((key,value)->{
            Integer type = value.getType();
            warehouseStorageFenceRuleMap.put(type,value);
        });
    }
}
