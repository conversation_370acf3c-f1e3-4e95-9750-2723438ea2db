package net.summerfarm.wnc.api.fence.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Description: 配送周期DTO<br/>
 * date: 2023/9/27 11:14<br/>
 *
 * <AUTHOR> />
 */
@Data
public class FenceDeliveryDTO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 围栏id
     */
    private Integer fenceId;

    /**
     * 配送周期
     */
    private String deliveryFrequent;

    /**
     * 首配日
     */
    private LocalDate nextDeliveryDate;

    /**
     * 逻辑删除 0否 1是
     */
    private Integer deleteFlag;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private Integer updater;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 周期方案 1周计算 2间隔计算
     */
    private Integer frequentMethod;

    /**
     * 配送间隔周期
     */
    private Integer deliveryFrequentInterval;

    /**
     * 开始计算日期
     */
    private LocalDate beginCalculateDate;
}
