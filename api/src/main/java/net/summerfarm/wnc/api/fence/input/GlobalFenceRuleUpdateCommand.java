package net.summerfarm.wnc.api.fence.input;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * Description: <br/>
 * date: 2023/4/15 15:40<br/>
 *
 * <AUTHOR> />
 */
@Data
public class GlobalFenceRuleUpdateCommand {

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 全局配送规则 0自营仓优先 1三方仓优先
     */
    @NotNull(message = "globalDeliveryRule不能为空")
    private Integer globalDeliveryRule;
}
