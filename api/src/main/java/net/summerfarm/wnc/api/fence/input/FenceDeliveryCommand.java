package net.summerfarm.wnc.api.fence.input;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * Description:围栏配送周期更新命令
 * date: 2023/10/27 15:12
 *
 * <AUTHOR>
 */
@Data
public class FenceDeliveryCommand implements Serializable {

    private static final long serialVersionUID = 2040026956641049122L;

    /**
     * 首配日
     */
    @NotNull(message = "首配日不能为空")
    private LocalDate nextDeliveryDate;

    /**
     * 周期方案，1：周计算，2：间隔计算
     */
    @NotNull(message = "周期方案不能为空")
    private Integer frequentMethod;

    /**
     * 配送周期
     */
    private String deliveryFrequent;

    /**
     * 开始计算日期
     */
    private LocalDate beginCalculateDate;

    /**
     * 配送间隔周期
     */
    private Integer deliveryFrequentInterval;

}
