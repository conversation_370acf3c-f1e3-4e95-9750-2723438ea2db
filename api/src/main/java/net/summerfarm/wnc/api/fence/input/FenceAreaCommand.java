package net.summerfarm.wnc.api.fence.input;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * Description:围栏区域命令
 * date: 2023/10/27 15:29
 *
 * <AUTHOR>
 */
@Data
public class FenceAreaCommand implements Serializable {

    private static final long serialVersionUID = -2365151953767873310L;

    /**
     * 区域编码
     */
    @NotBlank(message = "区域编码不能为空")
    private String adCode;

    /**
     * 省
     */
    @NotBlank(message = "区域行政省不能为空")
    private String province;

    /**
     * 市
     */
    @NotBlank(message = "区域行政市不能为空")
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 等级
     */
    @NotBlank(message = "区域等级不能为空")
    private String level;
}
