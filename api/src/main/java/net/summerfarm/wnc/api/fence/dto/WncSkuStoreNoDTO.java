package net.summerfarm.wnc.api.fence.dto;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2023/4/10 18:26<br/>
 *
 * <AUTHOR> />
 */
@Data
public class WncSkuStoreNoDTO {
    /**
     * sku
     */
    private String sku;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /** 二级类型：1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-经销 **/
    private Integer subType;
    /**
     * 配送时间
     */
    private LocalDate deliveryTime;

    /**
     * 配送截单时间
     */
    private LocalDateTime deliveryCloseTime;
}
