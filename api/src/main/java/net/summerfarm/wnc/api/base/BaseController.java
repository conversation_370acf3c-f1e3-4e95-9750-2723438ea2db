package net.summerfarm.wnc.api.base;

import net.summerfarm.wnc.api.auth.AuthService;
import net.summerfarm.wnc.common.auth.dto.SaasTokenInfoDTO;
import net.xianmu.authentication.controller.AuthBaseController;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.user.UserBase;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;


/**
 * Description:BaseController
 * date: 2023/4/7 16:36
 *
 * <AUTHOR>
 */
@Controller
public class BaseController extends AuthBaseController {
    @Resource
    private HttpServletRequest request;
    @Resource
    private AuthService authService;

    /**
     * 获取用户业务userName
     *
     * @return 鲜沐体系：realname，
    tms\srm体系：，
    SaaS manage：teanantAccountName
     */
    @Override
    protected String getBizUserName() {
        UserBase userBase = getUserBase();
        if (userBase.getSystemOrigin().equals(SystemOriginEnum.ADMIN.type)) {
            return getUserBase().getNickname();
        }
        if (userBase.getSystemOrigin().equals(SystemOriginEnum.COSFO_MANAGE.type)) {
            String token = request.getHeader("token");
            SaasTokenInfoDTO saasTokenInfoDTO = authService.querySaasTokenInfo(token);
            return saasTokenInfoDTO == null ? "" : saasTokenInfoDTO.getTenantAccountName();
        }
        return getUserBase().getNickname();
    }
}
