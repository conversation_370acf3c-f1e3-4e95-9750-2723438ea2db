package net.summerfarm.wnc.api.fence.dto;

import com.sun.org.apache.xpath.internal.operations.Bool;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Description: 商品配送信息<br/>
 * date: 2023/9/27 14:53<br/>
 *
 * <AUTHOR> />
 */
@Data
public class PopSkuDeliveryDateDTO {

    /**
     * sku
     */
    private String sku;

    /**
     * 配送日期
     */
    private LocalDate deliveryDate;

    /** 是否是鲜沐POP品 true 是，false不是 **/
    private Boolean isXmPopSku;

    /**
     * 配送截单日期时间
     */
    private LocalDateTime deliveryCloseTime;
}
