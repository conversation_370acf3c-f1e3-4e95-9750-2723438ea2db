package net.summerfarm.wnc.api.fence.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * Description: 配送围栏DTO
 * date: 2023/9/27 10:42<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliveryFenceDTO {
    /**
     * 截单时间
     */
    private LocalTime closeTime;

    /**
     *是否日配的标识 0日配，1非日配
     */
    private Integer isEveryDayFlag;

    /**
     * 非全品类配送日期集合
     */
    private List<LocalDate> deliveryDateList;
    /**
     * 配送日期全品类
     */
    private LocalDate fullCategoryDeliveryDay;

    /**
     * 非全品类配送日期
     */
    private LocalDate deliveryTime;

    /**
     * 商品配送信息
     */
    private List<SkuDeliveryDateDTO> skuDeliveryDates;

    /**
     * 配送截单时间
     */
    private LocalDateTime deliveryCloseTime;

    /**
     * 全品类配送截单时间
     */
    private LocalDateTime fullCategoryDeliveryCloseTime;

    /**
     * 履约方式，0：城配履约，1：快递履约
     */
    private Integer fulfillmentMethod;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 允许加单小时时长
     */
    private BigDecimal addOrderHourDuration;

    /**
     * 客户门店首个可配送日期
     */
    private LocalDate firstMerchantDeliveryTime;

    /**
     * 客户门店的下一个可配送日期
     */
    private LocalDate nextMerchantDeliveryTime;

    /**
     * pop sku配送日期信息
     */
    private List<PopSkuDeliveryDateDTO> popSkuDeliveryDates;
}
