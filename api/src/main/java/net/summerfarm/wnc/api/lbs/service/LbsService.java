package net.summerfarm.wnc.api.lbs.service;

import net.summerfarm.wnc.api.fence.dto.LocationDTO;
import net.summerfarm.wnc.common.query.fence.AddressQuery;

import java.util.List;

/**
 * Description:位置服务接口
 * date: 2023/11/15 10:29
 *
 * <AUTHOR>
 */
public interface LbsService {

    /**
     * 根据文本地址查询poi
     * @param addressQuery 查询参数
     * @return 结果
     */
    LocationDTO queryPoi(AddressQuery addressQuery);

    /**
     * 根据文本地址批量查询poi
     * @param addressQueryList 查询参数
     * @return 结果
     */
    List<LocationDTO> batchQueryPoi(List<AddressQuery> addressQueryList);

    /**
     * 根据文本地址批量查询位置相关信息
     * @param addressQueryList 查询参数
     * @return 结果
     */
    List<LocationDTO> batchQueryLocation(List<AddressQuery> addressQueryList);

    /**
     * 柠季POI处理
     */
    void updateNingJiAddressPoi();

    /**
     * 工具类处理
     * @param contactIds 联系人id
     */
    void updateAddressPoi(List<Long> contactIds);
}
