package net.summerfarm.wnc.api.fence.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Description:围栏区域过滤数据转换对象
 * date: 2023/10/27 13:52
 *
 * <AUTHOR>
 */
@Data
public class FenceAreaFilterDTO implements Serializable {

    private static final long serialVersionUID = -7490618708848596606L;

    /**
     * 围栏ID
     */
    private Integer fenceId;

    /**
     * 围栏名称
     */
    private String fenceName;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 围栏归属省集合
     */
    List<ProvinceDTO> provinceDTOList;

}
