package net.summerfarm.wnc.api.changeTask.dto;

import lombok.Data;
import net.summerfarm.wnc.api.fence.dto.FenceAreaDTO;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Description:切仓任务数据转换对象
 * date: 2023/8/24 15:17
 *
 * <AUTHOR>
 */
@Data
public class FenceChangeTaskDTO implements Serializable {

    private static final long serialVersionUID = -2687792212756547938L;

    /**
     * 切仓任务ID
     */
    private Long id;

    /**
     * 围栏名称
     */
    private String fenceName;

    /**
     * 类型，0：切仓，1：切围栏
     */
    private Integer type;

    /**
     * 类型描述
     */
    private String typeDesc;

    /**
     * 状态，0：待处理，1：已取消，2：已完成，10：区域切换中，15：订单切换中，20：处理失败
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 运营服务区编号
     */
    private Integer areaNo;

    /**
     * 操作围栏ID
     */
    private Integer fenceId;

    /**
     * 目标围栏ID/目标城配仓编号
     */
    private Integer targetNo;

    /**
     * 区域集合
     */
    private List<FenceAreaDTO> fenceAreaDTOList;

    /**
     * 切仓说明
     */
    private FenceChangeRemarkDTO fenceChangeRemarkDTO;

    /**
     * 执行时间
     */
    private LocalDateTime exeTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 操作人adminId
     */
    private Integer creatorId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新人
     */
    private String updater;
}
