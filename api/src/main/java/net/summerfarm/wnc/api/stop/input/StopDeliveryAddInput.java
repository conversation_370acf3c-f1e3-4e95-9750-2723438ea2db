package net.summerfarm.wnc.api.stop.input;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * Description: 停配新增
 * date: 2023/10/9 16:40<br/>
 *
 * <AUTHOR> />
 */
@Data
public class StopDeliveryAddInput {
    /**
     * 城配仓集合
     */
    @NotEmpty(message = "storeNos不能为空")
    private List<Integer> storeNos;

    /**
     * 停运开始时间
     */
    @NotNull(message = "shutdownStartTime不能为空")
    private LocalDate shutdownStartTime;

    /**
     * 停运结束时间
     */
    @NotNull(message = "shutdownEndTime不能为空")
    private LocalDate shutdownEndTime;}
