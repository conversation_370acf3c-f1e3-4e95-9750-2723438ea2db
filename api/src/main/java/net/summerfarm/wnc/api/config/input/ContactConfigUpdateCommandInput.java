package net.summerfarm.wnc.api.config.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.wnc.api.fence.input.AddressQueryInput;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * Description:联系人配置更新命令实体
 * date: 2024/1/2 16:54
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContactConfigUpdateCommandInput implements Serializable {

    private static final long serialVersionUID = 3139524167895644269L;

    /**
     * 租户ID
     */
    @NotNull(message = "租户ID不能为空")
    private Long tenantId;

    /**
     * 外部联系人ID
     */
    @NotBlank(message = "外部联系人ID不能为空")
    private Long outerContactId;

    /**
     * 履约方式，0：城配履约，1：快递履约
     */
    @NotNull(message = "履约方式不能为空")
    private Integer fulfillmentMethod;

    /**
     * 联系人地址
     */
    @NotNull(message = "联系人地址不能为空")
    private AddressQueryInput addressQuery;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    public String buildUk(){
        return String.format("%s#%s", this.tenantId, this.outerContactId);
    }
}
