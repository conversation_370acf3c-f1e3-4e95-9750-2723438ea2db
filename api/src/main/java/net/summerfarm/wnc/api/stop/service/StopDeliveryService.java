package net.summerfarm.wnc.api.stop.service;

import net.summerfarm.wnc.api.stop.input.StopDeliveryAddInput;
import net.summerfarm.wnc.api.stop.input.StopDeliveryCancelInput;

/**
 * Description: <br/>
 * date: 2023/10/9 16:34<br/>
 *
 * <AUTHOR> />
 */
public interface StopDeliveryService {
    /**
     * 设置停配
     * @param input 停配实体
     */
    void stopDeliveryAdd(StopDeliveryAddInput input);

    /**
     * 取消停配
     * @param input 取消停配实体
     */
    void stopDeliveryCancel(StopDeliveryCancelInput input);
}
