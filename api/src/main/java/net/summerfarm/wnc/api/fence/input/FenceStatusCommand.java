package net.summerfarm.wnc.api.fence.input;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * Description:围栏状态更新命令
 * date: 2023/10/27 11:10
 *
 * <AUTHOR>
 */
@Data
public class FenceStatusCommand implements Serializable {

    private static final long serialVersionUID = -8513547968742521767L;

    /**
     * 围栏ID
     */
    @NotNull(message = "围栏ID不能为空")
    private Integer fenceId;

    /**
     * 围栏状态，0：正常，3：暂停
     */
    @NotNull(message = "围栏状态不能为空")
    private Integer status;
}
