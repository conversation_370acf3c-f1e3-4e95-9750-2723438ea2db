package net.summerfarm.wnc.domain.fence;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.common.query.fence.WncWarehouseStorageFenceRuleQuery;
import net.summerfarm.wnc.domain.warehouse.entity.WncWarehouseStorageFenceRuleEntity;

import java.util.List;

/**
 * Description: <br/>
 * date: 2023/4/6 17:18<br/>
 *
 * <AUTHOR> />
 */
public interface WncWarehouseStorageFenceRuleRepository {
    /**
     * 根据uk查询信息
     * @param wncWarehouseStorageFenceRuleQuery 查询
     * @return 结果
     */
    WncWarehouseStorageFenceRuleEntity queryByUk(WncWarehouseStorageFenceRuleQuery wncWarehouseStorageFenceRuleQuery);

    /**
     * 查询
     * @param wncWarehouseStorageFenceRuleQuery 查询
     * @return 结果
     */
    List<WncWarehouseStorageFenceRuleEntity> queryList(WncWarehouseStorageFenceRuleQuery wncWarehouseStorageFenceRuleQuery);

    /**
     * 更新
     * @param wncWarehouseStorageFenceRuleEntity 更新
     */
    void update(WncWarehouseStorageFenceRuleEntity wncWarehouseStorageFenceRuleEntity);

    /**
     * 保存
     * @param wncWarehouseStorageFenceRuleEntitySave 保存
     */
    void save(WncWarehouseStorageFenceRuleEntity wncWarehouseStorageFenceRuleEntitySave);

    /**
     * 分页查询规则信息
     * @param wncWarehouseStorageFenceRuleQuery 查询
     * @return 结果
     */
    PageInfo<WncWarehouseStorageFenceRuleEntity> queryPage(WncWarehouseStorageFenceRuleQuery wncWarehouseStorageFenceRuleQuery);

    /**
     * 根据主键查询信息
     * @param id 主键
     * @return 结果
     */
    WncWarehouseStorageFenceRuleEntity queryById(Long id);
    /**
     * 根据id集合删除
     * @param needDeleteIdList id集合
     */
    void removeByIdList(List<Long> needDeleteIdList);

    /**
     * 更新
     * @param needUpdateWarehouseStorageFenceRuleList 更新
     */
    void updateList(List<WncWarehouseStorageFenceRuleEntity> needUpdateWarehouseStorageFenceRuleList);
}
