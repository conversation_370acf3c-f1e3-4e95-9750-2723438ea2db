package net.summerfarm.wnc.domain.fence;

import net.summerfarm.wnc.domain.warehouse.entity.WncTenantGlobalFenceRuleEntity;

/**
 * Description: <br/>
 * date: 2023/4/6 17:26<br/>
 *
 * <AUTHOR> />
 */
public interface WncTenantGlobalFenceRuleRepository {

    /**
     * 根据租户查询全局优先配置规则
     * @param tenantId 租户ID
     * @return 结果
     */
    WncTenantGlobalFenceRuleEntity queryUk(Long tenantId);

    /**
     * 新增
     * @param wncTenantGlobalFenceRuleEntity 新增
     */
    void save(WncTenantGlobalFenceRuleEntity wncTenantGlobalFenceRuleEntity);

    /**
     * 修改
     * @param wncTenantGlobalFenceRuleEntity 修改
     */
    void updateGlobalFenceRule(WncTenantGlobalFenceRuleEntity wncTenantGlobalFenceRuleEntity);
}