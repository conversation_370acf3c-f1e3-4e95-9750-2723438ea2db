package net.summerfarm.wnc.domain.stop;

import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.domain.fence.entity.StopDeliveryEntity;
import net.summerfarm.wnc.domain.warehouse.StopDeliveryRepository;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/10/9 16:44<br/>
 *
 * <AUTHOR> />
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class StopDeliveryDomainService {

    private final StopDeliveryRepository stopDeliveryRepository;

    public void stopDeliveryAdd(List<StopDeliveryEntity> stopDeliveryEntities) {
        //更新城配仓历史停配信息为无效
        stopDeliveryRepository.updateDeleteStatus(stopDeliveryEntities.stream().map(StopDeliveryEntity::getStoreNo).collect(Collectors.toList()));
        //设置停配时间
        stopDeliveryRepository.batchSave(stopDeliveryEntities);
    }

    public void stopDeliveryCancel(Integer storeNo) {
        stopDeliveryRepository.updateDeleteStatus(Collections.singletonList(storeNo));
    }

    /**
     * 获取可配送日期
     * @param stopDeliveryEntitys 停配信息
     * @param deliveryDate 配送日期
     * @return 结果
     */
    public LocalDate findCouldDeliveryDate(List<StopDeliveryEntity> stopDeliveryEntitys, LocalDate deliveryDate) {
        LocalDate lastStopCouldDeliveryDate = null;
        if(this.inStoreStopDeliveryDay(stopDeliveryEntitys,deliveryDate)){
            lastStopCouldDeliveryDate = this.findLastStopCouldDeliveryDate(stopDeliveryEntitys);
        }
        return lastStopCouldDeliveryDate == null ? deliveryDate : lastStopCouldDeliveryDate;
    }

    /**
     * 是否在停配日里面
     * @param stopDeliveryEntityList 停配信息
     * @param deliveryDate 配送日期
     * @return 结果
     */
    public Boolean inStoreStopDeliveryDay(List<StopDeliveryEntity> stopDeliveryEntityList,LocalDate deliveryDate) {
        log.info("停配日期 inStoreStopDeliveryDay:{}", JSON.toJSONString(stopDeliveryEntityList));
        if(CollectionUtils.isEmpty(stopDeliveryEntityList)){
            return false;
        }
        for (StopDeliveryEntity stopDeliveryEntity : stopDeliveryEntityList) {
            LocalDate shutdownStartTime = stopDeliveryEntity.getShutdownStartTime();
            LocalDate shutdownEndTime = stopDeliveryEntity.getShutdownEndTime();

            if(shutdownStartTime == null || shutdownEndTime == null || deliveryDate == null){
                continue;
            }
            //异常数据 过滤掉
            if(shutdownStartTime.compareTo(shutdownEndTime) > 0){
                continue;
            }
            if(deliveryDate.compareTo(shutdownEndTime) <= 0 && deliveryDate.compareTo(shutdownStartTime) >= 0){
                return true;
            }
        }
        return false;
    }

    /**
     * 停配日之后的配送日
     * @param stopDeliveryEntity 停配日
     * @return 结果
     */
    public LocalDate findLastStopCouldDeliveryDate(List<StopDeliveryEntity> stopDeliveryEntitys) {
        if(CollectionUtils.isEmpty(stopDeliveryEntitys)){
            return null;
        }
        //根据起点日期排序
        stopDeliveryEntitys = stopDeliveryEntitys.stream()
                .filter(date -> date.getShutdownStartTime() != null && date.getShutdownEndTime() != null)
                .sorted(Comparator.comparing(StopDeliveryEntity::getShutdownStartTime))
                .collect(Collectors.toList());

        LocalDate shutdownEndTimeRest = stopDeliveryEntitys.get(0).getShutdownEndTime();

        for (StopDeliveryEntity stopDeliveryEntity : stopDeliveryEntitys) {

            LocalDate shutdownStartTime = stopDeliveryEntity.getShutdownStartTime();
            LocalDate shutdownEndTime = stopDeliveryEntity.getShutdownEndTime();

            if(shutdownEndTimeRest.compareTo(shutdownEndTime) <= 0 && shutdownEndTimeRest.compareTo(shutdownStartTime) >= 0){
                shutdownEndTimeRest = stopDeliveryEntity.getShutdownEndTime().plusDays(1);
            }
        }

        return shutdownEndTimeRest;
    }
}
