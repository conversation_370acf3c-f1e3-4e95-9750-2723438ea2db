package net.summerfarm.wnc.domain.config.repository;

import net.summerfarm.wnc.common.enums.ContactConfigEnums;
import net.summerfarm.wnc.common.query.config.ContactConfigQuery;
import net.summerfarm.wnc.domain.config.entity.ContactConfigEntity;

import java.util.List;

/**
 * Description:联系人配置仓库接口
 * date: 2023/9/22 18:22
 *
 * <AUTHOR>
 */
public interface ContactConfigRepository {

    /**
     * 根据uk查询
     * @param source 来源
     * @param outerContactId 外部联系人ID
     * @return 联系人配置
     */
    ContactConfigEntity queryByUk(ContactConfigEnums.Source source, Long outerContactId);

    /**
     * 保存联系人配置
     * @param contactConfigEntity 联系人配置实体
     * @return 影响条数
     */
    int save(ContactConfigEntity contactConfigEntity);

    /**
     * 删除联系人配置
     * @param configId 主键ID
     * @return 影响条数
     */
    int remove(Long configId);

    /**
     * 查询联系人配置集合
     * @param contactConfigQuery 查询
     * @return 结果
     */
    List<ContactConfigEntity> queryList(ContactConfigQuery contactConfigQuery);

    /**
     * 更新联系人配置集合
     * @param contactConfigEntity 联系人配置实体
     * @return 影响条数
     */
    int update(ContactConfigEntity contactConfigEntity);
}
