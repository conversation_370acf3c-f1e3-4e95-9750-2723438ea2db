package net.summerfarm.wnc.domain.warehouse;

import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.common.base.WncAssert;
import net.summerfarm.wnc.common.enums.WarehouseLogisticsCenterEnums;
import net.summerfarm.wnc.common.enums.WarehouseStorageCenterEnums;
import net.summerfarm.wnc.common.query.fence.AreaByListWarehouseAndSkuQuery;
import net.summerfarm.wnc.common.query.fence.FenceQuery;
import net.summerfarm.wnc.common.query.warehouse.SkuWarehouseMappingQuery;
import net.summerfarm.wnc.common.query.warehouse.WarehouseInventoryMappingQuery;
import net.summerfarm.wnc.common.query.warehouse.WarehouseStorageQuery;
import net.summerfarm.wnc.domain.fence.DeliveryFenceRepository;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.summerfarm.wnc.domain.fence.entity.SkuManyWarehouseEntity;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseInventoryMappingEntity;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseLogisticsCenterEntity;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseStorageEntity;
import net.summerfarm.wnc.facade.gc.GcQueryFacade;
import net.summerfarm.wnc.facade.gc.dto.ProductSkuDTO;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.Map;

/**
 * Description: <br/>
 * date: 2023/4/7 19:06<br/>
 *
 * <AUTHOR> />
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class WarehouseInventoryMappingDomainService {

    final private WarehouseInventoryMappingRepository warehouseInventoryMappingRepository;
    final private DeliveryFenceRepository deliveryFenceRepository;
    final private GcQueryFacade gcQueryFacade;
    final private WarehouseStorageCenterRepository warehouseStorageCenterRepository;
    final private WarehouseLogisticsCenterRepository warehouseLogisticsCenterRepository;

    /**
     * 查询租户和省市区查询代仓信息
     *
     * @param warehouseInventoryMappingQuery 查询
     * @return 自营仓集合
     */
    public List<WarehouseInventoryMappingEntity> queryTenantFenceWarehouse(WarehouseInventoryMappingQuery warehouseInventoryMappingQuery) {
        WncAssert.hasText(warehouseInventoryMappingQuery.getCity(), "city不能为空");

        FenceEntity fenceEntity = deliveryFenceRepository.queryOneFenceByProvinceCityArea(FenceQuery.builder()
                .city(warehouseInventoryMappingQuery.getCity())
                .area(warehouseInventoryMappingQuery.getArea())
                .build());
        if (fenceEntity == null) {
            return null;
        }

        return warehouseInventoryMappingRepository.queryList(WarehouseInventoryMappingQuery.builder()
                .storeNo(fenceEntity.getStoreNo())
                .skus(warehouseInventoryMappingQuery.getSkus())
                .build());
    }

    /**
     * 批量插入
     *
     * @param needCreateMapping 映射关系集合
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchSave(List<WarehouseInventoryMappingEntity> needCreateMapping) {
        if (CollectionUtils.isEmpty(needCreateMapping)) {
            return;
        }
        warehouseInventoryMappingRepository.batchSave(needCreateMapping);
    }

    /**
     * 修改映射关系
     *
     * @param skuManyWarehouseList sku存在多个仓库使用关系
     */
    @Transactional(rollbackFor = Exception.class)
    public void mappingDataFix(List<SkuManyWarehouseEntity> skuManyWarehouseList) {
        if (CollectionUtils.isEmpty(skuManyWarehouseList)) {
            return;
        }
        //过滤出数量不是0的sku信息
        Map<String, SkuManyWarehouseEntity> haveQuantitySkuList = skuManyWarehouseList.stream().filter(sku -> sku.getQuantity() != 0)
                .collect(Collectors.toMap(SkuManyWarehouseEntity::getSku , Function.identity(), (oldValue, newValue) -> newValue));

        ArrayList<SkuManyWarehouseEntity> needChangeWarehouseList = new ArrayList<>();
        //过滤出0的库存使用关系，修改为有库存的库存仓
        for (SkuManyWarehouseEntity skuManyWarehouseEntity : skuManyWarehouseList) {
            if(skuManyWarehouseEntity.getQuantity() != 0){
                continue;
            }
            SkuManyWarehouseEntity haveQuantitySkuEntity = haveQuantitySkuList.get(skuManyWarehouseEntity.getSku());
            if(haveQuantitySkuEntity == null){
                continue;
            }
            skuManyWarehouseEntity.setWarehouseNo(haveQuantitySkuEntity.getWarehouseNo());

            needChangeWarehouseList.add(skuManyWarehouseEntity);
        }

        List<SkuWarehouseMappingQuery> skuWarehouseMappingQueries = new ArrayList<>();
        for (SkuManyWarehouseEntity skuStoreNo : needChangeWarehouseList) {
            SkuWarehouseMappingQuery query = new SkuWarehouseMappingQuery();
            query.setSku(skuStoreNo.getSku());
            query.setStoreNo(skuStoreNo.getStoreNo());
            skuWarehouseMappingQueries.add(query);
        }
        //进行查询
        List<WarehouseInventoryMappingEntity> warehouseInventoryMappingEntities = warehouseInventoryMappingRepository.queryMappingByUkList(skuWarehouseMappingQueries);

        List<WarehouseInventoryMappingEntity> needUpdateList = new ArrayList<>();
        //需要更新的数据map结构
        Map<String, SkuManyWarehouseEntity> needChangeMap = needChangeWarehouseList.stream().collect(Collectors.toMap(sku -> sku.getSku() + "#" +sku.getStoreNo(), Function.identity(), (oldValue, newValue) -> newValue));
        //拼装更新数据
        for (WarehouseInventoryMappingEntity warehouseInventoryMappingEntity : warehouseInventoryMappingEntities) {
            SkuManyWarehouseEntity needChangeEntity = needChangeMap.get(warehouseInventoryMappingEntity.getSku() + "#" + warehouseInventoryMappingEntity.getStoreNo());
            if(needChangeEntity == null){
                continue;
            }
            WarehouseInventoryMappingEntity mappingEntity = new WarehouseInventoryMappingEntity();
            mappingEntity.setId(warehouseInventoryMappingEntity.getId());
            mappingEntity.setWarehouseNo(needChangeEntity.getWarehouseNo());

            needUpdateList.add(mappingEntity);

            log.info("修改sku多仓映射关系,原数据:{},新数据:{}", JSON.toJSONString(warehouseInventoryMappingEntity),JSON.toJSONString(mappingEntity));
        }
        log.info("修改sku多仓映射关系,处理了共计:{}条数据",needUpdateList.size());
        //批量更新
        warehouseInventoryMappingRepository.batchUpdate(needUpdateList);
    }

    /**
     * 获取映射关系和sku销售类型(结果按照库存仓分组)
     * @param skuList sku集合
     * @param warehouseNos 仓库信息
     * @return 仓库映射关系
     */
    public Map<Integer, List<WarehouseInventoryMappingEntity>> queryGroupMappingWithSkuSubTypeBySkusWarehouseNos(List<String> skuList, List<Integer> warehouseNos) {
        if(CollectionUtils.isEmpty(skuList) || CollectionUtils.isEmpty(warehouseNos)){
            return Collections.emptyMap();
        }
        //根据sku和城配仓编号查询对应的城配配送信息,获取有效的城配仓映射关系
        List<WarehouseInventoryMappingEntity> warehouseInventoryMappingEntities = warehouseInventoryMappingRepository.queryValideStoreWarehouseNoMappingBySkuWarehouseNos(skuList,warehouseNos);
        //查询sku代仓信息
        this.querySkuSubType(skuList, warehouseInventoryMappingEntities);

        return warehouseInventoryMappingEntities.stream().collect(Collectors.groupingBy(WarehouseInventoryMappingEntity::getWarehouseNo));
    }

    /**
     * 获取映射关系和sku销售类型
     * @param skuList sku集合
     * @param warehouseNos 仓库信息
     * @return 仓库映射关系
     */
    public List<WarehouseInventoryMappingEntity> queryMappingWithSkuSubTypeBySkusWarehouseNos(List<String> skuList, List<Integer> warehouseNos) {
        if(CollectionUtils.isEmpty(skuList) || CollectionUtils.isEmpty(warehouseNos)){
            return Collections.emptyList();
        }
        //根据sku和城配仓编号查询对应的城配配送信息,获取有效的城配仓映射关系
        List<WarehouseInventoryMappingEntity> warehouseInventoryMappingEntities = warehouseInventoryMappingRepository.queryValideStoreWarehouseNoMappingBySkuWarehouseNos(skuList,warehouseNos);
        //查询sku代仓信息
        this.querySkuSubType(skuList, warehouseInventoryMappingEntities);

        return warehouseInventoryMappingEntities;
    }

    /**
     * 获取映射关系和sku销售类型
     * @param skuList sku集合
     * @param storeNo 城配仓编号
     * @return 仓库映射关系
     */
    public List<WarehouseInventoryMappingEntity> queryMappingWithSkuSubTypeBySkusStoreNo(List<String> skuList, Integer storeNo) {
        if(CollectionUtils.isEmpty(skuList) || storeNo == null){
            return Collections.emptyList();
        }
        List<WarehouseInventoryMappingEntity> warehouseInventoryMappingList = warehouseInventoryMappingRepository.queryValideStoreWarehouseNoMappingBySkuStoreNos(skuList,Collections.singletonList(storeNo));

        if(CollectionUtils.isEmpty(warehouseInventoryMappingList)){
            return Collections.emptyList();
        }

        List<Integer> warehouseNos = warehouseInventoryMappingList.stream().map(WarehouseInventoryMappingEntity::getWarehouseNo).collect(Collectors.toList());
        List<Integer> storeNos = warehouseInventoryMappingList.stream().map(WarehouseInventoryMappingEntity::getStoreNo).collect(Collectors.toList());

        //查询仓库有效的状态
        List<WarehouseStorageEntity> warehouseStorageEntities = warehouseStorageCenterRepository.queryList(WarehouseStorageQuery.builder()
                .status(WarehouseStorageCenterEnums.Status.OPEN.getValue())
                .warehouseNos(warehouseNos)
                .build());
        List<Integer> warehouseNoValidList = warehouseStorageEntities.stream()
                .map(WarehouseStorageEntity::getWarehouseNo)
                .collect(Collectors.toList());

        //查询城配仓有效的状态
        List<WarehouseLogisticsCenterEntity> warehouseLogisticsCenterEntities = warehouseLogisticsCenterRepository.queryListByStoreNosAndStatus(storeNos,WarehouseLogisticsCenterEnums.Status.VALID.getValue());
        List<Integer> storeNoValidList = warehouseLogisticsCenterEntities.stream()
                .map(WarehouseLogisticsCenterEntity::getStoreNo)
                .collect(Collectors.toList());

        List<WarehouseInventoryMappingEntity> warehouseInventoryMappingEntities = warehouseInventoryMappingList.stream()
                .filter(e -> warehouseNoValidList.contains(e.getWarehouseNo()) && storeNoValidList.contains(e.getStoreNo()))
                .collect(Collectors.toList());

        //查询sku代仓信息
        this.querySkuSubType(skuList, warehouseInventoryMappingEntities);

        return warehouseInventoryMappingEntities;
    }

    /**
     * 查询sku属性信息
     * @param skuList sku集合
     * @param warehouseInventoryMappingEntities 映射信息
     */
    private void querySkuSubType(List<String> skuList, List<WarehouseInventoryMappingEntity> warehouseInventoryMappingEntities) {
        List<ProductSkuDTO> productSkus = gcQueryFacade.querySkuListInfo(skuList);
        if (CollectionUtils.isEmpty(productSkus)) {
           return;
        }
        Map<String, ProductSkuDTO> skuMap = productSkus.stream().collect(Collectors.toMap(ProductSkuDTO::getSku, Function.identity(), (oldData, newData) -> newData));
        for (WarehouseInventoryMappingEntity mapping : warehouseInventoryMappingEntities) {
            if (skuMap.get(mapping.getSku()) == null) {
               continue;
            }
            //设置二级类型
            mapping.setSubType(skuMap.get(mapping.getSku()).getSubType());
        }
    }

    /**
     * 根据sku和仓库查询映射关系
     * @param query 查询
     * @return 结果
     */
    public List<WarehouseInventoryMappingEntity> queryMappingListBySkuWarehouseNo(List<AreaByListWarehouseAndSkuQuery> query) {
        if(CollectionUtils.isEmpty(query)){
            return Collections.emptyList();
        }
        List<String> skuList = query.stream().map(AreaByListWarehouseAndSkuQuery::getSku).collect(Collectors.toList());
        List<Integer> warehouseNoList = query.stream().map(AreaByListWarehouseAndSkuQuery::getWarehouseNo).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(skuList) || CollectionUtils.isEmpty(warehouseNoList)){
            throw new BizException("sku和仓库编号都需要存在");
        }
        List<WarehouseInventoryMappingEntity> warehouseInventoryMappingList = warehouseInventoryMappingRepository.queryValideStoreWarehouseNoMappingBySkuWarehouseNos(skuList,warehouseNoList);

        if(CollectionUtils.isEmpty(warehouseInventoryMappingList)){
            return Collections.emptyList();
        }
        return warehouseInventoryMappingList;
    }
}
