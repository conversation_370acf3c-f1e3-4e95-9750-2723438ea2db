package net.summerfarm.wnc.domain.config.param.query;

import java.time.LocalDateTime;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.xianmu.common.input.BasePageInput;


/**
 * <AUTHOR>
 * @date 2025-01-06 15:31:26
 * @version 1.0
 *
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WncFullCategoryWarehouseSkuWhiteConfigQueryParam extends BasePageInput {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 库存仓编号
	 */
	private Integer warehouseNo;

	/**
	 * sku
	 */
	private String sku;


	/**
	 * sku集合
	 */
	private List<String> skus;

	/**
	 * 库存仓编号集合
	 */
	private List<Integer> warehouseNos;
	
}