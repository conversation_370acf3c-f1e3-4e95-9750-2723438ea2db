package net.summerfarm.wnc.domain.path.entity;

import lombok.Data;
import net.summerfarm.wnc.common.enums.PathConfigEnums;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/11/28 14:31<br/>
 *
 * <AUTHOR> />
 */
@Data
public class PathConfigEntity {

    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 开始编号
     */
    private Integer beginOutNo;

    /**
     * 结束编号
     */
    private Integer endOutNo;

    /**
     * 业务类型 0调拨 仓到仓
     */
    private PathConfigEnums.BusinseeType businseeType;

    /**
     * 周期方案 1每周 2两周
     */
    private PathConfigEnums.FrequentMethod frequentMethod;

    /**
     * 周期 1是周一类推
     */
    private String frequent;

    /**
     * 路途时长
     */
    private Integer travelDuratio;

    /**
     * 下一次路线时间
     */
    private LocalDateTime lastTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新人
     */
    private String updater;

    /**
     * sku路线映射
     */
    private List<SkuPathMappingEntity> skuPathMappingEntities;
}
