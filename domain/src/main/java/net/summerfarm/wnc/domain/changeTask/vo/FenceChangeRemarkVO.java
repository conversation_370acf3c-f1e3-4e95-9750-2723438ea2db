package net.summerfarm.wnc.domain.changeTask.vo;

import lombok.Data;
import net.summerfarm.wnc.common.enums.FenceChangeTaskEnums;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * Description:切仓说明值对象
 * date: 2023/8/24 14:27
 *
 * <AUTHOR>
 */
@Data
public class FenceChangeRemarkVO implements Serializable {

    private static final long serialVersionUID = -3339287217483260921L;

    /**
     * 原库存使用仓
     */
    private List<String> oldWarehouses;

    /**
     * 新库存使用仓
     */
    private List<String> newWarehouses;

    /**
     * 原运营区域
     */
    private String oldArea;

    /**
     * 新运营区域
     */
    private String newArea;

    /**
     * 原城配仓
     */
    private String oldStore;

    /**
     * 新城配仓
     */
    private String newStore;

    /**
     * 原运营区域编号
     */
    private Integer oldAreaNo;

    /**
     * 新运营区域编号
     */
    private Integer newAreaNo;

    /**
     * 原城配仓编号
     */
    private Integer oldStoreNo;

    /**
     * 新城配仓编号
     */
    private Integer newStoreNo;

    /**
     * 执行时间
     */
    private LocalDateTime exeTime;

    public void init(Integer oldAreaNo, String oldArea, String oldStore, List<String> oldWarehouses, FenceChangeTaskEnums.Type type){
        this.oldArea = oldArea;
        this.oldStore = oldStore;
        this.oldWarehouses = oldWarehouses;
        if (type.isStore()){
            //城配仓切仓运营区域不变
            this.newAreaNo = oldAreaNo;
            this.newArea = oldArea;
        }
    }

    public boolean isAreaChange(){
        return !Objects.equals(this.oldArea, this.newArea);
    }

    public boolean isStoreChange(){
        return !Objects.equals(this.oldStore, this.newStore);
    }

    public boolean isAreaAndStoreChange() {
        return this.isAreaChange() || this.isStoreChange();
    }

    public String buildStoreChangeStr(){
        return String.format("%s--->%s",this.oldStore, this.newStore);
    }

    public String buildAreaChangeStr(){
        return String.format("%s--->%s",this.oldArea, this.newArea);
    }

    public String buildWarehouseChangeStr(){
        return String.format("%s--->%s",this.buildWarehouseStr(this.oldWarehouses), this.buildWarehouseStr(this.newWarehouses));
    }

    public String buildOldWarehouseStr(){
        return this.buildWarehouseStr(this.oldWarehouses);
    }

    public String buildWarehouseStr(List<String> warehouses){
        if (CollectionUtils.isEmpty(warehouses)){
            return null;
        }
        return String.join("、", warehouses);
    }

}
