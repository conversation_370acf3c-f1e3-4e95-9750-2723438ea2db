package net.summerfarm.wnc.domain.fence;

import cn.hutool.core.bean.BeanUtil;
import lombok.RequiredArgsConstructor;
import net.summerfarm.wnc.common.enums.WncWarehouseStorageFenceRuleEnums;
import net.summerfarm.wnc.common.query.fence.WncWarehouseStorageFenceRuleQuery;
import net.summerfarm.wnc.common.query.warehouse.WarehouseStorageFenceQuery;
import net.summerfarm.wnc.common.query.warehouse.WarehouseStorageQuery;
import net.summerfarm.wnc.common.util.MapUtil;
import net.summerfarm.wnc.domain.warehouse.WarehouseStorageCenterDomainService;
import net.summerfarm.wnc.domain.warehouse.WarehouseStorageCenterRepository;
import net.summerfarm.wnc.domain.warehouse.WncWarehouseStorageFenceDomain;
import net.summerfarm.wnc.domain.warehouse.WncWarehouseStorageFenceRepository;
import net.summerfarm.wnc.domain.warehouse.entity.ConflictWarehouseJsonEntity;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseStorageEntity;
import net.summerfarm.wnc.domain.warehouse.entity.WncWarehouseStorageFenceEntity;
import net.summerfarm.wnc.domain.warehouse.entity.WncWarehouseStorageFenceRuleEntity;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/4/14 16:01<br/>
 *
 * <AUTHOR> />
 */
@Service
@RequiredArgsConstructor
public class WncWarehouseStorageFenceRuleDomainService {

    private final WncWarehouseStorageFenceRuleRepository wncWarehouseStorageFenceRuleRepository;
    private final WarehouseStorageCenterRepository warehouseStorageCenterRepository;
    private final WarehouseStorageCenterDomainService warehouseStorageCenterDomainService;
    private final WncWarehouseStorageFenceDomain wncWarehouseStorageFenceDomain;
    private final WncWarehouseStorageFenceRepository warehouseStorageFenceRepository;

    /**
     * 新增规则
     * @param fenceEntities 冲突围栏
     * @param warehouseStorageEntity 仓库信息
     */
    public void addFenceConflictRule(List<WncWarehouseStorageFenceEntity> fenceEntities, WarehouseStorageEntity warehouseStorageEntity) {
        if(CollectionUtils.isEmpty(fenceEntities)){
            return;
        }
        //冲突的围栏
        List<WncWarehouseStorageFenceEntity> conflictFenceEntities = new ArrayList<>(fenceEntities);
        for (WncWarehouseStorageFenceEntity fenceEntity : fenceEntities) {
            WncWarehouseStorageFenceEntity wncWarehouseStorageFenceEntity = new WncWarehouseStorageFenceEntity();
            wncWarehouseStorageFenceEntity.setProvince(fenceEntity.getProvince());
            wncWarehouseStorageFenceEntity.setCity(fenceEntity.getCity());
            wncWarehouseStorageFenceEntity.setArea(fenceEntity.getArea());
            wncWarehouseStorageFenceEntity.setWarehouseNo(warehouseStorageEntity.getWarehouseNo());
            conflictFenceEntities.add(wncWarehouseStorageFenceEntity);
        }
        conflictFenceEntities = conflictFenceEntities.stream().distinct().collect(Collectors.toList());
        List<Integer> warehouseNos = conflictFenceEntities.stream().map(WncWarehouseStorageFenceEntity::getWarehouseNo).collect(Collectors.toList());
        //查询仓库信息
        List<WarehouseStorageEntity> warehouseStorageEntityList = warehouseStorageCenterRepository.queryList(WarehouseStorageQuery.builder().warehouseNos(warehouseNos).build());
        Map<Integer, WarehouseStorageEntity> warehouseNoMap = warehouseStorageEntityList.stream().collect(Collectors.toMap(WarehouseStorageEntity::getWarehouseNo, Function.identity()));

        //查询是否已经存在配送规则
        List<WncWarehouseStorageFenceRuleEntity> wncWarehouseStorageFenceRuleEntities = wncWarehouseStorageFenceRuleRepository.queryList(WncWarehouseStorageFenceRuleQuery.builder()
                .tenantId(warehouseStorageEntity.getTenantId())
                .build());
        //存在了话需要在后面加上
        for (WncWarehouseStorageFenceRuleEntity wncWarehouseStorageFenceRuleEntity : wncWarehouseStorageFenceRuleEntities) {
            List<ConflictWarehouseJsonEntity> conflictWarehouseJsonEntityList = wncWarehouseStorageFenceRuleEntity.getConflictWarehouseJsonEntityList();
            List<Integer> warehouseNoList = conflictWarehouseJsonEntityList.stream().map(ConflictWarehouseJsonEntity::getWarehouseNo).collect(Collectors.toList());

            //过滤出存在配送规则，没有这个仓库的数据，进行更新
            updateHaveRuleNoWarehouse(conflictFenceEntities, warehouseNoMap, wncWarehouseStorageFenceRuleEntity, conflictWarehouseJsonEntityList, warehouseNoList);
        }
        //保存没有数据规则
        Map<String, List<WncWarehouseStorageFenceRuleEntity>> dataHaveFenceListMap = wncWarehouseStorageFenceRuleEntities.stream()
                .collect(Collectors.groupingBy(fenceRule -> fenceRule.getProvince() + fenceRule.getCity() + fenceRule.getArea()));
        //过滤不存在的数据，需要新增
        List<WncWarehouseStorageFenceEntity> noRuleFenceList = conflictFenceEntities.stream().filter(conflictFence -> {
            if (CollectionUtils.isEmpty(dataHaveFenceListMap.get(conflictFence.getProvince() + conflictFence.getCity() + conflictFence.getArea()))) {
                return true;
            }
            return false;
        }).collect(Collectors.toList());

        //不存在需要生成一条
        save(conflictFenceEntities,warehouseNoMap,noRuleFenceList);
    }

    /**
     * 保存
     * @param fenceConflictList
     * @param warehouseNoMap
     * @param noRuleFenceList
     */
    private void save(List<WncWarehouseStorageFenceEntity> fenceConflictList, Map<Integer, WarehouseStorageEntity> warehouseNoMap, List<WncWarehouseStorageFenceEntity> noRuleFenceList) {
        //需要按照省市区分组新增
        Map<String, List<WncWarehouseStorageFenceEntity>> fenceRuleListMap = noRuleFenceList.stream()
                .collect(Collectors.groupingBy(fence ->
                        (StringUtils.isNotBlank(fence.getProvince()) ? fence.getProvince() : "") +
                        (StringUtils.isNotBlank(fence.getCity()) ? fence.getCity() : "" )+
                        (StringUtils.isNotBlank(fence.getArea()) ? fence.getArea() : "")
                ));
        //过滤key为空字符串的数据
        MapUtil.removeMapKey(fenceRuleListMap);
        for (String proCityArea : fenceRuleListMap.keySet()) {
            List<WncWarehouseStorageFenceEntity> wncWarehouseStorageFenceEntities = fenceRuleListMap.get(proCityArea);
            StringJoiner warehouseNameList = new StringJoiner(",");

            List<ConflictWarehouseJsonEntity> conflictWarehouseJsonEntities = new ArrayList<>();
            for (WncWarehouseStorageFenceEntity wncWarehouseStorageFenceEntity : wncWarehouseStorageFenceEntities) {
                if(warehouseNoMap.get(wncWarehouseStorageFenceEntity.getWarehouseNo()) != null){
                    warehouseNameList.add(warehouseNoMap.get(wncWarehouseStorageFenceEntity.getWarehouseNo()).getWarehouseName());

                    ConflictWarehouseJsonEntity conflictWarehouseJsonEntity = new ConflictWarehouseJsonEntity();
                    conflictWarehouseJsonEntity.setWarehouseName(warehouseNoMap.get(wncWarehouseStorageFenceEntity.getWarehouseNo()).getWarehouseName());
                    conflictWarehouseJsonEntity.setWarehouseNo(wncWarehouseStorageFenceEntity.getWarehouseNo());
                    conflictWarehouseJsonEntity.setTenentId(wncWarehouseStorageFenceEntity.getTenantId());

                    conflictWarehouseJsonEntities.add(conflictWarehouseJsonEntity);
                }
            }
            //warehouseNoMap
            WncWarehouseStorageFenceRuleEntity wncWarehouseStorageFenceRuleEntitySave = new WncWarehouseStorageFenceRuleEntity();

            wncWarehouseStorageFenceRuleEntitySave.setWarehouseNameList(warehouseNameList.toString());
            wncWarehouseStorageFenceRuleEntitySave.setTenantId(wncWarehouseStorageFenceEntities.get(0).getTenantId());
            wncWarehouseStorageFenceRuleEntitySave.setProvince(wncWarehouseStorageFenceEntities.get(0).getProvince());
            wncWarehouseStorageFenceRuleEntitySave.setCity(wncWarehouseStorageFenceEntities.get(0).getCity());
            wncWarehouseStorageFenceRuleEntitySave.setArea(wncWarehouseStorageFenceEntities.get(0).getArea());
            wncWarehouseStorageFenceRuleEntitySave.setLastOperatorName(fenceConflictList.get(0).getLastOperatorName());
            wncWarehouseStorageFenceRuleEntitySave.setConflictWarehouseJsonEntityList(conflictWarehouseJsonEntities);
            wncWarehouseStorageFenceRuleEntitySave.setDeliveryRule(WncWarehouseStorageFenceRuleEnums.deliveryRule.DISTANCE_FIRST.getValue());
            wncWarehouseStorageFenceRuleRepository.save(wncWarehouseStorageFenceRuleEntitySave);
        }
    }

    private void updateHaveRuleNoWarehouse(List<WncWarehouseStorageFenceEntity> fenceConflictList, Map<Integer, WarehouseStorageEntity> warehouseNoMap, WncWarehouseStorageFenceRuleEntity wncWarehouseStorageFenceRuleEntity, List<ConflictWarehouseJsonEntity> conflictWarehouseJsonEntityList, List<Integer> warehouseNoList) {
        List<WncWarehouseStorageFenceEntity> haveRuleFenceNoWarehouseList = fenceConflictList.stream().filter(conflictFence -> {
            if (Objects.equals(conflictFence.getProvince(), wncWarehouseStorageFenceRuleEntity.getProvince()) &&
                    Objects.equals(conflictFence.getCity(), wncWarehouseStorageFenceRuleEntity.getCity()) &&
                    Objects.equals(conflictFence.getArea(), wncWarehouseStorageFenceRuleEntity.getArea()) &&
                    !warehouseNoList.contains(conflictFence.getWarehouseNo())) {
                return true;
            }
            return false;
        }).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(haveRuleFenceNoWarehouseList)){
            return;
        }

        for (WncWarehouseStorageFenceEntity wncWarehouseStorageFenceEntity : haveRuleFenceNoWarehouseList) {
            ConflictWarehouseJsonEntity conflictWarehouseJsonEntity = new ConflictWarehouseJsonEntity();
            conflictWarehouseJsonEntity.setWarehouseNo(wncWarehouseStorageFenceEntity.getWarehouseNo());
            conflictWarehouseJsonEntity.setWarehouseName(warehouseNoMap.get(wncWarehouseStorageFenceEntity.getWarehouseNo()).getWarehouseName());
            conflictWarehouseJsonEntity.setTenentId(warehouseNoMap.get(wncWarehouseStorageFenceEntity.getWarehouseNo()).getTenantId());
            conflictWarehouseJsonEntityList.add(conflictWarehouseJsonEntity);
        }
        wncWarehouseStorageFenceRuleEntity.setConflictWarehouseJsonEntityList(conflictWarehouseJsonEntityList);
        wncWarehouseStorageFenceRuleEntity.setWarehouseNameList(conflictWarehouseJsonEntityList.stream().map(ConflictWarehouseJsonEntity::getWarehouseName).collect(Collectors.joining(",")));
        wncWarehouseStorageFenceRuleEntity.setLastOperatorName(fenceConflictList.get(0).getLastOperatorName());

        wncWarehouseStorageFenceRuleRepository.update(wncWarehouseStorageFenceRuleEntity);
    }

    /**
     * 冲突围栏处理
     * @param warehouseStorageEntity 仓库信息
     */
    public void fenceConflictRule(WarehouseStorageEntity warehouseStorageEntity) {
        //处理老的围栏信息
        handleOldDeleteUpdateFence(warehouseStorageEntity);
        //更新围栏信息
        warehouseStorageCenterDomainService.update(warehouseStorageEntity);
        List<WncWarehouseStorageFenceEntity> fenceEntities = warehouseStorageEntity.getWarehouseStorageFenceEntities();
        //查询是否存在冲突的区域
        List<String> citys = fenceEntities.stream().map(WncWarehouseStorageFenceEntity::getCity).collect(Collectors.toList());
        List<String> areas = fenceEntities.stream().map(WncWarehouseStorageFenceEntity::getArea).collect(Collectors.toList());
        List<WncWarehouseStorageFenceEntity> wncWarehouseStorageFenceEntities = wncWarehouseStorageFenceDomain.queryFenceConflict(WarehouseStorageFenceQuery.builder()
                .tenantId(warehouseStorageEntity.getTenantId())
                .citys(citys)
                .areas(areas)
                .noThisWarehouseNo(warehouseStorageEntity.getWarehouseNo())
                .build());
        if(CollectionUtils.isEmpty(wncWarehouseStorageFenceEntities) || CollectionUtils.isEmpty(warehouseStorageEntity.getWarehouseStorageFenceEntities())){
            return;
        }
        //新增配送规则
        this.addFenceConflictRule(wncWarehouseStorageFenceEntities,warehouseStorageEntity);
    }

    private void handleOldDeleteUpdateFence(WarehouseStorageEntity warehouseStorageEntity) {
        //先查询原来的租户仓库的覆盖范围
        List<WncWarehouseStorageFenceEntity> oldWarehouseStorageFenceList = warehouseStorageFenceRepository.queryList(WarehouseStorageFenceQuery.builder()
                .warehouseNo(warehouseStorageEntity.getWarehouseNo())
                .tenantId(warehouseStorageEntity.getTenantId())
                .build());
        //新的围栏信息
        Map<String, List<WncWarehouseStorageFenceEntity>> newFenceListMap = warehouseStorageEntity.getWarehouseStorageFenceEntities().stream().collect(Collectors.groupingBy(fence ->
                (StringUtils.isNotBlank(fence.getProvince()) ? fence.getProvince() : "") +
                        (StringUtils.isNotBlank(fence.getCity()) ? fence.getCity() : "") +
                        (StringUtils.isNotBlank(fence.getArea()) ? fence.getArea() : "")
        ));
        //过滤key为空字符串的数据
        MapUtil.removeMapKey(newFenceListMap);
        List<WncWarehouseStorageFenceEntity> haveDeleteUpdateFenceList = new ArrayList<>();
        //标识被去掉或者需要更新的围栏信息
        for (WncWarehouseStorageFenceEntity wncWarehouseStorageFenceEntity : oldWarehouseStorageFenceList) {
            if(newFenceListMap.get((StringUtils.isNotBlank(wncWarehouseStorageFenceEntity.getProvince()) ? wncWarehouseStorageFenceEntity.getProvince() : "") +
                    (StringUtils.isNotBlank(wncWarehouseStorageFenceEntity.getCity()) ? wncWarehouseStorageFenceEntity.getCity() : "") +
                    (StringUtils.isNotBlank(wncWarehouseStorageFenceEntity.getArea()) ? wncWarehouseStorageFenceEntity.getArea() : "")) == null){
                haveDeleteUpdateFenceList.add(wncWarehouseStorageFenceEntity);
            }
        }

        if(CollectionUtils.isEmpty(haveDeleteUpdateFenceList)){
            return;
        }
        //查询在配送规则里面是否存在
        List<WncWarehouseStorageFenceRuleEntity> wncWarehouseStorageFenceRuleEntities = wncWarehouseStorageFenceRuleRepository.queryList(WncWarehouseStorageFenceRuleQuery.builder()
                .tenantId(warehouseStorageEntity.getTenantId())
                .citys(haveDeleteUpdateFenceList.stream().map(WncWarehouseStorageFenceEntity::getCity).collect(Collectors.toList()))
                .areas(haveDeleteUpdateFenceList.stream().map(WncWarehouseStorageFenceEntity::getArea).collect(Collectors.toList()))
                .build());
        //不存在无需处理
        if(CollectionUtils.isEmpty(wncWarehouseStorageFenceRuleEntities)){
            return;
        }
        //需要删除的
        List<Long> needDeleteIdList = new ArrayList<>();
        //需要更新的
        List<WncWarehouseStorageFenceRuleEntity> needUpdateWarehouseStorageFenceRuleList = new ArrayList<>();
        //在里面需要给剔除掉，如果只存在一条直接删除
        wncWarehouseStorageFenceRuleEntities.forEach(fenceRule->{
            List<ConflictWarehouseJsonEntity> conflictWarehouseJsonEntityList = fenceRule.getConflictWarehouseJsonEntityList();
            conflictWarehouseJsonEntityList = conflictWarehouseJsonEntityList.stream()
                    .filter(conflictWarehouse -> !Objects.equals(conflictWarehouse.getWarehouseNo(), warehouseStorageEntity.getWarehouseNo()))
                    .collect(Collectors.toList());
            fenceRule.setConflictWarehouseJsonEntityList(conflictWarehouseJsonEntityList);
            fenceRule.setLastOperatorName(warehouseStorageEntity.getOperatorName());
            if(conflictWarehouseJsonEntityList.size() <= 1){
                needDeleteIdList.add(fenceRule.getId());
            }else{
                needUpdateWarehouseStorageFenceRuleList.add(fenceRule);
            }
        });
        //删除
        wncWarehouseStorageFenceRuleRepository.removeByIdList(needDeleteIdList);
        //更新
        wncWarehouseStorageFenceRuleRepository.updateList(needUpdateWarehouseStorageFenceRuleList);
    }

    /**
     * 修改仓库名称
     * @param warehouseNo 仓库编号
     * @param oldWarehouseName 旧仓库名称
     * @param newWarehouseName 新仓库名称
     * @param tenantId 租户Id
     */
    public void updateWarehouseName(Integer warehouseNo, String oldWarehouseName, String newWarehouseName,Long tenantId) {
        List<WncWarehouseStorageFenceRuleEntity> wncWarehouseStorageFenceRuleEntities = wncWarehouseStorageFenceRuleRepository.queryList(WncWarehouseStorageFenceRuleQuery.builder().tenantId(tenantId).build());
        if(CollectionUtils.isEmpty(wncWarehouseStorageFenceRuleEntities)){
            return;
        }

        Set<Long> needFenceRuleIds = new HashSet<>();

        for (WncWarehouseStorageFenceRuleEntity wncWarehouseStorageFenceRuleEntity : wncWarehouseStorageFenceRuleEntities) {
            List<ConflictWarehouseJsonEntity> conflictWarehouseJsonEntityList = wncWarehouseStorageFenceRuleEntity.getConflictWarehouseJsonEntityList();
            for (ConflictWarehouseJsonEntity conflictWarehouseJsonEntity : conflictWarehouseJsonEntityList) {
                if(oldWarehouseName.equals(conflictWarehouseJsonEntity.getWarehouseName()) && Objects.equals(warehouseNo,conflictWarehouseJsonEntity.getWarehouseNo())){
                    conflictWarehouseJsonEntity.setWarehouseName(newWarehouseName);
                    needFenceRuleIds.add(wncWarehouseStorageFenceRuleEntity.getId());
                }
            }
        }
        List<WncWarehouseStorageFenceRuleEntity> needUpdateStorageFenceRuleList = wncWarehouseStorageFenceRuleEntities.stream().filter(rule -> needFenceRuleIds.contains(rule.getId())).collect(Collectors.toList());
        wncWarehouseStorageFenceRuleRepository.updateList(needUpdateStorageFenceRuleList);
    }
}
