package net.summerfarm.wnc.domain.closeTime;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.domain.closeTime.entity.CloseTimeAreaConfigEntity;
import net.summerfarm.wnc.domain.closeTime.param.CloseTimeAreaConfigQueryParam;
import org.springframework.stereotype.Component;

import java.time.LocalTime;

/**
 * Description:截单时间配置查询领域服务
 * date: 2024/3/21 10:35
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CloseTimeConfigQueryDomainService {

    private final CloseTimeConfigQueryRepository closeTimeConfigQueryRepository;

    public LocalTime queryConfiguredCloseTime(CloseTimeAreaConfigQueryParam queryParam){
        if (queryParam == null){
            return null;
        }
        CloseTimeAreaConfigEntity closeTimeAreaConfig = closeTimeConfigQueryRepository.queryByUk(queryParam.getTenantId(), queryParam.getCity(), queryParam.getArea());
        if (closeTimeAreaConfig == null){
            return null;
        }
        return closeTimeAreaConfig.getCloseTime();
    }
}
