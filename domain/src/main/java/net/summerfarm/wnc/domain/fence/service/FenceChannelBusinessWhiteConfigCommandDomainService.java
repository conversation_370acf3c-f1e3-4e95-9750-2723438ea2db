package net.summerfarm.wnc.domain.fence.service;


import net.summerfarm.wnc.domain.fence.repository.FenceChannelBusinessWhiteConfigQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.FenceChannelBusinessWhiteConfigCommandRepository;
import net.summerfarm.wnc.domain.fence.entity.FenceChannelBusinessWhiteConfigEntity;
import net.summerfarm.wnc.domain.fence.param.command.FenceChannelBusinessWhiteConfigCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;


/**
 *
 * @Title: 围栏渠道业务白名单配置领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-10-12 16:29:33
 * @version 1.0
 *
 */
@Service
public class FenceChannelBusinessWhiteConfigCommandDomainService {


    @Autowired
    private FenceChannelBusinessWhiteConfigCommandRepository fenceChannelBusinessWhiteConfigCommandRepository;
    @Autowired
    private FenceChannelBusinessWhiteConfigQueryRepository fenceChannelBusinessWhiteConfigQueryRepository;



    public FenceChannelBusinessWhiteConfigEntity insert(FenceChannelBusinessWhiteConfigCommandParam param) {
        return fenceChannelBusinessWhiteConfigCommandRepository.insertSelective(param);
    }


    public int update(FenceChannelBusinessWhiteConfigCommandParam param) {
        return fenceChannelBusinessWhiteConfigCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return fenceChannelBusinessWhiteConfigCommandRepository.remove(id);
    }

    /**
     * 围栏维度 批量更新添加渠道业务白名单配置
     * @param fenceId 围栏ID
     * @param businessWhiteConfigEntities 渠道业务白名单配置
     */
    public void fenceBatchSaveOrUpdate(Integer fenceId, List<FenceChannelBusinessWhiteConfigEntity> businessWhiteConfigEntities) {
        if(fenceId == null || CollectionUtils.isEmpty(businessWhiteConfigEntities)){
            return;
        }
        // 批量删除
        fenceChannelBusinessWhiteConfigCommandRepository.removeByFenceId(fenceId);

        // 批量添加
        fenceChannelBusinessWhiteConfigCommandRepository.batchInsert(businessWhiteConfigEntities);
    }
}
