package net.summerfarm.wnc.domain.warehouse.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2023/3/28 16:37<br/>
 *
 * <AUTHOR> />
 */
@Data
public class WncWarehouseStorageFenceEntity {

    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 库存仓编号
     */
    private Integer warehouseNo;

    /**
     * 所属租户
     */
    private Long tenantId;

    /**
     * 最后操作人名称
     */
    private String lastOperatorName;

}
