package net.summerfarm.wnc.domain.warehouse.entity;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Description: <br/>
 * date: 2023/4/7 18:37<br/>
 *
 * <AUTHOR> />
 */
@Data
public class WarehouseInventoryMappingEntity {
    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 物流中心编号
     */
    private Integer storeNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 物流中心销售冻结
     */
    private Integer saleLockQuantity;

    /**
     * 预留库存使用数量
     */
    private Integer reserveQuantity;

    /**
     * 是否支持预留库存
     */
    private Integer supportReserved;

    /**
     * 更新人
     */
    private Integer updater;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /** 二级类型：1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-经销 **/
    private Integer subType;

    @Override
    public boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj == null || obj.getClass() != this.getClass()) {
            return false;
        }
        WarehouseInventoryMappingEntity p = (WarehouseInventoryMappingEntity) obj;
        return Objects.equals(this.storeNo,p.storeNo) && Objects.equals(this.sku,p.sku);
    }

    @Override
    public int hashCode() {
        return Objects.hash(storeNo, sku);
    }
}
