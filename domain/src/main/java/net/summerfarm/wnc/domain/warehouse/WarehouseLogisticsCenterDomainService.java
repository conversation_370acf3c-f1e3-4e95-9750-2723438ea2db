package net.summerfarm.wnc.domain.warehouse;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.tms.constants.RedisConstants;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskRepository;
import net.summerfarm.wnc.domain.fence.FenceRepository;
import net.summerfarm.wnc.domain.fence.entity.StopDeliveryEntity;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseLogisticsCenterEntity;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseLogisticsMappingEntity;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseStorageEntity;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.user.UserBase;
import net.xianmu.common.user.UserInfoHolder;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023-06-20
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class WarehouseLogisticsCenterDomainService {
	private final WarehouseLogisticsCenterRepository warehouseLogisticsCenterRepository;
	private final StopDeliveryRepository stopDeliveryRepository;
	@Lazy
	private final WarehouseStorageCenterDomainService warehouseStorageCenterDomainService;
	private final WarehouseLogisticsMappingRepository warehouseLogisticsMappingRepository;
	private final FenceChangeTaskRepository fenceChangeTaskRepository;
	private final FenceRepository fenceRepository;
	private final WarehouseInventoryMappingRepository warehouseInventoryMappingRepository;

	/**
	 * 停配规则状态设置
	 * @param logisticsCenterEntities 城配仓集合
	 */
	public void handleStopDeliveryStatus(List<WarehouseLogisticsCenterEntity> logisticsCenterEntities) {
		Map<Integer, StopDeliveryEntity> storeStopMap = stopDeliveryRepository.queryRecentlyStopMapByStoreNos(logisticsCenterEntities.stream().map(WarehouseLogisticsCenterEntity::getStoreNo).collect(Collectors.toList()));
		logisticsCenterEntities.forEach(store ->{
			StopDeliveryEntity stopDeliveryEntity = storeStopMap.get(store.getStoreNo());
			if (Objects.isNull(stopDeliveryEntity) || Objects.equals(stopDeliveryEntity.getDeleteFlag(), NumberUtils.INTEGER_ONE)) {
				store.setStopDeliveryStatus(NumberUtils.INTEGER_ZERO);
			} else if (Objects.equals(stopDeliveryEntity.getDeleteFlag(), NumberUtils.INTEGER_ZERO) && !stopDeliveryEntity.getShutdownEndTime().isBefore(LocalDate.now())) {
				store.setStopDeliveryStatus(NumberUtils.INTEGER_ONE);
				store.setStopDeliveryEntity(stopDeliveryEntity);
			} else if (Objects.equals(stopDeliveryEntity.getDeleteFlag(), NumberUtils.INTEGER_ZERO) && stopDeliveryEntity.getShutdownEndTime().isBefore(LocalDate.now())) {
				store.setStopDeliveryStatus(NumberUtils.INTEGER_TWO);
			}
		});
	}

    public LocalTime selectCloseTime(Integer storeNo) {
		return warehouseLogisticsCenterRepository.selectCloseTime(storeNo);
	}

	public void handleLogisticsMapping(List<WarehouseLogisticsCenterEntity> logisticsCenterEntities) {
		List<Integer> storeNoList = logisticsCenterEntities.stream().map(WarehouseLogisticsCenterEntity::getStoreNo).collect(Collectors.toList());
		Map<Integer, List<WarehouseStorageEntity>> storeWarehouseListMap = warehouseStorageCenterDomainService.handleLogisticsMapping(storeNoList);
		for (WarehouseLogisticsCenterEntity logisticsCenterEntity : logisticsCenterEntities) {
			List<WarehouseStorageEntity> warehouseStorageEntities = storeWarehouseListMap.get(logisticsCenterEntity.getStoreNo());
			if(CollectionUtils.isEmpty(warehouseStorageEntities)){
				continue;
			}
			logisticsCenterEntity.setWarehouseStorageEntities(warehouseStorageEntities.stream().filter(Objects::nonNull).collect(Collectors.toList()));
		}
	}

	public void updateLogistics(WarehouseLogisticsCenterEntity entity) {
		upsertVerifyParam(entity);
		//查询城配仓原先库存仓映射信息
		List<Integer> oldWarehouseNos = warehouseLogisticsMappingRepository.queryWarehouseNosByStoreNo(entity.getStoreNo());

		List<Integer> newWarehouseNos = new ArrayList<>();
		if(!CollectionUtils.isEmpty(entity.getWarehouseStorageEntities())){
			newWarehouseNos = entity.getWarehouseStorageEntities().stream().map(WarehouseStorageEntity::getWarehouseNo).collect(Collectors.toList());
		}
		if (!CollectionUtils.isEqualCollection(oldWarehouseNos, newWarehouseNos)){
			long taskCount = fenceChangeTaskRepository.queryHandlingTasksByStoreNo(entity.getStoreNo());
			if(taskCount > 0){
				throw new BizException("存在“待处理、区域切换中、订单切换中”的切仓任务，不可变更挂靠的库存使用仓");
			}
		}
		//设置操作人
		entity.setUpdater(UserInfoHolder.getUser() != null ? UserInfoHolder.getUser().getBizUserId() : null);
		//修改信息
		warehouseLogisticsCenterRepository.updateById(entity);
		//映射关系的处理
		handleLogisticsMapping(entity,entity.getUpdater());
	}

	/**
	 * 城配仓和库存仓映射关系的处理
	 * @param entity 城配仓信息
	 * @param adminId 操作人
	 */
	public void handleLogisticsMapping(WarehouseLogisticsCenterEntity entity,Integer adminId) {
		if(entity == null || entity.getStoreNo() == null){
			throw new BizException("处理映射关系城配仓编号不能为空");
		}
		if(CollectionUtils.isEmpty(entity.getWarehouseStorageEntities())){
			return;
		}
		Integer storeNo = entity.getStoreNo();
		LocalDateTime now = LocalDateTime.now();
		//修改库存使用仓 打包数据统一修改
		List<Integer> storeNos = fenceRepository.queryPackAllByStoreNo(storeNo);
		if (CollectionUtils.isEmpty(storeNos)) {
			storeNos = new ArrayList<>();
			storeNos.add(storeNo);
		}
		//处理仓库和城配仓的映射关系
		List<WarehouseLogisticsMappingEntity> oldMapping = warehouseLogisticsMappingRepository.queryByStoreNoList(storeNos);
		Map<Integer, List<WarehouseLogisticsMappingEntity>> oldMappingStoreMappingListMap = oldMapping.stream().collect(Collectors.groupingBy(WarehouseLogisticsMappingEntity::getStoreNo));
		List<Integer> warehouseNos = entity.getWarehouseStorageEntities().stream().map(WarehouseStorageEntity::getWarehouseNo).collect(Collectors.toList());

		List<WarehouseLogisticsMappingEntity> saveMappingList = new ArrayList<>();
		//查询城配仓是否有对应的映射关系
		for (Integer storeNoData : storeNos) {
			//不存在映射关系需要保存到数据库
			List<WarehouseLogisticsMappingEntity> mappingEntities = oldMappingStoreMappingListMap.get(storeNoData);
			if(CollectionUtils.isEmpty(mappingEntities)){
				for (Integer warehouseNo : warehouseNos) {
					WarehouseLogisticsMappingEntity saveMappingEntity = new WarehouseLogisticsMappingEntity();
					saveMappingEntity.setStoreNo(storeNoData);
					saveMappingEntity.setWarehouseNo(warehouseNo);
					saveMappingEntity.setCreator(adminId);
					saveMappingEntity.setCreateTime(now);
					saveMappingList.add(saveMappingEntity);
				}
				continue;
			}

			List<Integer> warehouseNoDataList = mappingEntities.stream().map(WarehouseLogisticsMappingEntity::getWarehouseNo).collect(Collectors.toList());
			//获取不存在数据库的仓库集合
			Collection<Integer> needSaveWarehouseNos = CollectionUtils.subtract(warehouseNos, warehouseNoDataList);
			for (Integer warehouseNo : needSaveWarehouseNos) {
				WarehouseLogisticsMappingEntity saveMappingEntity = new WarehouseLogisticsMappingEntity();
				saveMappingEntity.setStoreNo(storeNoData);
				saveMappingEntity.setWarehouseNo(warehouseNo);
				saveMappingEntity.setCreator(adminId);
				saveMappingEntity.setCreateTime(now);
				saveMappingList.add(saveMappingEntity);
			}
		}

		//批量插入
		warehouseLogisticsMappingRepository.batchSave(saveMappingList);
	}

	/**
	 * 校验信息
	 * @param entity 实体
	 */
	private void upsertVerifyParam(WarehouseLogisticsCenterEntity entity) {
		if (!entity.getStoreName().matches("^[一-龥]{1,10}$")) {
			throw new BizException("配送仓名称不符合规范");
		}
		long nameCount = warehouseLogisticsCenterRepository.countByNameAndId(entity.getStoreName(), entity.getId());
		if (nameCount > 0) {
			throw new BizException("配送仓名称重复");
		}
	}

	public void mappingDelete(Integer storeNo, Integer warehouseNo) {
		if(storeNo == null || warehouseNo == null){
			throw new BizException("城配仓编号和仓库编号不能为空");
		}
		List<Integer> storeNos = fenceRepository.queryPackAllByStoreNo(storeNo);

		for (Integer storeNoReq : storeNos) {
			long count = warehouseInventoryMappingRepository.queryCountByStoreWarehouseNo(storeNoReq,warehouseNo);
			if (count > 0) {
				WarehouseLogisticsCenterEntity storeEntity = warehouseLogisticsCenterRepository.queryByUk(storeNoReq);
				throw new BizException(storeEntity.getStoreName() + "正使用该仓的库存，请切换库存使用仓后再进行删除");
			} else {
				warehouseLogisticsMappingRepository.removeMapping(storeNoReq,warehouseNo);
			}
		}
		if(CollectionUtils.isEmpty(storeNos)){
			warehouseLogisticsMappingRepository.removeMapping(storeNo,warehouseNo);
		}
	}

	public void logisticsSave(WarehouseLogisticsCenterEntity entity) {
		//参数验证
		upsertVerifyParam(entity);
		//获取最大的城配仓编号
		int maxStoreNo = warehouseLogisticsCenterRepository.selectMaxStoreNo();

		entity.setStoreNo(maxStoreNo + 1);
		entity.setCreator(UserInfoHolder.getUser() != null ? UserInfoHolder.getUser().getBizUserId() : null);
		//保存城配仓信息
		warehouseLogisticsCenterRepository.save(entity);
		//映射关系的处理
		handleLogisticsMapping(entity,entity.getCreator());
	}

	public void closeTimeCancel(Integer storeNo) {
		warehouseLogisticsCenterRepository.cancelUpdateCloseTime(storeNo);
	}
}
