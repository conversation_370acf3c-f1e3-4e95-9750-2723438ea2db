package net.summerfarm.wnc.domain.fence.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.wnc.common.constants.AppConsts;
import net.summerfarm.wnc.common.enums.FenceEnums;

import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2023/3/16 15:30<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdCodeMsgEntity {

    private Integer id;

    /**
     * 区域编码
     */
    private String adCode;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 等级
     */
    private String level;

    /**
     * 高德id
     */
    private String gdId;

    private LocalDateTime addTime;

    private LocalDateTime updateTime;

    /**
     * 状态 0 正常 1 失效
     */
    private Integer status;

    /**
     * 围栏id
     */
    private Integer fenceId;

    public void create(){
        this.create(FenceEnums.Status.VALID);
    }

    public void create(FenceEnums.Status status){
        if (status == null){
            return;
        }
        this.status = status.getValue();
        this.addTime = LocalDateTime.now();
    }

    public String buildUk(){
        return this.city + AppConsts.Symbol.HASH_TAG +  this.area;
    };

}
