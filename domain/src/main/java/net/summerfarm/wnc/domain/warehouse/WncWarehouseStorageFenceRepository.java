package net.summerfarm.wnc.domain.warehouse;

import net.summerfarm.wnc.common.query.warehouse.WarehouseStorageFenceQuery;
import net.summerfarm.wnc.domain.warehouse.entity.WncWarehouseStorageFenceEntity;
import net.summerfarm.wnc.domain.warehouse.entity.WncWarehouseStorageFenceRuleEntity;

import java.util.List;

/**
 * Description: <br/>
 * date: 2023/3/28 17:24<br/>
 *
 * <AUTHOR> />
 */
public interface WncWarehouseStorageFenceRepository {
    /**
     * 查询仓库围栏信息
     * @param warehouseStorageFenceQuery 查询
     * @return 结果
     */
    List<WncWarehouseStorageFenceEntity> queryList(WarehouseStorageFenceQuery warehouseStorageFenceQuery);

    /**
     * 查询自营仓冲突的围栏信息
     * @param warehouseStorageFenceQuery 查询
     * @return 结果
     */
    List<WncWarehouseStorageFenceEntity> queryFenceConflict(WarehouseStorageFenceQuery warehouseStorageFenceQuery);
}
