package net.summerfarm.wnc.domain.warehouse;

import lombok.RequiredArgsConstructor;
import net.summerfarm.wnc.common.base.WncAssert;
import net.summerfarm.wnc.common.query.warehouse.WarehouseLogisticsMappingQuery;
import net.summerfarm.wnc.common.query.warehouse.WncWarehouseStorageTenantQuery;
import net.summerfarm.wnc.domain.fence.DeliveryFenceRepository;
import net.summerfarm.wnc.domain.fence.entity.AdCodeMsgEntity;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseLogisticsMappingEntity;
import net.summerfarm.wnc.domain.warehouse.entity.WncWarehouseStorageTenantEntity;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/4/6 11:08<br/>
 *
 * <AUTHOR> />
 */
@Service
@RequiredArgsConstructor
public class WncWarehouseStorageTenantDomain {

    private final WncWarehouseStorageTenantRepository wncWarehouseStorageTenantRepository;

    public void bindingTenant(WncWarehouseStorageTenantEntity rsWarehouseStorageTenantEntity) {
        //判断是否已经存在
        WncWarehouseStorageTenantEntity rsTenantData = wncWarehouseStorageTenantRepository
                .queryUk(rsWarehouseStorageTenantEntity.getWarehouseNo(),rsWarehouseStorageTenantEntity.getTenantId());
        if(rsTenantData != null){
            throw new BizException("已存在此数据");
        }
        //保存
        wncWarehouseStorageTenantRepository.save(rsWarehouseStorageTenantEntity);
    }

    public void unbindingTenant(WncWarehouseStorageTenantEntity rsWarehouseStorageTenantEntity) {
        wncWarehouseStorageTenantRepository.remove(rsWarehouseStorageTenantEntity);
    }

}
