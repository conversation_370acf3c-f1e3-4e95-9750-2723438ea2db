package net.summerfarm.wnc.domain.fence.entity;

import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2024-10-12 16:29:33
 * @version 1.0
 *
 */
@Data
public class FenceChannelBusinessWhiteConfigEntity {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 围栏ID
	 */
	private Integer fenceId;

	/**
	 * 下单渠道类型 1000鲜沐平台客户 2000鲜沐大客户 3000 Saas客户
	 */
	private String orderChannelType;

	/**
	 * 租户ID
	 */
	private Long tenantId;

	/**
	 * 作用域下的业务ID 0是全部
	 */
	private String scopeChannelBusinessId;

	/**
	 * 业务方名称
	 */
	private String scopeChannelBusinessName;

	
}