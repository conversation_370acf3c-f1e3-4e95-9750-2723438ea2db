package net.summerfarm.wnc.domain.warehouse;

import net.summerfarm.wnc.common.query.warehouse.WarehouseTakeStandardQuery;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseTakeStandardEntity;

import java.util.List;

/**
 * Description: <br/>
 * date: 2023/4/4 18:56<br/>
 *
 * <AUTHOR> />
 */
public interface WarehouseTakeStandardRepository {
    /**
     * 查询
     * @param warehouseTakeStandardQuery
     * @return
     */
    List<WarehouseTakeStandardEntity> queryList(WarehouseTakeStandardQuery warehouseTakeStandardQuery);
}
