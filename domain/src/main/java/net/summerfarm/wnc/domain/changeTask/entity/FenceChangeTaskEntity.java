package net.summerfarm.wnc.domain.changeTask.entity;

import lombok.Data;
import net.summerfarm.wnc.client.mq.WncMqConstants;
import net.summerfarm.wnc.client.mq.msg.out.FenceChangeAreaHandleMsg;
import net.summerfarm.wnc.common.enums.FenceChangeTaskEnums;
import net.summerfarm.wnc.domain.changeTask.vo.FenceChangeRemarkVO;
import net.summerfarm.wnc.domain.fence.entity.AdCodeMsgEntity;
import net.xianmu.common.exception.BizException;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description:围栏切仓任务实体
 * date: 2023/8/24 17:08
 *
 * <AUTHOR>
 */
@Data
public class FenceChangeTaskEntity implements Serializable {

    private static final long serialVersionUID = 1649098422216599366L;

    /**
     * 切仓任务ID
     */
    private Long id;

    /**
     * 围栏ID
     */
    private Integer fenceId;

    /**
     * 围栏名称
     */
    private String fenceName;

    /**
     * 类型，0：切仓，1：切围栏
     */
    private FenceChangeTaskEnums.Type type;

    /**
     * 状态，0：待处理，1：已取消，2：已完成，10：区域切换中，15：订单切换中，20：处理失败
     */
    private FenceChangeTaskEnums.Status status;

    /**
     * 运营服务区编号
     */
    private Integer areaNo;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 目标围栏ID/目标城配仓编号
     */
    private Integer targetNo;

    /**
     * 区域集合
     */
    private List<AdCodeMsgEntity> adCodeMsgEntities;

    /**
     * 切仓说明值对象
     */
    private FenceChangeRemarkVO fenceChangeRemarkVO;

    /**
     * 执行时间
     */
    private LocalDateTime exeTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 操作人adminId
     */
    private Integer creatorId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 创建围栏切仓任务
     */
    public void create(String creator, Integer operator) {
        this.status = FenceChangeTaskEnums.Status.WAIT;
        this.createTime = LocalDateTime.now();
        this.creator = creator;
        this.creatorId = operator;
    }

    /**
     * 取消围栏切仓任务
     */
    public FenceChangeTaskEntity cancel(String updater, Integer operator) {
        this.status = FenceChangeTaskEnums.Status.CANCELED;
        this.updateTime = LocalDateTime.now();
        this.updater = updater;
        this.creatorId = operator;
        FenceChangeTaskEntity cancel = new FenceChangeTaskEntity();
        cancel.setId(this.id);
        cancel.setStatus(this.status);
        cancel.setUpdateTime(this.updateTime);
        cancel.setUpdater(this.updater);
        cancel.setCreatorId(this.creatorId);
        return cancel;
    }

    /**
     * 围栏切仓任务是否取消
     */
    public boolean isCancel() {
        return this.status == FenceChangeTaskEnums.Status.CANCELED;
    }

    /**
     * 围栏切仓任务是否待处理
     */
    public boolean isWaitHandle() {
        return this.status == FenceChangeTaskEnums.Status.WAIT;
    }

    public boolean isFenceType(){
        return this.type == FenceChangeTaskEnums.Type.FENCE;
    }

    public void resetFenceChangeRemark(FenceChangeRemarkVO fenceChangeRemarkVO){
        if (fenceChangeRemarkVO == null){
            return;
        }
        this.fenceChangeRemarkVO = fenceChangeRemarkVO;
        this.exeTime = fenceChangeRemarkVO.getExeTime();
    }

    public String getFenceProvinceStr(){
        if (CollectionUtils.isEmpty(this.adCodeMsgEntities)){
            throw new BizException("获取围栏切仓行政省异常");
        }
        return adCodeMsgEntities.stream().map(AdCodeMsgEntity::getProvince).findFirst().orElseThrow(() -> new BizException("获取围栏切仓行政省异常"));
    }

    public String getFenceCityNameStr(){
        if (CollectionUtils.isEmpty(this.adCodeMsgEntities)){
            throw new BizException("获取围栏切仓行政市异常");
        }
        return adCodeMsgEntities.stream().map(AdCodeMsgEntity::getCity).findFirst().orElseThrow(() -> new BizException("获取围栏切仓行政市异常"));
    }

    public String getFenceAreasNameStr(){
        if (CollectionUtils.isEmpty(this.adCodeMsgEntities)){
            return null;
        }
        return adCodeMsgEntities.stream().map(AdCodeMsgEntity::getArea).collect(Collectors.joining("、"));
    }

    public List<String> getFenceAreasName(){
        if (CollectionUtils.isEmpty(this.adCodeMsgEntities)){
            return Collections.emptyList();
        }
        return adCodeMsgEntities.stream().map(AdCodeMsgEntity::getArea).collect(Collectors.toList());
    }


    public FenceChangeTaskEntity execute(FenceChangeTaskEnums.Status status) {
        this.status = status;
        this.updateTime = LocalDateTime.now();
        FenceChangeTaskEntity update = new FenceChangeTaskEntity();
        update.setId(this.id);
        update.setStatus(this.status);
        update.setUpdateTime(this.updateTime);
        return update;
    }

    public LocalDate getExeTimePlus2Date(){
        //执行切仓需获取配送时间T+1之后的切仓订单(T+2的时间点)
        LocalDate localDate = this.exeTime.toLocalDate();
        return localDate.plusDays(2);
    }

    public FenceChangeAreaHandleMsg buildMqMsg() {
        FenceChangeAreaHandleMsg msg = new FenceChangeAreaHandleMsg();
        msg.setFenceProvince(this.getFenceProvinceStr());
        msg.setFenceCity(this.getFenceCityNameStr());
        msg.setFenceAreas(this.getFenceAreasName());
        msg.setOldStoreNo(this.fenceChangeRemarkVO.getOldStoreNo());
        msg.setNewStoreNo(this.fenceChangeRemarkVO.getNewStoreNo());
        msg.setOldAreaNo(this.fenceChangeRemarkVO.getOldAreaNo());
        msg.setNewAreaNo(this.fenceChangeRemarkVO.getNewAreaNo());
        return msg;
    }
}
