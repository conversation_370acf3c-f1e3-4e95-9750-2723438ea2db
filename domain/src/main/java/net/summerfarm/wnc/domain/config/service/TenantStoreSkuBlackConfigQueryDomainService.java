package net.summerfarm.wnc.domain.config.service;

import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.domain.config.entity.TenantStoreSkuBlackConfigEntity;
import net.summerfarm.wnc.domain.config.repository.TenantStoreSkuBlackConfigQueryRepository;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseInventoryMappingEntity;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Description: 租户城配仓sku黑名单查询服务<br/>
 * date: 2024/8/26 15:26<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TenantStoreSkuBlackConfigQueryDomainService {

    private final TenantStoreSkuBlackConfigQueryRepository tenantStoreSkuBlackConfigQueryRepository;

    /**
     * 过滤非黑名单的映射关系
     * @param warehouseInventoryMappingEntities 映射关系
     * @param tenantId 租户ID
     */
    public List<WarehouseInventoryMappingEntity> filterNotInBlackConfigMapping(List<WarehouseInventoryMappingEntity> warehouseInventoryMappingEntities,Long tenantId) {
        if(CollectionUtils.isEmpty(warehouseInventoryMappingEntities)){
            return Collections.emptyList();
        }
        if(tenantId == null){
            return Collections.emptyList();
        }
        List<String> skuList = warehouseInventoryMappingEntities.stream().map(WarehouseInventoryMappingEntity::getSku).distinct().collect(Collectors.toList());

        List<TenantStoreSkuBlackConfigEntity> tenantStoreSkuBlackConfigEntityList = tenantStoreSkuBlackConfigQueryRepository.queryByTenantIdAndSkuList(tenantId, skuList);
        if(CollectionUtils.isEmpty(tenantStoreSkuBlackConfigEntityList)){
            log.info("查询租户SKU黑名单为空,租户ID:{},SKU集合:{}",tenantId,skuList);
            return warehouseInventoryMappingEntities;
        }
        log.info("查询租户SKU黑名单,租户ID:{},SKU集合:{},结果:{}",tenantId,skuList, JSON.toJSONString(tenantStoreSkuBlackConfigEntityList));

        Map<String, TenantStoreSkuBlackConfigEntity> storeNoSku2Map = tenantStoreSkuBlackConfigEntityList.stream().collect(Collectors.toMap(config -> config.getStoreNo() + "#" + config.getSku(), config -> config, (o, n) -> n));

        List<WarehouseInventoryMappingEntity> filteredEntities = warehouseInventoryMappingEntities.stream().filter(mapping -> {
            TenantStoreSkuBlackConfigEntity tenantStoreSkuBlackConfigEntity = storeNoSku2Map.get(mapping.getStoreNo() + "#" + mapping.getSku());
            return tenantStoreSkuBlackConfigEntity == null;
        }).collect(Collectors.toList());

        // 将新的列表赋值给原始参数
        warehouseInventoryMappingEntities = filteredEntities;

        log.info("过滤租户SKU黑名单,租户ID:{},SKU集合:{},结果:{}",tenantId,skuList, JSON.toJSONString(warehouseInventoryMappingEntities));

        return warehouseInventoryMappingEntities;
    }
}
