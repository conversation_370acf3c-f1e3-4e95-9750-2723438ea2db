package net.summerfarm.wnc.domain.closeTime.entity;

import lombok.Data;
import net.summerfarm.wnc.common.enums.CloseTimeConfigEnums;
import net.summerfarm.wnc.domain.closeTime.param.CloseTimeAreaConfigCommandParam;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Objects;

/**
 * Description:截单时间区域配置实体
 * date: 2024/3/20 11:45
 *
 * <AUTHOR>
 */
@Data
public class CloseTimeAreaConfigEntity implements Serializable {

    private static final long serialVersionUID = -1094489541802188641L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 品牌ID
     */
    private Long brandId;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 截单时间
     */
    private LocalTime closeTime;

    /**
     * 更新截单时间
     */
    private LocalTime updateCloseTime;

    /**
     * 有无更新标识，0：无，1：有
     */
    private CloseTimeConfigEnums.UpdateFlag updateFlag;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public CloseTimeAreaConfigCommandParam takeEffect() {
        CloseTimeAreaConfigCommandParam commandParam = new CloseTimeAreaConfigCommandParam();
        commandParam.setId(this.id);
        commandParam.setCloseTime(this.updateCloseTime);
        commandParam.setUpdateCloseTime(null);
        commandParam.setUpdateFlag(CloseTimeConfigEnums.UpdateFlag.NO.getValue());
        commandParam.setUpdateTime(LocalDateTime.now());
        return commandParam;
    }

    public String buildAreaUk(){
        return String.format("%s#%s", this.city, this.area);
    }

}
