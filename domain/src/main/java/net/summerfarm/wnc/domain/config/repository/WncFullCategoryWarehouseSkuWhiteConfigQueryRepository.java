package net.summerfarm.wnc.domain.config.repository;



import com.github.pagehelper.PageInfo;
import java.util.List;
import java.util.Map;

import net.summerfarm.wnc.domain.config.entity.WncFullCategoryWarehouseSkuWhiteConfigEntity;
import net.summerfarm.wnc.domain.config.param.query.WncFullCategoryWarehouseSkuWhiteConfigQueryParam;



/**
*
* <AUTHOR>
* @date 2025-01-06 15:31:26
* @version 1.0
*
*/
public interface WncFullCategoryWarehouseSkuWhiteConfigQueryRepository {

    PageInfo<WncFullCategoryWarehouseSkuWhiteConfigEntity> getPage(WncFullCategoryWarehouseSkuWhiteConfigQueryParam param);

    WncFullCategoryWarehouseSkuWhiteConfigEntity selectById(Long id);

    List<WncFullCategoryWarehouseSkuWhiteConfigEntity> selectByCondition(WncFullCategoryWarehouseSkuWhiteConfigQueryParam param);

    /**
     * 根据sku和仓库号查询白名单配置
     * @param skus SKU集合
     * @param warehouseNos 仓库号集合
     * @return 结果 key为sku,value为仓库号集合
     */
    Map<String, List<Integer>> selectWhiteConfigMapBySkusWarehouseNos(List<String> skus, List<Integer> warehouseNos);
}