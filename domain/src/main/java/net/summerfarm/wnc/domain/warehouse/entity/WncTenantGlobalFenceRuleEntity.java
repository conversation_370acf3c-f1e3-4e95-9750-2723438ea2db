package net.summerfarm.wnc.domain.warehouse.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2023/4/7 13:48<br/>
 *
 * <AUTHOR> />
 */
@Data
public class WncTenantGlobalFenceRuleEntity {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 全局配送规则 0自营仓优先 1三方仓优先
     */
    private Integer globalDeliveryRule;

    /**
     * 租户ID
     */
    private Long tenantId;

}
