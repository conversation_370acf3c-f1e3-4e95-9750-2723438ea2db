package net.summerfarm.wnc.domain.fence;

import net.summerfarm.wnc.domain.fence.entity.SkuManyWarehouseEntity;

import java.util.List;
import java.util.Map;

/**
 * Description: <br/>
 * date: 2023/8/2 16:38<br/>
 *
 * <AUTHOR> />
 */
public interface AreaSkuRepository {
    /**
     * 根据运营区域编号查询上架的sku
     * @param areaNo 运营区域编号
     */
    List<String> queryAreaOnSaleSku(Integer areaNo);

    /**
     * 根据运营区域编号查询sku未上架
     * @param areaNo 运营区域编号
     */
    List<String> queryAreaNoSaleSku(Integer areaNo);

    /**
     * 根据运营区域编号查询区域sku信息
     * @param areaNo 运营区域编号
     * @return key 0下架 1上架 value sku集合
     */
    Map<Integer, List<String>> querySkuByAreaNo(Integer areaNo);

    /**
     * 根据运营区域、城配仓编号查询运营区域仓库集合
     * @param areaNo 运营区域编号
     * @param storeNos 城配仓编号集合
     * @return 仓库集合
     */
    List<Integer> queryWareNoByAreaNoStoreNo(Integer areaNo,List<Integer> storeNos);

    /**
     * 查询同一个运营区域存在多个仓库的sku
     * @param areaNo 运营区域编号
     * @param warehouseNos 仓库编号集合
     * @return 多个仓库的sku集合
     */
    List<SkuManyWarehouseEntity> querySkuManyWarehouse(Integer areaNo, List<Integer> warehouseNos);
}
