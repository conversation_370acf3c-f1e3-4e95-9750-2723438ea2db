package net.summerfarm.wnc.domain.config.service;


import net.summerfarm.wnc.domain.config.repository.WncStockingTimeConfigQueryRepository;
import net.summerfarm.wnc.domain.config.repository.WncStockingTimeConfigCommandRepository;
import net.summerfarm.wnc.domain.config.entity.WncStockingTimeConfigEntity;
import net.summerfarm.wnc.domain.config.param.command.WncStockingTimeConfigCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 备货时长配置领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-04-15 14:51:44
 * @version 1.0
 *
 */
@Service
public class WncStockingTimeConfigCommandDomainService {


    @Autowired
    private WncStockingTimeConfigCommandRepository wncStockingTimeConfigCommandRepository;
    @Autowired
    private WncStockingTimeConfigQueryRepository wncStockingTimeConfigQueryRepository;



    public WncStockingTimeConfigEntity insert(WncStockingTimeConfigCommandParam param) {
        return wncStockingTimeConfigCommandRepository.insertSelective(param);
    }


    public int update(WncStockingTimeConfigCommandParam param) {
        return wncStockingTimeConfigCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return wncStockingTimeConfigCommandRepository.remove(id);
    }
}
