package net.summerfarm.wnc.domain.warehouse.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/3/28 16:36<br/>
 *
 * <AUTHOR> />
 */
@Data
public class WarehouseStorageEntity {

    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 仓库负责人
     */
    private Integer manageAdminId;

    /**
     * 仓库类型：0、本部仓 1、外部仓 2、合伙人仓
     */
    private Integer type;

    /**
     * 仓库所属合伙人
     */
    private Integer areaManageId;

    /**
     * 开放状态：0、不开放 1、开放
     */
    private Integer status;

    /**
     * 仓库地址
     */
    private String address;

    /**
     * 高德poi
     */
    private String poiNote;

    /**
     * 邮件接收人
     */
    private String mailToAddress;

    /**
     * 修改人
     */
    private Integer updater;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 联系人
     */
    private String personContact;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 所属租户0鲜沐
     */
    private Long tenantId;

    /**
     * 城配仓编号
     */
    private List<Integer> storeNoList;

    /**
     * 仓库业务表 产能 预约提前期
     */
    private WarehouseStorageCenterBusEntity warehouseStorageCenterBusEntity;

    /**
     * 仓库收货标准
     */
    private List<WarehouseTakeStandardEntity> warehouseTakeStandardEntities;

    /**
     * 仓库围栏信息
     */
    private List<WncWarehouseStorageFenceEntity> warehouseStorageFenceEntities;

    /**
     * 作业时间
     */
    private List<WarehouseStorageCenterWorkEntity> warehouseStorageCenterWorkEntities;
    /**
     * 距离仓库距离
     */
    private BigDecimal distance;

    /**
     * sku
     */
    private String sku;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 仓库管理负责人信息
     */
    private AdminEntity adminEntity;

    /**
     * 仓库照片
     */
    private String warehousePic;
}
