package net.summerfarm.wnc.domain.config.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.common.enums.WarehouseLogisticsCenterEnums;
import net.summerfarm.wnc.common.query.fence.FenceQuery;
import net.summerfarm.wnc.domain.config.repository.ContactConfigCommandRepository;
import net.summerfarm.wnc.domain.config.entity.ContactConfigEntity;
import net.summerfarm.wnc.domain.config.param.command.ContactConfigCommandParam;
import net.summerfarm.wnc.domain.fence.DeliveryFenceRepository;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.summerfarm.wnc.domain.fence.param.AddressParam;
import net.summerfarm.wnc.domain.warehouse.WarehouseLogisticsCenterRepository;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseLogisticsCenterEntity;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * Description:联系人配置操作领域服务
 * date: 2024/1/3 11:17
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ContactConfigCommandDomainService {

    private final ContactConfigCommandRepository contactConfigCommandRepository;
    private final WarehouseLogisticsCenterRepository warehouseLogisticsCenterRepository;
    private final DeliveryFenceRepository deliveryFenceRepository;

    @Deprecated
    @Transactional(rollbackFor = Exception.class)
    public void updateConfig(ContactConfigEntity existedContactConfig, ContactConfigCommandParam param, String nickname) {
        if (existedContactConfig == null){
            //不存在 即代表该门店地址原先是城配履约方式(也有可能是配置门店ID错误导致未查询出来)
            if (Objects.equals(param.getFulfillmentMethod(), WarehouseLogisticsCenterEnums.FulfillmentType.EXPRESS_DELIVERY.getValue())){
                //城配履约->快递履约
                //校验所选城配仓是否存在，是否是快递履约类型
                this.validateStoreNo(param.getStoreNo());
                //创建联系人配置
                contactConfigCommandRepository.save(param);
            }
        }else {
            //存在 即代表该门店地址原先是快递履约方式
            if (Objects.equals(param.getFulfillmentMethod(), WarehouseLogisticsCenterEnums.FulfillmentType.EXPRESS_DELIVERY.getValue())){
                //快递履约->快递履约 判断城配仓编号是否变更
                //校验所选城配仓是否存在，是否是快递履约类型
                this.validateStoreNo(param.getStoreNo());
                if (Objects.equals(param.getStoreNo(), existedContactConfig.getStoreNo())){
                    //城配履约 城配仓编号相同 默认处理成功
                    return;
                }
                ContactConfigEntity update = existedContactConfig.updateStoreNo(param.getStoreNo());
                contactConfigCommandRepository.update(update);

            }else {
                //快递履约->城配履约
                //校验地址是否在围栏范围内
                AddressParam addressParam = param.getAddressParam();
                FenceEntity fenceEntity = deliveryFenceRepository.queryOneFenceByProvinceCityArea(FenceQuery.builder()
                        .city(addressParam.getCity())
                        .area(addressParam.getArea())
                        .build());
                if (fenceEntity == null){
                    throw new BizException("该地区未开启配送，不可作为城配履约");
                }
                //删除联系人配置
                contactConfigCommandRepository.remove(existedContactConfig.getId());
            }
        }
    }

    @Deprecated
    private void validateStoreNo(Integer storeNo) {
        if (storeNo == null){
            throw new BizException("快递履约方式城配仓编号不能为空");
        }
        WarehouseLogisticsCenterEntity warehouseLogisticsCenterEntity = warehouseLogisticsCenterRepository.queryByUk(storeNo);
        if (warehouseLogisticsCenterEntity == null){
            throw new BizException("无效城配仓编号");
        }
        if (Objects.equals(warehouseLogisticsCenterEntity.getStatus(), WarehouseLogisticsCenterEnums.Status.NO_VALID.getValue())){
            throw new BizException("该城配仓已停配");
        }
        if (!Objects.equals(warehouseLogisticsCenterEntity.getFulfillmentType(), WarehouseLogisticsCenterEnums.FulfillmentType.EXPRESS_DELIVERY)){
            throw new BizException("非快递城配仓， 不可作为快递履约");
        }
    }

    public void save(ContactConfigEntity contactConfigEntity) {
        if(contactConfigEntity == null){
            return;
        }
        //校验城配仓编号
        this.checkStoreNo(contactConfigEntity);
        contactConfigCommandRepository.save(contactConfigEntity);
    }

    private void checkStoreNo(ContactConfigEntity contactConfigEntity) {
        Integer storeNo = contactConfigEntity.getStoreNo();
        WarehouseLogisticsCenterEntity warehouseLogisticsCenterEntity = warehouseLogisticsCenterRepository.queryByUk(storeNo);
        if (warehouseLogisticsCenterEntity == null) {
            throw new BizException("无效城配仓编号");
        }
    }

    public void update(ContactConfigEntity contactConfigEntity) {
        if(contactConfigEntity == null){
            return;
        }
        //校验城配仓编号
        this.checkStoreNo(contactConfigEntity);
        contactConfigCommandRepository.update(contactConfigEntity);
    }
}
