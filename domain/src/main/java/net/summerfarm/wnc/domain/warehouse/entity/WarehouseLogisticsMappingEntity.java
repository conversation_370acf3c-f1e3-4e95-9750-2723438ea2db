package net.summerfarm.wnc.domain.warehouse.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2023/4/7 16:14<br/>
 *
 * <AUTHOR> />
 */
@Data
public class WarehouseLogisticsMappingEntity {

    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 物流中心编号（配送仓编号）
     */
    private Integer storeNo;

    /**
     * 仓库编号（库存仓编号）
     */
    private Integer warehouseNo;

    /**
     * 创建人adminId
     */
    private Integer creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 仓库信息
     */
    private WarehouseStorageEntity warehouseStorageEntity;

}
