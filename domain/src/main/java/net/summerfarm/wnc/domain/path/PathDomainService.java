package net.summerfarm.wnc.domain.path;

import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.common.enums.PathConfigEnums;
import net.summerfarm.wnc.common.util.LocalDateUtil;
import net.summerfarm.wnc.domain.path.entity.PathConfigEntity;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.user.UserInfoHolder;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjuster;
import java.time.temporal.WeekFields;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: 路线领域服务<br/>
 * date: 2023/11/29 14:26<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PathDomainService {

    private final PathConfigRepository pathConfigRepository;

    public void saveOrUpdateConfig(PathConfigEntity reqEntity) {
        //根据uk查询数据是否存在
        PathConfigEntity dataPathEntity = pathConfigRepository.queryByUk(reqEntity.getBeginOutNo(), reqEntity.getEndOutNo(), reqEntity.getBusinseeType().getValue());
        if(dataPathEntity == null){
            reqEntity.setCreator(UserInfoHolder.getUserRealName());
            //不存在则新增
            pathConfigRepository.save(reqEntity);
        }else{
            //存在则更新
            reqEntity.setId(dataPathEntity.getId());
            reqEntity.setUpdater(UserInfoHolder.getUserRealName());
            pathConfigRepository.updateById(reqEntity);
        }
    }

    public void updatePathLastTime() {
        //查询所有的路线配置数据
        List<PathConfigEntity> pathConfigEntities = pathConfigRepository.queryAll();
        //设置下一个配送路线日期
        this.queryLastTime(pathConfigEntities,null);
        //批量更新
        pathConfigRepository.batchUpdate(pathConfigEntities);
    }

    /**
     * 获取下一个配送路线的日期
     * @param pathConfigEntities 路线配置
     * @param expectLastTime    期望路线时间
     */
    public void queryLastTime(List<PathConfigEntity> pathConfigEntities,LocalDate expectLastTime) {
        if(CollectionUtils.isEmpty(pathConfigEntities)){
            throw new BizException("路线配置不能为空");
        }
        if(expectLastTime == null){
            expectLastTime = LocalDate.now();
        }
        //根据周期计算下一个日期
        for (PathConfigEntity pathConfigEntity : pathConfigEntities) {
            LocalDate copyExpectLastTime = JSON.parseObject(JSON.toJSONString(expectLastTime), LocalDate.class);
            //上一次路线时间
            LocalDateTime lastTime = pathConfigEntity.getLastTime();
            //获取周期
            String frequent = pathConfigEntity.getFrequent();
            List<String> weekDayList = Arrays.asList(frequent.split(","));
            if(CollectionUtils.isEmpty(weekDayList)){
                log.error("当前配置没有配置周期:{}", JSON.toJSONString(pathConfigEntity));
                continue;
            }
            weekDayList.sort(Comparator.comparing(String::valueOf));

            if(pathConfigEntity.getFrequentMethod() == PathConfigEnums.FrequentMethod.ONE_WEEK){
                //当前时间晚于上一次时间 则取下一个周期
                if(copyExpectLastTime.isBefore(lastTime.toLocalDate())){
                    continue;
                }
                while (!weekDayList.contains(copyExpectLastTime.getDayOfWeek().getValue()+"")) {
                    copyExpectLastTime = copyExpectLastTime.plusDays(1);
                }
                pathConfigEntity.setLastTime(copyExpectLastTime.atStartOfDay());
            }else{
                String firstConfigDay = weekDayList.get(0);
                //每两周 当前时间晚于上一次时间 则取下一个周期
                if(copyExpectLastTime.isBefore(lastTime.toLocalDate())){
                    continue;
                }
                //如果不在周期里面需要循环获取下一个周期
                while (!weekDayList.contains(copyExpectLastTime.getDayOfWeek().getValue()+"")) {
                    copyExpectLastTime = copyExpectLastTime.plusDays(1);
                }
                //是否相差两周或者相同的一周
                if(LocalDateUtil.isSameWeek(lastTime.toLocalDate(),copyExpectLastTime)){
                    pathConfigEntity.setLastTime(copyExpectLastTime.atStartOfDay());
                }else{
                    //不同的一周 并且期望时间在上一次时间之后
                    while(!LocalDateUtil.isSameWeek(lastTime.toLocalDate(),copyExpectLastTime) && copyExpectLastTime.isAfter(lastTime.toLocalDate())){
                        lastTime = lastTime.plusWeeks(2);
                        //相同周
                        if(LocalDateUtil.isSameWeek(lastTime.toLocalDate(),copyExpectLastTime)){
                            pathConfigEntity.setLastTime(copyExpectLastTime.atStartOfDay());
                        }
                    }
                    //不相同的周，要取到下一个周期的的最早的时间
                    if(!lastTime.getDayOfWeek().toString().equals(firstConfigDay)){
                        lastTime = lastTime.minusDays(lastTime.getDayOfWeek().getValue() - Integer.parseInt(firstConfigDay));
                        pathConfigEntity.setLastTime(lastTime);
                    }
                }
            }
        }
    }

    /**
     * 查询符合期望配送日期的路线配置
     * @param pathConfigEntities 路线配置数据
     * @param expectLastTime    期望时间
     * @return
     */
    public List<PathConfigEntity> expectLastTimeReallocatePath(List<PathConfigEntity> pathConfigEntities, LocalDate expectLastTime) {
        if(CollectionUtils.isEmpty(pathConfigEntities) || expectLastTime == null){
            return Collections.emptyList();
        }

        //直接符合期望的时间的路线配置
        List<PathConfigEntity> rightExpectLastTimePathConfigList = pathConfigEntities.stream().filter(config -> config.getLastTime().equals(expectLastTime.atStartOfDay())).collect(Collectors.toList());
        List<PathConfigEntity> resultList = new ArrayList<>(rightExpectLastTimePathConfigList);
        //过滤当前下次时间在期望时间之前的路线配置
        List<PathConfigEntity> couldCalculateList = pathConfigEntities.stream().filter(config -> !(expectLastTime.atStartOfDay().compareTo(config.getLastTime()) <= 0)).collect(Collectors.toList());

        while (couldCalculateList.size() > 0){
            //查询下一个配送路线日期
            this.queryLastTime(couldCalculateList,expectLastTime);
            //保存匹配日期的路线配置
            resultList.addAll(pathConfigEntities.stream().filter(config -> config.getLastTime().equals(expectLastTime.atStartOfDay())).collect(Collectors.toList()));
            //过滤当前下次时间在期望时间之前的路线配置
            couldCalculateList = couldCalculateList.stream().filter(config -> !(expectLastTime.atStartOfDay().compareTo(config.getLastTime()) <= 0)).collect(Collectors.toList());
        }

        return resultList.stream().distinct().collect(Collectors.toList());
    }
}
