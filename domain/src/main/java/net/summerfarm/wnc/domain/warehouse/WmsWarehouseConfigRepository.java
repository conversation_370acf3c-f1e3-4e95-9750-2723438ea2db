package net.summerfarm.wnc.domain.warehouse;

import net.summerfarm.wnc.domain.warehouse.entity.WmsWarehouseConfigEntity;

import java.util.List;

/**
 * Description: <br/>
 * date: 2023/8/28 11:17<br/>
 *
 * <AUTHOR> />
 */
public interface WmsWarehouseConfigRepository {
    /**
     * 根据仓库编号查询库位信息
     * @param warehouseNos 仓库信息
     * @return 结果
     */
    List<WmsWarehouseConfigEntity> queryCabinetByWarehouseNos(List<Integer> warehouseNos);

}
