package net.summerfarm.wnc.domain.fence.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2023-06-20
 **/
@Data
public class OutLandMerchantEntity {

	private Long mId;

	/**
	 * 商户类型
	 */
	private Integer roleId;

	/**
	 * 商户名称
	 */
	private String mname;

	/**
	 * 主联系人
	 */
	private String mcontact;

	/**
	 * 微信用户id
	 */
	private String openid;

	/**
	 * 手机
	 */
	private String phone;

	/**
	 * 审核状态：0、审核通过 1、审核中 2、审核未通过 3、账号被拉黑
	 */
	private Integer islock;

	/**
	 * 等级
	 */
	private Integer rankId;

	/**
	 * 注册时间
	 */
	private LocalDateTime registerTime;

	/**
	 * 登录时间
	 */
	private LocalDateTime loginTime;

	/**
	 * 6位邀请码
	 */
	private String invitecode;

	/**
	 * 用户分享码
	 */
	private String channelCode;

	/**
	 * 邀请人渠道码
	 */
	private String inviterChannelCode;

	/**
	 * 审核时间
	 */
	private LocalDateTime auditTime;

	/**
	 * 审核人
	 */
	private Integer auditUser;

	/**
	 * 营业执照路径
	 */
	private String businessLicense;

	/**
	 * 省
	 */
	private String province;

	/**
	 * 市
	 */
	private String city;

	/**
	 * 地区
	 */
	private String area;

	/**
	 * 详细地址
	 */
	private String address;

	/**
	 * 商家腾讯地图坐标
	 */
	private String poiNote;

	/**
	 * 审核备注
	 */
	private String remark;

	/**
	 * 店铺招牌
	 */
	private String shopSign;

	/**
	 * 其他证明照片
	 */
	private String otherProof;

	/**
	 * 上次下单时间
	 */
	private LocalDateTime lastOrderTime;

	private Integer areaNo;

	/**
	 * 1大客户\2大连锁3\小连锁\4单点
	 */
	private String size;

	/**
	 * 客户类型
	 */
	private String type;

	/**
	 * 商圈
	 */
	private String tradeArea;

	/**
	 * 商圈组
	 */
	private String tradeGroup;

	private String unionid;

	private String mpOpenid;

	/**
	 * 用户ID
	 */
	private Integer adminId;

	/**
	 * 1是直营 2是加盟
	 1 账期 2  现结
	 */
	private Integer direct;

	/**
	 * 1服务区内 2服务区外
	 */
	private Integer server;

	private Integer popView;

	/**
	 * 会员当月积分
	 */
	private BigDecimal memberIntegral;

	/**
	 * 会员等级
	 */
	private Integer grade;

	private Integer skuShow;

	/**
	 * 余额
	 */
	private BigDecimal rechargeAmount;

	/**
	 * 可提现金额
	 */
	private BigDecimal cashAmount;

	/**
	 * cash_amount更新时间
	 */
	private LocalDateTime cashUpdateTime;

	/**
	 * 配送单是否展示价格
	 */
	private Boolean showPrice;

	/**
	 * 账号合并人
	 */
	private String mergeAdmin;

	/**
	 * 账号和并时间
	 */
	private LocalDateTime mergeTime;

	/**
	 * 首次登录弹窗：0、未弹 1、已弹
	 */
	private Integer firstLoginPop;

	/**
	 * 更换账号绑定弹窗：0、未弹 1、已弹或未更换账号绑定
	 */
	private Integer changePop;

	/**
	 * 拉黑备注
	 */
	private String pullBlackRemark;

	/**
	 * 操作人
	 */
	private String pullBlackOperator;

	/**
	 * 门牌号
	 */
	private String houseNumber;

	/**
	 * 企业品牌
	 */
	private String companyBrand;

	/**
	 * 是否选择线索池 0 不是 1 是
	 */
	private Integer cluePool;

	/**
	 * 大客户类型: ka,批发大客户,普通
	 */
	private String merchantType;

	/**
	 * 规模
	 */
	private String enterpriseScale;

	/**
	 * 修改时间
	 */
	private LocalDateTime updateTime;

	private Integer examineType;

	/**
	 * 开关状态 0 开（展示） 1 关（不展示）
	 */
	private Integer displayButton;

	/**
	 * 运营状态:正常(0),倒闭(1)
	 */
	private Integer operateStatus;

	/**
	 * 更新人adminId
	 */
	private Integer updater;
}
