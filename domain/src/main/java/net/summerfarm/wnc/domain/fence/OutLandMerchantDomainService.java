package net.summerfarm.wnc.domain.fence;

import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.wnc.domain.fence.entity.OutLandMerchantEntity;
import net.summerfarm.wnc.domain.fence.valueObject.FenceBigCustomerValueObject;
import net.summerfarm.wnc.domain.warehouse.AdminRepository;
import net.summerfarm.wnc.domain.warehouse.entity.AdminEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2023-06-20
 **/
@Service
public class OutLandMerchantDomainService {

	@Autowired
	private OutLandMerchantRepository outLandMerchantRepository;

	@Autowired
	private AdminRepository adminRepository;

	/**
	 * 查询客户截单时间
	 *
	 * @param merchantId 鲜沐门店ID
	 * @return 大客户截单信息
	 */
	public FenceBigCustomerValueObject queryBigCustomerCloseTime(Long merchantId) {
		FenceBigCustomerValueObject fenceBigCustomer = new FenceBigCustomerValueObject();
		OutLandMerchantEntity outLandMerchant = outLandMerchantRepository.queryByUk(merchantId);
		if (outLandMerchant != null && outLandMerchant.getAdminId() != null) {
			AdminEntity admin = adminRepository.queryById(outLandMerchant.getAdminId());
			if(admin == null){
				return fenceBigCustomer;
			}
			fenceBigCustomer.setBigCustomerFlag(true);
			//是否支持提前截单 0 不支持 1 支持
			if (Objects.equals(admin.getCloseOrderType(), 1)) {
				String closeOrderTime = admin.getCloseOrderTime();
				fenceBigCustomer.setCloseOrderTime(LocalTime.parse(closeOrderTime, BaseDateUtils.DEFAULT_LOCAL_TIME));
			}
			fenceBigCustomer.setXmBigAdminId(admin.getAdminId());
		}

		return fenceBigCustomer;
	}
}
