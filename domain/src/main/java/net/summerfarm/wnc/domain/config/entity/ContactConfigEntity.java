package net.summerfarm.wnc.domain.config.entity;

import lombok.Data;
import net.summerfarm.wnc.common.enums.ContactConfigEnums;
import net.summerfarm.wnc.common.enums.WarehouseStorageCenterEnums;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Description:联系人配置实体
 * date: 2023/9/22 18:24
 *
 * <AUTHOR>
 */
@Data
public class ContactConfigEntity implements Serializable {

    private static final long serialVersionUID = -8225581752774674553L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 来源，0：鲜沐，1：saas
     */
    private ContactConfigEnums.Source source;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 外部联系人ID
     */
    private Long outerContactId;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    public void create(String creator) {
        this.createTime = LocalDateTime.now();
        this.creator = creator;
    }

    public String buildUk(){
        return String.format("%s#%s", this.tenantId, this.outerContactId);
    }

    public ContactConfigEntity updateStoreNo(Integer storeNo) {
        if (storeNo == null){
            return null;
        }
        ContactConfigEntity update = new ContactConfigEntity();
        update.setId(this.id);
        update.setStoreNo(storeNo);
        return update;
    }
}
