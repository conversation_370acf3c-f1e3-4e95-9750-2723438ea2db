package net.summerfarm.wnc.domain.fence.dataObject;

import lombok.Data;


/**
 * Description: 围栏地址<br/>
 * date: 2023/12/11 18:42<br/>
 *
 * <AUTHOR> />
 */
@Data
public class AddrDO {
    /**
     * 围栏名称
     */
    private String fenceName;

    /**
     * 城配仓
     */
    private Integer storeNo;


    /**
     * 状态 0正常 1失效
     */
    private Integer fenceStatus;

    /**
     * 区域编码
     */
    private String adCode;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 状态 0 正常 1 失效 3停用
     */
    private Integer adCodeStatus;

    /**
     * 围栏id
     */
    private Integer fenceId;
}
