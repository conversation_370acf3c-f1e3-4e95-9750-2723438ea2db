package net.summerfarm.wnc.domain.fence.repository;



import com.github.pagehelper.PageInfo;
import java.util.List;
import net.summerfarm.wnc.domain.fence.entity.FenceChannelBusinessWhiteConfigEntity;
import net.summerfarm.wnc.domain.fence.param.query.FenceChannelBusinessWhiteConfigQueryParam;



/**
*
* <AUTHOR>
* @date 2024-10-12 16:29:33
* @version 1.0
*
*/
public interface FenceChannelBusinessWhiteConfigQueryRepository {

    PageInfo<FenceChannelBusinessWhiteConfigEntity> getPage(FenceChannelBusinessWhiteConfigQueryParam param);

    FenceChannelBusinessWhiteConfigEntity selectById(Long id);

    List<FenceChannelBusinessWhiteConfigEntity> selectByCondition(FenceChannelBusinessWhiteConfigQueryParam param);

    /**
     * 查询白名单配置存在围栏ID列表
     * @return 围栏ID列表
     */
    List<Integer> queryAllFenceIdList();
}