package net.summerfarm.wnc.domain.warehouse;

import net.summerfarm.wnc.common.query.warehouse.WarehouseBusinessQuery;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseStorageCenterBusEntity;

import java.util.List;

/**
 * Description: <br/>
 * date: 2023/4/4 18:55<br/>
 *
 * <AUTHOR> />
 */
public interface WarehouseStorageCenterBusinessRepository {
    /**
     * 查询
     * @param warehouseBusinessQuery 查询
     * @return 结果
     */
    List<WarehouseStorageCenterBusEntity> queryList(WarehouseBusinessQuery warehouseBusinessQuery);
}
