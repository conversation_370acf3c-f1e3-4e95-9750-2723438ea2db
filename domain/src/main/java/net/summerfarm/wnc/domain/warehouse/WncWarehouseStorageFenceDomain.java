package net.summerfarm.wnc.domain.warehouse;

import lombok.RequiredArgsConstructor;
import net.summerfarm.wnc.common.query.warehouse.WarehouseStorageFenceQuery;
import net.summerfarm.wnc.domain.warehouse.entity.WncWarehouseStorageFenceEntity;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Description: <br/>
 * date: 2023/3/28 17:25<br/>
 *
 * <AUTHOR> />
 */
@Service
@RequiredArgsConstructor
public class WncWarehouseStorageFenceDomain {

    private final WncWarehouseStorageFenceRepository warehouseStorageFenceRepository;
    /**
     * 查询重复区域
     * @param warehouseStorageFenceQuery 查询
     */
    public List<WncWarehouseStorageFenceEntity> queryFenceConflict(WarehouseStorageFenceQuery warehouseStorageFenceQuery) {
        //查询冲突的区域
        return warehouseStorageFenceRepository.queryFenceConflict(warehouseStorageFenceQuery);
    }
}
