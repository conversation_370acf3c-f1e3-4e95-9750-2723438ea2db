package net.summerfarm.wnc.domain.config.entity;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 租户城配仓SKU黑名单配置
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-239 14:42:07
 */
@Data
public class TenantStoreSkuBlackConfigEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * sku
     */
    private String sku;


}
