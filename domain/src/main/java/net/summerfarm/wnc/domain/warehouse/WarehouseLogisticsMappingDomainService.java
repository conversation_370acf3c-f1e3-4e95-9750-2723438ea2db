package net.summerfarm.wnc.domain.warehouse;

import lombok.RequiredArgsConstructor;
import net.summerfarm.wnc.domain.fence.DeliveryFenceRepository;
import org.springframework.stereotype.Service;

/**
 * Description: <br/>
 * date: 2023/4/10 9:29<br/>
 *
 * <AUTHOR> />
 */
@Service
@RequiredArgsConstructor
public class WarehouseLogisticsMappingDomainService {

    final private WarehouseLogisticsMappingRepository warehouseLogisticsMappingRepository;
    final private DeliveryFenceRepository deliveryFenceRepository;


}
