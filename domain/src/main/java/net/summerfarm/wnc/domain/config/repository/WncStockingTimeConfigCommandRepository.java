package net.summerfarm.wnc.domain.config.repository;



import net.summerfarm.wnc.domain.config.entity.WncStockingTimeConfigEntity;
import net.summerfarm.wnc.domain.config.param.command.WncStockingTimeConfigCommandParam;


/**
*
* <AUTHOR>
* @date 2025-04-15 14:51:44
* @version 1.0
*
*/
public interface WncStockingTimeConfigCommandRepository {

    WncStockingTimeConfigEntity insertSelective(WncStockingTimeConfigCommandParam param);

    int updateSelectiveById(WncStockingTimeConfigCommandParam param);

    int remove(Long id);

}