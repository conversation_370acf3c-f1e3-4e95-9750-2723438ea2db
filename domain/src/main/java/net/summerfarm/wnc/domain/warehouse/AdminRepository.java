package net.summerfarm.wnc.domain.warehouse;

import net.summerfarm.wnc.domain.warehouse.entity.AdminEntity;

import java.util.List;
import java.util.Map;

/**
 * Description: <br/>
 * date: 2023/4/26 15:54<br/>
 *
 * <AUTHOR> />
 */
public interface AdminRepository {

    /**
     * 根据id集合查询实际名称
     * @param manageAdminIds id集合
     * @return 结果
     */
    Map<Integer, String> queryByIds(List<Integer> manageAdminIds);

    /**
     * 根据id查询名称
     *
     * @param manageAdminId
     * @return 名称
     */
    String queryNameById(Integer manageAdminId);

    /**
     * 根据id查询详情
     * @param adminId
     * @return
     */
    AdminEntity queryById(Integer adminId);
}
