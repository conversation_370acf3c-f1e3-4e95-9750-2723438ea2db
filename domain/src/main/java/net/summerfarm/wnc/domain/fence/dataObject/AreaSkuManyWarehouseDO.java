package net.summerfarm.wnc.domain.fence.dataObject;

import lombok.Data;

/**
 * Description: 运营区域sku多仓<br/>
 * date: 2023/12/26 15:04<br/>
 *
 * <AUTHOR> />
 */
@Data
public class AreaSkuManyWarehouseDO {

    /**
     * 运营区域编号
     */
    private Integer areaNo;

    /**
     * 运营区域名称
     */
    private String areaName;

    /**
     * sku
     */
    private String sku;

    /**
     * 多仓库名称
     */
    private String manyWarehouseName;

    /**
     * 多围栏名称
     */
    private String manyFenceName;

    /**
     * 城配仓名称
     */
    private String manyStoreName;
}
