package net.summerfarm.wnc.domain.warehouse.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2023/3/28 16:59<br/>
 *
 * <AUTHOR> />
 */
@Data
public class WarehouseStorageCenterBusEntity {
    /**
     * primary key
     */
    private Long id;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 产能
     */
    private Long capacity;

    /**
     * 预约提前期
     */
    private Integer advanceDay;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    private Integer isDelete;
}
