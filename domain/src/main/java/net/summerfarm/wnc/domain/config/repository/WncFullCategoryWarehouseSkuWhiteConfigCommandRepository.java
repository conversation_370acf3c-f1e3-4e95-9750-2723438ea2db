package net.summerfarm.wnc.domain.config.repository;



import net.summerfarm.wnc.domain.config.entity.WncFullCategoryWarehouseSkuWhiteConfigEntity;
import net.summerfarm.wnc.domain.config.param.command.WncFullCategoryWarehouseSkuWhiteConfigCommandParam;

import java.util.List;


/**
*
* <AUTHOR>
* @date 2025-01-06 15:31:26
* @version 1.0
*
*/
public interface WncFullCategoryWarehouseSkuWhiteConfigCommandRepository {

    void batchInsert(List<WncFullCategoryWarehouseSkuWhiteConfigCommandParam> paramList);

    WncFullCategoryWarehouseSkuWhiteConfigEntity insertSelective(WncFullCategoryWarehouseSkuWhiteConfigCommandParam param);

    int updateSelectiveById(WncFullCategoryWarehouseSkuWhiteConfigCommandParam param);

    int remove(Long id);

}