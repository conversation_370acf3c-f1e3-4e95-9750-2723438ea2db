package net.summerfarm.wnc.domain.warehouse.entity;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/4/7 14:00<br/>
 *
 * <AUTHOR> />
 */
@Data
public class WncWarehouseStorageFenceRuleEntity {

    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 配送规则 0 距离最短 1手动设置优先级
     */
    private Integer deliveryRule;

    /**
     * 仓库优先级JSON规则
     */
    private List<ConflictWarehouseJsonEntity> conflictWarehouseJsonEntityList;

    /**
     * 仓库名称合集
     */
    private String warehouseNameList;

    /**
     * 最后操作人名称
     */
    private String lastOperatorName;

    /**
     * 最后操作人id
     */
    private Long lastOperatorId;

    /**
     * 租户ID
     */
    private Long tenantId;
}
