package net.summerfarm.wnc.domain.fence.entity;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * Description: 自定义围栏区域实体类<br/>
 * date: 2023/12/6 15:16<br/>
 *
 * <AUTHOR> />
 */
@Data
public class CustomFenceAreaEntity {

    /**
     * 区域编码
     */
    private String adCode;

    /**
     * 省
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域
     */
    private String area;

    /**
     * POI信息
     */
    private String geoLocation;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
