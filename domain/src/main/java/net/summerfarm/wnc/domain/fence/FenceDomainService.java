package net.summerfarm.wnc.domain.fence;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.common.enums.*;
import net.summerfarm.wnc.common.query.fence.FenceQuery;
import net.summerfarm.wnc.common.query.warehouse.WarehouseInventoryMappingQuery;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.summerfarm.wnc.domain.warehouse.WarehouseInventoryMappingRepository;
import net.summerfarm.wnc.domain.warehouse.WarehouseLogisticsMappingRepository;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseInventoryMappingEntity;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:围栏领域服务
 * date: 2023/11/29 18:44
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FenceDomainService {

    private final FenceRepository fenceRepository;
    private final WarehouseLogisticsMappingRepository warehouseLogisticsMappingRepository;
    private final WarehouseInventoryMappingRepository warehouseInventoryMappingRepository;

    public Integer queryFencePackId(FenceEntity fenceEntity) {
        if (fenceEntity == null){
            return null;
        }
        //查询相同运营区域的全部围栏
        List<FenceEntity> fenceEntities = fenceRepository.queryList(FenceQuery.builder().areaNo(fenceEntity.getAreaNo()).build());

        if (CollectionUtils.isEmpty(fenceEntities)){
            //获取最大打包ID
            return fenceRepository.queryMaxPackId() + 1;
        }

        FenceEntity firstFenceWithSameArea = fenceEntities.get(0);
        //校验库存使用仓
        List<Integer> newStorageCenters = warehouseLogisticsMappingRepository.queryValidWarehouseNosByStoreNo(fenceEntity.getStoreNo());
        List<Integer> oldStorageCenters = warehouseLogisticsMappingRepository.queryValidWarehouseNosByStoreNo(firstFenceWithSameArea.getStoreNo());
        if(!Objects.equals(oldStorageCenters.size(),newStorageCenters.size()) || !oldStorageCenters.containsAll(newStorageCenters) ){
            throw new BizException("请调整配送仓的库存使用仓");
        }
        return firstFenceWithSameArea.getPackId();
    }

    public void createInventoryMapping(FenceEntity fenceEntity) {
        if (fenceEntity == null){
            return;
        }
        Long count = warehouseInventoryMappingRepository.queryCountByStoreNo(fenceEntity.getStoreNo());
        //判断该城配仓是否已有映射关系 有映射信息无需处理
        if (count != null && count > 0){
            return;
        }
        //无映射关系则为新城配仓 需根据围栏绑定运营服务区找到已有城配仓同步库存使用关系
        //判断该运营区域是否已使用 查询相同运营区域的全部围栏
        List<FenceEntity> fenceEntities = fenceRepository.queryList(FenceQuery.builder().areaNo(fenceEntity.getAreaNo()).status(FenceEnums.Status.VALID.getValue()).build());
        //新运营区域 需要运营人员投放商品信息
        if (CollectionUtils.isEmpty(fenceEntities)){
            return;
        }
        FenceEntity firstFenceWithSameArea = fenceEntities.get(0);
        //老运营区域 获取该运营区域所覆盖有效状态围栏的首个已有配送仓
        Integer oldStoreNo = fenceRepository.queryValidStoreNoByPackId(firstFenceWithSameArea.getPackId()).get(0);
        //获取已有配送仓的库存使用关系
        List<WarehouseInventoryMappingEntity> oldWarehouseInventoryMappings = warehouseInventoryMappingRepository.queryList(WarehouseInventoryMappingQuery.builder().storeNo(oldStoreNo).build());
        if (CollectionUtils.isEmpty(oldWarehouseInventoryMappings)){
            return;
        }
        List<WarehouseInventoryMappingEntity> needInsertMappings = oldWarehouseInventoryMappings.stream().map(e -> {
            WarehouseInventoryMappingEntity mapping = new WarehouseInventoryMappingEntity();
            mapping.setSku(e.getSku());
            mapping.setStoreNo(fenceEntity.getStoreNo());
            mapping.setWarehouseNo(e.getWarehouseNo());
            mapping.setCreateTime(LocalDateTime.now());
            return mapping;
        }).collect(Collectors.toList());
        warehouseInventoryMappingRepository.batchSave(needInsertMappings);
    }

    public FenceEntity queryDetail(Integer fenceId) {
        if (fenceId == null){
            return null;
        }
        FenceEntity fenceEntity = fenceRepository.queryDetail(fenceId);
        if (fenceEntity == null){
            throw new BizException("无效围栏信息");
        }
        if (Objects.equals(fenceEntity.getStatus(), FenceEnums.Status.INVALID)){
            //历史逻辑 无效状态置空区域信息
            fenceEntity.setAdCodeMsgEntities(Collections.emptyList());
        }
        return fenceEntity;
    }

    /**
     * 根据城配仓查询有效的围栏和有效的运营区域
     * @param storeNos 城配仓编号
     * @return 结果
     */
    public List<FenceEntity> queryValidFenceAreaByStoreNos(List<Integer> storeNos) {
        if(CollectionUtils.isEmpty(storeNos)){
            return Collections.emptyList();
        }
        List<FenceEntity> fenceEntities = fenceRepository.queryList(FenceQuery.builder()
                .storeNos(storeNos)
                .status(FenceEnums.Status.VALID.getValue())
                .build());

        if(CollectionUtils.isEmpty(fenceEntities)){
            return Collections.emptyList();
        }
        List<Integer> areaNos = fenceEntities.stream().map(FenceEntity::getAreaNo).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(areaNos)){
            return Collections.emptyList();
        }

        return fenceEntities.stream().filter(fence -> areaNos.contains(fence.getAreaNo())).collect(Collectors.toList());
    }
}
