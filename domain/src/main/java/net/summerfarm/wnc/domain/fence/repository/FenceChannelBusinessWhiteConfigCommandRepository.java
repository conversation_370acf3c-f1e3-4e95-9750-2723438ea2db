package net.summerfarm.wnc.domain.fence.repository;



import net.summerfarm.wnc.domain.fence.entity.FenceChannelBusinessWhiteConfigEntity;
import net.summerfarm.wnc.domain.fence.param.command.FenceChannelBusinessWhiteConfigCommandParam;

import java.util.List;


/**
*
* <AUTHOR>
* @date 2024-10-12 16:29:33
* @version 1.0
*
*/
public interface FenceChannelBusinessWhiteConfigCommandRepository {

    FenceChannelBusinessWhiteConfigEntity insertSelective(FenceChannelBusinessWhiteConfigCommandParam param);

    int updateSelectiveById(FenceChannelBusinessWhiteConfigCommandParam param);

    int remove(Long id);

    /**
     * 批量插入
     * @param businessWhiteConfigEntities 业务白名单配置实体
     */
    void batchInsert(List<FenceChannelBusinessWhiteConfigEntity> businessWhiteConfigEntities);

    /**
     * 根据围栏ID删除
     * @param fenceId 围栏ID
     */
    void removeByFenceId(Integer fenceId);
}