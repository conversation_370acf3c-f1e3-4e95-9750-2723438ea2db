package net.summerfarm.wnc.domain.closeTime.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.wnc.common.constants.AppConsts;
import net.xianmu.common.input.BasePageInput;
import java.io.Serializable;
import java.util.Optional;

/**
 * Description:截单时间区域配置查询参数
 * date: 2024/3/20 11:09
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CloseTimeAreaConfigQueryParam extends BasePageInput implements Serializable {

    private static final long serialVersionUID = -2745973007783692359L;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 品牌ID
     */
    private Long brandId;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 有无更新标识，0：无，1：有
     */
    private Integer updateFlag;

    public Long getTenantBrandId(){
        return Optional.ofNullable(this.brandId).orElse(AppConsts.Tenant.DEFAULT_BRAND_ID) ;
    }

}
