package net.summerfarm.wnc.domain.path;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.common.query.path.PathQuery;
import net.summerfarm.wnc.domain.path.entity.PathConfigEntity;

import java.util.List;

/**
 * Description: <br/>
 * date: 2023/11/28 14:31<br/>
 *
 * <AUTHOR> />
 */
public interface PathConfigRepository {
    /**
     * 查询集合信息
     * @param pathQuery 查询
     * @return 结果
     */
    List<PathConfigEntity> queryPathConfigList(PathQuery pathQuery);

    /**
     * 根据uk查询信息
     * @param beginOutNo 开始位置
     * @param endOutNo 结束位置
     * @param businseeType 类型
     * @return 结果
     */
    PathConfigEntity queryByUk(Integer beginOutNo, Integer endOutNo, Integer businseeType);

    /**
     * 保存
     * @param reqEntity 保存
     */
    void save(PathConfigEntity reqEntity);

    /**
     * 根据ID更新数据
     * @param reqEntity 实体
     */
    void updateById(PathConfigEntity reqEntity);

    /**
     * 根据开始编号和类型查询路线配置信息
     * @param beginOutNoList 开始编号集合
     * @param businseeType 类型
     * @return 结果
     */
    List<PathConfigEntity> queryByBeginOutNosWithType(List<Integer> beginOutNoList, Integer businseeType);

    /**
     * 根据sku和仓库查询sku路径映射信息
     * @param skuList sku集合
     * @param warehouseList 仓库集合
     * @return 结果
     */
    List<PathConfigEntity> queryPathConfigWithSkuPathMappingList(List<String> skuList, List<Integer> warehouseList);

    /**
     * 查询所有
     * @return 结果
     */
    List<PathConfigEntity> queryAll();

    /**
     * 批量更新
     * @param pathConfigEntities 实体集合
     */
    void batchUpdate(List<PathConfigEntity> pathConfigEntities);

    /**
     * 初始化数据
     */
    void initData();

    /**
     * 查询路线是否存在
     * @param id 路线ID
     * @return 结果
     */
    PathConfigEntity queryById(Long id);

    /**
     * 删除路线配置信息
     * @param id 路线ID
     */
    void deleteById(Long id);

    /**
     * 分页查询
     * @param query 查询
     * @return 分页结果
     */
    PageInfo<PathConfigEntity> queryPagePathConfig(PathQuery query);
}
