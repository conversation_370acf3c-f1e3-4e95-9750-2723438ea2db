package net.summerfarm.wnc.domain.fence;

import net.summerfarm.wnc.domain.fence.entity.OutLandContactEntity;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-06-20
 **/
public interface OutLandContactRepository {

	/**
	 * 唯一键查询
	 * @param contactId
	 * @return
	 */
	OutLandContactEntity queryWithRuleByUk(Long contactId);

	/**
	 * 批量查询
	 * @param beginNum 开始id数
	 * @param endNum 结束id数
	 * @return
	 */
    List<OutLandContactEntity> queryListByBeginEndNum(Integer beginNum, Integer endNum);

	/**
	 * 保存不同的客户城配仓不同的记录
	 * @param contactId 客户id
	 * @param storeNo 城配仓编号
	 * @param fenceStoreNo POI匹配的城配仓
	 */
	void saveCompareError(Long contactId, Integer storeNo, Integer fenceStoreNo);

	/**
	 * 批量保存不同的客户城配仓不同的记录
	 * @param saveList 数据
	 */
	void saveBatchCompareError(List<OutLandContactEntity> saveList);

	/**
	 * 查询客户有效的总条数
	 * @return 结果
	 */
	Long queryContactTotal();

	/**
	 * 柠季处理
	 */
    void updateNingJiAddressPoi();

	/**
	 * 更新地址
	 * @param contactIds 联系人ID集合
	 */
	void updateAddressPoi(List<Long> contactIds);

    /**
     * 根据id查询
     * @param contactIds 客户地址ID
     * @return 结果
     */
	List<OutLandContactEntity> queryByIds(List<Long> contactIds);

	/**
	 * 查询近几天下单的客户信息
	 * @return 客户信息
	 */
    List<OutLandContactEntity> queryContactRecentOrderDays(LocalDate beginDay, LocalDate endDay);

	/**
	 * 指定城配仓编号
	 * @param contactId 联系人ID
	 * @param newStoreNo 新的城配仓编号
	 */
	void contactAppointStoreNo(Long contactId, Integer newStoreNo);
}
