package net.summerfarm.wnc.domain.fence.entity;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import net.summerfarm.wnc.common.constants.AppConsts;
import net.summerfarm.wnc.common.enums.FenceDeliveryEnums;
import net.summerfarm.wnc.common.enums.FenceEnums;
import net.summerfarm.wnc.common.enums.WeekTimeEnum;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/3/7 19:40<br/>
 *
 * <AUTHOR> />
 */
@Data
public class FenceDeliveryEntity {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 围栏id
     */
    private Integer fenceId;

    /**
     * 配送周期
     */
    private String deliveryFrequent;

    /**
     * 首配日
     */
    private LocalDate nextDeliveryDate;

    /**
     * 逻辑删除 0否 1是
     */
    private Integer deleteFlag;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private Integer updater;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 周期方案 1周计算 2间隔计算
     */
    private Integer frequentMethod;

    /**
     * 配送间隔周期
     */
    private Integer deliveryFrequentInterval;

    /**
     * 开始计算日期
     */
    private LocalDate beginCalculateDate;

    /**
     * 下单渠道类型 1000鲜沐平台客户 2000鲜沐大客户 3000 Saas客户
     */
    private String orderChannelType;
    /**
     * 首配日判断
     * @param deliveryTime 配送时间
     * @return 配送时间
     */
    public LocalDateTime fristDateJudge(LocalDateTime deliveryTime) {
        LocalDate firstDeliveryDate = this.getNextDeliveryDate();
        if(firstDeliveryDate != null){
            //早于首配配送时间,变成首配日期
            if(deliveryTime.isBefore(firstDeliveryDate.atStartOfDay())){
                deliveryTime = firstDeliveryDate.atStartOfDay();
            }
        }
        return deliveryTime;
    }

    public void resetDailyDelivery(){
        this.deliveryFrequent = "0";
        this.frequentMethod = 1;
        this.nextDeliveryDate = LocalDate.now().minusDays(1);
        this.orderChannelType = FenceEnums.OrderChannelType.getAllValueSplit();
    }

    /**
     * 重新排序配送周期字段
     */
    public void resortDeliveryFrequent() {
        if (!Objects.equals(this.frequentMethod, FenceDeliveryEnums.FrequentMethod.WEEK_CALC.getValue())){
            return;
        }
        if (StrUtil.isBlank(this.deliveryFrequent)){
            return;
        }
        this.deliveryFrequent = Arrays.stream(this.deliveryFrequent.split(AppConsts.Symbol.COMMA)).sorted().collect(Collectors.joining(AppConsts.Symbol.COMMA));
    }

    public void create(Integer creatorId) {
        this.createTime = LocalDateTime.now();
        this.creator = creatorId;
        this.deleteFlag = FenceDeliveryEnums.DeleteFlag.NO.getValue();
    }

    public FenceDeliveryEntity update(FenceDeliveryEntity editedFenceDeliveryEntity, Integer updaterId) {
        FenceDeliveryEntity update = new FenceDeliveryEntity();
        update.setId(this.id);
        update.setNextDeliveryDate(editedFenceDeliveryEntity.getNextDeliveryDate());
        update.setFrequentMethod(editedFenceDeliveryEntity.getFrequentMethod());
        update.setDeliveryFrequent(editedFenceDeliveryEntity.getDeliveryFrequent());
        update.setBeginCalculateDate(editedFenceDeliveryEntity.getBeginCalculateDate());
        update.setDeliveryFrequentInterval(editedFenceDeliveryEntity.getDeliveryFrequentInterval());
        update.setUpdater(updaterId);
        update.setUpdateTime(LocalDateTime.now());
        return update;
    }

    public String getDeliveryFrequentStr() {
        String deliveryFrequentStr = "";
        if(Objects.equals(this.frequentMethod, FenceDeliveryEnums.FrequentMethod.WEEK_CALC.getValue())){
            if(Objects.equals("0",this.deliveryFrequent)){
                deliveryFrequentStr = "每天";
            }else{
                deliveryFrequentStr = Arrays.stream(this.deliveryFrequent.split(AppConsts.Symbol.COMMA))
                        .map(e -> WeekTimeEnum.getWeekTime(Integer.valueOf(e))).collect(Collectors.joining(AppConsts.Symbol.COMMA));
            }
        }
        if(Objects.equals(this.frequentMethod,FenceDeliveryEnums.FrequentMethod.INTERVAL_CALC.getValue())){
            deliveryFrequentStr = String.format("每%d天(开始计算时间:%s)", this.deliveryFrequentInterval,this.beginCalculateDate);
        }
        return deliveryFrequentStr;
    }
}
