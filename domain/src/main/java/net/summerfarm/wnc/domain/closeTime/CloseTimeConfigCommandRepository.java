package net.summerfarm.wnc.domain.closeTime;

import net.summerfarm.wnc.domain.closeTime.param.CloseTimeAreaConfigCommandParam;

import java.util.List;

/**
 * Description:截单时间配置仓库操作接口
 * date: 2024/3/20 10:35
 *
 * <AUTHOR>
 */
public interface CloseTimeConfigCommandRepository {


    /**
     * 批量删除截单时间区域配置
     * @param deleteIds 删除ID集合
     * @return 影响条数
     */
    int batchRemoveAreaConfig(List<Long> deleteIds);

    /**
     * 更新截单时间区域配置
     * @param commandParam 操作参数
     * @return 影响条数
     */
    int updateAreaConfig(CloseTimeAreaConfigCommandParam commandParam);

    /**
     * 批量保存截单时间区域配置
     * @param commandParams 操作参数
     */
    void batchSaveAreaConfig(List<CloseTimeAreaConfigCommandParam> commandParams);
}
