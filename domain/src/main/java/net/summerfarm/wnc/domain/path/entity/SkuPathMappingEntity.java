package net.summerfarm.wnc.domain.path.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2023/11/28 14:32<br/>
 *
 * <AUTHOR> />
 */
@Data
public class SkuPathMappingEntity {

    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 路线信息
     */
    private Long pathId;

    /**
     * 创建人名称
     */
    private String creator;

    /**
     * 更新人
     */
    private String updater;

}
