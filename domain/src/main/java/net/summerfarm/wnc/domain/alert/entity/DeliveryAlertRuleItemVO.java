package net.summerfarm.wnc.domain.alert.entity;

import lombok.Data;
import net.summerfarm.wnc.common.enums.DeliveryAlertEnums;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * Description:配送提醒规则项实体
 * date: 2023/3/21 16:42
 *
 * <AUTHOR>
 */
@Data
public class DeliveryAlertRuleItemVO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 规则ID
     */
    private Long ruleId;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 配送仓编号
     */
    private Integer storeNo;

    /**
     * 渠道，0：鲜沐，1：SAAS
     */
    private DeliveryAlertEnums.Channel channel;

    /**
     * 类型，0：品牌，1：门店
     */
    private DeliveryAlertEnums.Type type;

    /**
     * 对应类型业务编号
     */
    private String bizNo;

    /**
     * 对应类型业务名称
     */
    private String bizName;

    /**
     * 配送提醒开始时间
     */
    private LocalTime beginTime;

    /**
     * 配送提醒结束时间
     */
    private LocalTime endTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public String buildUk(){
        return this.storeNo + "#" + this.buildStoreUk();
    }

    public String buildStoreUk(){
        return this.channel.getValue() + "#" + this.type.getValue() + "#" + this.bizNo;
    }

    public String findTimeFrame(){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
        String beginTimeStr = this.beginTime.format(formatter);
        String endTimeStr = this.endTime.format(formatter);
        return String.format("%s-%s", beginTimeStr, endTimeStr);
    }
}
