package net.summerfarm.wnc.domain.config.repository;

import net.summerfarm.wnc.common.enums.ContactConfigEnums;
import net.summerfarm.wnc.common.query.config.ContactConfigQuery;
import net.summerfarm.wnc.domain.config.entity.ContactConfigEntity;
import net.summerfarm.wnc.domain.config.monit.PopMerchantMonit;

import java.util.List;

/**
 * Description:联系人配置仓库查询接口
 * date: 2023/9/22 18:22
 *
 * <AUTHOR>
 */
public interface ContactConfigQueryRepository {

    /**
     * 根据uk查询
     * @param source 来源
     * @param outerContactId 外部联系人ID
     * @return 联系人配置
     */
    ContactConfigEntity queryByUk(ContactConfigEnums.Source source, Long outerContactId);

    /**
     * 查询联系人配置集合
     * @param contactConfigQuery 查询
     * @return 结果
     */
    List<ContactConfigEntity> queryList(ContactConfigQuery contactConfigQuery);

    /**
     * POP查询没有指定城配仓的门店信息
     * @param adminId 大客户ID
     * @return 结果
     */
    List<PopMerchantMonit> queryPopNoHaveStoreNoMonit(Integer adminId);
}
