package net.summerfarm.wnc.domain.alert;


import net.summerfarm.wnc.common.query.alert.CompleteDeliveryTimeQueryParam;

import java.time.LocalTime;
import java.util.List;

/**
 * Description: 完成配送提醒表<br/>
 * date: 2024/3/29 14:53<br/>
 *
 * <AUTHOR> />
 */
public interface CompleteDeliveryQueryRepository {

    /**
     * 查询区域最后配送时间
     * @param query 查询
     * @return 结果
     */
    List<LocalTime> queryListAreaLastTime(CompleteDeliveryTimeQueryParam query);
}
