package net.summerfarm.wnc.domain.changeTask.entity;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import net.summerfarm.wnc.client.mq.msg.out.FenceChangeOrderHandleMsg;
import net.summerfarm.wnc.common.enums.FenceChangeTaskDetailEnums;
import net.summerfarm.wnc.common.enums.FenceChangeTaskEnums;
import net.summerfarm.wnc.domain.changeTask.vo.FenceChangeRemarkVO;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Description:切仓任务订单明细实体
 * date: 2023/8/24 17:11
 *
 * <AUTHOR>
 */
@Data
public class FenceChangeTaskOrderEntity implements Serializable {

    private static final long serialVersionUID = -6591580395923380185L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 切仓任务ID
     */
    private Long taskId;

    /**
     * 外部单号
     */
    private String outerOrderId;

    /**
     * 来源，200：鲜沐-订单，201：鲜沐-售后，202：鲜沐-样品，203：鲜沐-省心送，210：saas-订单，211：saas-售后，151：外单干线-城配，230：POP-订单，231：POP-售后
     */
    private FenceChangeTaskDetailEnums.Source source;

    /**
     * 外部联系人ID
     */
    private String outerContactId;

    /**
     * 配送时间
     */
    private LocalDate deliveryTime;

    /**
     * 外部客户号
     */
    private String outerClientId;

    /**
     * 外部客户名
     */
    private String outerClientName;

    /**
     * 状态，10：待处理，20：处理成功，30：处理失败
     */
    private FenceChangeTaskDetailEnums.Status status;

    /**
     * 失败原因
     */
    private String remark;

    /**
     * 履约确认时间
     */
    private LocalDateTime fulfillConfirmTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建围栏切仓任务订单明细
     */
    public void create(Long taskId) {
        this.taskId = taskId;
        this.status = FenceChangeTaskDetailEnums.Status.WAIT;
        this.createTime = LocalDateTime.now();
    }

    public FenceChangeTaskOrderEntity execute(FenceChangeTaskDetailEnums.Status status, String remark) {
        this.status = status;
        this.updateTime = LocalDateTime.now();
        this.remark = remark;
        if (StrUtil.isNotBlank(remark) && remark.length() > 500){
            this.remark = remark.substring(0,500);
        }
        FenceChangeTaskOrderEntity update = new FenceChangeTaskOrderEntity();
        update.setId(this.id);
        update.setStatus(this.status);
        update.setRemark(this.remark);
        update.setUpdateTime(this.updateTime);
        return update;
    }

    public FenceChangeOrderHandleMsg buildMqMsg(FenceChangeRemarkVO fenceChangeRemarkVO) {
        FenceChangeOrderHandleMsg msg = new FenceChangeOrderHandleMsg();
        msg.setOuterOrderId(this.outerOrderId);
        msg.setSource(this.source.getValue());
        msg.setOuterContactId(this.outerContactId);
        msg.setDeliveryTime(this.deliveryTime);
        msg.setOldAreaNo(fenceChangeRemarkVO.getOldAreaNo());
        msg.setNewAreaNo(fenceChangeRemarkVO.getNewAreaNo());
        return msg;
    }

    public String buildUk(){
        return this.taskId + "#" + this.outerOrderId + "#" + this.source.getContent() + "#" + this.deliveryTime + "#" + this.outerContactId;
    }

    public boolean isSuccess() {
        return this.status == FenceChangeTaskDetailEnums.Status.SUCCESS;
    }

    public boolean isRetryable(FenceChangeTaskEnums.Status taskStatus) {
        if (taskStatus == null) {
            return false;
        }
        return taskStatus == FenceChangeTaskEnums.Status.COMPLETED && this.status == FenceChangeTaskDetailEnums.Status.FAIL;
    }

    @Override
    public int hashCode() {
        return this.buildUk().hashCode();
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        FenceChangeTaskOrderEntity entity = (FenceChangeTaskOrderEntity) obj;
        return Objects.equals(this.buildUk(), entity.buildUk());
    }

}
