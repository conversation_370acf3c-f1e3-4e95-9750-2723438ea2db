package net.summerfarm.wnc.domain.warehouse;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.client.req.WarehouseStorageListQueryReq;
import net.summerfarm.wnc.common.query.warehouse.WarehouseStorageQuery;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseStorageEntity;

import java.util.List;

/**
 * Description: <br/>
 * date: 2023/3/28 17:34<br/>
 *
 * <AUTHOR> />
 */
public interface WarehouseStorageCenterRepository {
    /**
     * 保存
     * @param warehouseStorageEntity 保存
     */
    void save(WarehouseStorageEntity warehouseStorageEntity);

    /**
     * 更新信息
     * @param warehouseStorageUpdate2Entity 更新
     */
    void update(WarehouseStorageEntity warehouseStorageUpdate2Entity);

    /**
     * 查询信息
     * @param warehouseStorageQuery 查询参数
     * @return 结果
     */
    List<WarehouseStorageEntity> queryWarehouseStorage(WarehouseStorageQuery warehouseStorageQuery);

    /**
     * 查询仓库详情
     * @param id 仓库id
     * @return 仓库信息
     */
    WarehouseStorageEntity queryDetail(Integer id);

    /**
     * 查询自营仓列表
     * @param warehouseStorageQuery
     * @return
     */
    PageInfo<WarehouseStorageEntity> querySelfWarehouseStoragePage(WarehouseStorageQuery warehouseStorageQuery);

    /**
     * 根据仓库编号查询仓库信息
     * @param warehouseNo 仓库编号
     * @return 结果
     */
    WarehouseStorageEntity queryByWarehouseNo(Integer warehouseNo);

    /**
     * 查询仓库信息
     * @param warehouseStorageQuery 查询
     * @return 结果
     */
    List<WarehouseStorageEntity> queryList(WarehouseStorageQuery warehouseStorageQuery);

    /**
     * 查询代仓分页信息
     * @param warehouseStorageQuery 查询
     * @return 结果
     */
    PageInfo<WarehouseStorageEntity> queryProxyWarehouseStoragePage(WarehouseStorageQuery warehouseStorageQuery);

    /**
     * 根据主键查询信息
     * @param id 主键Id
     * @return 结果
     */
    WarehouseStorageEntity queryById(Long id);

    /**
     * 查询围栏分页信息
     * @param warehouseStorageQuery 查询
     * @return 结果
     */
    PageInfo<WarehouseStorageEntity> queryFencePageList(WarehouseStorageQuery warehouseStorageQuery);

    /**
     * 查询仓库分页信息
     * @param warehouseStorageQuery 查询
     * @return 结果
     */
    PageInfo<WarehouseStorageEntity>  queryWarehouseStoragePage(WarehouseStorageQuery warehouseStorageQuery);

    /**
     * 查询鲜沐是否存在相同的仓库名称
     * @param warehouseName 仓库名称
     * @param id 主键Id
     * @return 结果
     */
    long queryXmCountByNameAndId(String warehouseName, Integer id);

    /**
     * 保存鲜沐仓库信息
     * @param warehouseStorageEntity 仓库信息
     */
    void saveXmWarehouse(WarehouseStorageEntity warehouseStorageEntity);

    /**
     * 查询仓库业务、时间、标准信息
     * @param warehouseStorageQuery 查询
     * @return 结果
     */
    List<WarehouseStorageEntity> queryListWithBusinessWorkStandard(WarehouseStorageQuery warehouseStorageQuery);

    /**
     * 根据仓库编号查询仓库信息
     * @param warehouseNos 仓库编号
     * @return 结果
     */
    List<WarehouseStorageEntity> queryListByWarehouseNos(List<Integer> warehouseNos);

    /**
     * 根据仓库名称查询仓库信息，仓库名称必须完全匹配
     *
     * @param warehouseNames 仓库名称
     * @return 结果
     */
    List<WarehouseStorageEntity> queryListByWarehouseNames(List<String> warehouseNames);

    /**
     * 根据城配仓查询库存使用仓信息
     * @param storeNo 城配仓编号
     * @return 库存使用仓信息集合
     */
    List<WarehouseStorageEntity> queryListByStoreNo(Integer storeNo);
    /**
     * 更新库存仓相关信息
     * @param warehouseStorageEntity 库存仓相关
     */
    void updateStorage(WarehouseStorageEntity warehouseStorageEntity);
}
