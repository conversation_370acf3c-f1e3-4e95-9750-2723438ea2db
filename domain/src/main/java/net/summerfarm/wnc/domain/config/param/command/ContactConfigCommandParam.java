package net.summerfarm.wnc.domain.config.param.command;

import lombok.Data;
import net.summerfarm.wnc.common.enums.ContactConfigEnums;
import net.summerfarm.wnc.domain.fence.param.AddressParam;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Description:联系人配置参数
 * date: 2024/1/3 16:19
 *
 * <AUTHOR>
 */
@Data
public class ContactConfigCommandParam implements Serializable {

    private static final long serialVersionUID = 3138024754399077110L;

    /**
     * 来源，0：鲜沐，1：saas
     */
    private ContactConfigEnums.Source source;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 外部联系人ID
     */
    private Long outerContactId;

    /**
     * 履约方式，0：城配履约，1：快递履约
     */
    private Integer fulfillmentMethod;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 地址参数
     */
    private AddressParam addressParam;

    public void create(String creator) {
        this.createTime = LocalDateTime.now();
        this.creator = creator;
    }

    public String buildUk(){
        return String.format("%s#%s", this.tenantId, this.outerContactId);
    }

}
