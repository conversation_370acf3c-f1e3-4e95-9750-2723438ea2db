package net.summerfarm.wnc.domain.fence.service;


import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.client.enums.SourceEnum;
import net.summerfarm.wnc.common.constants.AppConsts;
import net.summerfarm.wnc.common.enums.FenceChannelBusinessWhiteConfigEnums;
import net.summerfarm.wnc.common.enums.FenceEnums;
import net.summerfarm.wnc.domain.fence.param.query.FenceChannelBusinessWhiteConfigQueryParam;
import net.summerfarm.wnc.domain.fence.repository.FenceChannelBusinessWhiteConfigQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.FenceChannelBusinessWhiteConfigCommandRepository;
import net.summerfarm.wnc.domain.fence.entity.FenceChannelBusinessWhiteConfigEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;


/**
 *
 * @Title: 围栏渠道业务白名单配置领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-10-12 16:29:33
 * @version 1.0
 *
 */
@Service
@Slf4j
public class FenceChannelBusinessWhiteConfigQueryDomainService {

    @Resource
    private FenceChannelBusinessWhiteConfigQueryRepository fenceChannelBusinessWhiteConfigQueryRepository;
    /**
     * 查询渠道业务白名单配置
     * @param fenceId 围栏ID
     * @param tenantId 租户ID
     * @param xmBigAdminId 鲜沐大客户ID
     * @return
     */
    public List<FenceChannelBusinessWhiteConfigEntity> queryChannelBusinessWhiteConfig(Integer fenceId,Long tenantId, Integer xmBigAdminId) {
        log.info("查询渠道业务白名单配置: fenceId:{},tenantId:{}, xmBigAdminId:{}", fenceId,tenantId, xmBigAdminId);
        if(tenantId == null || fenceId == null){
            return Collections.emptyList();
        }
        FenceChannelBusinessWhiteConfigQueryParam queryParam = new FenceChannelBusinessWhiteConfigQueryParam();
        if(Objects.equals(tenantId, AppConsts.Tenant.XM_TENANT_ID) && xmBigAdminId != null){
            // 鲜沐大客户
            queryParam.setFenceId(fenceId);
            queryParam.setOrderChannelType(FenceEnums.OrderChannelType.XM_BIG_CUSTOM.getValue());
            queryParam.setScopeChannelBusinessIds(Arrays.asList(xmBigAdminId.toString(), FenceChannelBusinessWhiteConfigEnums.ScopeChannelBusinessId.ALL.getValue()));

            return fenceChannelBusinessWhiteConfigQueryRepository.selectByCondition(queryParam);
        }else if(!Objects.equals(tenantId, AppConsts.Tenant.XM_TENANT_ID)){
            // Saas大客户
            queryParam.setFenceId(fenceId);
            queryParam.setOrderChannelType(FenceEnums.OrderChannelType.SAAS.getValue());
            queryParam.setScopeChannelBusinessIds(Arrays.asList(tenantId.toString(), FenceChannelBusinessWhiteConfigEnums.ScopeChannelBusinessId.ALL.getValue()));

            return fenceChannelBusinessWhiteConfigQueryRepository.selectByCondition(queryParam);
        }else{
            // 鲜沐平台客户
            queryParam.setFenceId(fenceId);
            queryParam.setOrderChannelType(FenceEnums.OrderChannelType.XM_CUSTOM.getValue());
            queryParam.setScopeChannelBusinessId(FenceChannelBusinessWhiteConfigEnums.ScopeChannelBusinessId.ALL.getValue());

            return fenceChannelBusinessWhiteConfigQueryRepository.selectByCondition(queryParam);
        }
    }
}
