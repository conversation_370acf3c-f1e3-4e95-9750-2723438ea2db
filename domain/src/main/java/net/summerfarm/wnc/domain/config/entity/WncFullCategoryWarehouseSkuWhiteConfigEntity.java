package net.summerfarm.wnc.domain.config.entity;

import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2025-01-06 15:31:26
 * @version 1.0
 *
 */
@Data
public class WncFullCategoryWarehouseSkuWhiteConfigEntity {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 库存仓编号
	 */
	private Integer warehouseNo;

	/**
	 * sku
	 */
	private String sku;

	

	
}