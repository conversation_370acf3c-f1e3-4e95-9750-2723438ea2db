package net.summerfarm.wnc.domain.closeTime;

import lombok.RequiredArgsConstructor;
import net.summerfarm.wnc.domain.closeTime.param.CloseTimeAreaConfigCommandParam;
import net.summerfarm.wnc.domain.preciseDelivery.param.PreciseDeliveryConfigCommandParam;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * Description:截单时间配置校验器
 * date: 2024/3/21 10:08
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class CloseTimeConfigValidator {


    public void validateParams(List<CloseTimeAreaConfigCommandParam> commandParams) {
        if (CollectionUtils.isEmpty(commandParams)){
            return;
        }
        long tenantCount = commandParams.stream().map(CloseTimeAreaConfigCommandParam::getTenantId).distinct().count();
        if (tenantCount > 1){
            throw new BizException("不能跨租户进行截单时间区域配置");
        }
        long brandCount = commandParams.stream().map(CloseTimeAreaConfigCommandParam::getBrandId).distinct().count();
        if (brandCount > 1){
            throw new BizException("不能跨品牌进行截单时间区域配置");
        }
    }
}
