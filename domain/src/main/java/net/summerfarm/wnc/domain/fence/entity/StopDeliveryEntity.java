package net.summerfarm.wnc.domain.fence.entity;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2023/3/8 15:12<br/>
 *
 * <AUTHOR> />
 */
@Data
public class StopDeliveryEntity {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建时间
     */
    private LocalDateTime updateTime;

    /**
     * 城配仓
     */
    private Integer storeNo;

    /**
     * 停运开始时间
     */
    private LocalDate shutdownStartTime;

    /**
     * 停运结束时间
     */
    private LocalDate shutdownEndTime;

    /**
     * 是否删除
     */
    private Integer deleteFlag;
    /**
     * 停配日判断
     * @param deliveryTime 配送日期
     * @return 配送日期
     */
    public LocalDateTime stopDeliveryJudge(LocalDateTime deliveryTime) {
        LocalDate shutdownStartTime = this.getShutdownStartTime();
        LocalDate shutdownEndTime = this.getShutdownEndTime();
        //在停配规则里面
        if(deliveryTime.toLocalDate().compareTo(shutdownEndTime) <= 0 && deliveryTime.toLocalDate().compareTo(shutdownStartTime) >= 0){
            //设置时间为停配后一天
            deliveryTime = shutdownEndTime.plusDays(1).atStartOfDay();
        }

        return deliveryTime;
    }

    /**
     * 是否在停配日里面
     * @param deliveryDate 配送日期
     * @return 配送日期
     */
    public Boolean isStopDeliveryDate(LocalDate deliveryDate) {
        LocalDate shutdownStartTime = this.getShutdownStartTime();
        LocalDate shutdownEndTime = this.getShutdownEndTime();
        //在停配规则里面
        return deliveryDate.compareTo(shutdownEndTime) <= 0 && deliveryDate.compareTo(shutdownStartTime) >= 0;
    }
}
