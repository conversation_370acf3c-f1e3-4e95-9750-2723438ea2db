package net.summerfarm.wnc.domain.warehouse;

import net.summerfarm.wnc.domain.warehouse.entity.WarehouseLogisticsMappingEntity;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Description: <br/>
 * date: 2023/4/7 16:11<br/>
 *
 * <AUTHOR> />
 */
public interface WarehouseLogisticsMappingRepository {

    /**
     * 根据城配仓编号查询库存仓
     * @param storeNoList 城配仓编号集合
     * @return 结果
     */
    List<WarehouseLogisticsMappingEntity> queryByStoreNoList(List<Integer> storeNoList);

    /**
     * 查询城配仓映射有效的库存仓
     * @param storeNoList 城配仓编号
     * @return 仓库信息
     */
    List<WarehouseLogisticsMappingEntity> queryWithValidWarehouseMapping(List<Integer> storeNoList);

    /**
     * 根据城配仓编号查询库存仓
     * @param storeNo 城配仓编号
     * @return 结果
     */
    List<Integer> queryWarehouseNosByStoreNo(Integer storeNo);

    /**
     * 根据城配仓编号查询有效库存仓
     * @param storeNo 城配仓编号
     * @return 结果
     */
    List<Integer> queryValidWarehouseNosByStoreNo(Integer storeNo);

    /**
     * 批量插入
     * @param warehouseLogisticsMappingEntities 实体
     */
    void batchSave(List<WarehouseLogisticsMappingEntity> warehouseLogisticsMappingEntities);

    /**
     * 删除城配仓和库存仓的映射关系
     * @param storeNo 城配仓编号
     * @param warehouseNo 仓库编号
     */
    void removeMapping(Integer storeNo, Integer warehouseNo);

    /**
     * 查询城配仓的库存仓映射关系
     * @param storeNos 城配仓编号
     * @return 结果
     */
    Map<Integer, List<Integer>> queryLogisticsMapping(List<Integer> storeNos);
}
