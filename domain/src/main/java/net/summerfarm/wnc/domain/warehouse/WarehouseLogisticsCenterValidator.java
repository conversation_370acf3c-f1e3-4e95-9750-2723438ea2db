package net.summerfarm.wnc.domain.warehouse;

import lombok.RequiredArgsConstructor;
import net.summerfarm.wnc.common.enums.FenceEnums;
import net.summerfarm.wnc.common.enums.WarehouseLogisticsCenterEnums;
import net.summerfarm.wnc.common.query.fence.FenceQuery;
import net.summerfarm.wnc.domain.fence.FenceRepository;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseLogisticsCenterEntity;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * Description: 城配仓校验器
 * date: 2023/12/7 11:45
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class WarehouseLogisticsCenterValidator {

    private final FenceRepository fenceRepository;

    public void validateFence(WarehouseLogisticsCenterEntity warehouseLogisticsCenterEntity, Integer editStatus) {
        if (warehouseLogisticsCenterEntity == null || editStatus == null) {
            return;
        }
        //城配仓的开放状态从 "开放"修改为"不开放"时 校验是否存在有效围栏
        boolean isValidFence = Objects.equals(editStatus, WarehouseLogisticsCenterEnums.Status.NO_VALID.getValue())
                && Objects.equals(warehouseLogisticsCenterEntity.getStatus(), WarehouseLogisticsCenterEnums.Status.VALID.getValue());
        if(!isValidFence){
            return;
        }

        List<FenceEntity> validFences = fenceRepository.queryList(FenceQuery.builder()
                .storeNo(warehouseLogisticsCenterEntity.getStoreNo())
                .status(FenceEnums.Status.VALID.getValue())
                .build());
        if (CollectionUtils.isEmpty(validFences)){
            return;
        }
        throw new BizException("城配仓下还存在正常状态围栏，不可关闭该城配仓");

    }
}
