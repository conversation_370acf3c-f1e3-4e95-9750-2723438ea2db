package net.summerfarm.wnc.domain.alert;


import net.summerfarm.wnc.common.enums.DeliveryAlertEnums;
import net.summerfarm.wnc.domain.alert.entity.DeliveryAlertRuleItemVO;

/**
 * Description:配送提醒仓库接口
 * date: 2023/3/21 15:09
 *
 * <AUTHOR>
 */
public interface DeliveryAlertRuleRepository {


    /**
     * 根据UK查询 主表数据
     * @param storeNo 配送仓编号
     * @param channel 渠道
     * @param type 类型
     * @param bizNo 业务编号
     * @return 配送提醒规则项
     */
    DeliveryAlertRuleItemVO queryByUk(Integer storeNo, DeliveryAlertEnums.Channel channel, DeliveryAlertEnums.Type type, String bizNo);
}
