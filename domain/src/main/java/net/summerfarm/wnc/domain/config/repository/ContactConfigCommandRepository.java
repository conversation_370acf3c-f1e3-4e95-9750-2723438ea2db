package net.summerfarm.wnc.domain.config.repository;

import net.summerfarm.wnc.domain.config.entity.ContactConfigEntity;
import net.summerfarm.wnc.domain.config.param.command.ContactConfigCommandParam;

/**
 * Description:联系人配置仓库操作接口
 * date: 2023/9/22 18:22
 *
 * <AUTHOR>
 */
public interface ContactConfigCommandRepository {


    /**
     * 保存联系人配置
     * @param contactConfigEntity 联系人配置实体
     * @return 影响条数
     */
    int save(ContactConfigEntity contactConfigEntity);

    /**
     * 保存联系人配置
     * @param contactConfigCommandParam 联系人配置参数
     * @return 影响条数
     */
    int save(ContactConfigCommandParam contactConfigCommandParam);

    /**
     * 删除联系人配置
     * @param configId 主键ID
     * @return 影响条数
     */
    int remove(Long configId);

    /**
     * 更新联系人配置集合
     * @param contactConfigEntity 联系人配置实体
     * @return 影响条数
     */
    int update(ContactConfigEntity contactConfigEntity);
}
