package net.summerfarm.wnc.domain.config.service;


import net.summerfarm.wnc.domain.config.entity.TenantStoreSkuBlackConfigEntity;
import net.summerfarm.wnc.domain.config.repository.TenantStoreSkuBlackConfigCommandRepository;
import net.summerfarm.wnc.domain.config.repository.TenantStoreSkuBlackConfigQueryRepository;
import net.summerfarm.wnc.domain.config.param.command.WncTenantStoreSkuBlackConfigCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 租户城配仓SKU黑名单配置领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-09-12 14:00:16
 * @version 1.0
 *
 */
@Service
public class TenantStoreSkuBlackConfigCommandDomainService {


    @Autowired
    private TenantStoreSkuBlackConfigCommandRepository tenantStoreSkuBlackConfigCommandRepository;
    @Autowired
    private TenantStoreSkuBlackConfigQueryRepository tenantStoreSkuBlackConfigQueryRepository;



    public TenantStoreSkuBlackConfigEntity insert(WncTenantStoreSkuBlackConfigCommandParam param) {
        return tenantStoreSkuBlackConfigCommandRepository.insertSelective(param);
    }


    public int update(WncTenantStoreSkuBlackConfigCommandParam param) {
        return tenantStoreSkuBlackConfigCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return tenantStoreSkuBlackConfigCommandRepository.remove(id);
    }
}
