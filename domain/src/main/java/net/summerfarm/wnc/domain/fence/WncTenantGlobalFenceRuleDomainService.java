package net.summerfarm.wnc.domain.fence;

import lombok.RequiredArgsConstructor;
import net.summerfarm.wnc.common.enums.WncTenantGlobalFenceRuleEnums;
import net.summerfarm.wnc.domain.warehouse.entity.WncTenantGlobalFenceRuleEntity;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;

/**
 * Description: <br/>
 * date: 2023/4/15 15:52<br/>
 *
 * <AUTHOR> />
 */
@Service
@RequiredArgsConstructor
public class WncTenantGlobalFenceRuleDomainService {
    private final WncTenantGlobalFenceRuleRepository wncTenantGlobalFenceRuleRepository;

    /**
     * 查询全局规则
     * @param tenantId 租户
     * @return 结果
     */
    public WncTenantGlobalFenceRuleEntity queryGlobalFenceRule(Long tenantId) {
        //查询全局配送优先级规则
        WncTenantGlobalFenceRuleEntity wncTenantGlobalFenceRuleEntity = wncTenantGlobalFenceRuleRepository.queryUk(tenantId);

        if(wncTenantGlobalFenceRuleEntity == null){
            wncTenantGlobalFenceRuleEntity = new WncTenantGlobalFenceRuleEntity();
            wncTenantGlobalFenceRuleEntity.setTenantId(tenantId);
            wncTenantGlobalFenceRuleEntity.setGlobalDeliveryRule(WncTenantGlobalFenceRuleEnums.deliveryRule.SELF_FIRST.getValue());
            wncTenantGlobalFenceRuleRepository.save(wncTenantGlobalFenceRuleEntity);
        }

        return wncTenantGlobalFenceRuleEntity;
    }

    public void updateGlobalFenceRule(WncTenantGlobalFenceRuleEntity wncTenantGlobalFenceRuleEntity) {
        //查询全局配送优先级规则
        WncTenantGlobalFenceRuleEntity wncTenantGlobalFenceRuleEntityData = wncTenantGlobalFenceRuleRepository.queryUk(wncTenantGlobalFenceRuleEntity.getTenantId());
        if(wncTenantGlobalFenceRuleEntityData == null){
            throw new BizException("不存在此商户的全局信息");
        }
        wncTenantGlobalFenceRuleEntityData.setGlobalDeliveryRule(wncTenantGlobalFenceRuleEntity.getGlobalDeliveryRule());
        wncTenantGlobalFenceRuleRepository.updateGlobalFenceRule(wncTenantGlobalFenceRuleEntityData);
    }
}
