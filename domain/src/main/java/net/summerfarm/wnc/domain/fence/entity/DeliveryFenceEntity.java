package net.summerfarm.wnc.domain.fence.entity;

import lombok.Data;
import net.summerfarm.wnc.common.enums.WncContactDeliveryRuleEnums;
import net.summerfarm.wnc.domain.config.entity.ContactConfigEntity;
import net.summerfarm.wnc.domain.deliveryRule.entity.ContactDeliveryRuleEntity;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseLogisticsCenterEntity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Objects;

/**
 * Description: <br/>
 * date: 2023/3/7 19:21<br/>
 * 配送围栏
 *
 * <AUTHOR> />
 */
@Data
public class DeliveryFenceEntity {

    /**
     * 客户地址配送周期
     */
    private ContactDeliveryRuleEntity contactDeliveryRuleEntity;
    /**
     * 截单时间
     */
    private LocalTime closeTime;

    /**
     * 大客户截单时间
     */
    private LocalTime bigCustomerCloseTime;

    /**
     * 鲜沐大客户标识 true大客户
     */
    private boolean xmBigCustomerFlag;

    /**
     * 鲜沐大客户ID
     */
    private Integer xmBigAdminId;

    /**
     * 围栏
     */
    private FenceEntity fenceEntity;

    /**
     * 围栏渠道白名单
     */
    private List<FenceChannelBusinessWhiteConfigEntity> fenceChannelBusinessWhiteConfigEntities;

    /**
     * 围栏配送
     */
    private FenceDeliveryEntity fenceDeliveryEntity;

    /**
     * 停运规则
     */
    private List<StopDeliveryEntity> stopDeliveryEntitys;

    /**
     * 省市区
     */
    private AdCodeMsgEntity adCodeMsgEntity;

    /**
     * 联系人城配仓映射配置
     */
    private ContactConfigEntity contactConfigEntity;

    /**
     * 城配仓
     */
    private WarehouseLogisticsCenterEntity warehouseLogisticsCenterEntity;

    /**
     * 可加单时长
     */
    private BigDecimal couldAddOrderTimeHours;

    /**
     * 备货时长天数
     */
    private Integer stockingTime;

    /**
     * 配送截单时间的判断
     * @param deliveryDate 配送日期
     * @return 截单时间
     */
    public LocalDateTime deliveryCloseTimeJudge(LocalDate deliveryDate) {
        LocalDateTime closingTime = LocalDateTime.of(LocalDate.now(), this.closeTime);
        if (this.bigCustomerCloseTime != null) {
            LocalDateTime bigCloseTime = LocalDateTime.of(LocalDate.now(), this.bigCustomerCloseTime);
            //谁早取谁
            if (bigCloseTime.isBefore(closingTime)) {
                closingTime = bigCloseTime;
            }
        }
        if(deliveryDate == null){
            return closingTime;
        }
        //因为过了截单时间了所以当前deliveryDate就是下一个配送日,需要再减去备货时间
        closingTime = LocalDateTime.of(deliveryDate.minusDays(1).minusDays(this.stockingTime), closingTime.toLocalTime());
        return closingTime;
    }

    /**
     * 是否日配 0日配，1非日配
     */
    public Integer handleEveryDaySendFlag(){
        //客户地址周配送 并且是每天就是 日配
        if(this.contactDeliveryRuleEntity != null && this.contactDeliveryRuleEntity.getFrequentMethod() != null){
            if(this.contactDeliveryRuleEntity != null &&
                    Objects.equals(WncContactDeliveryRuleEnums.FrequentMethod.WEEK_CALC.getValue(),this.contactDeliveryRuleEntity.getFrequentMethod()) &&
                    Objects.equals(this.contactDeliveryRuleEntity.getWeekDeliveryFrequent(),"0")){
                return 0;
            }else if(this.contactDeliveryRuleEntity != null && Objects.equals(WncContactDeliveryRuleEnums.FrequentMethod.INTERVAL_CALC.getValue(),this.contactDeliveryRuleEntity.getFrequentMethod())
                    && Objects.equals(1,this.contactDeliveryRuleEntity.getDeliveryFrequentInterval())){
                //间隔时间是1天的也是日配
                return 0;
            }else{
                return 1;
            }
        }else if(this.fenceDeliveryEntity != null && "0".equals(this.fenceDeliveryEntity.getDeliveryFrequent())
                && Objects.equals(1,this.fenceDeliveryEntity.getFrequentMethod())){
            return 0;
        }else if(this.fenceDeliveryEntity != null && Objects.equals(1,this.fenceDeliveryEntity.getDeliveryFrequentInterval())
                && Objects.equals(2,this.fenceDeliveryEntity.getFrequentMethod())){
            return 0;
        }else{
            return 1;
        }
    }

    /**
     * 构建下单时刻的截单时间
     * @param orderTime 下单时间
     * @return 截单时间
     */
    public LocalDateTime buildOrderTimeCloseTime(LocalDateTime orderTime) {
        if(this.closeTime == null){
            throw new RuntimeException("构建截单时间不能为空");
        }
        return LocalDateTime.of(orderTime.toLocalDate(), this.closeTime);
    }
}
