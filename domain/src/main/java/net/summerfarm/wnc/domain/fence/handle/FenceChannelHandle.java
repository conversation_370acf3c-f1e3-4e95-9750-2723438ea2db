package net.summerfarm.wnc.domain.fence.handle;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.client.enums.SourceEnum;
import net.summerfarm.wnc.common.config.WncConfig;
import net.summerfarm.wnc.common.enums.FenceEnums;
import net.summerfarm.wnc.domain.fence.entity.DeliveryFenceEntity;
import net.summerfarm.wnc.domain.fence.entity.FenceChannelBusinessWhiteConfigEntity;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.xianmu.common.exception.BizException;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Description: 围栏渠道白名单处理器<br/>
 * date: 2024/10/14 15:46<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Component
public class FenceChannelHandle {

    @Resource
    private WncConfig wncConfig;

    /**
     * 检查当前客户渠道白名单
     * @param fenceEntity 围栏信息
     * @param customerMatchChannelConfig 根据客户信息匹配到的渠道配置
     * @return 结果 true:通过 false:不通过
     */
    public boolean checkCurrentCustomerMatchChannelBusinessWhite(FenceEntity fenceEntity, List<FenceChannelBusinessWhiteConfigEntity> customerMatchChannelConfig) {
        log.info("checkCurrentCustomerMatchChannelBusinessWhite fenceEntity:{},customerMatchChannelConfig:{}", JSON.toJSONString(fenceEntity), JSON.toJSONString(customerMatchChannelConfig));
        // 不存在围栏信息直接放行,因为存在客户指定城配仓会跳过围栏
        if(fenceEntity == null || fenceEntity.getId() == null){
            return true;
        }
        // 没有配置渠道白名单 不能通过
        if(CollectionUtils.isEmpty(customerMatchChannelConfig)){
            return false;
        }
        Optional<FenceChannelBusinessWhiteConfigEntity> result = customerMatchChannelConfig.stream()
                .filter(config -> Objects.equals(fenceEntity.getId(), config.getFenceId()))
                .findFirst();

        // 如果存在符合条件的元素，则取出来
        if (result.isPresent()) {
            // 处理符合条件的元素
            log.info("处理符合条件的元素:{}",JSON.toJSONString(result.get()));
            return true;
        } else {
            // 没有找到符合条件的元素
            return false;
        }
    }

    /**
     * 渠道校验
     * @param deliveryFenceEntity 配送围栏
     * @param sourceEnum  订单来源
     * @param fenceChannelBusinessWhiteConfigEntities 渠道白名单配置
     * @return 校验结果
     */
    public boolean channelCheck(DeliveryFenceEntity deliveryFenceEntity, SourceEnum sourceEnum, List<FenceChannelBusinessWhiteConfigEntity> fenceChannelBusinessWhiteConfigEntities) {
        // 围栏渠道校验（PS兼容刚上线逻辑）
        boolean fenceChannelFlag = this.orderChannelTypeCheck(deliveryFenceEntity, sourceEnum);

        // 围栏渠道业务校验开关
        boolean channelWhiteSupportFlag = true;
        if(wncConfig.isFenceChannelBusinessWhiteConfigOpen()){
            // 白名单渠道配置校验
            channelWhiteSupportFlag = this.checkCurrentCustomerMatchChannelBusinessWhite(deliveryFenceEntity.getFenceEntity(), fenceChannelBusinessWhiteConfigEntities);
        }

        return fenceChannelFlag && channelWhiteSupportFlag;
    }

    /**
     * 围栏渠道校验
     * @param deliveryFenceEntity 配送围栏实体
     * @param sourceEnum 来源
     */
    public boolean orderChannelTypeCheck(DeliveryFenceEntity deliveryFenceEntity, SourceEnum sourceEnum) {
        if (deliveryFenceEntity == null) {
            throw new BizException("此地区未开启配送");
        }
        FenceEntity fenceEntity = deliveryFenceEntity.getFenceEntity();
        if(fenceEntity == null){
            return true;
        }
        //获取当前客户类型
        String customerType = "";
        //鲜沐大客户
        if(deliveryFenceEntity.isXmBigCustomerFlag()){
            customerType = FenceEnums.OrderChannelType.XM_BIG_CUSTOM.getValue();
        }else if(SourceEnum.getSaasSource().contains(sourceEnum)){
            //Saas客户
            customerType = FenceEnums.OrderChannelType.SAAS.getValue();
        }else{
            //鲜沐客户
            customerType = FenceEnums.OrderChannelType.XM_CUSTOM.getValue();
        }

        if(!fenceEntity.checkOrderChannelType(customerType)){
            return false;
        }

        return true;
    }
}
