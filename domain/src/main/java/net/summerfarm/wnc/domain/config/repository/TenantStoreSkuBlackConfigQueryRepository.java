package net.summerfarm.wnc.domain.config.repository;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.domain.config.entity.TenantStoreSkuBlackConfigEntity;
import net.summerfarm.wnc.domain.config.param.query.WncTenantStoreSkuBlackConfigQueryParam;

import java.util.List;

/**
 * Description: 租户城配仓sku黑名单配置查询<br/>
 * date: 2024/8/26 15:14<br/>
 *
 * <AUTHOR> />
 */
public interface TenantStoreSkuBlackConfigQueryRepository{
    /**
     * 根据租户id和sku列表查询
     * @param tenantId 租户ID
     * @param skuList SKU集合
     * @return 结果
     */
    List<TenantStoreSkuBlackConfigEntity> queryByTenantIdAndSkuList(Long tenantId, List<String> skuList);

    PageInfo<TenantStoreSkuBlackConfigEntity> getPage(WncTenantStoreSkuBlackConfigQueryParam param);

    TenantStoreSkuBlackConfigEntity selectById(Long id);

    List<TenantStoreSkuBlackConfigEntity> selectByCondition(WncTenantStoreSkuBlackConfigQueryParam param);

}
