package net.summerfarm.wnc.domain.closeTime;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.domain.closeTime.entity.CloseTimeAreaConfigEntity;
import net.summerfarm.wnc.domain.closeTime.param.CloseTimeAreaConfigQueryParam;

import java.util.List;

/**
 * Description:截单时间配置仓库查询接口
 * date: 2024/3/20 10:32
 *
 * <AUTHOR>
 */
public interface CloseTimeConfigQueryRepository {

    /**
     * 分页查询
     * @param queryParam 查询参数
     * @return 结果
     */
    PageInfo<CloseTimeAreaConfigEntity> queryPage(CloseTimeAreaConfigQueryParam queryParam);

    /**
     * 查询列表
     * @param queryParam 查询参数
     * @return 结果
     */
    List<CloseTimeAreaConfigEntity> queryList(CloseTimeAreaConfigQueryParam queryParam);

    /**
     * 根据UK查询截单时间区域配置
     * @param tenantId 租户ID
     * @param city 行政市
     * @param area 行政区
     * @return 截单时间区域配置
     */
    CloseTimeAreaConfigEntity queryByUk(Long tenantId, String city, String area);
}
