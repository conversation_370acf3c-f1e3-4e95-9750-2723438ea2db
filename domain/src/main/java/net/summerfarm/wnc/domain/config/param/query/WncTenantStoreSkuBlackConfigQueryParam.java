package net.summerfarm.wnc.domain.config.param.query;

import java.time.LocalDateTime;
import lombok.Data;
import net.xianmu.common.input.BasePageInput;


/**
 * <AUTHOR>
 * @date 2024-09-12 14:00:16
 * @version 1.0
 *
 */
@Data
public class WncTenantStoreSkuBlackConfigQueryParam extends BasePageInput {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 租户ID
	 */
	private Long tenantId;

	/**
	 * 城配仓编号
	 */
	private Integer storeNo;

	/**
	 * sku
	 */
	private String sku;

	

	
}