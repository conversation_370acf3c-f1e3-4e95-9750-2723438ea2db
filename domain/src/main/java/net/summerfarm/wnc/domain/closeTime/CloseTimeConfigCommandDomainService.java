package net.summerfarm.wnc.domain.closeTime;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.common.constants.AppConsts;
import net.summerfarm.wnc.common.enums.CloseTimeConfigEnums;
import net.summerfarm.wnc.domain.closeTime.entity.CloseTimeAreaConfigEntity;
import net.summerfarm.wnc.domain.closeTime.param.CloseTimeAreaConfigCommandParam;
import net.summerfarm.wnc.domain.closeTime.param.CloseTimeAreaConfigQueryParam;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.user.UserInfoHolder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description:截单时间配置操作领域服务
 * date: 2024/3/20 10:37
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CloseTimeConfigCommandDomainService {

    private final CloseTimeConfigQueryRepository closeTimeConfigQueryRepository;
    private final CloseTimeConfigCommandRepository closeTimeConfigCommandRepository;

    /**
     * 生效截单时间区域配置
     */
    public void takeEffectAreaConfig() {
        //查询出带更新标记的配置
        List<CloseTimeAreaConfigEntity> updatedConfigs = closeTimeConfigQueryRepository.queryList(
                CloseTimeAreaConfigQueryParam.builder().updateFlag(CloseTimeConfigEnums.UpdateFlag.YES.getValue()).build());
        if (CollectionUtils.isEmpty(updatedConfigs)){
            return;
        }
        List<CloseTimeAreaConfigCommandParam> commandParams = updatedConfigs.stream().map(CloseTimeAreaConfigEntity::takeEffect).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(commandParams)){
            return;
        }
        List<Long> deleteIds = commandParams.stream().filter(e -> e.getCloseTime() == null)
                .map(CloseTimeAreaConfigCommandParam::getId).collect(Collectors.toList());
        List<CloseTimeAreaConfigCommandParam> updateParams = commandParams.stream().filter(e -> e.getCloseTime() != null)
                .sorted(Comparator.comparing(CloseTimeAreaConfigCommandParam::getId)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(deleteIds)){
            closeTimeConfigCommandRepository.batchRemoveAreaConfig(deleteIds);
        }
        if (!CollectionUtils.isEmpty(updateParams)){
            updateParams.forEach(closeTimeConfigCommandRepository::updateAreaConfig);
        }

    }

    /**
     * 编辑截单时间区域配置
     * @param commandParams 操作参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void editAreaConfig(List<CloseTimeAreaConfigCommandParam> commandParams) {
        if (CollectionUtils.isEmpty(commandParams)){
            return;
        }
        Long tenantId = commandParams.get(0).getTenantId();
        Long brandId = commandParams.get(0).getTenantBrandId();
        //查询出该租户下全部配置
        List<CloseTimeAreaConfigEntity> existedConfigs = closeTimeConfigQueryRepository.queryList(CloseTimeAreaConfigQueryParam.builder()
                .tenantId(tenantId)
                .brandId(brandId)
                .build());
        Map<String, CloseTimeAreaConfigEntity> closeTimeAreaConfigMap = Optional.ofNullable(existedConfigs).orElse(Lists.newArrayList()).stream()
                .collect(Collectors.toMap(CloseTimeAreaConfigEntity::buildAreaUk, Function.identity(), (oldData, newData) -> newData));

        List<CloseTimeAreaConfigCommandParam> saveParams = Lists.newArrayList();
        List<CloseTimeAreaConfigCommandParam> updateParams = Lists.newArrayList();
        List<Long> deleteIds = Lists.newArrayList();

        for (CloseTimeAreaConfigCommandParam commandParam : commandParams) {
            CloseTimeAreaConfigEntity existedConfig = closeTimeAreaConfigMap.get(commandParam.buildAreaUk());
            if (existedConfig == null){
                commandParam.create(UserInfoHolder.getUserRealName());
                saveParams.add(commandParam);
            }else {
                Boolean isUpdate = commandParam.update(existedConfig, UserInfoHolder.getUserRealName());
                if (isUpdate){
                    updateParams.add(commandParam);
                }else {
                    deleteIds.add(existedConfig.getId());
                }

            }
        }
        //首次创建
        if (!CollectionUtils.isEmpty(saveParams)){
            closeTimeConfigCommandRepository.batchSaveAreaConfig(saveParams);
        }
        //创建还未生效就被删除了
        if (!CollectionUtils.isEmpty(deleteIds)){
            closeTimeConfigCommandRepository.batchRemoveAreaConfig(deleteIds);
        }
        //已生效后更新了
        if (!CollectionUtils.isEmpty(updateParams)){
            updateParams.forEach(closeTimeConfigCommandRepository::updateAreaConfig);
        }

    }
}
