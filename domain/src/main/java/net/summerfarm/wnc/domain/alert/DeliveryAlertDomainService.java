package net.summerfarm.wnc.domain.alert;

import lombok.RequiredArgsConstructor;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.wnc.common.config.WncConfig;
import net.summerfarm.wnc.common.enums.DeliveryAlertEnums;
import net.summerfarm.wnc.common.query.alert.CompleteDeliveryTimeQueryParam;
import net.summerfarm.wnc.common.query.alert.LastDeliveryTimeQueryParam;
import net.summerfarm.wnc.domain.alert.entity.DeliveryAlertRuleItemVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalTime;
import java.util.List;

/**
 * Description:配送提醒 domain服务
 * date: 2023/3/21 15:07
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class DeliveryAlertDomainService {

    private final DeliveryAlertRuleRepository deliveryAlertRuleRepository;
    private final WncConfig wncConfig;
    private final CompleteDeliveryQueryRepository completeDeliveryQueryRepository;


    /**
     * 查询最晚配送时效
     * @param queryParam 查询
     * @return 时间
     */
    public LocalTime queryDeliveryAlertTimeFrameList(LastDeliveryTimeQueryParam queryParam) {
        DeliveryAlertEnums.Channel channel = queryParam.getChannel();
        Integer storeNo = queryParam.getStoreNo();
        String city = queryParam.getCity();

        if(channel == null){
            throw new TmsRuntimeException("渠道来源不能为空");
        }
        if(StringUtils.isBlank(city)){
            throw new TmsRuntimeException("城市不能为空");
        }
        if(storeNo == null){
            throw new TmsRuntimeException("城配仓编号不能为空");
        }


        //先获取门店规则配送时效
        if(StringUtils.isNotBlank(queryParam.getOuterClientId())){
            DeliveryAlertRuleItemVO rule = deliveryAlertRuleRepository.queryByUk(storeNo,channel, DeliveryAlertEnums.Type.MERCHANT, queryParam.getOuterClientId());
            if(rule != null){
                return rule.getEndTime();
            }
        }
        //获取品牌规则配送时效
        if(StringUtils.isNotBlank(queryParam.getOuterTenantId())){
            DeliveryAlertRuleItemVO rule = deliveryAlertRuleRepository.queryByUk(storeNo,channel, DeliveryAlertEnums.Type.BRAND, queryParam.getOuterTenantId());
            if(rule != null){
                return rule.getEndTime();
            }
        }

        //获取地区规则配送时效
        if(StringUtils.isNotBlank(city)){
            CompleteDeliveryTimeQueryParam query = CompleteDeliveryTimeQueryParam.builder()
                    .storeNo(storeNo)
                    .city(city)
                    .area(queryParam.getArea())
                    .build();
            List<LocalTime> lastTimes = completeDeliveryQueryRepository.queryListAreaLastTime(query);
            if(!CollectionUtils.isEmpty(lastTimes)){
                return lastTimes.get(0);
            }
        }
        //默认配送规则
        return wncConfig.queryDefaultLastDelivertTime();
    }
}
