package net.summerfarm.wnc.domain.warehouse.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2023/4/6 11:19<br/>
 *
 * <AUTHOR> />
 */
@Data
public class WncWarehouseStorageTenantEntity {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 仓库编号
     */
    private Long warehouseNo;

    /**
     * 租户Id
     */
    private Long tenantId;
}
