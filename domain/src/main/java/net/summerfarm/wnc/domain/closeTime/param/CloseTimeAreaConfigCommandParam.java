package net.summerfarm.wnc.domain.closeTime.param;

import lombok.Data;
import net.summerfarm.wnc.common.constants.AppConsts;
import net.summerfarm.wnc.common.enums.CloseTimeConfigEnums;
import net.summerfarm.wnc.domain.closeTime.entity.CloseTimeAreaConfigEntity;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Objects;
import java.util.Optional;

/**
 * Description:截单时间区域配置操作参数
 * date: 2024/3/20 11:09
 *
 * <AUTHOR>
 */
@Data
public class CloseTimeAreaConfigCommandParam implements Serializable {

    private static final long serialVersionUID = 6103439011804991787L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 品牌ID
     */
    private Long brandId;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 截单时间
     */
    private LocalTime closeTime;

    /**
     * 更新截单时间
     */
    private LocalTime updateCloseTime;

    /**
     * 有无更新标识，0：无，1：有
     */
    private Integer updateFlag;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public String buildAreaUk(){
        return String.format("%s#%s", this.city, this.area == null ? "" : this.area);
    }

    public void create(String creator) {
        this.creator = creator;
        this.createTime = LocalDateTime.now();
        this.brandId = this.getTenantBrandId();
        this.updateCloseTime = this.closeTime;
        this.closeTime = null;
        this.updateFlag = CloseTimeConfigEnums.UpdateFlag.YES.getValue();
    }

    public Boolean update(CloseTimeAreaConfigEntity existedConfig, String updater) {
        if (existedConfig.getCloseTime() == null && this.closeTime == null){
            return Boolean.FALSE;
        }
        this.id = existedConfig.getId();
        this.updater = updater;
        this.updateTime = LocalDateTime.now();
        this.updateCloseTime = this.closeTime;
        //截单时间置空不做修改
        this.closeTime = null;
        this.updateFlag = CloseTimeConfigEnums.UpdateFlag.YES.getValue();
        return Boolean.TRUE;
    }

    public Long getTenantBrandId(){
        return Optional.ofNullable(this.brandId).orElse(AppConsts.Tenant.DEFAULT_BRAND_ID) ;
    }
}
