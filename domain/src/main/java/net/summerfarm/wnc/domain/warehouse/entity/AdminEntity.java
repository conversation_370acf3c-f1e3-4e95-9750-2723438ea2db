package net.summerfarm.wnc.domain.warehouse.entity;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2023/4/26 11:56<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
public class AdminEntity {

    /**
     * 名称
     */
    private String manageAdminName;


    private Integer adminId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 登录失败次数，若到达五次则锁定账户；
     */
    private Integer loginFailTimes;

    /**
     * 是否禁用
     */
    private Boolean isDisabled;

    private String username;

    private String password;

    private LocalDateTime loginTime;

    private String realname;

    private Boolean gender;

    private String department;

    private String phone;

    private String kp;

    private Integer salerId;

    private String salerName;

    private String contract;

    private String contractMethod;

    /**
     * 名称备注
     */
    private String nameRemakes;

    /**
     * 运营
     */
    private Integer operateId;

    /**
     * 报价周期： 0周报价, 1半月报价, 2月报价，3日报价
     */
    private Integer majorCycle;

    /**
     * 大客户是否提前截单 0 不是 1 是
     */
    private Integer closeOrderType;

    /**
     * 标识ka大客户状态0（默认）非ka客户或是，1:试样，2:报价，3:试配，4:合作稳定期，5:合作困难期，6:流失，7:暂不合作
     */
    private Integer cooperationStage;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 大客户提前截单时间
     */
    private String closeOrderTime;

    /**
     * 修改后大客户提前截单时间
     */
    private String updateCloseOrderTime;

    /**
     * 是否开启低价监控：t、开启 f、关闭
     */
    private Boolean lowPriceRemainder;

    /**
     * 低价监控排除城市
     */
    private String notIncludedArea;

    /**
     * 是否是特殊捡货大客户
     */
    private Boolean skuSorting;

    /**
     * 客户类别 0大客户 1普通 2批发客户
     */
    private Integer adminType;

    /**
     * 连锁范围0（NKA）1(LKA) 2(其他连锁)
     */
    private Integer adminChain;

    /**
     * 品牌等级 0普通 1KA
     */
    private Integer adminGrade;

    /**
     * 充送开关 0 开启，1 关闭
     */
    private Integer adminSwitch;

    /**
     * 统一社会信用代码
     */
    private String creditCode;

    /**
     * 营业执照地址
     */
    private String businessLicenseAddress;

    /**
     * 票到付款 0 是 1 否
     */
    private Integer billToPay;

    /**
     * auth中base_user_id
     */
    private Long baseUserId;


}
