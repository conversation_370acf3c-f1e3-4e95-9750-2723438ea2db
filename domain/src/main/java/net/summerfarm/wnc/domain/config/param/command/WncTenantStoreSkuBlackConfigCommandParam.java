package net.summerfarm.wnc.domain.config.param.command;

import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2024-09-12 14:00:16
 * @version 1.0
 *
 */
@Data
public class WncTenantStoreSkuBlackConfigCommandParam {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 租户ID
	 */
	private Long tenantId;

	/**
	 * 城配仓编号
	 */
	private Integer storeNo;

	/**
	 * sku
	 */
	private String sku;

	

	
}