package net.summerfarm.wnc.domain.fence;

import net.summerfarm.wnc.domain.fence.entity.OutLandContactEntity;

import java.util.List;

/**
 * Description: <br/>
 * date: 2023/12/15 13:50<br/>
 *
 * <AUTHOR> />
 */
public interface ContactGdPoiDistanceRepository {
    /**
     * 计算客户和高德POI之间的距离
     * @param outLandContactEntities 客户信息
     */
    void contactPoiDistanceSave(List<OutLandContactEntity> outLandContactEntities);

    /**
     * 清空数据
     */
    void deleteContactPoiDistance();
}
