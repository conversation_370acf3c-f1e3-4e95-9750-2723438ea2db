package net.summerfarm.wnc.domain.warehouse.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Description: 城配仓点位信息
 * date: 2023/10/7 18:16<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StoreSiteEntity {

    /**
     * 是否需要打卡0 不需要 1需要
     */
    private Integer punchState;

    /**
     * 打卡距离
     */
    private BigDecimal punchDistance;

    /**
     * 出仓时间 hh:mm:ss
     */
    private String outTime;

    /**
     * 0智能排线 1手动排线
     */
    private Integer intelligencePath;
}
