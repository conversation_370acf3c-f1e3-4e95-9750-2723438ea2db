package net.summerfarm.wnc.domain.config.service;


import net.summerfarm.wnc.domain.config.repository.WncFullCategoryWarehouseSkuWhiteConfigQueryRepository;
import net.summerfarm.wnc.domain.config.repository.WncFullCategoryWarehouseSkuWhiteConfigCommandRepository;
import net.summerfarm.wnc.domain.config.entity.WncFullCategoryWarehouseSkuWhiteConfigEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 全品类仓加SKU白名单配置领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-01-06 15:31:26
 * @version 1.0
 *
 */
@Service
public class WncFullCategoryWarehouseSkuWhiteConfigQueryDomainService {


}
