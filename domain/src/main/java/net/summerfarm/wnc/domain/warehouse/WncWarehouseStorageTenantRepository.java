package net.summerfarm.wnc.domain.warehouse;

import net.summerfarm.wnc.common.query.warehouse.WncWarehouseStorageTenantQuery;
import net.summerfarm.wnc.domain.warehouse.entity.WncWarehouseStorageTenantEntity;

import java.util.List;

/**
 * Description: <br/>
 * date: 2023/4/6 11:08<br/>
 *
 * <AUTHOR> />
 */
public interface WncWarehouseStorageTenantRepository {
    /**
     * 根据唯一索引进行穿
     * @param warehouseNo 仓库编号
     * @param tenantId 租户id
     * @return 结果
     */
    WncWarehouseStorageTenantEntity queryUk(Long warehouseNo, Long tenantId);

    /**
     * 新增
     * @param rsWarehouseStorageTenantEntity 实体
     */
    void save(WncWarehouseStorageTenantEntity rsWarehouseStorageTenantEntity);

    /**
     * 删除
     * @param rsWarehouseStorageTenantEntity 实体
     */
    void remove(WncWarehouseStorageTenantEntity rsWarehouseStorageTenantEntity);

    /**
     * 查询集合
     * @param rsWarehouseStorageTenantQuery 查询
     * @return 结果
     */
    List<WncWarehouseStorageTenantEntity> queryList(WncWarehouseStorageTenantQuery rsWarehouseStorageTenantQuery);

}
