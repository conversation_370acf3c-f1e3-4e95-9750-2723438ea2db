package net.summerfarm.wnc.domain.fence.valueObject;

import lombok.Data;

import java.time.LocalTime;

/**
 * Description: 围栏大客户值对象<br/>
 * date: 2024/3/1 14:18<br/>
 *
 * <AUTHOR> />
 */
@Data
public class FenceBigCustomerValueObject {

    /**
     * 大客户截单时间
     */
    private LocalTime closeOrderTime;

    /**
     * 大客户标识 true大客户
     */
    private boolean bigCustomerFlag;

    /**
     * 鲜沐大客户ID
     */
    private Integer xmBigAdminId;
}
