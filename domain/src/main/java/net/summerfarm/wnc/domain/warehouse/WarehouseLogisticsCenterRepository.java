package net.summerfarm.wnc.domain.warehouse;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.common.query.warehouse.WarehouseLogisticsQuery;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseLogisticsCenterEntity;

import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-06-20
 **/
public interface WarehouseLogisticsCenterRepository {
	LocalTime selectCloseTime(Integer storeNo);

    List<WarehouseLogisticsCenterEntity> queryListByStoreNos(List<Integer> storeNos);

    /**
     * 分页查询城配仓信息
     * @param warehouseLogisticsQuery 查询
     * @return 结果
     */
    PageInfo<WarehouseLogisticsCenterEntity> queryPageList(WarehouseLogisticsQuery warehouseLogisticsQuery);

	/**
	 * 根据UK查询城配仓
	 * @param storeNo 城配仓编号
	 * @return 城配仓信息
	 */
	WarehouseLogisticsCenterEntity queryByUk(Integer storeNo);

	/**
	 * 查询城配仓信息
	 * @param storeNos 城配仓编号集合
	 * @param status 状态
	 * @return 结果
	 */
	List<WarehouseLogisticsCenterEntity> queryListByStoreNosAndStatus(List<Integer> storeNos, Integer status);

	/**
	 * 根据主键ID查询城配仓信息
	 * @param id 主键ID
	 * @return 结果
	 */
	WarehouseLogisticsCenterEntity queryById(Integer id);

	/**
	 * 查询城配仓数量
	 * @param storeName 城配仓名称
	 * @param id 主键ID
	 * @return
	 */
    long countByNameAndId(String storeName, Integer id);

	/**
	 * 更新
	 * @param entity 更新实体
	 */
	void updateById(WarehouseLogisticsCenterEntity entity);

	/**
	 * 获取最大城配仓编号
	 * @return 城配仓编号
	 */
    int selectMaxStoreNo();

	/**
	 * 保存城配仓信息
	 * @param entity 城配信息
	 */
	void save(WarehouseLogisticsCenterEntity entity);

	/**
	 * 城配仓取消更改截单时间
	 * @param storeNo 城配仓编号
	 */
    void cancelUpdateCloseTime(Integer storeNo);

	/**
	 * 批量查询城配仓信息
	 * @param query 查询
	 * @return 结果
	 */
	List<WarehouseLogisticsCenterEntity> queryList(WarehouseLogisticsQuery query);

	/**
	 * 查询城配仓基本信息
	 * @param query 查询
	 * @return 结果
	 */
    List<WarehouseLogisticsCenterEntity> queryLogisticsList(WarehouseLogisticsQuery query);
}
