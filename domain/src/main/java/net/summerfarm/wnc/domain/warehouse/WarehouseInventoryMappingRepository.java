package net.summerfarm.wnc.domain.warehouse;

import net.summerfarm.wnc.common.query.warehouse.SkuWarehouseMappingQuery;
import net.summerfarm.wnc.common.query.warehouse.WarehouseInventoryMappingQuery;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseInventoryMappingEntity;

import java.util.List;

/**
 * Description: <br/>
 * date: 2023/4/7 18:33<br/>
 *
 * <AUTHOR> />
 */
public interface WarehouseInventoryMappingRepository {

    /**
     * 查询
     * @param warehouseInventoryMappingQuery 查询
     * @return 结果
     */
    List<WarehouseInventoryMappingEntity> queryList(WarehouseInventoryMappingQuery warehouseInventoryMappingQuery);

    /**
     * 批量插入
     * @param needCreateMapping 映射关系
     */
    void batchSave(List<WarehouseInventoryMappingEntity> needCreateMapping);

    /**
     * 查询SKU库存仓映射关系
     * @param skuWarehouseMappingQueries 查询
     * @return SKU库存仓映射关系集合
     */
    List<WarehouseInventoryMappingEntity> queryListBySkuStoreNo(List<SkuWarehouseMappingQuery> skuWarehouseMappingQueries);

    /**
     * 根据城配仓编号和库存仓编号查询映射关系数量
     * @param storeNo 城配仓编号
     * @param warehouseNo 库存仓编号
     * @return
     */
    long queryCountByStoreWarehouseNo(Integer storeNo, Integer warehouseNo);

    /**
     * 查询SKU库存仓映射关系
     * @param skuWarehouseMappingQueries 查询
     * @return SKU库存仓映射关系集合
     */
    List<WarehouseInventoryMappingEntity> queryMappingByUkList(List<SkuWarehouseMappingQuery> skuWarehouseMappingQueries);

    /**
     * 批量更新
     * @param needUpdateList 更新数据
     */
    void batchUpdate(List<WarehouseInventoryMappingEntity> needUpdateList);

    /**
     * 查询SKU库存使用关系数量
     * @param storeNo 城配仓编号
     * @return 结构
     */
    Long queryCountByStoreNo(Integer storeNo);

    /**
     * 查询城配仓库存仓有对应关系的映射集合
     * @param skuList sku集合
     * @param warehouseNos 仓库编号集合
     * @return 结果
     */
    List<WarehouseInventoryMappingEntity> queryValideStoreWarehouseNoMappingBySkuWarehouseNos(List<String> skuList, List<Integer> warehouseNos);

    /**
     * 查询城配仓库存仓有对应关系的映射集合
     * @param skuList sku集合
     * @param storeNos 城配仓编号集合
     * @return 结果
     */
    List<WarehouseInventoryMappingEntity> queryValideStoreWarehouseNoMappingBySkuStoreNos(List<String> skuList, List<Integer> storeNos);
}
