package net.summerfarm.wnc.domain.config.monit;

import lombok.Data;

/**
 * Description: pop门店监控<br/>
 * date: 2024/8/2 15:23<br/>
 *
 * <AUTHOR> />
 */
@Data
public class PopMerchantMonit {

    /**
     * 地址ID
     */
    private Integer contactId;

    /**
     * 门店ID
     */
    private Integer merchantId;

    /**
     * 门店名称
     */
    private String mname;

    @Override
    public String toString(){
        return "地址ID:" + contactId + ", 门店ID:" + merchantId + ", 门店名称:'" + mname;
    }
}
