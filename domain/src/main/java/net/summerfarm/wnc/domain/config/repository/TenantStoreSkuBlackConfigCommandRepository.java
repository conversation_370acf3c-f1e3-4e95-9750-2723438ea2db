package net.summerfarm.wnc.domain.config.repository;

import net.summerfarm.wnc.domain.config.entity.TenantStoreSkuBlackConfigEntity;
import net.summerfarm.wnc.domain.config.param.command.WncTenantStoreSkuBlackConfigCommandParam;

/**
 * Description: 租户城配仓sku黑名单配置写操作<br/>
 * date: 2024/8/26 15:17<br/>
 *
 * <AUTHOR> />
 */
public interface TenantStoreSkuBlackConfigCommandRepository {
    TenantStoreSkuBlackConfigEntity insertSelective(WncTenantStoreSkuBlackConfigCommandParam param);

    int updateSelectiveById(WncTenantStoreSkuBlackConfigCommandParam param);

    int remove(Long id);

}
