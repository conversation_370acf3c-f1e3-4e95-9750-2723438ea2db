package net.summerfarm.wnc.domain.warehouse;

import net.summerfarm.wnc.domain.fence.entity.StopDeliveryEntity;

import java.util.List;
import java.util.Map;

/**
 * Description: <br/>
 * date: 2023/9/21 17:58<br/>
 *
 * <AUTHOR> />
 */
public interface StopDeliveryRepository {
    /**
     * 查询城配仓最近一条的停配规则
     * @param storeNoList 城配仓编号
     * @return 结果
     */
    List<StopDeliveryEntity> queryRecentlyStopByStoreNoList(List<Integer> storeNoList);

    /**
     * 查询城配仓最近的停配
     * @param storeNoList 城配仓编号
     * @return 结果
     */
    Map<Integer, StopDeliveryEntity> queryRecentlyStopMapByStoreNos(List<Integer> storeNoList);

    /**
     * 更新停配状态
     * @param storeNos 城配仓编号
     */
    void updateDeleteStatus(List<Integer> storeNos);

    /**
     * 保存停配信息
     * @param stopDeliveryEntities 停配信息
     */
    void batchSave(List<StopDeliveryEntity> stopDeliveryEntities);
}
