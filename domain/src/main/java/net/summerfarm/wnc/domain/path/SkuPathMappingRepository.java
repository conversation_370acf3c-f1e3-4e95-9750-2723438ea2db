package net.summerfarm.wnc.domain.path;

import net.summerfarm.wnc.domain.path.entity.SkuPathMappingEntity;

import java.util.List;

/**
 * Description: <br/>
 * date: 2023/11/28 14:32<br/>
 *
 * <AUTHOR> />
 */
public interface SkuPathMappingRepository {
    /**
     * 查询sku路线仓库信息
     * @param skuList sku集合
     * @param warehouseList 仓库集合
     * @return 结果
     */
    List<SkuPathMappingEntity> queryList(List<String> skuList, List<Integer> warehouseList);

    /**
     * 批量保存
     * @param skuPathMappingEntities 集合
     */
    void batchSave(List<SkuPathMappingEntity> skuPathMappingEntities);

    /**
     * 批量更新
     * @param skuPathMappingEntities 集合
     */
    void batchUpdate(List<SkuPathMappingEntity> skuPathMappingEntities);

    /**
     * 根据路线信息查询sku配置信息
     * @param id 路线ID
     * @return 结果
     */
    List<SkuPathMappingEntity> queryByPathId(Long id);

    /**
     * 根据sku和路径ID查询sku配置信息
     * @param skus sku集合
     * @param pathIdList 路径ID集合
     * @return 结果
     */
    List<SkuPathMappingEntity> queryListBySkusPathIdList(List<String> skus, List<Long> pathIdList);

    /**
     * 根据sku和仓库，删除sku仓库路线配置信息
     * @param skuPathMappingEntities 信息
     */
    void batchDeleteByWarehouseNosSkus(List<SkuPathMappingEntity> skuPathMappingEntities);
}
