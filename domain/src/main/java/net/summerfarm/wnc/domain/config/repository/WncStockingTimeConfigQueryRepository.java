package net.summerfarm.wnc.domain.config.repository;



import com.github.pagehelper.PageInfo;
import java.util.List;
import net.summerfarm.wnc.domain.config.entity.WncStockingTimeConfigEntity;
import net.summerfarm.wnc.domain.config.param.query.WncStockingTimeConfigQueryParam;



/**
*
* <AUTHOR>
* @date 2025-04-15 14:51:44
* @version 1.0
*
*/
public interface WncStockingTimeConfigQueryRepository {

    PageInfo<WncStockingTimeConfigEntity> getPage(WncStockingTimeConfigQueryParam param);

    WncStockingTimeConfigEntity selectById(Long id);

    List<WncStockingTimeConfigEntity> selectByCondition(WncStockingTimeConfigQueryParam param);

}