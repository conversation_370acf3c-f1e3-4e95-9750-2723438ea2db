package net.summerfarm.wnc.config.inbound.provider;

import net.summerfarm.wnc.application.inbound.provider.warehouseMapping.WarehouseInventoryMappingQueryProviderImpl;
import net.summerfarm.wnc.client.req.warehouseMapping.BatchMappingWarehouseQueryReq;
import net.summerfarm.wnc.client.resp.warehouseMapping.BatchMappingWarehouseResp;
import net.summerfarm.wnc.starter.Application;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * Description: 映射关系查询测试<br/>
 * date: 2024/6/24 14:23<br/>
 *
 * <AUTHOR> />
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class WarehouseInventoryMappingQueryProviderImplTest {

    @Resource
    private WarehouseInventoryMappingQueryProviderImpl warehouseInventoryMappingQueryProvider;

    @Test
    public void queryBatchWarehouseMapping(){
        BatchMappingWarehouseQueryReq req = new BatchMappingWarehouseQueryReq();
        req.setAreaNo(29371);
        req.setSkuList(Arrays.asList("2160247810060"));
        DubboResponse<List<BatchMappingWarehouseResp>> listDubboResponse = warehouseInventoryMappingQueryProvider.queryBatchWarehouseMapping(req);
        System.out.println(listDubboResponse);
    }
}
