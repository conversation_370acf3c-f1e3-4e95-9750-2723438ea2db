package net.summerfarm.wnc.config.inbound.provider;

import com.alibaba.fastjson.JSON;
import net.summerfarm.wnc.client.req.fence.FenceQueryReq;
import net.summerfarm.wnc.client.req.storeConfig.ContactConfigAdjustBatchCommandReq;
import net.summerfarm.wnc.client.resp.fence.FenceResp;
import net.summerfarm.wnc.client.resp.storeConfig.ContactConfigAdjustBatchResp;
import net.summerfarm.wnc.inbound.provider.fence.DeliveryFenceQueryProviderImpl;
import net.summerfarm.wnc.starter.Application;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * Description:配送围栏dubbo测试类
 * date: 2024/1/11 15:20
 *
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class DeliveryFenceQueryProviderImplTest {

    @Resource
    private DeliveryFenceQueryProviderImpl deliveryFenceQueryProvider;


    @Test
    public void testQueryFenceListWithArea(){
        FenceQueryReq queryReq = JSON.parseObject("{\"areaNo\":1001}", FenceQueryReq.class);
        DubboResponse<List<FenceResp>> response = deliveryFenceQueryProvider.queryFenceListWithArea(queryReq);
        Assertions.assertNotNull(response);
        Assertions.assertTrue(response.isSuccess());
    }

    @Test
    public void testQueryFenceListWithArea1(){
        FenceQueryReq queryReq = JSON.parseObject("{\"fenceId\":27}", FenceQueryReq.class);
        DubboResponse<List<FenceResp>> response = deliveryFenceQueryProvider.queryFenceListWithArea(queryReq);
        Assertions.assertNotNull(response);
        Assertions.assertTrue(response.isSuccess());
    }

    @Test
    public void testQueryFenceListWithArea2(){
        FenceQueryReq queryReq = JSON.parseObject("{\"fenceIds\":[27,28]}", FenceQueryReq.class);
        DubboResponse<List<FenceResp>> response = deliveryFenceQueryProvider.queryFenceListWithArea(queryReq);
        Assertions.assertNotNull(response);
        Assertions.assertTrue(response.isSuccess());
    }
}
