package net.summerfarm.wnc.config.inbound.provider;

import com.alibaba.fastjson.JSON;
import net.summerfarm.wnc.client.req.storeConfig.ContactConfigAdjustBatchCommandReq;
import net.summerfarm.wnc.client.resp.storeConfig.ContactConfigAdjustBatchResp;
import net.summerfarm.wnc.inbound.provider.config.ContactConfigCommandProviderImpl;
import net.summerfarm.wnc.starter.Application;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * Description:联系人配置dubbo测试类
 * date: 2024/1/4 11:26
 *
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class ContactConfigCommandProviderImplTest {

    @Resource
    private ContactConfigCommandProviderImpl contactConfigCommandProvider;

    @Test
    public void testBatchAdjustContactConfig(){
        //快递履约
        ContactConfigAdjustBatchCommandReq commandReq = JSON.parseObject("{\"commandReqs\":[{\"addressReq\":{\"address\":\"新杭商务中心\",\"area\":\"西湖区\",\"city\":\"杭州市\",\"province\":\"浙江省\"},\"contactId\":151793,\"fulfillmentMethod\":1,\"storeNo\":253,\"tenantId\":2}]}", ContactConfigAdjustBatchCommandReq.class);
        DubboResponse<ContactConfigAdjustBatchResp> response = contactConfigCommandProvider.batchAdjustContactConfig(commandReq);
        System.out.println(response);
    }

    @Test
    public void testBatchAdjustContactConfig1(){
        //城配履约
        ContactConfigAdjustBatchCommandReq commandReq = JSON.parseObject("{\"commandReqs\":[{\"addressReq\":{\"address\":\"新杭商务中心\",\"area\":\"西湖区\",\"city\":\"杭州市\",\"province\":\"浙江省\"},\"contactId\":151793,\"fulfillmentMethod\":0,\"tenantId\":2}]}", ContactConfigAdjustBatchCommandReq.class);
        DubboResponse<ContactConfigAdjustBatchResp> response = contactConfigCommandProvider.batchAdjustContactConfig(commandReq);
        System.out.println(response);
    }
}
