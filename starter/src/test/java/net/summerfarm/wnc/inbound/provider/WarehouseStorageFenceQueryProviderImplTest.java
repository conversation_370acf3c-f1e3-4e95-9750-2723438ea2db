package net.summerfarm.wnc.inbound.provider;

import net.summerfarm.wnc.client.req.WarehouseStorageFenceQueryReq;
import net.summerfarm.wnc.client.resp.WarehouseStorageFenceRuleResp;
import net.summerfarm.wnc.inbound.provider.fence.WarehouseStorageFenceQueryProviderImpl;
import net.summerfarm.wnc.starter.Application;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * Description: 仓库围栏调度<br/>
 * date: 2024/3/4 13:42<br/>
 *
 * <AUTHOR> />
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class WarehouseStorageFenceQueryProviderImplTest {

    @Resource
    private WarehouseStorageFenceQueryProviderImpl warehouseStorageFenceQueryProvider;

    @Test
    public void queryWarehouseStorageFence(){
        WarehouseStorageFenceQueryReq queryAddressSkuInventoryReqDTO = new WarehouseStorageFenceQueryReq();
        queryAddressSkuInventoryReqDTO.setTenantId(1L);
        //queryAddressSkuInventoryReqDTO.setStoreNo(1);
        queryAddressSkuInventoryReqDTO.setContactId(349094L);
        queryAddressSkuInventoryReqDTO.setMerchantId(350322L);
        queryAddressSkuInventoryReqDTO.setSource(230);
        queryAddressSkuInventoryReqDTO.setArea("西湖区");
        queryAddressSkuInventoryReqDTO.setCity("杭州市");
        queryAddressSkuInventoryReqDTO.setPoi("113.03853,28.135795");
        queryAddressSkuInventoryReqDTO.setSkuList(Arrays.asList("2180126341877", "2180610666348","2185132172024","2185764770724","2192144020618"));
        DubboResponse<List<WarehouseStorageFenceRuleResp>> listDubboResponse = warehouseStorageFenceQueryProvider.queryWarehouseStorageFence(queryAddressSkuInventoryReqDTO);
        System.out.println(listDubboResponse);


       /* queryAddressSkuInventoryReqDTO.setTenantId(2L);
        queryAddressSkuInventoryReqDTO.setContactId(162489L);
        queryAddressSkuInventoryReqDTO.setSource(210);
        queryAddressSkuInventoryReqDTO.setArea("余杭区");
        queryAddressSkuInventoryReqDTO.setCity("杭州市");
        queryAddressSkuInventoryReqDTO.setPoi("113.03853,28.135795");
        queryAddressSkuInventoryReqDTO.setSkuList(Arrays.asList("**********", "**********"));
        DubboResponse<List<WarehouseStorageFenceRuleResp>> listDubboResponse2 = warehouseStorageFenceQueryProvider.queryWarehouseStorageFence(queryAddressSkuInventoryReqDTO);
        System.out.println(listDubboResponse2);*/
    }
}
