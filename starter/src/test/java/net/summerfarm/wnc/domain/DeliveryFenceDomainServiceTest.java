package net.summerfarm.wnc.domain;

import com.alibaba.schedulerx.shade.com.google.common.collect.Lists;
import net.summerfarm.wnc.client.enums.SourceEnum;
import net.summerfarm.wnc.client.req.WarehouseBySkuStoreNoDataReq;
import net.summerfarm.wnc.client.req.WarehouseBySkuStoreNoQueryReq;
import net.summerfarm.wnc.client.resp.SkuWarehouseMappingResp;
import net.summerfarm.wnc.common.query.fence.FenceQuery;
import net.summerfarm.wnc.domain.fence.DeliveryFenceDomainService;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.summerfarm.wnc.domain.fence.param.CloseTimeQueryParam;
import net.summerfarm.wnc.inbound.provider.warehouse.WarehouseSkuAreaNoQueryProviderImpl;
import net.summerfarm.wnc.starter.Application;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalTime;
import java.util.List;

/**
 * Description: 配送围栏领域服务测试类
 * date: 2023/6/8 15:53
 *
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class DeliveryFenceDomainServiceTest {

    @Resource
    private DeliveryFenceDomainService deliveryFenceDomainService;
    @Resource
    private WarehouseSkuAreaNoQueryProviderImpl warehouseSkuAreaNoQueryProvider;

    @Test
    public void testQueryDeliveryFence(){
        FenceQuery fenceQuery = FenceQuery.builder().city("杭州市").area("西湖区").build();
        FenceEntity fenceEntity = deliveryFenceDomainService.queryDeliveryFence(fenceQuery);
        Assertions.assertEquals(3229,fenceEntity.getId());
    }

    @Test
    public void testXmQueryDeliveryFence(){
        FenceQuery fenceQuery = FenceQuery.builder().city("中山市").area(null).build();
        FenceEntity fenceEntity = deliveryFenceDomainService.queryDeliveryFence(fenceQuery);
        Assertions.assertEquals(3455,fenceEntity.getId());
    }

    @Test
    public void testSaasDeliveryFence(){
        FenceQuery fenceQuery = FenceQuery.builder().city("中山市").area("xx街道").build();
        FenceEntity fenceEntity = deliveryFenceDomainService.queryDeliveryFence(fenceQuery);
        Assertions.assertEquals(3455,fenceEntity.getId());
    }

    @Test
    public void testQuerySkuWarehouseMappings(){
        WarehouseBySkuStoreNoQueryReq req = new WarehouseBySkuStoreNoQueryReq();
        WarehouseBySkuStoreNoDataReq warehouse = new WarehouseBySkuStoreNoDataReq();
        warehouse.setSku("858225423081");
        warehouse.setStoreNo(13);
        req.setWarehouseBySkuStoreNoDataReqList(Lists.newArrayList(warehouse));
        DubboResponse<List<SkuWarehouseMappingResp>> listDubboResponse = warehouseSkuAreaNoQueryProvider.querySkuWarehouseMappings(req);
        System.out.println();
    }

    @Test
    public void queryCloseTime(){
        CloseTimeQueryParam queryParam = CloseTimeQueryParam.builder()
                .tenantId(1L)
                .city("杭州市")
                .area("滨江区123")
                .source(SourceEnum.XM_AFTER_SALE)
                .contactId(1L)
                .merchantId(1L)
                .build();
        LocalTime localTime = deliveryFenceDomainService.queryCloseTime(queryParam);
        System.out.println(localTime);
    }
}
