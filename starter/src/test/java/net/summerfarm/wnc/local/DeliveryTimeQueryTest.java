package net.summerfarm.wnc.local;

import cn.hutool.json.JSONUtil;
import com.alibaba.excel.util.DateUtils;
import com.google.common.collect.Lists;
import net.summerfarm.wnc.client.enums.SourceEnum;
import net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider;
import net.summerfarm.wnc.client.provider.fence.DeliveryFenceQueryProvider;
import net.summerfarm.wnc.client.provider.lbs.LbsProvider;
import net.summerfarm.wnc.client.provider.warehouse.WarehouseSkuAreaNoQueryProvider;
import net.summerfarm.wnc.client.provider.warehouse.WarehouseStorageQueryProvider;
import net.summerfarm.wnc.client.req.*;
import net.summerfarm.wnc.client.req.delivery.PayAfterDeliveryDateQueryReq;
import net.summerfarm.wnc.client.resp.*;
import net.summerfarm.wnc.client.resp.delivery.PayAfterDeliveryDateResp;
import net.summerfarm.wnc.common.query.warehouse.SkuWarehouseMappingQuery;
import net.summerfarm.wnc.domain.warehouse.WarehouseInventoryMappingRepository;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseInventoryMappingEntity;
import net.summerfarm.wnc.starter.Application;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023-06-30
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class DeliveryTimeQueryTest {

	@Resource
	private WarehouseSkuAreaNoQueryProvider warehouseSkuAreaNoQueryProvider;
	@Resource
	private WarehouseStorageQueryProvider warehouseStorageQueryProvider;
	@Resource
	private DeliveryFenceQueryProvider deliveryFenceQueryProvider;
	@Resource
	private LbsProvider lbsProvider;

	@Resource
	private DeliveryRuleQueryProvider deliveryRuleQueryProvider;
	@Resource
	private WarehouseInventoryMappingRepository warehouseInventoryMappingRepository;
	@Test
	public void xmMallQueryDeliveryDate() {
		DeliveryRuleQueryReq req = new DeliveryRuleQueryReq();
		req.setContactId(352852L);
		req.setMerchantId(351899L);
		req.setOrderTime(DateUtils.parseLocalDateTime("2024-05-08 18:58:00", net.summerfarm.tms.util.DateUtils.LONG_DATE_FORMAT, null));
		/*req.setArea("余江区");
		req.setCity("鹰潭市");*/
		req.setSource(SourceEnum.POP_MALL);
		/*req.setQueryBeginDate(LocalDate.now());
		req.setQueryEndDate(LocalDate.now().plusDays(30));*/
		//********** **********
		req.setSkus(Lists.newArrayList("2185235215881","2210017120005","2207037775264","2176333028157","2176873037771","2210450125438","592565351821","2185764770724","2180474560631","2210787224500","2180126341877","2180610666348","2180684208178","2185132172024","2192572577704","2185261650483","2154405237780","2192144020618","2154522251247","2185061200574","2185346571512","**********600","299554374354","2215200680130","2180061530388","2180270448104","2185811000770","2185005522432","2185831866065","2207470166786","2185708860600","2193885376722","2185868541358","2183552346155","2180306101760","2192866358422","2176214026866","2180306101557","299484018162","2176552812344","2192866358146","299544445024","1806257732413","2188652662223","2154611202238","2150415528440","2180412882472","2215407174061","2230265833455","2180781444576","2176214703773","2180403863224","2180260800344","2189126010762","2180871188886","2180827385100","2189243328667","2185420103875","2185243201877"));
		DubboResponse<DeliveryRuleResp> response = deliveryRuleQueryProvider.queryDeliveryDateInfo(req);
		/*req.setSource(SourceEnum.XM_AFTER_SALE);
		DubboResponse<DeliveryRuleResp> response2 = deliveryRuleQueryProvider.queryDeliveryDateInfo(req);
		req.setSource(SourceEnum.XM_SAMPLE_APPLY);
		DubboResponse<DeliveryRuleResp> response3 = deliveryRuleQueryProvider.queryDeliveryDateInfo(req);
*/
		/*this.SaasQueryDeliveryDate();
		System.out.println(response);*/
	}

	@Test
	public void xmAfterQueryDeliveryDate() {
		DeliveryRuleQueryReq req = new DeliveryRuleQueryReq();
		req.setContactId(347052L);
		req.setMerchantId(289629L);
		req.setOrderTime(DateUtils.parseLocalDateTime("2024-04-30 16:33:00", net.summerfarm.tms.util.DateUtils.LONG_DATE_FORMAT, null));
		req.setArea("拱墅区");
		req.setCity("杭州市");
		req.setSource(SourceEnum.XM_AFTER_SALE);
		/*req.setQueryBeginDate(LocalDate.now());
		req.setQueryEndDate(LocalDate.now().plusDays(30));*/
		//********** **********
		//req.setSkus(Lists.newArrayList("**********", "**********"));
		DubboResponse<DeliveryRuleResp> response = deliveryRuleQueryProvider.queryDeliveryDateInfo(req);

		System.out.println(response);
	}


	@Test
	public void xmSampleQueryDeliveryDate() {
		DeliveryRuleQueryReq req = new DeliveryRuleQueryReq();
		req.setContactId(347052L);
		req.setMerchantId(289629L);
		req.setOrderTime(DateUtils.parseLocalDateTime("2024-04-30 16:33:00", net.summerfarm.tms.util.DateUtils.LONG_DATE_FORMAT, null));
		req.setArea("拱墅区");
		req.setCity("杭州市");
		req.setSource(SourceEnum.XM_SAMPLE_APPLY);
		/*req.setQueryBeginDate(LocalDate.now());
		req.setQueryEndDate(LocalDate.now().plusDays(30));*/
		//********** **********
		//req.setSkus(Lists.newArrayList("**********", "**********"));
		DubboResponse<DeliveryRuleResp> response = deliveryRuleQueryProvider.queryDeliveryDateInfo(req);

		System.out.println(response);
	}

	@Test
	public void saasDeliveryDate() {

		//{"area":"西湖区","city":"杭州市","contactId":4339,"orderTime":"2024-05-30T16:04:55.451","skus":["289180880837"],"source":"SAAS_MALL","tenantId":2}
		DeliveryRuleQueryReq req = new DeliveryRuleQueryReq();
		req.setContactId(350723L);
		req.setMerchantId(102680L);
		req.setTenantId(1L);
		req.setOrderTime(DateUtils.parseLocalDateTime("2025-09-23 13:04:00", net.summerfarm.tms.util.DateUtils.LONG_DATE_FORMAT, null));
		req.setArea("拱墅区");
		req.setCity("杭州市");
		req.setSource(SourceEnum.XM_MALL);
		/*req.setQueryBeginDate(LocalDate.now());
		req.setQueryEndDate(LocalDate.now().plusDays(30));*/
		//********** **********
		req.setSkus(Lists.newArrayList("**********"));
		DubboResponse<DeliveryRuleResp> response = deliveryRuleQueryProvider.queryDeliveryDateInfo(req);

		System.out.println(response);
	}

	@Test
	public void SaasQueryDeliveryDate() {
		DeliveryRuleQueryReq req = new DeliveryRuleQueryReq();
		//req.setContactId(143025L);
		req.setOrderTime(DateUtils.parseLocalDateTime("2024-04-30 16:33:00", net.summerfarm.tms.util.DateUtils.LONG_DATE_FORMAT, null));
		req.setArea("余江区");
		req.setCity("鹰潭市");
		req.setSource(SourceEnum.SAAS_MALL);
		req.setTenantId(4L);
		//req.setTenantId(2L);
		//********** **********
		//req.setSkus(Lists.newArrayList("**********", "**********"));
		DubboResponse<DeliveryRuleResp> response = deliveryRuleQueryProvider.queryDeliveryDateInfo(req);
		req.setSource(SourceEnum.SAAS_AFTER_SALE);
		//********** **********
		req.setSkus(Lists.newArrayList("**********", "**********"));
		DubboResponse<DeliveryRuleResp> response2 = deliveryRuleQueryProvider.queryDeliveryDateInfo(req);
		System.out.println(response2);
	}
	@Test
	public void test() {
		WarehouseBySkuAreaNoQueryReq req = new WarehouseBySkuAreaNoQueryReq();
		List<WarehouseBySkuAreaNoDataReq> areaSkuList = Lists.newArrayList();
		WarehouseBySkuAreaNoDataReq req1 = new WarehouseBySkuAreaNoDataReq();
		req1.setSku("170463418478");
		req1.setAreaNoList(Lists.newArrayList(29301, 29303, 29304, 29305, 29306, 29310, 1650, 29315));
		areaSkuList.add(req1);

		WarehouseBySkuAreaNoDataReq req2 = new WarehouseBySkuAreaNoDataReq();
		req2.setSku("19570010686");
		req2.setAreaNoList(Lists.newArrayList(29301, 29303, 29304, 29305, 29306, 29310, 1650, 29315));
		areaSkuList.add(req2);

		WarehouseBySkuAreaNoDataReq req3 = new WarehouseBySkuAreaNoDataReq();
		req3.setSku("25220012561");
		req3.setAreaNoList(Lists.newArrayList(29301, 29303, 29304, 29305, 29306, 29310, 1650, 29315));
		areaSkuList.add(req3);

		WarehouseBySkuAreaNoDataReq req4 = new WarehouseBySkuAreaNoDataReq();
		req4.setSku("25256221848");
		req4.setAreaNoList(Lists.newArrayList(29301, 29303, 29304, 29305, 29306, 29310, 1650, 29315));
		areaSkuList.add(req4);

		WarehouseBySkuAreaNoDataReq req5 = new WarehouseBySkuAreaNoDataReq();
		req5.setSku("29645578272");
		req5.setAreaNoList(Lists.newArrayList(29301, 29303, 29304, 29305, 29306, 29310, 1650, 29315));
		areaSkuList.add(req5);
		req.setAreaSkuList(areaSkuList);
		DubboResponse<List<WarehouseBySkuAreaNoResp>> listDubboResponse = warehouseSkuAreaNoQueryProvider.queryBySkuAreNo(req);
		System.out.println(JSONUtil.toJsonStr(listDubboResponse));
	}

	@Test
	public void test2() {
		List<Integer> warehouseNos = Lists.newArrayList(1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 10000001, 361);
		WarehouseBaseInfoByNoReq req = new WarehouseBaseInfoByNoReq();
		req.setWarehouseNos(warehouseNos);
		System.out.println(JSONUtil.toJsonStr(warehouseStorageQueryProvider.queryBaseInfoByWarehouseNo(req)));

	}

	@Test
	public void test3() {
		FenceCloseTimeQueryReq req = new FenceCloseTimeQueryReq();
		req.setArea("雨城区");
		req.setCity("雅安市");
		req.setTenantId(1L);
//		req.setContactId(348137L);
		req.setSource(SourceEnum.POP_MALL);
		DubboResponse<FenceCloseTimeResp> fenceCloseTimeRespDubboResponse = deliveryFenceQueryProvider.queryCloseTime(req);
		System.out.println(JSONUtil.toJsonStr(fenceCloseTimeRespDubboResponse));
	}


	@Test
	public void test6() {
		QueryAreaLegitimacyReq req = new QueryAreaLegitimacyReq();
		req.setCityList(Lists.newArrayList("七台河市", "上海市", "上饶市", "中卫市", "丽水市","淮安市"));
		DubboResponse<List<QueryAreaLegitimacyResp>> listDubboResponse = deliveryFenceQueryProvider.queryAreaLegitimacyResp(req);
		System.out.println(JSONUtil.toJsonStr(listDubboResponse.getData()));
	}

	@Test
	public void test5() {
		AreaQueryReq areaQueryReq = new AreaQueryReq();
		areaQueryReq.setCity("中山市");
		areaQueryReq.setArea("虹口区");
		DubboResponse<AreaQueryResp> areaQueryRespDubboResponse = deliveryFenceQueryProvider.queryAreaByAddress(areaQueryReq);
		System.out.println(JSONUtil.toJsonStr(areaQueryRespDubboResponse.getData()));
	}

	@Test
	public void test7() {
		WarehouseListQuery req = new WarehouseListQuery();
		req.setTenantId(1L);
		System.out.println(JSONUtil.toJsonStr(warehouseStorageQueryProvider.queryWarehouseList(req)));

	}

	@Test
	public void test8() {
		AddressBatchQueryReq req = new AddressBatchQueryReq();
		AddressQueryReq req1 = new AddressQueryReq();
		req1.setProvince("湖北");
		req1.setCity("宜昌市");
		req1.setArea("当阳市");
		req1.setAddress("当阳南正街50一5号");

		AddressQueryReq req2 = new AddressQueryReq();
		req2.setProvince("湖北");
		req2.setCity("随州市");
		req2.setArea("广水市");
		req2.setAddress("应山办事处东大街256号门面");

		AddressQueryReq req3 = new AddressQueryReq();
		req3.setProvince("湖北");
		req3.setCity("黄冈市");
		req3.setArea("红安县");
		req3.setAddress("成龙小区");

		AddressQueryReq req4 = new AddressQueryReq();
		req4.setProvince("湖北");
		req4.setCity("荆州市");
		req4.setArea("洪湖市");
		req4.setAddress("宏伟南路36号");

		AddressQueryReq req5 = new AddressQueryReq();
		req5.setProvince("湖北");
		req5.setCity("荆州市");
		req5.setArea("石首市");
		req5.setAddress("城市广场g105门面");

		AddressQueryReq req6 = new AddressQueryReq();
		req6.setProvince("湖北");
		req6.setCity("黄石市");
		req6.setArea("阳新县");
		req6.setAddress("创业步行街像座柠季手打柠檬茶店");

		AddressQueryReq req7 = new AddressQueryReq();
		req7.setProvince("湖北");
		req7.setCity("咸宁市");
		req7.setArea("嘉鱼县");
		req7.setAddress("沙阳大道实验小学（老）162-10号铺子");

		AddressQueryReq req8 = new AddressQueryReq();
		req8.setProvince("湖北");
		req8.setCity("荆门市");
		req8.setArea("京山市");
		req8.setAddress("新市镇京源大道，华联商厦旁");

		AddressQueryReq req9 = new AddressQueryReq();
		req9.setProvince("湖北");
		req9.setCity("黄冈市");
		req9.setArea("浠水县");
		req9.setAddress("新华正街356-1号");

		AddressQueryReq req10 = new AddressQueryReq();
		req10.setProvince("湖南");
		req10.setCity("湘西土家族苗族自治州");
		req10.setArea("保靖县");
		req10.setAddress("建新路廊桥天街广场5栋1-109号");

		AddressQueryReq req11 = new AddressQueryReq();
		req11.setProvince("111");
		req11.setCity("111");
		req11.setArea("111");
		req11.setAddress("1111");


		req.setAddressQueryReqList(Lists.newArrayList(req1, req2, req3, req4, req5, req6, req7, req8, req9, req10, req11));
		long start = System.currentTimeMillis();
		System.out.println(JSONUtil.toJsonStr(lbsProvider.batchQueryLocationByAddress(req)));
		System.out.println(System.currentTimeMillis() - start);

		long start1 = System.currentTimeMillis();
		for (AddressQueryReq contactQueryReq : req.getAddressQueryReqList()) {
			lbsProvider.queryPoiByAddress(contactQueryReq);
		}
		System.out.println(System.currentTimeMillis() - start1);

	}

	@Test
	public void test9() {
		DeliveryFenceByCityQueryReq req = new DeliveryFenceByCityQueryReq();
		req.setCityNames(Lists.newArrayList("杭州市","上海市"));
		System.out.println(JSONUtil.toJsonStr(deliveryFenceQueryProvider.batchQueryDeliveryFenceByCity(req)));

	}

	@Test
	public void queryPayAfterDeliveryDateTest() {
		DeliveryRuleQueryReq req = new DeliveryRuleQueryReq();
		//req.setContactId(349094L);
		//req.setMerchantId(350322L);
		req.setOrderTime(DateUtils.parseLocalDateTime("2025-04-16 17:36:00", net.summerfarm.tms.util.DateUtils.LONG_DATE_FORMAT, null));
		//req.setPayTime(DateUtils.parseLocalDateTime("2025-03-31 11:33:00", net.summerfarm.tms.util.DateUtils.LONG_DATE_FORMAT, null));
		req.setContactId(166262L);
		req.setArea("金山区");
		req.setCity("上海市");
		req.setSource(SourceEnum.XM_MALL);
		req.setTenantId(1L);
		//PayAfterDeliveryDateQueryReq req = new PayAfterDeliveryDateQueryReq();
		req.setContactId(347052L);
		req.setMerchantId(289629L);
		req.setOrderTime(DateUtils.parseLocalDateTime("2025-01-07 10:33:00", net.summerfarm.tms.util.DateUtils.LONG_DATE_FORMAT, null));
		//req.setPayTime(DateUtils.parseLocalDateTime("2025-01-07 23:33:00", net.summerfarm.tms.util.DateUtils.LONG_DATE_FORMAT, null));

		req.setArea("拱墅区");
		req.setCity("杭州市");
		req.setSource(SourceEnum.SAAS_MALL);
		req.setTenantId(2L);
		req.setAddOrderFlag(true);
		//req.setNoNeedToStockUp(true);
		//req.setTenantId(2L);
		//********** **********
		//req.setSkus(Lists.newArrayList("2215007023434", "2154456644206","2185132172024","2185764770724","2192144020618"));
		DubboResponse<DeliveryRuleResp> response = deliveryRuleQueryProvider.queryCacheDeliveryDateInfo(req);
		//********** ***********/

		/*req.setSkus(Lists.newArrayList("2180126341877", "**********"));*/
		PayAfterDeliveryDateQueryReq payAfterDeliveryDateQueryReq = new PayAfterDeliveryDateQueryReq();
		payAfterDeliveryDateQueryReq.setContactId(348137L);
		payAfterDeliveryDateQueryReq.setMerchantId(349839L);
		payAfterDeliveryDateQueryReq.setArea("西湖区");
		payAfterDeliveryDateQueryReq.setCity("杭州市");
		payAfterDeliveryDateQueryReq.setSource(SourceEnum.POP_MALL);
		payAfterDeliveryDateQueryReq.setTenantId(1L);
		payAfterDeliveryDateQueryReq.setOrderTime(DateUtils.parseLocalDateTime("2025-05-06 14:43:54", net.summerfarm.tms.util.DateUtils.LONG_DATE_FORMAT, null));
		payAfterDeliveryDateQueryReq.setPayTime(DateUtils.parseLocalDateTime("2025-05-06 14:43:54", net.summerfarm.tms.util.DateUtils.LONG_DATE_FORMAT, null));
		//payAfterDeliveryDateQueryReq.setAddOrderFlag(true);
		payAfterDeliveryDateQueryReq.setSkus(Lists.newArrayList("299554374354"));
		//payAfterDeliveryDateQueryReq.setNoNeedToStockUp();

		DubboResponse<PayAfterDeliveryDateResp> response2 = deliveryRuleQueryProvider.queryPayAfterDeliveryDate(payAfterDeliveryDateQueryReq);
		System.out.println(response2);
	}

	@Test
	public void PopMallQueryDeliveryDate() {
		DeliveryRuleQueryReq req = new DeliveryRuleQueryReq();
		req.setContactId(352842L);
		req.setMerchantId(349839L);
		req.setOrderTime(DateUtils.parseLocalDateTime("2025-05-08 10:52:00", net.summerfarm.tms.util.DateUtils.LONG_DATE_FORMAT, null));
		req.setSource(SourceEnum.POP_MALL);
		/*req.setQueryBeginDate(LocalDate.now());
		req.setQueryEndDate(LocalDate.now().plusDays(30));*/
		//********** **********
		//req.setSkus(Lists.newArrayList("**********", "**********"));
		req.setSkus(Lists.newArrayList("299554374354", "2207037775264"));
		DubboResponse<DeliveryRuleResp> response = deliveryRuleQueryProvider.queryDeliveryDateInfo(req);
		/*req.setSource(SourceEnum.XM_AFTER_SALE);
		DubboResponse<DeliveryRuleResp> response2 = deliveryRuleQueryProvider.queryDeliveryDateInfo(req);
		req.setSource(SourceEnum.XM_SAMPLE_APPLY);
		DubboResponse<DeliveryRuleResp> response3 = deliveryRuleQueryProvider.queryDeliveryDateInfo(req);
*/
		System.out.println(response);
	}
}
