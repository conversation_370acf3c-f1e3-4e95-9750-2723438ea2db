server:
  port: 80
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.jdbc.Driver
    url: ***********************************************************************************************************************************************
    username: test
    password: xianmu619
  authRedis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 5    #dev1:0  dev2:1  dev3:2 dev4:4  qa:5
  redis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 5
    jedis:
      pool:
        max-active: 4 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 4 # 连接池中的最大空闲连接
        min-idle: 4 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）

dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    address: nacos://test-nacos.summerfarm.net:11000
    parameters:
      namespace: 34792f7a-aaa2-41ee-8a7f-53be483c2533
  protocol:
    id: dubbo
    name: dubbo
    port: 20880
  provider:
    version: 1.0.0
    group: online
    timeout: 6000
    retries: 0
    telnet: ls,ps,cd,pwd,trace,count,invoke,select,status,log,help,clear,exit,shutdown
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    check: false
    threadpool: cached #线程池类型，主要有fixed，cached，eager，limited
    corethreads: 10 #核心线程数，默认0
    threads: 20 #最大线程数，默认Integer.MAX_VALUE，2的32次方-1
    queues: 200 #默认为0，SynchronousQueue，不存储任务 直接丢弃，配置数量>0则是LinkedBlockingQueue，如果设置成负数，默认是 LinkedBlockingQueue<Runnable>(Integer.MAX_VALUE)
    alive: 30000 #默认60秒
aspect:
  log:
    open: true

xm:
  log:
    enable: true
    resp: true
  sentinel:
    nacos:
      serverAddr: test-nacos.summerfarm.net:11000
      groupId: sentinel
      namespace: e5ca5c64-a551-4889-b5a1-7bf9c90b4752

rocketmq:
  consumer:
    access-key: ''
    secret-key: ''
  name-server: test-mq-nameserver.summerfarm.net:9876
  producer:
    access-key: ''
    group: GID_wnc
    secret-key: ''
    sendMsgTimeout: 10000
es:
  port: 80
  servlet:
    context-path: /summerfarm-wnc

mybatis-plus:
  mapper-locations: classpath:net/summerfarm/wnc/infrastructure/mapper/*.xml
  global-config:
    db-config:
      update-strategy: NOT_NULL
      field-strategy: not_empty
      id-type: auto
      db-type: mysql
  configuration:
    # sql 打印
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    call-setters-on-nulls: true
    map-underscore-to-camel-case: true

nacos:
  config:
    server-addr: test-nacos.summerfarm.net:11000
    namespace: b781e552-933d-44c5-b642-49dd30c5ba5f

#pagehelper分页插件配置
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql

easy-es:
  enable: true # 是否开启EE自动配置 默认开启,可缺省
  address: dev.es.summerfarm.net:80  #填你的es连接地址
  username: elastic # 账号
  password: Xianmu619 # 密码
  schema: http # 默认为http 可缺省
  banner: true # 默认为true 打印banner 若您不期望打印banner,可配置为false
  keep-alive-millis: 30000 # 心跳策略时间 单位:ms
  connect-timeout: 5000 # 连接超时时间 单位:ms
  socket-timeout: 600000 # 通信超时时间 单位:ms
  request-timeout: 5000 # 请求超时时间 单位:ms
  connection-request-timeout: 5000 # 连接请求超时时间 单位:ms
  max-conn-total: 100 # 最大连接数 单位:个
  max-conn-per-route: 100 # 最大连接路由数 单位:个
  global-config:
    process-index-mode: manual #索引处理模式,smoothly:平滑模式, not_smoothly:非平滑模式, manual:手动模式,,默认开启此模式
    print-dsl: false # 开启控制台打印通过本框架生成的DSL语句,默认为开启,测试稳定后的生产环境建议关闭,以提升少量性能
    distributed: false # 当前项目是否分布式项目,默认为true,在非手动托管索引模式下,若为分布式项目则会获取分布式锁,非分布式项目只需synchronized锁.
    reindexTimeOutHours: 72 # 重建索引超时时间 单位小时,默认72H 可根据ES中存储的数据量调整
    async-process-index-blocking: true # 异步处理索引是否阻塞主线程 默认阻塞 数据量过大时调整为非阻塞异步进行 项目启动更快
    active-release-index-max-retry: 4320 # 分布式环境下,平滑模式,当前客户端激活最新索引最大重试次数,若数据量过大,重建索引数据迁移时间超过4320/60=72H,可调大此参数值,此参数值决定最大重试次数,超出此次数后仍未成功,则终止重试并记录异常日志
    active-release-index-fixed-delay: 60 # 分布式环境下,平滑模式,当前客户端激活最新索引最大重试次数 分布式环境下,平滑模式,当前客户端激活最新索引重试时间间隔 若您期望最终一致性的时效性更高,可调小此值,但会牺牲一些性能

    db-config:
      map-underscore-to-camel-case: false # 是否开启下划线转驼峰 默认为false
      # id-type: customize # id生成策略 customize为自定义,id值由用户生成,比如取MySQL中的数据id,如缺省此项配置,则id默认策略为es自动生成
      field-strategy: not_empty # 字段更新策略 默认为not_null