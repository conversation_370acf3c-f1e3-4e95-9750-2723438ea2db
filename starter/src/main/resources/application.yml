server:
  port: 80
  servlet:
    context-path: /summerfarm-wnc

spring:
  application:
    name: summerfarm-wnc
  profiles:
    active: dev2

mybatis-plus:
  mapper-locations: classpath:net/summerfarm/wnc/infrastructure/mapper/*.xml
  global-config:
    db-config:
      update-strategy: NOT_NULL
      field-strategy: not_empty
      id-type: auto
      db-type: mysql
  configuration:
    # sql 打印
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    call-setters-on-nulls: true
    map-underscore-to-camel-case: true

#pagehelper分页插件配置
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql