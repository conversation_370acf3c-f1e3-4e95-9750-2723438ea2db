
package net.summerfarm.wnc.starter.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.DubboComponentScan;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

import javax.annotation.PostConstruct;

@Slf4j
@EnableAsync
@EnableAutoConfiguration
@ComponentScan(value = {"net.summerfarm.wnc.*"})
@DubboComponentScan({"net.summerfarm.wnc.inbound.provider","net.summerfarm.wnc.application.inbound.provider"})
@MapperScan({"net.summerfarm.wnc.infrastructure.mapper","net.summerfarm.authorization.mapper"})
public class MainConfiguration {

    @PostConstruct
    public void init() {
        log.info("MainConfiguration init...");
    }
}
