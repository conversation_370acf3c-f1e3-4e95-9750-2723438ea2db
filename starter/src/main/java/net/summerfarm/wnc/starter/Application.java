package net.summerfarm.wnc.starter;

import cn.easyes.starter.register.EsMapperScan;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.starter.config.MainConfiguration;
import org.apache.dubbo.config.spring.context.annotation.DubboComponentScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Import;

@Slf4j
@Import({MainConfiguration.class})
@SpringBootApplication(scanBasePackages = {"net.summerfarm.wnc","net.xianmu.authentication"})
@DubboComponentScan(basePackages = "net.xianmu.authentication.**.provider")
@EsMapperScan("net.summerfarm.wnc.infrastructure.es.mapper")
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
        log.info("#############服务启动成功##############");
    }
}
