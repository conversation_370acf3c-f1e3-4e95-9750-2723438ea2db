package net.summerfarm.wnc.common.query.warehouse;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Description: <br/>
 * date: 2023/4/4 19:01<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WarehouseBusinessQuery {
    /**
     * 仓库编号
     */
    private List<Integer> warehouseNos;
}
