package net.summerfarm.wnc.common.base;

import net.xianmu.common.exception.BizException;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.Collection;
import java.util.Map;

public class WncAssert {

    public static void notNull(Object object, String msg) {
        if (object == null) {
            throw new BizException(msg);
        }
    }

    public static void isTrue(boolean expression, String msg) {
        if (!expression) {
            throw new BizException(msg);
        }
    }

    public static void notEmpty(Collection<?> collection, String msg) {
        if (CollectionUtils.isEmpty(collection)) {
            throw new BizException(msg);
        }
    }

    public static void notEmpty(Map<?, ?> map, String msg) {
        if (CollectionUtils.isEmpty(map)) {
            throw new BizException(msg);
        }
    }

    public static void notEmpty(Object[] array, String msg) {
        if (ObjectUtils.isEmpty(array)) {
            throw new BizException(msg);
        }
    }

    public static void hasText(String text, String msg) {
        if (!StringUtils.hasText(text)) {
            throw new BizException(msg);
        }
    }

    public static void isNull(Object object, String msg) {
        if (object != null) {
            throw new BizException(msg);
        }
    }


    public static void isTrue(boolean expression, String msg, String... errorParams) {
        if (!expression) {
            throw new BizException(msg);
        }
    }


    public static void notEmpty(String text, String msg) {
        if (StringUtils.isEmpty(text)) {
            throw new BizException(msg);
        }
    }


    public static void isEmpty(Collection<?> collection, String msg) {
        if (!CollectionUtils.isEmpty(collection)) {
            throw new BizException(msg);
        }
    }

}
