package net.summerfarm.wnc.common.query.fence;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.common.util.StringUtils;
import java.io.Serializable;

/**
 * Description:地址查询参数
 * date: 2023/11/14 14:36
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AddressQuery implements Serializable {

    private static final long serialVersionUID = 8671819790832218675L;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 详细地址
     */
    private String address;

    public String getCompleteAddress() {
        return this.province + this.city + (StringUtils.isNotBlank(this.area) ? this.area : "") + this.address;
    }
}
