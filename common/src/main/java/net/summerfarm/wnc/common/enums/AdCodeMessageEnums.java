package net.summerfarm.wnc.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description: <br/>
 * date: 2023/3/8 11:45<br/>
 *
 * <AUTHOR> />
 */
public interface AdCodeMessageEnums {
    //状态 0 正常 1 失效
    @Getter
    @AllArgsConstructor
    enum Status{
        NORMAL(0,"正常"),
        DELETE(1,"失效"),
        ;
        private Integer value;
        private String content;
    }
}
