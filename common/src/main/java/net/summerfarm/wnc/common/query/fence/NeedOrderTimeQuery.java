package net.summerfarm.wnc.common.query.fence;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Description: 需要下单时间查询对象<br/>
 * date: 2024/7/11 14:06<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NeedOrderTimeQuery {

    /**
     * 联系人id
     */
    private Long contactId;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域
     */
    private String area;


    /**
     * sku集合
     */
    private List<String> skus;

    /**
     * 租户Id
     */
    private Long tenantId;
}
