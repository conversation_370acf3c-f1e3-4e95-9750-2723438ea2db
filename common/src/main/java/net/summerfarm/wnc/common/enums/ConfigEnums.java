package net.summerfarm.wnc.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.common.enums.base.Enum2Args;
import net.xianmu.common.enums.base.Enum2StringArgs;

/**
 * Description:配置类枚举
 * date: 2023/8/29 17:57
 *
 * <AUTHOR>
 */
public interface ConfigEnums {

    /**
     * 配置类键信息
     */
    @Getter
    @AllArgsConstructor
    enum Key implements Enum2StringArgs {

        /**
         * 围栏切仓任务创建消息发送
         */
        FENCE_CHANGE_TASK_CREATE("FENCE_CHANGE_TASK_CREATE", "围栏切仓任务创建消息通知机器人URL"),
        /**
         * 围栏切仓任务取消消息发送
         */
        FENCE_CHANGE_TASK_CANCEL("FENCE_CHANGE_TASK_CANCEL", "围栏切仓任务取消消息通知机器人URL"),
        /**
         * 围栏切仓任务-区域切仓阶段执行失败消息发送
         */
        FENCE_CHANGE_TASK_AREA_EXECUTE_FAIL("FENCE_CHANGE_TASK_AREA_EXECUTE_FAIL", "围栏切仓任务-区域切仓阶段执行失败消息通知机器人URL"),
        /**
         * 围栏切仓任务-订单切仓阶段执行失败消息发送
         */
        FENCE_CHANGE_TASK_ORDER_EXECUTE_FAIL("FENCE_CHANGE_TASK_ORDER_EXECUTE_FAIL", "围栏切仓任务-订单切仓阶段执行失败消息通知机器人URL"),
        /**
         * 运营区域sku多仓库警告消息发送
         */
        AREA_SKU_MANY_WAREHOUSE_WARNING("AREA_SKU_MANY_WAREHOUSE_WARNING", "运营区域sku多仓库警告消息发送通知机器人URL"),
        ;

        private final String value;
        private final String content;
    }
}
