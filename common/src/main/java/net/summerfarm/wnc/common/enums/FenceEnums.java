package net.summerfarm.wnc.common.enums;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.common.enums.base.Enum2Args;
import net.xianmu.common.enums.base.Enum2StringArgs;
import net.xianmu.common.exception.BizException;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * Description: <br/>
 * date: 2023/3/16 15:34<br/>
 *
 * <AUTHOR> />
 */
public interface FenceEnums {

    /**
     * 围栏状态
     */
    @Getter
    @AllArgsConstructor
    enum Status{

        /**
         * 正常
         */
        VALID(0, "正常"),
        /**
         * 失效
         */
        INVALID(1, "失效"),
        /**
         * 停用
         */
        STOP(3, "停用"),
        ;
        private final Integer value;
        private final String content;

        /**
         * 根据值获取围栏状态枚举
         *
         * @param value 值
         * @return 围栏状态枚举
         */
        public static FenceEnums.Status getStatusByValue(Integer value) {
            return Arrays.stream(values()).filter(e -> Objects.equals(e.getValue(), value)).findFirst()
                    .orElseThrow(() -> new BizException("不支持的围栏状态:" + value));
        }

        public static List<Integer> getUsableStatus() {
            return Lists.newArrayList(FenceEnums.Status.VALID.getValue(), FenceEnums.Status.STOP.getValue());
        }
    }

    /**
     * 围栏类型
     */
    @Getter
    @AllArgsConstructor
    enum Type implements Enum2Args {

        /**
         * 新建
         */
        NEW_BUILD(0, "新建围栏"),
        /**
         * 拆分
         */
        OLD_SPILT(1, "围栏拆分"),
        ;
        private final Integer value;
        private final String content;

        /**
         * 根据值获取围栏类型枚举
         *
         * @param value 值
         * @return 围栏类型枚举
         */
        public static FenceEnums.Type getTypeByValue(Integer value) {
            return Arrays.stream(values()).filter(e -> Objects.equals(e.getValue(), value)).findFirst()
                    .orElseThrow(() -> new BizException("不支持的围栏类型:" + value));
        }
    }


    /**
     * 下单渠道类型
     */
    @Getter
    @AllArgsConstructor
    enum OrderChannelType implements Enum2StringArgs {
        /**
         * 鲜沐平台客户
         */
        XM_CUSTOM("1000", "鲜沐平台客户"),
        /**
         * 鲜沐大客户
         */
        XM_BIG_CUSTOM("2000", "鲜沐大客户"),
        /**
         * Saas
         */
        SAAS("3000", "Saas"),
        ;
        private final String value;
        private final String content;

        /**
         * 根据值获取下单渠道类型枚举
         *
         * @param value 值
         * @return 下单渠道类型枚举
         */
        public static FenceEnums.OrderChannelType getChannelByValue(String value) {
            return Arrays.stream(values()).filter(e -> Objects.equals(e.getValue(), value)).findFirst()
                    .orElseThrow(() -> new BizException("不支持的渠道类型:" + value));
        }

        public static String getAllValueSplit(){
            return String.join(",", Arrays.stream(values()).map(Enum2StringArgs::getValue).toArray(String[]::new));
        }
    }
}
