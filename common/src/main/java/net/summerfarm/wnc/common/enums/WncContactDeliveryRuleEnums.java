package net.summerfarm.wnc.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.summerfarm.wnc.common.constants.AppConsts;

import java.util.Objects;

/**
 * Description: 地址配送规则枚举<br/>
 * date: 2023/11/13 15:38<br/>
 *
 * <AUTHOR> />
 */
public interface WncContactDeliveryRuleEnums {
    //系统来源 0鲜沐商城
    @Getter
    @AllArgsConstructor
    enum SystemSource{
        XM(0, "鲜沐商城"),
        SAAS(1, "Saas"),
        ;
        private Integer value;
        private String content;

        public static WncContactDeliveryRuleEnums.SystemSource getSourceByTenantId(Long tenantId){
            if (Objects.equals(tenantId, AppConsts.Tenant.XM_TENANT_ID)){
                return SystemSource.XM;
            }
            return SystemSource.SAAS;
        }
    }

    //周期方案 1周计算 2间隔计算
    @Getter
    @AllArgsConstructor
    enum FrequentMethod{
        WEEK_CALC(1,"周计算"),
        INTERVAL_CALC(2,"间隔计算"),
        ;
        private Integer value;
        private String content;
    }

}
