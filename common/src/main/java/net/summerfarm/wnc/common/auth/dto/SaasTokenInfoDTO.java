package net.summerfarm.wnc.common.auth.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Description:SaasTokenInfoDTO
 * date: 2023/4/7 16:34
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SaasTokenInfoDTO {
    /**
     * 手机号
     */
    private String phone;
    /**
     * 租户名称
     */
    private String tenantName;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * adminId
     */
    private Long adminId;
    /**
     * SaaS登录账号id
     */
    private Long tenantAccountId;
    /** saas账号名称 **/
    private String tenantAccountName;
}

