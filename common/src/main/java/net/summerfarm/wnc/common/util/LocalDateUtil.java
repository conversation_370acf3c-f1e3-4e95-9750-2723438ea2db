package net.summerfarm.wnc.common.util;

import java.time.LocalDate;
import java.time.temporal.WeekFields;

/**
 * Description: <br/>
 * date: 2023/12/5 16:18<br/>
 *
 * <AUTHOR> />
 */
public class LocalDateUtil {

    /**
     * 判断两个日期是否是同一周
     * @param date1 日期1
     * @param date2 日期1
     * @return 结果
     */
   public static Boolean isSameWeek(LocalDate date1,LocalDate date2) {
       // 获取默认的周字段，这里使用的是Locale.getDefault()，但可以根据需要更改为特定的Locale
       WeekFields weekFields = WeekFields.ISO;

       // 获取两个日期的年份和周数
       int weekYear1 = date1.get(weekFields.weekBasedYear());
       int weekYear2 = date2.get(weekFields.weekBasedYear());
       int week1 = date1.get(weekFields.weekOfWeekBasedYear());
       int week2 = date2.get(weekFields.weekOfWeekBasedYear());

       // 判断年份和周数是否都相等
       if (weekYear1 == weekYear2 && week1 == week2) {
           return true;
       } else {
           return false;
       }
   }
}
