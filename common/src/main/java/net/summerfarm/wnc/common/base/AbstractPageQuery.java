package net.summerfarm.wnc.common.base;

import lombok.*;

import java.util.List;

@Setter
@Getter
public abstract class AbstractPageQuery{
    private static final long serialVersionUID = 1L;

    public static Integer MAX_PAGE_SIZE = 500;

    private boolean needPagination = true;
    private int pageIndex = 1;
    private int pageSize = 20;

    private String lastSql;
    /**
     * 排序信息
     */
    private List<PageSortInput> sortList;

    /**
     * <AUTHOR>
     * @date 2022/9/1  14:56
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PageSortInput {
        /**
         * 排序字段
         */
        private String sortBy;
        /**
         * 排序方式：asc、desc
         */
        private String orderBy;
    }

    public void setPageSize(int pageSize) {
        if (pageSize > MAX_PAGE_SIZE) {
            throw new RuntimeException("page size need less than 500");
        }
        this.pageSize = pageSize;
    }

    public int getStartRow() {
        return (pageIndex - 1) * pageSize;
    }

    public String orderBys(){
        if (this.sortList == null || this.sortList.isEmpty()){
            return null;
        }
        StringBuilder orderBys = new StringBuilder();
        for (PageSortInput pageSortInput : sortList) {
            orderBys.append(pageSortInput.sortBy).append(" ").append(pageSortInput.orderBy).append(",");
        }
        return orderBys.deleteCharAt(orderBys.length() - 1).toString();
    }
}

