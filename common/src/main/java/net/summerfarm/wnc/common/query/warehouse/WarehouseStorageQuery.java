package net.summerfarm.wnc.common.query.warehouse;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.wnc.common.base.AbstractPageQuery;

import java.io.Serializable;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/3/30 16:24<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseStorageQuery extends AbstractPageQuery implements Serializable {
    /**
     * 主键id
     */
    private Integer id;
    /**
     * 仓库名称
     */
    private String warehouseName;
    /**
     * 仓库编号
     */
    private Integer warehouseNo;
    /**
     * 省
     */
    private List<String> provinces;

    /**
     * 市
     */
    private List<String> citys;

    /**
     * 区
     */
    private List<String> areas;

    /**
     * 1 鲜沐仓 2自营仓
     */
    private Integer warehouseSource;

    /**
     * 租户ID 鲜沐默认为0
     */
    private Long tenantId;
    /**
     * 仓库编号
     */
    private List<Integer> warehouseNos;
    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;
    /**
     * sku
     */
    private List<String> skuList;
    /**
     * sku
     */
    private String sku;

    /**
     * 开放状态：0、不开放 1、开放
     */
    private Integer status;
    /**
     * 定位信息
     */
    private String poi;

    /**
     * 1 按照创建时间排序 2按照更新时间排序
     */
    private Integer desc;

    /**
     * 仓库类型：0、本部仓 1、外部仓 2、合伙人仓
     */
    private Integer type;

    /**
     * 联系人ID
     */
    private Long contactId;

    /**
     * 是否自营仓仓库
     * ture 自营仓仓库 false 非自营仓仓库
     */
    private Boolean selfWarehouseFlag;

    /**
     * 自营仓仓库集合
     */
    private List<Integer> selfWarehouseNos;

    /**
     * 查询条件排除自营仓仓库集合
     */
    private List<Integer> noInSelfWarehouseNos;

    /**
     * 仓库名称 等值查询
     */
    private String warehouseNameEqualTo;

    /**
     * 仓库名称 全模糊查询
     */
    private String warehouseLike;
}
