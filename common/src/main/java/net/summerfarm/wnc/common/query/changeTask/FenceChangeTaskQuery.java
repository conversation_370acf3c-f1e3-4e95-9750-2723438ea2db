package net.summerfarm.wnc.common.query.changeTask;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * Description:切仓任务查询实体
 * date: 2023/8/28 18:40
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FenceChangeTaskQuery implements Serializable {

    private static final long serialVersionUID = -1808908268247081577L;

    /**
     * 切仓任务状态,0:待处理,1:已取消,2:已完成,10:区域切换中,15:订单切换中,20:处理失败
     */
    private List<Integer> status;

    /**
     * 类型，0：切仓，1：切围栏
     */
    private Integer type;

    /**
     * 围栏ID
     */
    private Integer fenceId;

}
