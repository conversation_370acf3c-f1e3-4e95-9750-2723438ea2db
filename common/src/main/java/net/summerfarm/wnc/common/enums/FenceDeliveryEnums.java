package net.summerfarm.wnc.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.common.enums.base.Enum2Args;
import net.xianmu.common.exception.BizException;

import java.util.Arrays;
import java.util.Objects;

/**
 * Description: <br/>
 * date: 2023/3/9 14:38<br/>
 *
 * <AUTHOR> />
 */
public interface FenceDeliveryEnums {

    @Getter
    @AllArgsConstructor
    enum FrequentMethod{
        //周期方案 1周计算 2间隔计算
        WEEK_CALC(1,"周计算"),
        INTERVAL_CALC(2,"间隔计算"),
        ;
        private Integer value;
        private String content;

        /**
         * 根据值获取围栏配送周期方案枚举
         *
         * @param value 值
         * @return 围栏配送周期方案枚举
         */
        public static FenceDeliveryEnums.FrequentMethod getMethodByValue(Integer value) {
            return Arrays.stream(values()).filter(e -> Objects.equals(e.getValue(), value)).findFirst()
                    .orElseThrow(() -> new BizException("不支持的围栏配送周期方案:" + value));
        }
    }

    /**
     * 是否删除标记类型
     */
    @Getter
    @AllArgsConstructor
    enum DeleteFlag implements Enum2Args {

        /**
         * 否
         */
        NO(0, "否"),
        /**
         * 是
         */
        YES(1, "是"),
        ;
        private final Integer value;
        private final String content;

        /**
         * 根据值获取是否删除标记类型枚举
         *
         * @param value 值
         * @return 是否删除标记类型枚举
         */
        public static FenceDeliveryEnums.DeleteFlag getTypeByValue(Integer value) {
            return Arrays.stream(values()).filter(e -> Objects.equals(e.getValue(), value)).findFirst()
                    .orElseThrow(() -> new BizException("不支持的是否删除标记类型:" + value));
        }
    }
}
