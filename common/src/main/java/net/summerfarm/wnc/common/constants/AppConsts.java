package net.summerfarm.wnc.common.constants;

import java.time.LocalTime;

public interface AppConsts {

    Integer errorCode = 500;

    interface OrderErrorMessage {
        String CREATE_FAIL = "创建订单失败";

        String UPDATE_FAIL = "更新订单失败";
    }

    interface WarehouseServiceName {
        String xianMu = "杭州鲜沐科技有限公司";
    }

    public interface XmCloseTime {
        /**
         * 鲜沐售后截单时间
         */
        LocalTime AFTER_SALE_CLOSE_TIME = LocalTime.of(20, 0, 0);
        /**
         * 鲜沐样品截单时间
         */
        LocalTime SAMPLE_APPLY_CLOSE_TIME = LocalTime.of(20, 0, 0);
    }

    interface Saas {
        /**
         * saas截单
         */
        LocalTime SAAS_ORDER_TIME = LocalTime.of(20, 0, 0);
    }

    /**
     * 配置key值
     */
    interface ConfigKey {
        String NO_AREA_CITY = "NO_AREA_CITY";
        String CHANGE_FENCE_FREQUENT_CONTACT_NOTICE = "change_fence_frequent_contact_notice";

        // 霸王茶姬租户ID截单时间(兜底使用)
        String CHAGEE_TENANT_ID_CLOSE_TIME = "CHAGEE_TENANT_ID_CLOSE_TIME";
        // 霸王茶姬加单时长
        String CHAGEE_ADD_ORDER_TIME = "CHAGEE_ADD_ORDER_TIME";
    }

    interface MapStructConstants {
        String COMPONENT_MODEL_SPRING = "spring";
    }

    interface Symbol {
        String COMMA = ",";
        String HASH_TAG = "#";
    }

    /**
     * 租户
     */
    interface Tenant {
        Long XM_TENANT_ID = 1L;
        /**
         * 租户维度默认品牌ID
         */
        Long DEFAULT_BRAND_ID = -1L;
    }

    /**
     * 前端处理
     */
    interface Frontend {
        String UNDEFINED = "undefined";
    }

}
