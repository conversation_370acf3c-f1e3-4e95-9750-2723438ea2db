package net.summerfarm.wnc.common.query.fence;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * Description:围栏ID查询
 * date: 2023/10/27 10:32
 *
 * <AUTHOR>
 */
@Data
public class FenceIdQuery implements Serializable {

    private static final long serialVersionUID = 9220348907197691045L;

    @NotNull(message = "围栏ID不能为空")
    private Integer fenceId;
}
