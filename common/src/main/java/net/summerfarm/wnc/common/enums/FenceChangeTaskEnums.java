package net.summerfarm.wnc.common.enums;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.common.enums.base.Enum2Args;
import net.xianmu.common.exception.BizException;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description:围栏切仓任务枚举
 * date: 2023/8/24 11:22
 *
 * <AUTHOR>
 */
public interface FenceChangeTaskEnums {

    /**
     * 切仓任务类型
     */
    @Getter
    @AllArgsConstructor
    enum Type implements Enum2Args {

        /**
         * 城配仓
         */
        STORE(0, "城配仓"),
        /**
         * 围栏
         */
        FENCE(1, "围栏"),
        ;
        private final Integer value;
        private final String content;

        public boolean isStore(){
            return this == STORE;
        }

        /**
         * 根据值获取切仓任务类型枚举
         *
         * @param value 值
         * @return 切仓任务类型枚举
         */
        public static Type getTypeByValue(Integer value) {
            return Arrays.stream(values()).filter(e -> Objects.equals(e.getValue(), value)).findFirst()
                    .orElseThrow(() -> new BizException("不支持的切仓任务类型:" + value));
        }
    }

    /**
     * 切仓任务状态
     */
    @Getter
    @AllArgsConstructor
    enum Status implements Enum2Args {

        /**
         * 待处理
         */
        WAIT(0, "待处理"),
        /**
         * 已取消
         */
        CANCELED(1, "已取消"),
        /**
         * 已完成
         */
        COMPLETED(2, "已完成"),
        /**
         * 区域切换中
         */
        AREA_CHANGE_ING(10, "区域切换中"),
        /**
         * 订单切换中
         */
        ORDER_CHANGE_ING(15, "订单切换中"),
        /**
         * 处理失败
         */
        FAIL(20, "处理失败"),
        ;
        private final Integer value;
        private final String content;

        /**
         * 根据值获取切仓任务状态枚举
         *
         * @param value 值
         * @return 切仓任务状态枚举
         */
        public static Status getStatusByValue(Integer value) {
            return Arrays.stream(values()).filter(e -> Objects.equals(e.getValue(), value)).findFirst()
                    .orElseThrow(() -> new BizException("不支持的切仓任务状态:" + value));
        }

        public static List<Status> getExecutingStatus() {
            return Lists.newArrayList(Status.WAIT, Status.AREA_CHANGE_ING, Status.ORDER_CHANGE_ING);
        }

        public static List<Integer> getExecutingStatusCode() {
            return getExecutingStatus().stream().map(Status::getValue).collect(Collectors.toList());
        }


    }


}
