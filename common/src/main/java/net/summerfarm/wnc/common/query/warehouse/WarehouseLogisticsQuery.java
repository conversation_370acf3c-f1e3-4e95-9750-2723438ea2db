package net.summerfarm.wnc.common.query.warehouse;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.wnc.common.base.AbstractPageQuery;

import java.util.List;

/**
 * Description: 城配仓查询
 * <AUTHOR> />
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseLogisticsQuery extends AbstractPageQuery {

    /**
     * 配送仓编号
     */
    private Integer storeNo;

    /**
     * 配送中心状态：0、未开放 1、开放
     */
    private Integer status;

    /**
     * 0是未设置 1已设置 2已完成
     */
    private Integer stopDeliveryStatus;

    /**
     * 0关闭 1开启
     */
    private Integer punchState;

    /**
     * 区域集合
     */
    private List<String> regions;

    /**
     * 配送仓编号集合
     */
    private List<Integer> storeNos;

    /**
     * 城配仓名称
     */
    private String storeName;

    /**
     * 履约类型，0：城配履约，1：快递履约
     */
    private Integer fulfillmentType;
}
