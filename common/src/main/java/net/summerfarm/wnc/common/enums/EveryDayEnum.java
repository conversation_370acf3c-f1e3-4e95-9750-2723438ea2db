package net.summerfarm.wnc.common.enums;

import lombok.Getter;

/**
 * Description: <br/>
 * date: 2025/4/15 14:37<br/>
 *
 * <AUTHOR> />
 */
@Getter
public enum EveryDayEnum {

    // 0日配 1非日配
    T_0(0, "日配"),
    T_1(1, "非日配")
    ;

    private Integer value;
    private String content;

    EveryDayEnum(Integer value, String content) {
        this.value = value;
        this.content = content;
    }
}
