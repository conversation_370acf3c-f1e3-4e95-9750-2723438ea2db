package net.summerfarm.wnc.common.query.changeTask;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Description:切仓任务分页查询实体
 * date: 2023/8/23 11:50
 *
 * <AUTHOR>
 */
@Data
public class FenceChangeTaskPageQuery extends BasePageInput {

    private static final long serialVersionUID = -2921450298425122947L;

    /**
     * 围栏ID
     */
    private Integer fenceId;

    /**
     * 围栏名称
     */
    private String fenceName;

    /**
     * 切仓任务状态,0:待处理,1:已取消,2:已完成,10:区域切换中,15:订单切换中,20:处理失败
     */
    private List<Integer> status;

    /**
     * 切仓时间开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime exeDateStart;

    /**
     * 切仓时间结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime exeDateEnd;

}
