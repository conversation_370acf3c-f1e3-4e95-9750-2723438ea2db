package net.summerfarm.wnc.common.util;

import java.util.Iterator;
import java.util.Map;
import java.util.Set;

/**
 * Description: <br/>
 * date: 2023/4/23 19:03<br/>
 *
 * <AUTHOR> />
 */
public class MapUtil {

    public static Map<String, Object> removeMapKey(Map param) {
        Set set = param.keySet();

        for (Iterator iterator = set.iterator(); iterator.hasNext(); ) {
            Object obj = (Object) iterator.next();
            Object value = (Object) param.get(obj);
            if (value == null || value.equals("") || value.equals("null") || obj.toString().length() == 0) {
                iterator.remove();
            }
        }

        return param;
    }
}
