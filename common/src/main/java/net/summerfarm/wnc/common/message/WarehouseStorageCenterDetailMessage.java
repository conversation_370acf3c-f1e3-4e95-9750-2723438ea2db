package net.summerfarm.wnc.common.message;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/8/29 16:01<br/>
 *
 * <AUTHOR> />
 */
@Data
public class WarehouseStorageCenterDetailMessage {
    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 仓库负责人
     */
    private Integer manageAdminId;

    /**
     * 仓库类型：0、本部仓 1、外部仓 2、合伙人仓
     */
    private Integer type;

    /**
     * 仓库所属合伙人
     */
    private Integer areaManageId;

    /**
     * 开放状态：0、不开放 1、开放
     */
    private Integer status;
    /**
     * 仓库地址
     */
    private String address;

    /**
     * 高德poi
     */
    private String poiNote;

    /**
     * 邮件接收人
     */
    private String mailToAddress;

    /**
     * 修改人
     */
    private Integer updater;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 联系人
     */
    private String personContact;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 所属租户0鲜沐
     */
    private Long tenantId;

    /**
     * 预约提前期
     */
    private Integer advanceDay;

    /**
     * 产能
     */
    private Long capacity;
    /**
     * 仓库作业时间
     */
    private List<WorkTimeMessage> workTimes;

    /**
     * 收货标准
     */
    private List<WarehouseTakeStandardMessage> warehouseTakeStandard;

    /**
     * 仓库照片
     */
    private String warehousePic;
}
