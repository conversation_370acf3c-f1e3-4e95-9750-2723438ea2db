package net.summerfarm.wnc.common.query.warehouse;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Description: <br/>
 * date: 2023/4/7 18:34<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WarehouseInventoryMappingQuery {

    /**
     * 城配仓编号
     */
    private Integer storeNo;
    /**
     * 城配仓编号集合
     */
    private List<Integer> storeNos;
    /**
     * 城市
     */
    private String city;

    /**
     * 区域
     */
    private String area;

    /**
     * sku信息
     */
    private List<String> skus;

    /**
     * sku信息
     */
    private String sku;

    /**
     * 仓库编号集合
     */
    private List<Integer> warehouseNos;
}
