package net.summerfarm.wnc.common.query.deliveryRule;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Description: 地址配送规则查询<br/>
 * date: 2023/11/13 15:33<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContactDeliveryRuleQuery {

    /**
     * 外部业务编号
     */
    private String outBusinessNo;

    /**
     * 系统来源 0鲜沐商城 1Saas
     */
    private Integer systemSource;

    /**
     * 租户ID
     */
    private Long tenantId;
}
