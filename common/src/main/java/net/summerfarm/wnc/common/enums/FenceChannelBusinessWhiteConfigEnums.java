package net.summerfarm.wnc.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description: 围栏渠道业务白名单<br/>
 * date: 2024/10/14 16:49<br/>
 *
 * <AUTHOR> />
 */
public interface FenceChannelBusinessWhiteConfigEnums {
    /**
     * 作用域下的业务ID 0是全部
     */
    @Getter
    @AllArgsConstructor
    enum ScopeChannelBusinessId {

        /**
         * 城配仓
         */
        ALL("0", "全部"),
        ;
        private final String value;
        private final String content;

    }
}
