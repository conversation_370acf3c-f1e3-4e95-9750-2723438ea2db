package net.summerfarm.wnc.common.query.alert;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Description: 完成配送提醒-查询最晚配送时间接口入参<br/>
 * date: 2024/3/29 15:03<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CompleteDeliveryTimeQueryParam {

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域
     */
    private String area;

    /**
     * 区域状态 0 正常 1 失效
     */
    private Integer adCodeStatus;

    /**
     * 状态 0 正常 1 暂停
     */
    private Integer completeDeliveryStatus;
}
