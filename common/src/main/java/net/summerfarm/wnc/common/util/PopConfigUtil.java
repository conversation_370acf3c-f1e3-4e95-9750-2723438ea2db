package net.summerfarm.wnc.common.util;

import net.summerfarm.wnc.common.config.WncConfig;
import net.summerfarm.wnc.common.config.obj.PopCityAreaOperatingAreaMappingObj;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class PopConfigUtil {

    @Autowired
    private WncConfig wncConfig;

    public PopCityAreaOperatingAreaMappingObj queryMatchedPopConfig(String city, String area) {
        if (StringUtils.isEmpty(city)) {
            return null;
        }
        List<PopCityAreaOperatingAreaMappingObj> mappingObjList = wncConfig.queryPopCityAreaOperatingAreaMapping();
        if (CollectionUtils.isEmpty(mappingObjList)) {
            return null;
        }
        List<PopCityAreaOperatingAreaMappingObj> matchMappingObjList = null;
        if (StringUtils.isEmpty(area)) {
            matchMappingObjList = mappingObjList.stream().filter(mappingObj -> city.equals(mappingObj.getCity())).collect(Collectors.toList());
        } else {
            matchMappingObjList = mappingObjList.stream()
                    .filter(mappingObj -> city.equals(mappingObj.getCity()) && (CollectionUtils.isEmpty(mappingObj.getAreas()) || mappingObj.getAreas().contains(area)))
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(matchMappingObjList)) {
            return null;
        }
        return matchMappingObjList.get(0);
    }
}
