package net.summerfarm.wnc.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description: <br/>
 * date: 2023/4/10 16:16<br/>
 *
 * <AUTHOR> />
 */
public interface WncWarehouseStorageFenceRuleEnums {
    //全局配送规则 0 距离最短 1手动设置优先级
    @Getter
    @AllArgsConstructor
    enum deliveryRule{
        DISTANCE_FIRST(0, "距离最短"),
        HANDLE_FIRST(1, "手动设置优先级");
        ;
        private Integer value;
        private String content;
    }
}
