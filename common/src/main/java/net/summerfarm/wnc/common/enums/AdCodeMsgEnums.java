package net.summerfarm.wnc.common.enums;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * Description: <br/>
 * date: 2023/3/16 15:38<br/>
 *
 * <AUTHOR> />
 */
public interface AdCodeMsgEnums {

    //状态 0正常 1失效
    @Getter
    @AllArgsConstructor
    enum Status{
        VALID(0, "正常"),
        INVALID(1, "失效"),
        STOP(3, "停用");
        ;
        private Integer value;
        private String content;

        public static List<Integer> getUsableStatus() {
            return Lists.newArrayList(AdCodeMsgEnums.Status.VALID.getValue(), AdCodeMsgEnums.Status.STOP.getValue());
        }
    }
}
