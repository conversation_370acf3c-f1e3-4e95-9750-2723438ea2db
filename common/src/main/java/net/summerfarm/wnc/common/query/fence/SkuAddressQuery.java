package net.summerfarm.wnc.common.query.fence;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * Description:Sku + 地址查询
 * date: 2023/12/7 18:40
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SkuAddressQuery implements Serializable {

    private static final long serialVersionUID = -4013281342113719892L;

    /**
     * sku
     */
    private String sku;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    @NotBlank(message = "城市不能为空")
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 详细地址
     */
    private String address;
}
