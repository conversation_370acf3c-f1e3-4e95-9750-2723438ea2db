package net.summerfarm.wnc.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description: <br/>
 * date: 2023/8/22 13:45<br/>
 *
 * <AUTHOR> />
 */
public interface InventoryEnums {

    @Getter
    @AllArgsConstructor
    public enum CreateTypeEnum {

        PLATFORM(0, "平台"),
        KA(1,"大客户"),
        SAAS_SELF_AND_AGENT(2,"saas自营&代仓"),
        SAAS_SELF(3,"saas自营")
        ;


        private Integer type;

        private String description;
    }
}
