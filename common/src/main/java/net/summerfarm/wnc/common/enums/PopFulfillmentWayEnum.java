package net.summerfarm.wnc.common.enums;

import lombok.Getter;

/**
 * POP 专属城配仓枚举
 * <AUTHOR>
 */
@Getter
public enum PopFulfillmentWayEnum {

    //0：POP专配履约，1：POP鲜沐共配履约
    POP_POP_FULFILLMENT_WAY(0, "POP专属城配仓履约"),
    POP_XM_FULFILLMENT_WAY(1, "POP鲜沐共配城配仓履约")
    ;

    private Integer value;
    private String content;

    PopFulfillmentWayEnum(Integer value, String content) {
        this.value = value;
        this.content = content;
    }

}
