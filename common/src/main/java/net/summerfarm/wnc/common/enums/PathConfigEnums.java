package net.summerfarm.wnc.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.common.enums.base.Enum2Args;
import net.xianmu.common.exception.BizException;

import java.util.Arrays;
import java.util.Objects;

/**
 * Description: <br/>
 * date: 2023/11/29 10:31<br/>
 *
 * <AUTHOR> />
 */
public interface PathConfigEnums {

    //业务类型 0调拨 仓到仓
    @Getter
    @AllArgsConstructor
    enum BusinseeType implements Enum2Args {
        REALLOCATE(0, "调拨仓到仓"),
        ;
        private Integer value;
        private String content;


        /**
         * 根据值来源枚举
         *
         * @param value 值
         * @return 枚举
         */
        public static PathConfigEnums.BusinseeType getBusinseeTypeByValue(Integer value) {
            return Arrays.stream(values()).filter(e -> Objects.equals(e.getValue(), value)).findFirst()
                    .orElseThrow(() -> new BizException("不支持的业务类型:" + value));
        }
    }

    //周期方案 1每周 2两周
    @Getter
    @AllArgsConstructor
    enum FrequentMethod{
        ONE_WEEK(1, "每周"),
        TWO_WEEK(2, "两周"),
        ;
        private Integer value;
        private String content;

        /**
         * 根据值来源枚举
         *
         * @param value 值
         * @return 枚举
         */
        public static PathConfigEnums.FrequentMethod getFrequentMethodByValue(Integer value) {
            return Arrays.stream(values()).filter(e -> Objects.equals(e.getValue(), value)).findFirst()
                    .orElseThrow(() -> new BizException("不支持的周期方案:" + value));
        }
    }
}
