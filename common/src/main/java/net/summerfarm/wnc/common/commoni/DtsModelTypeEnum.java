package net.summerfarm.wnc.common.commoni;

import java.util.Arrays;
import java.util.Objects;

/**
 * Description:DtsModel DML操作类型枚举
 * date: 2022/10/21 15:08
 *
 * <AUTHOR>
 */
public enum DtsModelTypeEnum {

    /**
     * 新增
     */
    INSERT,
    /**
     * 修改
     */
    UPDATE,

    /**
     * 删除
     */
    DELETE;

    /**
     * 根据类型获取DML操作类型枚举
     *
     * @param type 类型
     * @return DML操作类型枚举
     */
    public static DtsModelTypeEnum getDtsModelTypeByType(String type){
        return Arrays.stream(values()).filter(e -> Objects.equals(e.name(), type)).findFirst()
                .orElse(null);
    }
}
