package net.summerfarm.wnc.common.query.fence;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.wnc.common.base.AbstractPageQuery;

import java.io.Serializable;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/4/6 18:10<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WncWarehouseStorageFenceRuleQuery extends AbstractPageQuery implements Serializable {
    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * sku
     */
    private List<String> skuList;

    /**
     * 定位信息
     */
    private String poi;
    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 省
     */
    private List<String> provinces;
    /**
     * 市
     */
    private List<String> citys;
    /**
     * 区
     */
    private List<String> areas;

    /**
     * 联系人ID
     */
    private Long contactId;
    /**
     * 商户id
     */
    private Long merchantId;
    /**
     * 是否加单 true 加单 false不加单
     */
    private Boolean addOrderFlag;
    /**
     * 订单来源
     */
    private Integer source;
}
