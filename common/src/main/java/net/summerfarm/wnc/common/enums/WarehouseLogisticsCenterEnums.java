package net.summerfarm.wnc.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.common.enums.base.Enum2Args;
import net.xianmu.common.exception.BizException;

import java.util.Arrays;
import java.util.Objects;

/**
 * Description: <br/>
 * date: 2023/4/10 13:49<br/>
 *
 * <AUTHOR> />
 */
public interface WarehouseLogisticsCenterEnums {
    //状态 0正常 1失效
    @Getter
    @AllArgsConstructor
    enum Status {
        NO_VALID(0, "失效"),
        VALID(1, "有效");
        ;
        private Integer value;
        private String content;
    }

    /**
     * 履约类型
     */
    @Getter
    @AllArgsConstructor
    enum FulfillmentType implements Enum2Args {

        /**
         * 城配履约
         */
        CITY_DELIVERY(0, "城配履约"),

        /**
         * 快递履约
         */
        EXPRESS_DELIVERY(1, "快递履约");
        ;
        private final Integer value;
        private final String content;

        /**
         * 根据值获取城配仓履约类型枚举
         *
         * @param value 值
         * @return 城配仓履约类型枚举
         */
        public static WarehouseLogisticsCenterEnums.FulfillmentType getTypeByValue(Integer value) {
            return Arrays.stream(values()).filter(e -> Objects.equals(e.getValue(), value)).findFirst()
                    .orElseThrow(() -> new BizException("不支持的城配仓履约类型:" + value));
        }
    }
}
