package net.summerfarm.wnc.common.query.warehouse;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Description:SKU库存仓映射查询参数
 * date: 2023/9/4 11:09
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SkuWarehouseMappingQuery implements Serializable {

    private static final long serialVersionUID = 3351759846163517575L;

    /**
     * sku
     */
    private String sku;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

}
