package net.summerfarm.wnc.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.summerfarm.wnc.client.enums.SourceEnum;
import net.xianmu.common.exception.BizException;

import java.util.Arrays;
import java.util.Objects;

/**
 * Description:联系人配置枚举
 * date: 2023/9/22 18:18
 *
 * <AUTHOR>
 */
public interface ContactConfigEnums {

    @Getter
    @AllArgsConstructor
    enum Source{

        /**
         * 鲜沐
         */
        XM(0, "鲜沐"),
        /**
         * SAAS
         */
        SAAS(1, "SAAS");
        ;
        private final Integer value;
        private final String content;

        public static Source getSourceByTenantId(Long tenantId){
            if (Objects.equals(tenantId, WarehouseStorageCenterEnums.TENANT.SUMMER_FARM.getValue())){
                return Source.XM;
            }
            return Source.SAAS;
        }

        public static Source getSourceByOrderSource(SourceEnum sourceEnum){
            if (SourceEnum.getXmSource().contains(sourceEnum)){
                return Source.XM;
            }
            return Source.SAAS;
        }

        public static Source getSourceByValue(Integer value){
            return Arrays.stream(values()).filter(e -> Objects.equals(e.getValue(), value)).findFirst()
                    .orElseThrow(() -> new BizException("不支持的配置来源"));
        }
    }
}
