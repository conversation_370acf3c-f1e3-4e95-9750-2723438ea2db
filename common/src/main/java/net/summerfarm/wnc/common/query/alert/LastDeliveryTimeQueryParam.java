package net.summerfarm.wnc.common.query.alert;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.wnc.common.enums.DeliveryAlertEnums;

/**
 * Description: 查询最晚配送日期<br/>
 * date: 2024/3/29 11:50<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LastDeliveryTimeQueryParam {

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 渠道，0：鲜沐，1：SAAS
     */
    private DeliveryAlertEnums.Channel channel;

    /**
     * 门店ID
     */
    private String outerClientId;

    /**
     * 品牌Id
     */
    private String outerTenantId;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域
     */
    private String area;
}
