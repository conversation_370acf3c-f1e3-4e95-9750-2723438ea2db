package net.summerfarm.wnc.common.query.changeTask;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * Description:切仓任务明细ID查询
 * date: 2023/8/24 14:16
 *
 * <AUTHOR>
 */
@Data
public class FenceChangeTaskDetailIdQuery implements Serializable {

    private static final long serialVersionUID = -3222866773456438141L;

    @NotNull(message = "切仓任务明细ID不能为空")
    private Long changeTaskDetailId;
}
