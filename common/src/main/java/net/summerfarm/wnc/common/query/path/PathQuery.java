package net.summerfarm.wnc.common.query.path;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.wnc.common.base.AbstractPageQuery;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * Description: 路线查询<br/>
 * date: 2023/11/29 10:26<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PathQuery extends AbstractPageQuery {

    /**
     * 路线ID
     */
    private Long pathId;

    /**
     * 开始编号
     */
    private Integer beginOutNo;

    /**
     * 结束编号
     */
    private Integer endOutNo;

    /**
     * 业务类型 0调拨 仓到仓
     */
    private Integer businseeType;

    /**
     * 周期方案 1每周 2两周
     */
    private Integer frequentMethod;

    /**
     * sku
     */
    private String sku;

    /**
     * 仓库信息
     */
    private Integer warehouseNo;

    /**
     * 上级仓库信息
     */
    private Integer supWarehouseNo;
    /**
     * 期望下次调拨时间
     */
    private LocalDate expectLastTime;
}
