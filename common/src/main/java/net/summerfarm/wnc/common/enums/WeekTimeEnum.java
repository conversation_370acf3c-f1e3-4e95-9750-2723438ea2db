package net.summerfarm.wnc.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.common.enums.base.Enum2Args;
import net.xianmu.common.enums.base.Enum2StringArgs;

import java.util.HashMap;
import java.util.Map;

/**
 * Description:星期枚举
 * date: 2023/12/1 16:01
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum WeekTimeEnum implements Enum2Args {

    /**
     * 星期一
     */
    MON(1,"星期一"),
    /**
     * 星期二
     */
    TUE(2,"星期二"),
    /**
     * 星期三
     */
    WED(3,"星期三"),
    /**
     * 星期四
     */
    THUR(4,"星期四"),
    /**
     * 星期五
     */
    FRI(5,"星期五"),
    /**
     * 星期六
     */
    SAT(6,"星期六"),
    /**
     * 星期日
     */
    SUN(7,"星期日"),
    ;

    private final static Map<Integer,String> weekTimeMap = new HashMap<>();

    static{
        for (WeekTimeEnum week : WeekTimeEnum.values()) {
            weekTimeMap.put(week.getValue(),week.getContent());
        }
    }

    private final Integer value;
    private final String content;

    public static String getWeekTime(Integer value){
        return weekTimeMap.get(value);
    }

}
