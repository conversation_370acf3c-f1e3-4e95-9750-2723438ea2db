package net.summerfarm.wnc.common.config.obj;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.time.LocalTime;
import java.util.List;

/**
 * Description: Pop城市区域运营区域映射类<br/>
 * date: 2024/11/7 11:29<br/>
 *
 * <AUTHOR> />
 */
@Data
public class PopCityAreaOperatingAreaMappingObj {
    /**
     * 城市
     */
    private String city;

    /**
     * 区域
     */
    private List<String> areas;

    /**
     * 运营区域编号
     */
    private Integer areaNo;

    /**
     * 指定城配仓编号，可以为空，为空时根据城市区域匹配城配仓
     */
    private Integer storeNo;

    /**
     * 城配仓POP履约方式，0：POP专配履约，1：POP鲜沐共配履约。可以为空，为空时默认POP鲜沐共配履约
     */
    private Integer popFulfillmentWay;

    /**
     * POP配送日期规则，1：T+1，2：T+2，为空时默认T+1
     */
    private Integer popDeliveryDateRule;

    /**
     * 截单时间，可以为空，为空时使用城配仓的截单时间
     */
    @JSONField(format = "HH:mm:ss")
    private LocalTime closeTime;

}
