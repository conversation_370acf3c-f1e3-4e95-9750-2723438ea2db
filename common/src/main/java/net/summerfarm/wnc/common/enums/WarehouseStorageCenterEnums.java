package net.summerfarm.wnc.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description: <br/>
 * date: 2023/4/10 13:49<br/>
 *
 * <AUTHOR> />
 */
public interface WarehouseStorageCenterEnums {
    //状态 0正常 1失效
    @Getter
    @AllArgsConstructor
    enum Status{
        NO_OPEN(0, "不开放"),
        OPEN(1, "开放");
        ;
        private Integer value;
        private String content;
    }

    @Getter
    @AllArgsConstructor
    enum TENANT{
        SUMMER_FARM(1L, "鲜沐"),
        ;
        private Long value;
        private String content;
    }


    @Getter
    @AllArgsConstructor
    enum Type{
        INTERNAL_AREA(0, "本部仓"),
        EXTERNAL_AREA(1, "外部仓"),
        PARTNER_AREA(2, "合伙人仓"),
        ;
        private Integer value;
        private String content;
    }
}
