package net.summerfarm.wnc.common.util;

/**
 * <AUTHOR> :z<PERSON>liang
 * @version : v1.0
 * 创建时间：2018年6月26日 下午3:32:30
 * 类说明 :根据经纬度计算距离
 */
public class DistanceUtil {

    /**
     * 地球半径,单位 km
     */
    private static final double EARTH_RADIUS = 6378.137;

    /**
     * 根据经纬度，计算两点间的距离
     *
     * @param longitude1 第一个点的经度
     * @param latitude1  第一个点的纬度
     * @param longitude2 第二个点的经度
     * @param latitude2  第二个点的纬度
     * @return 返回距离 单位米
     */
    public static double getDistance(double longitude1, double latitude1, double longitude2, double latitude2) {
        // 纬度
        double lat1 = Math.toRadians(latitude1);
        double lat2 = Math.toRadians(latitude2);
        // 经度
        double lng1 = Math.toRadians(longitude1);
        double lng2 = Math.toRadians(longitude2);
        // 纬度之差
        double a = lat1 - lat2;
        // 经度之差
        double b = lng1 - lng2;
        // 计算两点距离的公式
        double s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) +
                Math.cos(lat1) * Math.cos(lat2) * Math.pow(Math.sin(b / 2), 2)));
        // 弧长乘地球半径, 返回单位: 米
        s =  s * EARTH_RADIUS * 1000;
        return s;
    }

    /**
     * 根据经纬度，计算两点间的距离
     *
     * @param poiOne 第一个点的经纬度
     * @param poiTwo  第二个点的经纬度
     * @return 返回距离 单位米
     */
    public static double getPoiDistance(String poiOne,String poiTwo) {
        String[] poiOneArry = poiOne.split(",");
        String[] poiTwoArry = poiTwo.split(",");

        double longitude1 = Double.parseDouble(poiOneArry[0]);
        double latitude1 = Double.parseDouble(poiOneArry[1]);

        double longitude2 = Double.parseDouble(poiTwoArry[0]);
        double latitude2 = Double.parseDouble(poiTwoArry[1]);
        // 纬度
        double lat1 = Math.toRadians(latitude1);
        double lat2 = Math.toRadians(latitude2);
        // 经度
        double lng1 = Math.toRadians(longitude1);
        double lng2 = Math.toRadians(longitude2);
        // 纬度之差
        double a = lat1 - lat2;
        // 经度之差
        double b = lng1 - lng2;
        // 计算两点距离的公式
        double s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) +
                Math.cos(lat1) * Math.cos(lat2) * Math.pow(Math.sin(b / 2), 2)));
        // 弧长乘地球半径, 返回单位: 米
        s =  s * EARTH_RADIUS * 1000;
        return s;
    }

    public static void main(String[] args) {
//        "latitude": 30.752061,
//                "longitude": 120.771093,
//        "latitude": 30.273954,
//                "longitude": 120.153968,
        double d = getDistance(120.771093, 30.752061, 116.353454, 30.273954);
        System.out.println(d);
    }
}