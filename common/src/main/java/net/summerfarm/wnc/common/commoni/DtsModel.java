package net.summerfarm.wnc.common.commoni;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @Description: binlog 同步mq数据接受实体；测试环境和生成环境字段略有不同，DtsModel不要如非必要不要加字段。
 */
@Data
public class DtsModel {
    /**
     * 记录ID（生产环境同时是mq 消息key）
     */
    private String id;
    /**
     * 数据库
     */
    private String database;

    /**
     * 表
     */
    private String table;

    /**
     * 操作类型：INSERT、UPDATE、DELETE
     */
    private String type;
    /**
     * 操作类型：INSERT、UPDATE、DELETE
     */
    private DtsModelTypeEnum dtsModelTypeEnum;

    /**
     * 影响数据
     */
    private List<Map<String, String>> data;

    /**
     * 更新字段数据
     */
    private List<Map<String, String>> old;

    /**
     * 字段容易产生歧义，不对外提供getId()方法
     *
     * @return ID
     */
    private String getId() {
        return this.id;
    }

    /**
     * 查询消息key
     *
     * @return id、msg-key
     */
    public String getMsgKey() {
        return this.id;
    }

    /**
     * 获取数据
     */
    public List<DtsModel> convertModelList() {
        List<DtsModel> list = new ArrayList<>();
        if (this.dtsModelTypeEnum == DtsModelTypeEnum.UPDATE) {
            if (CollectionUtils.isEmpty(this.data)) {
                return Collections.emptyList();
            }
            for (int i = 0; i < this.data.size(); i++) {
                list.add(convertDtsModel(data.get(i), old.get(0)));
            }
        } else if (this.dtsModelTypeEnum == DtsModelTypeEnum.INSERT) {
            if (CollectionUtils.isEmpty(this.data)) {
                return Collections.emptyList();
            }
            for (int i = 0; i < this.data.size(); i++) {
                list.add(convertDtsModel(data.get(i), null));
            }
        } else if (this.dtsModelTypeEnum == DtsModelTypeEnum.DELETE) {
            if (CollectionUtils.isEmpty(this.old)) {
                return Collections.emptyList();
            }
            for (int i = 0; i < this.old.size(); i++) {
                list.add(convertDtsModel(null, old.get(i)));
            }
        }
        return list;
    }

    private DtsModel convertDtsModel(Map<String, String> newData, Map<String, String> oldData) {
        DtsModel dtsModel = new DtsModel();
        dtsModel.setId(this.id);
        dtsModel.setDatabase(this.database);
        dtsModel.setTable(this.table);
        dtsModel.setType(this.type);
        dtsModel.setDtsModelTypeEnum(this.dtsModelTypeEnum);
        if (oldData != null) {
            dtsModel.setOld(Collections.singletonList(oldData));
        }
        if (newData != null) {
            dtsModel.setData(Collections.singletonList(newData));
        }
        return dtsModel;
    }

    /**
     * @param consumer 处理数据
     */
    public void consumerData(Consumer<Map<String, String>> consumer) {
        if (this.data == null || this.data.isEmpty()) {
            return;
        }
        for (Map<String, String> el : this.data) {
            consumer.accept(el);
        }
    }

    /**
     * @param consumer 处理变更数据
     */
    public void consumerOld(Consumer<Map<String, String>> consumer) {
        if (this.old == null || this.old.isEmpty()) {
            return;
        }
        for (Map<String, String> el : this.old) {
            consumer.accept(el);
        }
    }

    public <T> T getNewData(Class<T> clazz) {
        String fieldsJson = JSON.toJSONString(this.data);
        JSONObject jsonObject = JSONObject.parseObject(fieldsJson);
        return JSONObject.parseObject(JSON.toJSONString(jsonObject), clazz);
    }


}
