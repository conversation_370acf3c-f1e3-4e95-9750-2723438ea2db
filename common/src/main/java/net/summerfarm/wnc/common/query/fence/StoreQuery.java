package net.summerfarm.wnc.common.query.fence;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Description:门店信息查询
 * date: 2023/12/7 18:30
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StoreQuery implements Serializable {

    private static final long serialVersionUID = 3879250158769740863L;

    /**
     * 鲜沐门店ID
     */
    private Long merchantId;

    /**
     * 门店注册手机号
     */
    private String phone;
}
