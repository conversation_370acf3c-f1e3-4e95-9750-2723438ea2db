package net.summerfarm.wnc.common.middleware;

import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class MQOperator {

    @Resource
    private MqProducer mqProducer;

    public void sendDataToQueue(String topic,String tag, Object message) {
        mqProducer.send(topic, tag, message);
    }

}
