package net.summerfarm.wnc.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.common.enums.base.Enum2Args;
import net.xianmu.common.exception.BizException;

import java.util.Arrays;
import java.util.Objects;

/**
 * Description:截单时间配置类枚举
 * date: 2024/3/20 14:30
 *
 * <AUTHOR>
 */
public interface CloseTimeConfigEnums {

    /**
     * 更新标记
     */
    @Getter
    @AllArgsConstructor
    enum UpdateFlag implements Enum2Args {

        /**
         * 无
         */
        NO(0, "无"),
        /**
         * 有
         */
        YES(1, "有"),
        ;

        private final Integer value;
        private final String content;

        /**
         * 根据值获取截单时间配置更新标记枚举
         *
         * @param value 值
         * @return 截单时间配置更新标记枚举
         */
        public static CloseTimeConfigEnums.UpdateFlag getFlagByValue(Integer value) {
            return Arrays.stream(values()).filter(e -> Objects.equals(e.getValue(), value)).findFirst()
                    .orElseThrow(() -> new BizException("不支持的配置更新标记:" + value));
        }
    }
}
