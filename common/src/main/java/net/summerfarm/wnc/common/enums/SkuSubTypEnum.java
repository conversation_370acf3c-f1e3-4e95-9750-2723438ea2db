package net.summerfarm.wnc.common.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * Description: 商品二级类型
 * date: 2023/9/28 13:48<br/>
 *
 * <AUTHOR> />
 */
@Getter
@RequiredArgsConstructor
public enum SkuSubTypEnum {
    //二级类型：1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-经销、5 鲜果pop
    SELF_SALE_NO_WAREHOUSE(1,"自营-代销不入仓"),
    SELF_SALE_WAREHOUSE(2,"自营-代销入仓"),
    SELF_SALE(3,"自营-经销"),
    OTHER_SALE(4,"代仓-经销"),
    POP(5,"鲜果pop");

    ;

    private final Integer code;
    private final String name;
}
