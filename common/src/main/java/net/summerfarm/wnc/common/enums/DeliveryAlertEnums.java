package net.summerfarm.wnc.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.common.exception.BizException;

import java.util.Arrays;
import java.util.Objects;

/**
 * Description:配送提醒枚举
 * date: 2023/3/21 19:22
 *
 * <AUTHOR>
 */
public interface DeliveryAlertEnums {

    @Getter
    @AllArgsConstructor
    enum Status {

        /**
         * 正常
         */
        NORMAL(0, "正常"),
        /**
         * 暂停
         */
        STOP(1, "暂停"),
        ;
        private final Integer value;
        private final String content;
    }

    @Getter
    @AllArgsConstructor
    enum Channel {

        /**
         * 鲜沐
         */
        XM_MALL(0, "鲜沐"),
        /**
         * SAAS
         */
        SAAS(1, "SAAS"),
        ;
        private final Integer value;
        private final String content;

        /**
         * 根据数值获取配送提醒渠道枚举
         * @param value 数值
         * @return 配送提醒渠道枚举
         */
        public static Channel getChannelByValue(Integer value) {
            return Arrays.stream(values()).filter(e -> Objects.equals(e.getValue(), value)).findFirst()
                    .orElseThrow(() -> new BizException("不支持的配送提醒渠道:" + value));
        }
    }

    @Getter
    @AllArgsConstructor
    enum Type {

        /**
         * 品牌
         */
        BRAND(0, "品牌"),
        /**
         * 门店
         */
        MERCHANT(1, "门店"),
        ;
        private final Integer value;
        private final String content;

        /**
         * 根据数值获取配送提醒类型枚举
         * @param value 数值
         * @return 配送提醒类型枚举
         */
        public static Type getTypeByValue(Integer value) {
            return Arrays.stream(values()).filter(e -> Objects.equals(e.getValue(), value)).findFirst()
                    .orElseThrow(() -> new BizException("不支持的配送提醒类型:" + value));
        }
    }
}
