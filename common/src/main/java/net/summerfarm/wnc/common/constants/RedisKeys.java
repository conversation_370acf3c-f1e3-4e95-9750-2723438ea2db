package net.summerfarm.wnc.common.constants;

/**
 * Description:Redis 键名称
 * date: 2023/11/30 10:57
 *
 * <AUTHOR>
 */
public class RedisKeys {

    private static final String GLOBAL_FENCE_PACK_ID = "global_fence_pack_id";
    public static final String GLOBAL_FENCE_CREATE = "wnc-fence-create:";
    public static final String GLOBAL_FENCE_UPDATE = "wnc-fence-update:";
    public static final String GLOBAL_PRECISE_DELIVERY_CONFIG_UPSERT = "wnc-precise-delivery-config-upsert:";

    private static String buildRedisKey(String prefix, Object... params) {
        StringBuilder builder = new StringBuilder(prefix);
        for (Object param : params) {
            builder.append(":").append(param);
        }
        return builder.toString();
    }

    public static String buildGlobalFencePackIdKey() {
        return buildRedisKey(GLOBAL_FENCE_PACK_ID);
    }

    public static String buildGlobalFenceCreateKey() {
        return buildRedisKey(GLOBAL_FENCE_CREATE);
    }

    public static String buildFenceUpdateKey(Integer fenceId) {
        return buildRedisKey(GLOBAL_FENCE_UPDATE, fenceId);
    }

}
