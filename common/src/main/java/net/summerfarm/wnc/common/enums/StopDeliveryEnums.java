package net.summerfarm.wnc.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.common.enums.base.Enum2Args;

/**
 * Description: <br/>
 * date: 2023/10/9 17:11<br/>
 *
 * <AUTHOR> />
 */
public interface StopDeliveryEnums {

    /**
     * 是否删除
     */
    @Getter
    @AllArgsConstructor
    enum DeleteFlag implements Enum2Args {

        Effective(0, "有效"),
        Delete(1, "已删除"),
        ;

        private Integer value;
        private String content;
    }
}
