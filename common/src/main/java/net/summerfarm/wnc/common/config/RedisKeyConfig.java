package net.summerfarm.wnc.common.config;

/**
 * Description: redis key配置<br/>
 * date: 2024/4/2 10:12<br/>
 *
 * <AUTHOR> />
 */
public interface RedisKeyConfig {

    String APP_NAME = "wnc";

    String SPLIT = ":";

    String CACHE = "cache";

    /**
     * 查询鲜沐最晚配送时效缓存 key
     */
    String SUMMERFARM_DELIVERY_ALERT_TIME_CACHE_KEY = APP_NAME + SPLIT + CACHE + SPLIT + "summerfarm_delivery_alert_time_cache_key";
}
