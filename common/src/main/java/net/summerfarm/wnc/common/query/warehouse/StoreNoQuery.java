package net.summerfarm.wnc.common.query.warehouse;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Description:城配仓编号查询
 * date: 2023/9/25 14:51
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StoreNoQuery implements Serializable {

    private static final long serialVersionUID = -8268513008530986936L;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 定位信息
     */
    private String poi;

    /**
     * 联系人ID
     */
    private Long contactId;

    /**
     * 租户ID
     */
    private Long tenantId;
}
