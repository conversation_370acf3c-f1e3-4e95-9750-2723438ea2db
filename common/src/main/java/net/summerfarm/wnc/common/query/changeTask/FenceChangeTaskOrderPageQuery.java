package net.summerfarm.wnc.common.query.changeTask;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import javax.validation.constraints.NotNull;

/**
 * Description:围栏切仓任务订单分页查询实体
 * date: 2023/8/24 13:52
 *
 * <AUTHOR>
 */
@Data
public class FenceChangeTaskOrderPageQuery extends BasePageInput {

    private static final long serialVersionUID = 5040864470863838773L;

    /**
     * 切仓任务ID
     */
    @NotNull(message = "切仓任务ID不能为空")
    private Long changeTaskId;

}
