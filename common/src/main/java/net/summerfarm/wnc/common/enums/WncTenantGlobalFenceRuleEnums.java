package net.summerfarm.wnc.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description: <br/>
 * date: 2023/4/10 15:22<br/>
 *
 * <AUTHOR> />
 */
public interface WncTenantGlobalFenceRuleEnums {
    //全局配送规则 0自营仓优先 1三方仓优先
    @Getter
    @AllArgsConstructor
    enum deliveryRule{
        SELF_FIRST(0, "自营仓优先"),
        THIRD_FIRST(1, "三方仓优先");
        ;
        private Integer value;
        private String content;
    }
}
