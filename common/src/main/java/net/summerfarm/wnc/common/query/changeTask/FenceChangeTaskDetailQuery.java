package net.summerfarm.wnc.common.query.changeTask;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Description:切仓任务明细查询实体
 * date: 2023/8/28 18:40
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FenceChangeTaskDetailQuery implements Serializable {

    private static final long serialVersionUID = -1808908268247081577L;

    /**
     * 状态，10：待处理，20：处理成功，30：处理失败
     */
    private Integer status;

    /**
     * 切仓任务ID
     */
    private Long taskId;

}
