package net.summerfarm.wnc.common.util;

import cn.hutool.core.thread.NamedThreadFactory;

import java.util.concurrent.*;

/**
 * Description:线程池管理类
 * date: 2023/11/10 14:16
 *
 * <AUTHOR>
 */
public class ExecutorUtil {

    /**
     * 高德查询文本地址poi线程池
     */
    public static ExecutorService gdPoiExecutor = new ThreadPoolExecutor(50, 200,
            60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(500), new NamedThreadFactory("gaoDeQueryPoi-", false),
            new ThreadPoolExecutor.AbortPolicy());

    /**
     * 高德查询文本地址poi线程池
     */
    public static ExecutorService gdGeoExecutor = new ThreadPoolExecutor(50, 200,
            60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(500), new NamedThreadFactory("gaoDeQueryGeo-", false),
            new ThreadPoolExecutor.AbortPolicy());
}
