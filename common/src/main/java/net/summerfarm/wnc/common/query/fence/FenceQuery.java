package net.summerfarm.wnc.common.query.fence;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/3/16 15:15<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FenceQuery {

    /**
     * 围栏ID
     */
    private Integer fenceId;

    /**
     * 围栏ID集合
     */
    private List<Integer> fenceIds;

    /**
     * 状态 0正常 1失效
     */
    private Integer status;

    /**
     * 状态 0正常 1失效
     */
    private List<Integer> statusList;

    /**
     * 省市区
     */
    private List<AdCodeMsgQuery> adCodeMsgList;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 定位信息
     */
    private String poi;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 行政市名称集合
     */
    private List<String> cityNames;

    /**
     * 开始ID
     */
    private Integer beginId;
    /**
     * 结束ID
     */
    private Integer endId;

    /**
     * 围栏名称
     */
    private String fenceName;

    /**
     * 运营服务区编号
     */
    private Integer areaNo;

    /**
     * 运营服务区编号集合
     */
    private List<Integer> areaNos;

    /**
     * 城配仓编号集合
     */
    private List<Integer> storeNos;

    /**
     * 围栏ID集合
     */
    private List<Integer> notInFenceIds;
}
