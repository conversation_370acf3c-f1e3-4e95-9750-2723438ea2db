package net.summerfarm.wnc.common.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.FieldDefaults;
import net.xianmu.common.exception.error.code.ErrorCode;

/**
 * Description:
 * date: 2023/11/30 11:09
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public enum WncErrorCode implements ErrorCode {

    /**
     * 1000000 ~ 1000100
     * 通用错误
     */
    SYSTEM_ERROR("1000000", "系统繁忙,请稍后重试"),
    PARAM_ERROR("1000001", "参数有误"),
    EXPORT_ERROR("1000002", "导出异常"),
    EXCEL_PARSE_ERROR("1000003", "Excel解析异常"),
    IMPORT_ERROR("1000004", "导入Excel处理异常"),
    DUPLICATE_DATA_ERROR("1000005", "存在重复数据"),
    FREQUENT_OPERATION_ERROR("1000006", "操作频繁，请稍后重试"),
    OTHER_PERSON_OPERATION_ERROR("1000007", "已有人在进行当前操作，请稍后重试"),
    ;

    String code;
    String message;

    @Override
    public Integer getStatus() {
        return null;
    }
}
