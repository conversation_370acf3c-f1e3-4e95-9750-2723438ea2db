package net.summerfarm.wnc.common.query.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * Description: 联系人配置查询实体
 * date: 2023/9/22 17:27
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContactConfigQuery implements Serializable {

    private static final long serialVersionUID = 7607553984885825439L;

    /**
     * 来源，0：鲜沐，1：saas
     */
    private Integer source;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 外部联系人ID
     */
    private Long outerContactId;

    /**
     * 外部联系人ID集合
     */
    private List<Long> outerContactIds;
}
