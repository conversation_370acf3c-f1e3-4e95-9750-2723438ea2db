package net.summerfarm.wnc.common.query.warehouse;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.wnc.common.base.AbstractPageQuery;

import java.io.Serializable;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/3/30 17:04<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WarehouseStorageFenceQuery extends AbstractPageQuery implements Serializable {
    /**
     * 仓库名称
     */
    private String warehouseName;
    /**
     * 省
     */
    private List<String> provinces;
    /**
     * 市
     */
    private List<String> citys;
    /**
     * 区
     */
    private List<String> areas;
    /**
     * 租户ID 鲜沐默认为0
     */
    private Long tenantId;
    /**
     * 仓库编号集合
     */
    private List<Integer> warehouseNos;
    /**
     * 仓库编号
     */
    private Integer warehouseNo;
    /**
     * 不包含此仓库编号
     */
    private Integer noThisWarehouseNo;

    /**
     * sku
     */
    private String sku;
}
