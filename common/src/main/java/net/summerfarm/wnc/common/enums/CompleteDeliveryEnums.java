package net.summerfarm.wnc.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description: 完成配送提醒枚举<br/>
 * date: 2023/3/8 11:45<br/>
 *
 * <AUTHOR> />
 */
public interface CompleteDeliveryEnums {
    //状态 0 正常 1 失效
    @Getter
    @AllArgsConstructor
    enum Status{
        NORMAL(0,"正常"),
        STOP(1,"暂停"),
        ;
        private Integer value;
        private String content;
    }
}
