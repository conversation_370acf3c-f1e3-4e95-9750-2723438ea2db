package net.summerfarm.wnc.common.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.common.enums.base.Enum2Args;

/**
 * 订单表中的枚举
 */
public interface OrderEnums {

    /**
     * 订单状态
     */
    @Getter
    @AllArgsConstructor
    enum Status implements Enum2Args {

        WAIT_PAY(100, "待支付"),
        CANCEL(200, "已取消"),
        PAID(300, "已支付"),
        DELIVERED(400, "已发货"),
        SIGNED(500, "已签收"),
        ;

        private Integer value;
        private String content;
    }

    /**
     * 订单类型
     */
    @Getter
    @AllArgsConstructor
    enum OrderType implements Enum2Args {
        /**
         * 创建
         */
        SAAS(100, "Saas订单"),
        XIANMU(200, "鲜沐订单"),
        KA(300, "大客户订单"),
        ;

        private Integer value;
        private String content;
    }

}
